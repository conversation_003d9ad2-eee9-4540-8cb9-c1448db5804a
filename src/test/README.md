# AcRoomRocketV2Service 概率抽奖测试

本目录包含用于测试 `AcRoomRocketV2Service` 类中 `getMetaIdByProbability2` 方法的测试类。该方法用于按照给定权重概率抽取物品，在活动奖励、礼物发放等场景中使用。

## 测试类说明

### 1. AcRoomRocketV2ServiceTest

- 标准单元测试类，使用 JUnit 和 Mockito 框架
- 需要在 Spring 环境下运行
- 测试方法覆盖了各种边界情况和正常使用场景

该测试类验证了以下场景：
- 空 Map 处理
- 单一元素抽取
- 多元素不同权重抽取
- 零权重元素处理
- 全零权重处理
- 极端性能测试
- 负权重处理

### 2. MetaIdProbabilityTest

- 独立测试类，可以直接运行，不依赖 Spring 环境
- 包含对方法的重新实现，适合快速演示和验证
- 提供了概率统计和可视化输出功能

该类包含以下功能：
- 概率抽奖方法的实现
- 运行多次测试并统计结果
- 计算期望概率和实际概率对比
- 多个典型场景的测试示例

## 使用方法

### 运行单元测试

确保已添加必要的测试依赖（JUnit, Mockito）到项目 pom.xml:

```xml
<dependency>
    <groupId>junit</groupId>
    <artifactId>junit</artifactId>
    <version>4.12</version>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <version>2.23.4</version>
    <scope>test</scope>
</dependency>
```

然后可以通过 Maven 运行测试：

```bash
mvn test -Dtest=AcRoomRocketV2ServiceTest
```

### 运行独立演示

直接运行 `MetaIdProbabilityTest` 类的 main 方法，可以看到各种场景下的抽奖结果：

```bash
java -cp target/test-classes com.quhong.service.MetaIdProbabilityTest
```

或在 IDE 中直接运行该类。

## 测试结果示例

`MetaIdProbabilityTest` 运行后将输出类似以下结果：

```
=========== 抽奖测试结果 ===========
测试次数: 10000
元素ID	权重	期望概率	实际概率	偏差
-----------------------------------
A	100	0.3333	0.3315	0.0018
B	100	0.3333	0.3376	0.0043
C	100	0.3333	0.3309	0.0024
===================================

场景2: 不同概率
=========== 抽奖测试结果 ===========
测试次数: 10000
元素ID	权重	期望概率	实际概率	偏差
-----------------------------------
稀有物品	5	0.0500	0.0506	0.0006
普通物品	35	0.3500	0.3486	0.0014
常见物品	60	0.6000	0.6008	0.0008
===================================
```

这有助于验证抽奖算法的正确性以及实际概率分布是否符合预期。 