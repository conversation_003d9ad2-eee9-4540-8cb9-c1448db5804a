package com.quhong.operation.test;

import com.alibaba.fastjson.JSONObject;
import com.quhong.operation.server.GeneralService;
import com.quhong.operation.share.vo.HuaWeiCheckVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 华为检查开关功能测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HuaweiCheckSwitchTest {

    @Resource
    private GeneralService generalService;

    @Test
    public void testHuaweiCheckSwitchSet() {
        String testUid = "test_uid_123";
        
        // 创建测试数据
        HuaWeiCheckVO dto = new HuaWeiCheckVO();
        dto.setHuaweiCheckSwitch(1);
        dto.setHuaweiCheckVersion(100);
        
        try {
            // 测试设置开关
            generalService.huaweiCheckSwitchSet(testUid, dto);
            System.out.println("华为检查开关设置成功");
            
            // 测试获取开关配置
            HuaWeiCheckVO result = generalService.huaweiCheckSwitchList(testUid);
            System.out.println("华为检查开关配置: " + JSONObject.toJSONString(result));
            
            // 验证结果
            assert result.getHuaweiCheckSwitch() == 1;
            assert result.getHuaweiCheckVersion() == 100;
            assert result.getLogList() != null;
            
            System.out.println("华为检查开关功能测试通过");
            
        } catch (Exception e) {
            System.err.println("华为检查开关功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testHuaweiCheckSwitchList() {
        String testUid = "test_uid_456";
        
        try {
            // 测试获取开关配置
            HuaWeiCheckVO result = generalService.huaweiCheckSwitchList(testUid);
            System.out.println("华为检查开关配置: " + JSONObject.toJSONString(result));
            
            // 验证结果结构
            assert result != null;
            assert result.getLogList() != null;
            
            System.out.println("华为检查开关列表功能测试通过");
            
        } catch (Exception e) {
            System.err.println("华为检查开关列表功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
