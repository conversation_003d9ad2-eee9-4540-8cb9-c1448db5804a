package com.quhong.constant;

import com.quhong.enums.SLangType;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 测试ActivityHttpCode的国际化功能
 * 注意：这些测试需要在Spring上下文中运行才能正确加载i18n资源文件
 */
public class ActivityHttpCodeTest {

    @Test
    public void testAlreadyPassKey() {
        // 测试key是否正确
        String key = ActivityHttpCode.ALREADY_PASS.getMsg();
        assertEquals("already_pass", key);
    }

    @Test
    public void testAlreadyPendingKey() {
        // 测试key是否正确
        String key = ActivityHttpCode.ALREADY_PENDING.getMsg();
        assertEquals("already_pending", key);
    }

    @Test
    public void testAlreadyRejectedKey() {
        // 测试key是否正确
        String key = ActivityHttpCode.ALREADY_REJECTED.getMsg();
        assertEquals("already_rejected", key);
    }

    @Test
    public void testErrorCodes() {
        // 测试错误代码
        assertEquals(6022, ActivityHttpCode.ALREADY_PASS.getCode());
        assertEquals(6023, ActivityHttpCode.ALREADY_PENDING.getCode());
        assertEquals(6024, ActivityHttpCode.ALREADY_REJECTED.getCode());
    }
}
