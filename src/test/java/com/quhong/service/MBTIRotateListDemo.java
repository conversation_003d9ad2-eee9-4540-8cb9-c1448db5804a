package com.quhong.service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MBTI mbtiRotateList计算演示类
 * 演示各维度占比的计算逻辑
 */
public class MBTIRotateListDemo {

    public static void main(String[] args) {
        System.out.println("=== MBTI mbtiRotateList 计算演示 ===");
        
        demonstrateRotateListCalculation();
    }

    /**
     * 演示mbtiRotateList的计算逻辑
     */
    private static void demonstrateRotateListCalculation() {
        
        // 示例1: ENFP类型 - 全E、全N、全F、全P
        System.out.println("\n示例1: ENFP类型");
        System.out.println("前7题全选A (E): E=7, I=0 -> E占比 = 7/(7+0) = 100%");
        System.out.println("第8-14题全选B (N): S=0, N=7 -> S占比 = 0/(0+7) = 0%");
        System.out.println("第15-21题全选B (F): T=0, F=7 -> T占比 = 0/(0+7) = 0%");
        System.out.println("第22-28题全选B (P): J=0, P=7 -> J占比 = 0/(0+7) = 0%");
        System.out.println("mbtiRotateList = [100, 0, 0, 0]");
        
        // 示例2: ISTJ类型 - 全I、全S、全T、全J
        System.out.println("\n示例2: ISTJ类型");
        System.out.println("前7题全选B (I): E=0, I=7 -> E占比 = 0/(0+7) = 0%");
        System.out.println("第8-14题全选A (S): S=7, N=0 -> S占比 = 7/(7+0) = 100%");
        System.out.println("第15-21题全选A (T): T=7, F=0 -> T占比 = 7/(7+0) = 100%");
        System.out.println("第22-28题全选A (J): J=7, P=0 -> J占比 = 7/(7+0) = 100%");
        System.out.println("mbtiRotateList = [0, 100, 100, 100]");
        
        // 示例3: 混合情况 - ENTP类型
        System.out.println("\n示例3: ENTP类型 (混合答案)");
        System.out.println("前7题: 5个A, 2个B -> E=5, I=2 -> E占比 = 5/(5+2) = 71%");
        System.out.println("第8-14题: 3个A, 4个B -> S=3, N=4 -> S占比 = 3/(3+4) = 43%");
        System.out.println("第15-21题: 6个A, 1个B -> T=6, F=1 -> T占比 = 6/(6+1) = 86%");
        System.out.println("第22-28题: 2个A, 5个B -> J=2, P=5 -> J占比 = 2/(2+5) = 29%");
        System.out.println("mbtiRotateList = [71, 43, 86, 29]");
        
        // 示例4: 边界情况 - 接近50%对50%
        System.out.println("\n示例4: 边界情况");
        System.out.println("前7题: 4个A, 3个B -> E=4, I=3 -> E占比 = 4/(4+3) = 57%");
        System.out.println("第8-14题: 3个A, 4个B -> S=3, N=4 -> S占比 = 3/(3+4) = 43%");
        System.out.println("第15-21题: 4个A, 3个B -> T=4, F=3 -> T占比 = 4/(4+3) = 57%");
        System.out.println("第22-28题: 3个A, 4个B -> J=3, P=4 -> J占比 = 3/(3+4) = 43%");
        System.out.println("mbtiRotateList = [57, 43, 57, 43]");
        
        System.out.println("\n=== 计算公式说明 ===");
        System.out.println("E占比 = E选择数量 / (E选择数量 + I选择数量) * 100");
        System.out.println("S占比 = S选择数量 / (S选择数量 + N选择数量) * 100");
        System.out.println("T占比 = T选择数量 / (T选择数量 + F选择数量) * 100");
        System.out.println("J占比 = J选择数量 / (J选择数量 + P选择数量) * 100");
        
        System.out.println("\n=== 注意事项 ===");
        System.out.println("1. mbtiRotateList固定包含4个元素，依次为E、S、T、J的占比");
        System.out.println("2. 占比范围为0-100的整数");
        System.out.println("3. 使用Math.round()进行四舍五入");
        System.out.println("4. 每个维度的两个选项占比之和为100%");
        System.out.println("5. 题目按key升序处理，前7题判定E/I，第8-14题判定S/N，第15-21题判定T/F，第22-28题判定J/P");
        
        System.out.println("\n=== 演示完成 ===");
    }
    
    /**
     * 模拟百分比计算
     */
    private static int calculatePercentage(int count, int total) {
        if (total == 0) {
            return 0;
        }
        return Math.round((float) count * 100 / total);
    }
}
