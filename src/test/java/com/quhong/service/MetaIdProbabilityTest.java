package com.quhong.service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 概率抽奖工具的测试和演示类
 * 可以直接运行此类进行测试，不依赖Spring环境
 */
public class MetaIdProbabilityTest {

    /**
     * 根据概率权重随机选择一个元素
     * 
     * @param sourceMap 包含元素ID和权重的Map
     * @return 选中的元素ID，若无有效元素则返回null
     */
    public static String getMetaIdByProbability2(Map<String, Integer> sourceMap) {
        if (sourceMap == null || sourceMap.isEmpty()) {
            return null;
        }
        
        TreeMap<Integer, String> weightMap = new TreeMap<>();
        int totalWeight = 0;
        
        for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
            if (entry.getValue() > 0) {
                totalWeight += entry.getValue();
                weightMap.put(totalWeight, entry.getKey());
            }
        }
        
        if (totalWeight == 0) {
            return null;
        }
        
        int randomWeight = ThreadLocalRandom.current().nextInt(totalWeight);
        return weightMap.higherEntry(randomWeight).getValue();
    }

    /**
     * 运行多次抽奖并统计结果
     * 
     * @param sourceMap 抽奖池
     * @param times 抽奖次数
     * @return 统计结果
     */
    public static Map<String, Double> runTest(Map<String, Integer> sourceMap, int times) {
        Map<String, Integer> countMap = new HashMap<>();
        
        // 运行多次抽奖
        for (int i = 0; i < times; i++) {
            String result = getMetaIdByProbability2(sourceMap);
            if (result != null) {
                countMap.put(result, countMap.getOrDefault(result, 0) + 1);
            }
        }
        
        // 计算概率
        Map<String, Double> ratioMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
            ratioMap.put(entry.getKey(), (double) entry.getValue() / times);
        }
        
        return ratioMap;
    }

    /**
     * 计算期望概率
     * 
     * @param sourceMap 抽奖池
     * @return 期望概率
     */
    public static Map<String, Double> calculateExpectedRatio(Map<String, Integer> sourceMap) {
        int totalWeight = 0;
        for (Integer weight : sourceMap.values()) {
            if (weight > 0) {
                totalWeight += weight;
            }
        }
        
        Map<String, Double> expectedMap = new HashMap<>();
        if (totalWeight > 0) {
            for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
                if (entry.getValue() > 0) {
                    expectedMap.put(entry.getKey(), (double) entry.getValue() / totalWeight);
                } else {
                    expectedMap.put(entry.getKey(), 0.0);
                }
            }
        }
        
        return expectedMap;
    }

    /**
     * 显示测试结果
     */
    public static void printTestResult(Map<String, Integer> sourceMap, int times) {
        Map<String, Double> actualRatio = runTest(sourceMap, times);
        Map<String, Double> expectedRatio = calculateExpectedRatio(sourceMap);
        
        System.out.println("=========== 抽奖测试结果 ===========");
        System.out.println("测试次数: " + times);
        System.out.println("元素ID\t权重\t期望概率\t实际概率\t偏差");
        System.out.println("-----------------------------------");
        
        for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
            String id = entry.getKey();
            int weight = entry.getValue();
            double expected = expectedRatio.getOrDefault(id, 0.0);
            double actual = actualRatio.getOrDefault(id, 0.0);
            double diff = Math.abs(expected - actual);
            
            System.out.printf("%s\t%d\t%.4f\t%.4f\t%.4f%n", 
                    id, weight, expected, actual, diff);
        }
        
        System.out.println("===================================");
    }

    /**
     * 测试一些典型场景
     */
    public static void main(String[] args) {
        // 测试1: 均匀概率
        Map<String, Integer> equalMap = new HashMap<>();
        equalMap.put("A", 100);
        equalMap.put("B", 100);
        equalMap.put("C", 100);
        System.out.println("场景1: 均匀概率");
        printTestResult(equalMap, 10000);
        
        // 测试2: 不同概率
        Map<String, Integer> unequalMap = new HashMap<>();
        unequalMap.put("稀有物品", 5);    // 5%
        unequalMap.put("普通物品", 35);   // 35%
        unequalMap.put("常见物品", 60);   // 60%
        System.out.println("\n场景2: 不同概率");
        printTestResult(unequalMap, 10000);
        
        // 测试3: 包含零权重
        Map<String, Integer> zeroWeightMap = new HashMap<>();
        zeroWeightMap.put("有效物品A", 50);
        zeroWeightMap.put("无效物品", 0);
        zeroWeightMap.put("有效物品B", 50);
        System.out.println("\n场景3: 包含零权重");
        printTestResult(zeroWeightMap, 10000);
        
        // 测试4: 单一物品
        Map<String, Integer> singleMap = new HashMap<>();
        singleMap.put("唯一物品", 1);
        System.out.println("\n场景4: 单一物品");
        printTestResult(singleMap, 1000);
        
        // 测试5: 大量物品
        Map<String, Integer> largeMap = new HashMap<>();
        for (int i = 1; i <= 10; i++) {
            largeMap.put("物品" + i, i * 10); // 权重递增
        }
        System.out.println("\n场景5: 多种物品(权重递增)");
        printTestResult(largeMap, 10000);

         // 测试6: 空map
         Map<String, Integer> emptyMap = new HashMap<>();
         System.out.println("\n场景6: 空map");
         printTestResult(emptyMap, 10);
    }
} 