package com.quhong.service;

import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.data.vo.MBTIVO;

import java.util.HashMap;
import java.util.Map;

/**
 * MBTI服务演示类
 * 演示按key升序排列和使用静态常量的功能
 */
public class MBTIServiceDemo {

    public static void main(String[] args) {
        // 模拟测试数据
        demonstrateKeyOrdering();
    }

    /**
     * 演示key排序功能
     */
    private static void demonstrateKeyOrdering() {
        System.out.println("=== MBTI性格判定演示 ===");
        
        // 创建测试数据 - 使用乱序的key
        Map<Integer, String> quizAnswer = new HashMap<>();
        
        // 故意使用乱序的key来测试排序功能
        // 前7题选A (E) - key: 100, 5, 200, 15, 300, 25, 400
        quizAnswer.put(100, "A"); // 第1题
        quizAnswer.put(5, "A");   // 第2题  
        quizAnswer.put(200, "A"); // 第3题
        quizAnswer.put(15, "A");  // 第4题
        quizAnswer.put(300, "A"); // 第5题
        quizAnswer.put(25, "A");  // 第6题
        quizAnswer.put(400, "A"); // 第7题
        
        // 第8-14题选B (N) - key: 500, 35, 600, 45, 700, 55, 800
        quizAnswer.put(500, "B"); // 第8题
        quizAnswer.put(35, "B");  // 第9题
        quizAnswer.put(600, "B"); // 第10题
        quizAnswer.put(45, "B");  // 第11题
        quizAnswer.put(700, "B"); // 第12题
        quizAnswer.put(55, "B");  // 第13题
        quizAnswer.put(800, "B"); // 第14题
        
        // 第15-21题选B (F) - key: 900, 65, 1000, 75, 1100, 85, 1200
        quizAnswer.put(900, "B");  // 第15题
        quizAnswer.put(65, "B");   // 第16题
        quizAnswer.put(1000, "B"); // 第17题
        quizAnswer.put(75, "B");   // 第18题
        quizAnswer.put(1100, "B"); // 第19题
        quizAnswer.put(85, "B");   // 第20题
        quizAnswer.put(1200, "B"); // 第21题
        
        // 第22-28题选B (P) - key: 1300, 95, 1400, 105, 1500, 115, 1600
        quizAnswer.put(1300, "B"); // 第22题
        quizAnswer.put(95, "B");   // 第23题
        quizAnswer.put(1400, "B"); // 第24题
        quizAnswer.put(105, "B");  // 第25题
        quizAnswer.put(1500, "B"); // 第26题
        quizAnswer.put(115, "B");  // 第27题
        quizAnswer.put(1600, "B"); // 第28题
        
        System.out.println("原始key顺序（HashMap无序）:");
        quizAnswer.keySet().forEach(key -> 
            System.out.print(key + ":" + quizAnswer.get(key) + " "));
        System.out.println();
        
        System.out.println("\n期望的处理顺序（按key升序）:");
        quizAnswer.keySet().stream()
            .sorted()
            .forEach(key -> System.out.print(key + ":" + quizAnswer.get(key) + " "));
        System.out.println();
        
        System.out.println("\n根据升序处理，期望结果: ENFP (绿人)");
        System.out.println("- 前7题全A -> E");
        System.out.println("- 第8-14题全B -> N"); 
        System.out.println("- 第15-21题全B -> F");
        System.out.println("- 第22-28题全B -> P");
        
        // 注意：这里只是演示逻辑，实际运行需要Spring容器
        System.out.println("\n=== 演示完成 ===");
    }
}
