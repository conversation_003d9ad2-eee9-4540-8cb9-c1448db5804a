<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.ApplePayMapper">

  <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.ApplePay">
    <id column="rid" jdbcType="INTEGER" property="rid" />
    <result column="fstatus" jdbcType="INTEGER" property="fstatus" />
    <result column="receipt_data_md5" jdbcType="VARCHAR" property="receiptDataMd5" />
    <result column="ctime" jdbcType="BIGINT" property="ctime" />
    <result column="mtime" jdbcType="BIGINT" property="mtime" />
    <result column="userid" jdbcType="LONGVARCHAR" property="userId" />
    <result column="productId" jdbcType="LONGVARCHAR" property="productId" />
    <result column="receipt_data" jdbcType="LONGVARCHAR" property="receiptData" />
    <result column="transaction_id" jdbcType="LONGVARCHAR" property="transactionId" />
  </resultMap>

  <select id="getPaySucceedList" resultMap="BaseResultMap" parameterType="Integer">
      SELECT
          userid,
          productId,
          fstatus,
          ctime
      FROM t_apple_pay
      WHERE fstatus = 1 AND <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
      ORDER BY ctime
  </select>

</mapper>
