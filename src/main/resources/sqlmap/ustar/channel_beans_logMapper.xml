<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.ChannelBeansLogMapper">

    <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.ChannelBeansLogData">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="channel_id" jdbcType="VARCHAR" property="channelId"/>
        <result column="aid" jdbcType="VARCHAR" property="aid"/>
        <result column="before_beans" jdbcType="INTEGER" property="beforeBeans"/>
        <result column="cost_beans" jdbcType="INTEGER" property="costBeans"/>
        <result column="after_beans" jdbcType="INTEGER" property="afterBeans"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="c_time" jdbcType="INTEGER" property="ctime"/>
        <result column="m_time" jdbcType="INTEGER" property="mtime"/>
    </resultMap>

    <sql id="fieldsSql">
        channel_id,
        aid,
        before_beans,
        cost_beans,
        after_beans,
        remark,
        status,
        c_time,
        `m_time`
    </sql>

    <sql id="itemsSql">
        #{channelId},
        #{aid},
        #{beforeBeans},
        #{costBeans},
        #{afterBeans},
        #{remark},
        #{status},
        #{ctime},
        #{mtime}
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.quhong.operation.share.mysql.ChannelBeansLogData">
        INSERT INTO ustar_log.t_channel_beans_log (<include refid="fieldsSql"/>)
        VALUES (<include refid="itemsSql"/>)
    </insert>

    <select id="selectByTime" resultMap="BaseResultMap">
        SELECT id,<include refid="fieldsSql"/> FROM ustar_log.t_channel_beans_log
        WHERE c_time &gt; #{start}
        AND c_time &lt; #{end}
        <if test="status != -1">
            AND status = #{status}
        </if>
        <if test="channel != null">
            AND channel_id = #{channel}
        </if>
        ORDER BY c_time DESC
    </select>

    <select id="selectListByChannelId" resultMap="BaseResultMap">
        SELECT id,<include refid="fieldsSql"/> FROM ustar_log.t_channel_beans_log
        WHERE channel_id = #{channelId}
        <if test="status != -1">
            AND status = #{status}
        </if>
        ORDER BY c_time DESC
    </select>

    <select id="selectSumBeans" resultType="java.lang.Integer">
        SELECT sum(cost_beans) FROM ustar_log.t_channel_beans_log
        WHERE c_time &gt; #{start}
        AND c_time &lt; #{end}
        <if test="status != -1">
            AND status = #{status}
        </if>
        <if test="channel != null">
            AND channel_id = #{channel}
        </if>
    </select>

</mapper>
