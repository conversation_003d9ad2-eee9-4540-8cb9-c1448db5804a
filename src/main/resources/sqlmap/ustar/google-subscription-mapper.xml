<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar.GoogleSubMapper">

    <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.GoogleSub">
        <id column="rid" jdbcType="INTEGER" property="rid"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="package_name" jdbcType="VARCHAR" property="packageName"/>
        <result column="purchase_token" jdbcType="VARCHAR" property="purchaseToken"/>
        <result column="purchase_state" jdbcType="BIGINT" property="purchaseState"/>
        <result column="purchase_time" jdbcType="BIGINT" property="purchaseTime"/>
        <result column="expires_timestamp" jdbcType="BIGINT" property="expiresTimestamp"/>
        <result column="auto_renew_status" jdbcType="TINYINT" property="autoRenewStatus"/>
        <result column="is_trial_period" jdbcType="TINYINT" property="isTrialPeriod"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="fstatus" jdbcType="TINYINT" property="fstatus"/>
        <result column="ctime" jdbcType="BIGINT" property="ctime"/>
        <result column="mtime" jdbcType="BIGINT" property="mtime"/>
    </resultMap>

    <sql id="fieldsSql">
        user_id, product_id, order_id, package_name, purchase_token, purchase_state, purchase_time,
        expires_timestamp, auto_renew_status, is_trial_period, channel, not_type, fstatus, ctime, mtime
    </sql>

    <sql id="itemsSql">
        #{userId}, #{productId}, #{orderId}, #{packageName}, #{purchaseToken}, #{purchaseState}, #{purchaseTime},
        #{expiresTimestamp}, #{autoRenewStatus}, #{isTrialPeriod}, #{channel}, #{notType}, #{fstatus}, #{ctime}, #{mtime}
    </sql>

    <select id="getGoogleSubInfo" resultMap="BaseResultMap" parameterType="Long">
        SELECT
            user_id, product_id, order_id, purchase_time,
            expires_timestamp, auto_renew_status, not_type, fstatus, ctime, mtime
        FROM t_google_subscription
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> AND fstatus != 3 AND fstatus != 0
    </select>

</mapper>
