<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.ChannelSourceMapper">

    <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.ChannelSourceData">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="source_id" jdbcType="INTEGER" property="sourceId"/>
        <id column="source_name" jdbcType="VARCHAR" property="sourceName"/>
        <id column="del" jdbcType="INTEGER" property="del"/>
        <id column="ctime" jdbcType="INTEGER" property="ctime"/>
        <id column="mtime" jdbcType="INTEGER" property="mtime"/>
    </resultMap>

    <sql id="fieldsSql">
        source_id,
        source_name,
        del,
        ctime,
        mtime
    </sql>

    <sql id="itemsSql">
        #{sourceId},
        #{sourceName},
        #{del},
        #{ctime},
        #{mtime}
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select * from t_channel_source where del = 0;
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        select * from t_channel_source where del = 0 and source_id = #{sourceId};
    </select>

</mapper>
