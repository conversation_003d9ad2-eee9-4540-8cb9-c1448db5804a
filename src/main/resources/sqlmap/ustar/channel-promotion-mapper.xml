<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.ChannelPromotionMapper">

    <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.ChannelPromotionData">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <id column="source_id" jdbcType="INTEGER" property="sourceId"/>
        <id column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <id column="source_name" jdbcType="VARCHAR" property="sourceName"/>
        <id column="url" jdbcType="VARCHAR" property="url"/>
        <id column="short_url" jdbcType="VARCHAR" property="shortUrl"/>
        <id column="op_user" jdbcType="VARCHAR" property="opUser"/>
        <id column="status" jdbcType="INTEGER" property="status"/>
        <id column="del" jdbcType="INTEGER" property="del"/>
        <id column="channel_id" jdbcType="VARCHAR" property="channelId"/>
        <id column="ctime" jdbcType="INTEGER" property="ctime"/>
        <id column="mtime" jdbcType="INTEGER" property="mtime"/>
    </resultMap>

    <sql id="fieldsSql">
        (source_id,
        channel_name,
        source_name,
        url,
        short_url,
        op_user,
        status,
        del,
        channel_id,
        ctime,
        mtime)
    </sql>

    <sql id="itemsSql">
        (#{item.sourceId},
        #{item.channelName},
        #{item.sourceName},
        #{item.url},
        #{item.shortUrl},
        #{item.opUser},
        #{item.status},
        #{item.del},
        #{item.channelId},
        #{item.ctime},
        #{item.mtime})
    </sql>

    <insert id="insertOne" parameterType="com.quhong.operation.share.mysql.ChannelPromotionData" useGeneratedKeys="true" keyProperty="id">
        insert into t_channel_promotion <include refid="fieldsSql"/> values <include refid="itemsSql"/>
    </insert>

    <update id="updateOne" parameterType="com.quhong.operation.share.mysql.ChannelPromotionData">
        UPDATE t_channel_promotion
        <set>
            <if test="item.sourceId != null">
                source_id = #{item.sourceId},
            </if>
            <if test="item.channelName != null">
                channel_name = #{item.channelName},
            </if>
            <if test="item.sourceName != null">
                source_name = #{item.sourceName},
            </if>
            <if test="item.url != null">
                url = #{item.url},
            </if>
            <if test="item.shortUrl != null">
                short_url = #{item.shortUrl},
            </if>
            <if test="item.opUser != null">
                op_user = #{item.opUser},
            </if>
            <if test="item.status != null">
                status = #{item.status},
            </if>
            <if test="item.del != null">
                del = #{item.del},
            </if>
            <if test="item.channelId != null">
                channel_id = #{item.channelId},
            </if>
            <if test="item.ctime != null">
                ctime = #{item.ctime},
            </if>
            <if test="item.mtime != null">
                mtime = #{item.mtime},
            </if>
        </set>
        where id = #{item.id} limit 1
    </update>

    <select id="selectListByPage" resultMap="BaseResultMap">
        select * from t_channel_promotion
        where ctime &gt;= #{start}
        and ctime &lt;= #{end}
        and del = 0
        <if test="status != -1">
            and status = #{status}
        </if>
        <if test="opUser != null">
            and op_user = #{opUser}
        </if>
        limit #{offset},#{pageSize}
    </select>

    <select id="countByTime" resultType="Integer">
        select count(1) from t_channel_promotion
        where ctime &gt;= #{start}
        and ctime &lt;= #{end}
        and del = 0
        <if test="status != -1">
            and status = #{status}
        </if>
        <if test="opUser != null">
            and op_user = #{opUser}
        </if>
    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        select * from t_channel_promotion where del = 0 and channel_name = #{channelName}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select * from t_channel_promotion where del = 0;
    </select>

</mapper>
