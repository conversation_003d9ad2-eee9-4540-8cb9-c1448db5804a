<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar.AppleSubMapper">

    <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.AppleSub">
        <id column="rid" jdbcType="INTEGER" property="rid"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="original_transaction_id" jdbcType="VARCHAR" property="originalTransactionId"/>
        <result column="transaction_id" jdbcType="VARCHAR" property="transactionId"/>
        <result column="receipt_data" jdbcType="LONGVARCHAR" property="receiptData"/>
        <result column="receipt_data_md5" jdbcType="VARCHAR" property="receiptDataMd5"/>
        <result column="purchase_timestamp" jdbcType="BIGINT" property="purchaseTimestamp"/>
        <result column="expires_timestamp" jdbcType="BIGINT" property="expiresTimestamp"/>
        <result column="auto_renew_status" jdbcType="TINYINT" property="autoRenewStatus"/>
        <result column="is_trial_period" jdbcType="TINYINT" property="isTrialPeriod"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="fstatus" jdbcType="INTEGER" property="fstatus"/>
        <result column="not_type" jdbcType="INTEGER" property="notType"/>
        <result column="ctime" jdbcType="BIGINT" property="ctime"/>
        <result column="mtime" jdbcType="BIGINT" property="mtime"/>
    </resultMap>

    <sql id="fieldsSql">
        user_id,
        product_id,
        original_transaction_id,
        transaction_id,
        receipt_data,
        receipt_data_md5,
        purchase_timestamp,
        expires_timestamp,
        auto_renew_status,
        is_trial_period,
        channel,
        fstatus,
        not_type,
        ctime,
        mtime
    </sql>

    <sql id="itemsSql">
        #{userId},
        #{productId},
        #{originalTransactionId},
        #{transactionId},
        #{receiptData},
        #{receiptDataMd5},
        #{purchaseTimestamp},
        #{expiresTimestamp},
        #{autoRenewStatus},
        #{isTrialPeriod},
        #{channel},
        #{fstatus},
        #{notType},
        #{ctime},
        #{mtime}
    </sql>

    <select id="getAppleSubInfo" resultMap="BaseResultMap" parameterType="Long">
        SELECT
            rid, <include refid="fieldsSql"/>
        FROM t_apple_subscription
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> AND fstatus IN (1, 2, 5, 7)
    </select>

</mapper>
