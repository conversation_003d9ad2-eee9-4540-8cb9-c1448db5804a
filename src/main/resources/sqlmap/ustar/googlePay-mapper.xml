<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.GooglePayMapper">

  <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.GooglePay">
    <id column="rid" jdbcType="INTEGER" property="rid" />
    <result column="gkind" jdbcType="TINYINT" property="gkind" />
    <result column="userid" jdbcType="CHAR" property="userId" />
    <result column="orderId" jdbcType="VARCHAR" property="orderId" />
    <result column="fstatus" jdbcType="TINYINT" property="fstatus" />
    <result column="productId" jdbcType="VARCHAR" property="productId" />
    <result column="purchaseToken" jdbcType="VARCHAR" property="purchaseToken" />
    <result column="purchaseTimeMillis" jdbcType="BIGINT" property="purchaseTimeMillis" />
    <result column="purchaseState" jdbcType="TINYINT" property="purchaseState" />
    <result column="payload" jdbcType="VARCHAR" property="payload" />
    <result column="fothers" jdbcType="VARCHAR" property="fothers" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <select id="getPaySucceedList" resultMap="BaseResultMap" parameterType="java.util.Date">
        SELECT
            userid,
            productId,
            fstatus,
            ctime
        FROM t_google_pay
        WHERE fstatus = 1 AND <![CDATA[ ctime >= #{startDate} AND ctime < #{endDate} ]]>
        ORDER BY ctime
  </select>

</mapper>
