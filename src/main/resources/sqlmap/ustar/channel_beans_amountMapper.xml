<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.mapper.ustar.ChannelBeansAmountMapper">

    <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.ChannelBeansAmountData">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="channel_id" jdbcType="VARCHAR" property="channelId"/>
        <result column="beans" jdbcType="INTEGER" property="beans"/>
        <result column="c_time" jdbcType="INTEGER" property="ctime"/>
        <result column="m_time" jdbcType="INTEGER" property="mtime"/>
    </resultMap>

    <sql id="fieldsSql">
        channel_id, `beans`, c_time, `m_time`
    </sql>

    <sql id="itemsSql">
        #{channelId}, #{beans}, #{ctime}, #{mtime}
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.quhong.operation.share.mysql.ChannelBeansAmountData">
        INSERT INTO t_channel_beans_amount (<include refid="fieldsSql"/>)
        VALUES (<include refid="itemsSql"/>)
    </insert>

    <update id="updateById" parameterType="com.quhong.operation.share.mysql.ChannelBeansAmountData">
        UPDATE t_channel_beans_amount
        <set>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="beans != null">beans = #{beans},</if>
            <if test="ctime != null">c_time = #{ctime},</if>
            <if test="mtime != null">m_time = #{mtime},</if>
        </set>
        where id = #{id}
    </update>

    <select id="selectByChannelId" resultMap="BaseResultMap">
        SELECT * from t_channel_beans_amount where channel_id = #{channelId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * from t_channel_beans_amount;
    </select>

</mapper>
