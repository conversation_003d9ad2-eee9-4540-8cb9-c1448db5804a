<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.EnterRoomMapper">
    <resultMap id="resultMap" type="com.quhong.operation.share.mysql.EnterRoom">
        <result column="id" property="id"/>
        <result column="room_id" property="roomId"/>
        <result column="user_id" property="userId"/>
        <result column="is_host" property="isHost"/>
        <result column="rookie_status" property="rookieStatus"/>
        <result column="os" property="os"/>
        <result column="ctime" property="ctime"/>
        <result column="version_code" property="versionCode"/>
        <result column="mtime" property="mtime"/>
        <result column="online_time" property="onlineTime"/>
    </resultMap>

    <resultMap id="statMap" type="com.quhong.operation.share.data.AggStatData">
        <result column="count" property="count"/>
        <result column="userCount" property="userCount"/>
        <result column="uid" property="uid"/>
        <result column="sum" property="sum"/>
    </resultMap>

    <select id="roomActorOnlineTime" resultType="com.quhong.operation.share.tool.TotalVO" parameterType="String">
        SELECT SUM(online_time) AS sumNum,
               COUNT(*)         AS countNum
        FROM s_enter_room_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
                AND room_id = #{roomId}
    </select>

    <select id="newActorInRoomPerson" resultType="String" parameterType="String">
        SELECT user_id
        FROM s_enter_room_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
                AND room_id = #{roomId}
        GROUP BY user_id
    </select>

    <select id="newActorInRoomCount" resultType="Integer" parameterType="String">
        SELECT COUNT(*)
        FROM s_enter_room_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
                AND room_id = #{roomId}
    </select>

    <select id="actorJoinRoomCount" resultType="Integer" parameterType="String">
        SELECT COUNT(*)
        FROM s_enter_room_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
                AND user_id = #{userId}
    </select>

    <select id="actorJoinRoomNum" resultType="String" parameterType="String">
        SELECT room_id
        FROM s_enter_room_${tableSuffix}
        WHERE user_id = #{userId}
          AND <![CDATA[ ctime < #{endTime}
          AND mtime > #{startTime} ]]>
        GROUP BY room_id
    </select>

    <select id="getRoomActorList" resultType="String" parameterType="String">
        SELECT user_id
        FROM s_enter_room_${tableSuffix}
        WHERE room_id = #{roomId}
            <![CDATA[ AND ctime < #{endTime}
          AND mtime > #{startTime} ]]>
        GROUP BY user_id;
    </select>

    <select id="getEnterRoomUserId" resultMap="resultMap" parameterType="String">
        SELECT
        user_id,
        os
        FROM s_enter_room_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime}
                AND user_id >= #{startUid} AND user_id < #{endUid} ]]>
        <if test="rookieStatus != null">
            AND rookie_status = #{rookieStatus}
        </if>
        GROUP BY user_id;
    </select>

    <select id="personEnterRoom" resultType="Integer" parameterType="String">
        SELECT
        COUNT(*) num
        FROM s_enter_room_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="uidList.size() > 0">
            AND user_id IN
            <foreach item="item" collection="uidList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY user_id, room_id
    </select>

    <select id="isRegisterInRoom" resultType="Integer" parameterType="String">
        SELECT COUNT(*)
        FROM s_enter_room_${tableSuffix}
        WHERE user_id = #{userId}
          AND
            <![CDATA[ ctime >= #{startTime}
          AND ctime < #{endTime} ]]>
            AND rookie_status = #{rookieStatus}
    </select>

    <select id="inWelcomeRoomActor" resultMap="resultMap" parameterType="Integer">
        SELECT user_id,
               os
        FROM s_enter_room_${tableSuffix}
        WHERE rookie_status = 1
            <![CDATA[ AND ctime >= #{startTime}
          AND ctime < #{endTime} ]]>
        GROUP BY user_id;
    </select>

    <select id="selectRookieUserCount" resultMap="resultMap">
        SELECT DISTINCT user_id as user_id
        FROM s_enter_room_${tableSuffix}
        WHERE rookie_status = 1
        <if test="os != -1">
            AND os = #{os}
        </if>
        AND ctime &gt;= #{startTime}
        AND ctime &lt; #{endTime}
        AND online_time &gt; 0;
    </select>

    <select id="selectUserCount" resultMap="resultMap">
        SELECT user_id
        FROM s_enter_room_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="newUserUidSet.size() > 0">
            AND user_id IN
            <foreach item="item" collection="newUserUidSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectRooms" resultMap="resultMap">
        SELECT *
        FROM s_enter_room_${tableSuffix}
        WHERE ctime &gt;= #{startTime}
          AND ctime &lt; #{endTime}
          AND room_id = #{roomId}
    </select>

    <select id="enterRoomStat" resultType="com.quhong.operation.share.vo.EnterRoomStatVO" parameterType="String">
        SELECT
        COUNT(DISTINCT user_id) AS enterRoomUser,
        COUNT(*) AS enterRoomCount,
        SUM(CASE WHEN online_time THEN online_time ELSE 0 END) AS totalOnlineTime
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id,online_time
            FROM s_enter_room_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="os != null">
                AND os = #{os}
            </if>
        </foreach>
        ) AS stat
    </select>

    <select id="enterRoomUid" resultType="String" parameterType="String">
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id
            FROM s_enter_room_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        </foreach>
    </select>

    <select id="enterRoomUidByRoomId" resultType="String" parameterType="String">
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id
            FROM s_enter_room_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            AND room_id = #{roomId}
        </foreach>
    </select>

    <select id="userDayRoomStats" resultMap="statMap">
        SELECT COUNT(1) count,user_id uid,SUM(online_time) sum
        FROM s_enter_room_${tableSuffix}
        WHERE
        <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="os != -1">
            AND os = #{os}
        </if>
        <if test="uidSet != null and uidSet.size() > 0">
            AND user_id IN
            <foreach item="item" collection="uidSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roomType != -1">
            AND room_type = #{roomType}
        </if>
        group by user_id
    </select>

    <select id="dayRoomStats" resultMap="statMap">
        SELECT COUNT(1) count,room_id uid,SUM(online_time) sum
        FROM s_enter_room_${tableSuffix}
        WHERE
        <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="os != -1">
            AND os = #{os}
        </if>
        <if test="uidSet != null and uidSet.size() > 0">
            AND user_id IN
            <foreach item="item" collection="uidSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roomType != -1">
            AND room_type = #{roomType}
        </if>
        group by room_id
    </select>

    <select id="enterRoomUser" resultType="String" parameterType="String">
            SELECT user_id
            FROM s_enter_room_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
    </select>

    <select id="enterRookieRoomNewUsers" resultType="String" parameterType="String">
        SELECT distinct user_id
        FROM s_enter_room_${tableSuffix}
        WHERE
        <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="ridSet != null and ridSet.size() > 0">
            AND room_id IN
            <foreach item="item" collection="ridSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="enterRookieRoomNewUsersTime" resultMap="statMap">
        SELECT user_id AS uid,SUM(CASE WHEN online_time THEN online_time ELSE 0 END) AS sum
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id,online_time
            FROM s_enter_room_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="ridSet != null and ridSet.size() > 0">
                AND room_id IN
                <foreach item="item" collection="ridSet" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </foreach>
        ) AS stat
        GROUP BY user_id
        HAVING  <![CDATA[ sum > #{low} AND sum <= #{hight} ]]>
    </select>
</mapper>
