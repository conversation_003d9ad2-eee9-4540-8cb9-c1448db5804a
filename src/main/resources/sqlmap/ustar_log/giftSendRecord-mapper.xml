<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.GiftSendRecordMapper" >

    <select id="giftRookieRoomNewUsers" resultType="String" parameterType="String">
        SELECT distinct from_uid
        FROM t_gift_send_record_${tableSuffix}
        WHERE
        <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime}
        AND from_uid >= #{startUid} AND from_uid <= #{endUid}]]>
        AND gift_id = 110
        <if test="ridSet != null and ridSet.size() > 0">
            AND room_id IN
            <foreach item="item" collection="ridSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
