<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.UserOnlineMapper">
    <resultMap id="baseMap" type="com.quhong.operation.share.mysql.UserOnlineData">
        <result column="id" property="id"/>
        <result column="user_online_count" property="userOnlineCount"></result>
        <result column="robot_online_count" property="robotOnlineCount"></result>
        <result column="room_active_count" property="roomActiveCount"></result>
        <result column="man_online_count" property="manOnlineCount"></result>
        <result column="new_man_online_count" property="newManOnlineCount"></result>
        <result column="girl_online_count" property="girlOnlineCount"></result>
        <result column="new_girl_online_count" property="newGirlOnlineCount"></result>
        <result column="ptg_online_count" property="ptgOnlineCount"></result>
        <result column="recharge_online_count" property="rechargeOnlineCount"></result>
        <result column="live_room_count" property="liveRoomCount"></result>
        <result column="voice_room_count" property="voiceRoomCount"></result>
        <result column="ludo_room_count" property="ludoRoomCount"></result>
        <result column="video_room_count" property="videoRoomCount"></result>
        <result column="turntable_room_count" property="turntableRoomCount"></result>
        <result column="ctime" property="ctime"></result>
        <result column="os" property="os"></result>
    </resultMap>

    <sql id="baseSql">
        user_online_count,robot_online_count,room_active_count,man_online_count,new_man_online_count,girl_online_count,new_girl_online_count,ptg_online_count,recharge_online_count,live_room_count,voice_room_count,ludo_room_count,video_room_count,turntable_room_count,ctime,os
    </sql>

    <select id="selectOnlineUsers" resultMap="baseMap">
        SELECT
        <include refid="baseSql"></include>
        FROM t_user_online_${tableSuffix}
        WHERE ctime &gt;= #{start}
        AND ctime &lt; #{end}
        <if test="os != -2">
            AND os = #{os}
        </if>
    </select>

</mapper>
