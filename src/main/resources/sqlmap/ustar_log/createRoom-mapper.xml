<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.CreateRoomMapper">

  <resultMap id="BaseResultMap" type="com.quhong.operation.share.mysql.CreateRoom">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="rookie_status" jdbcType="INTEGER" property="rookieStatus" />
    <result column="os" jdbcType="INTEGER" property="os" />
    <result column="ctime" jdbcType="BIGINT" property="ctime" />
    <result column="version_code" jdbcType="INTEGER" property="versionCode" />
  </resultMap>

  <sql id="fieldsSql">
        room_id, user_id, `rookie_status`, os, ctime, version_code
    </sql>

  <sql id="itemsSql">
        #{roomId}, #{userId}, #{rookieStatus}, #{os}, #{ctime}, #{versionCode}
    </sql>

  <select id="getCreateRoomPerson" resultMap="BaseResultMap" parameterType="String">
    SELECT
        user_id
    FROM s_create_room_${tableSuffix}
    WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
      <if test="os != null">
        AND os = #{os}
      </if>
    GROUP BY user_id
  </select>

    <select id="createRoomStat" resultType="com.quhong.operation.share.vo.CreateRoomStatVO" parameterType="String">
        SELECT
        COUNT(DISTINCT user_id) AS createRoomUser,
        COUNT(*) AS createRoomCount
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id
            FROM s_create_room_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="os != null">
                AND os = #{os}
            </if>
        </foreach>
        ) AS stat
    </select>

</mapper>
