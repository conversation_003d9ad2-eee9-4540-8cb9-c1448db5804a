<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.RoomMicMapper">

    <resultMap id="resultMap" type="com.quhong.operation.share.mysql.RoomMic">
        <result column="id" property="id"/>
        <result column="room_id" property="roomId"/>
        <result column="user_id" property="userId"/>
        <result column="mic_position" property="micPosition"/>
        <result column="mic_time" property="micTime"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="os" property="os"/>
        <result column="rookie_status" property="rookieStatus"/>
        <result column="version_code" property="versionCode"/>
    </resultMap>

    <select id="compereInMicTime" resultType="Integer" parameterType="String">
        SELECT SUM(mic_time)
        FROM s_room_mic_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
               AND user_id = #{userId}
          AND room_id = #{roomId}
    </select>

    <select id="newActorInMicPerson" resultType="String" parameterType="String">
        SELECT user_id AS num
        FROM s_room_mic_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
               AND rookie_status = 1
          AND room_id = #{roomId}
        GROUP BY user_id
    </select>

    <select id="newActorInMicCount" resultType="Integer" parameterType="String">
        SELECT COUNT(*) AS num
        FROM s_room_mic_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
               AND rookie_status = 1
          AND room_id = #{roomId}
    </select>

    <select id="newActorInMicTimeAvg" resultType="com.quhong.operation.share.tool.TotalVO" parameterType="String">
        SELECT SUM(mic_time) AS sumNum,
               COUNT(*)      AS countNum
        FROM s_room_mic_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
               AND rookie_status = 1
          AND room_id = #{roomId}
    </select>

    <select id="newActorInMicTime5Minute" resultType="Integer" parameterType="String">
        SELECT COUNT(m.user_id)
        FROM (
                 SELECT user_id
                 FROM s_room_mic_${tableSuffix}
                 WHERE <![CDATA[ ctime >= #{startTime}
                   AND mtime <= #{endTime}
                   AND room_id = #{roomId}
                   AND rookie_status = 1
                   AND mic_time >= 300 ]]>
            GROUP BY user_id
             ) AS m
    </select>

    <select id="actorAddUpMicTime" resultType="Integer" parameterType="String">
        SELECT SUM(mic_time) AS num
        FROM s_room_mic_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND mtime <= #{endTime} ]]>
               AND user_id = #{userId}
    </select>

    <select id="actorAddUpMicTimeByUidSet" resultType="com.quhong.operation.share.vo.UpMicTimeVO"
            parameterType="String">
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id AS uid,SUM(mic_time) AS sum
            FROM s_room_mic_${tableSuffix}
            WHERE <![CDATA[ ctime >= #{startTime} AND mtime <= #{endTime} ]]>
            <if test="uidSet.size() > 0">
                AND user_id IN
                <foreach item="item" collection="uidSet" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY user_id
        </foreach>
    </select>

    <select id="actorMicCount" resultType="Integer" parameterType="String">
        SELECT COUNT(*)
        FROM s_room_mic_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime}
          AND ctime <= #{endTime} ]]>
               AND user_id = #{userId}
    </select>

    <select id="upMicTotalInfo" resultType="com.quhong.operation.share.tool.TotalVO" parameterType="String">
        SELECT
        user_id as uid,
        COUNT(*) AS countNum,
        SUM(mic_time) AS sumNum
        FROM s_room_mic_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]>
        <if test="uidList.size() > 0">
            AND user_id IN
            <foreach item="item" collection="uidList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roomList.size() > 0">
            AND room_id IN
            <foreach item="item" collection="roomList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY user_id
    </select>

    <select id="mic5MinutesInfo" resultMap="resultMap" parameterType="String">
        SELECT
        user_id,
        os,
        SUM(mic_time) AS mic_time,
        ctime
        FROM s_room_mic_${tableSuffix}
        WHERE
        <if test="rookie != null">
            rookie_status = #{rookie} AND
        </if>
        <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime}
                AND user_id <= #{startUid} AND user_id <= #{endUid}
            ]]>
        AND mic_time >= 300
        GROUP BY user_id
    </select>

    <select id="roomMicStat" resultType="com.quhong.operation.share.vo.RoomMicStatVO" parameterType="String">
        SELECT
        COUNT(DISTINCT user_id) AS upMicUser,
        COUNT(*) AS upMicCount,
        SUM(CASE WHEN mic_time THEN mic_time ELSE 0 END) AS totalMicTime
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id,mic_time
            FROM s_room_mic_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="os != null">
                AND os = #{os}
            </if>
        </foreach>
        ) AS stat
    </select>

    <select id="userUpMicStat" resultType="com.quhong.operation.share.vo.RoomOnlineStatVO" parameterType="String">
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            <![CDATA[
                SELECT SUM(CASE WHEN mic_time < 300 THEN 1 ELSE 0 END)                         AS lt5Min,
                       SUM(CASE WHEN 300 <= mic_time AND mic_time < 600 THEN 1 ELSE 0 END)     AS gte5MinTo10Min,
                       SUM(CASE WHEN 600 <= mic_time AND mic_time < 1200 THEN 1 ELSE 0 END)    AS gte10MinTo20Min,
                       SUM(CASE WHEN 1200 <= mic_time AND mic_time < 2400 THEN 1 ELSE 0 END)   AS gte20MinTo40Min,
                       SUM(CASE WHEN 2400 <= mic_time AND mic_time < 3600 THEN 1 ELSE 0 END)   AS gte40MinTo60Min,
                       SUM(CASE WHEN 3600 <= mic_time AND mic_time < 7200 THEN 1 ELSE 0 END)   AS gte1HourTo2Hour,
                       SUM(CASE WHEN 7200 <= mic_time AND mic_time < 14400 THEN 1 ELSE 0 END)  AS gte2HourTo4Hour,
                       SUM(CASE WHEN 14400 <= mic_time AND mic_time < 28800 THEN 1 ELSE 0 END) AS gte4HourTo8Hour,
                       SUM(CASE WHEN 28800 <= mic_time AND mic_time < 43200 THEN 1 ELSE 0 END) AS gte8HourTo12Hour,
                       SUM(CASE WHEN 43200 <= mic_time THEN 1 ELSE 0 END)                      AS gte12Hour
            ]]>
            FROM s_room_mic_${tableSuffix}
            <![CDATA[ WHERE ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="os != -1">
                AND os = #{os}
            </if>
            <if test="userType == 1">
                AND rookie_status = 1
            </if>
        </foreach>
    </select>

    <select id="selectGtMicTimeUsers" parameterType="String" resultType="java.lang.String">
        select distinct uid from s_room_mic_${tableSuffix}
        <![CDATA[ WHERE ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="uidSet != null and uidSet.size() > 0">
            and uid in
            <foreach collection="uidSet" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="micGtTime != -1">
            and mic_time &gt;= #{micGtTime}
        </if>
    </select>


    <select id="micRookieRoomNewUsers" resultType="String" parameterType="String">
        SELECT distinct user_id
        FROM s_room_mic_${tableSuffix}
        WHERE
        <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="ridSet != null and ridSet.size() > 0">
            AND room_id IN
            <foreach item="item" collection="ridSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="micRookieRoomNewUsersTime" resultType="com.quhong.operation.share.vo.UpMicTimeVO" parameterType="String">
        SELECT user_id AS uid,SUM(CASE WHEN online_time THEN online_time ELSE 0 END) AS sum
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT user_id,mic_time as online_time
            FROM s_room_mic_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="ridSet != null and ridSet.size() > 0">
                AND room_id IN
                <foreach item="item" collection="ridSet" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </foreach>
        ) AS stat
        GROUP BY user_id
        HAVING  <![CDATA[ sum > #{low} AND sum <= #{hight} ]]>
    </select>


</mapper>
