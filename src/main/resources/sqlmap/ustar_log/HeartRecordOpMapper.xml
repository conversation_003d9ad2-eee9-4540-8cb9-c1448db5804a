<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.HeartRecordOpMapper">
    <resultMap id="resultMap" type="com.quhong.operation.share.mysql.HeartRecord">
        <result column="id" property="id"/>
        <result column="uid" property="uid"/>
        <result column="changed" property="changed"/>
        <result column="g_balance" property="gBalance"/>
        <result column="r_balance" property="rBalance"/>
        <result column="room_id" property="roomId"/>
        <result column="aid" property="aid"/>
        <result column="title" property="title"/>
        <result column="remark" property="remark"/>
        <result column="c_time" property="ctime"/>
    </resultMap>

    <select id="heartRecordStat" resultType="com.quhong.operation.share.vo.HeartRecordStatVO" parameterType="String">
        SELECT
        SUM(CASE WHEN remark = "send" THEN 1 ELSE 0 END) AS sendCount,
        SUM(CASE WHEN remark = "In room task" THEN 1 ELSE 0 END) AS receiveCount,
        COUNT(DISTINCT CASE WHEN remark = "send" THEN uid END) AS sendPerson,
        COUNT(DISTINCT CASE WHEN remark = "In room task" THEN uid END) AS receivePerson
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT uid,remark
            FROM t_heart_record_${tableSuffix}
            WHERE
            <![CDATA[ c_time >= #{startTime} AND c_time < #{endTime} ]]>
            <if test="newUserUidSet.size() > 0">
                AND uid IN
                <foreach item="item" collection="newUserUidSet" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </foreach>
        ) AS stat
    </select>
</mapper>
