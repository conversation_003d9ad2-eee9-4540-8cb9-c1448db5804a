<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.VideoStatLogMapper">
    <resultMap id="baseResultMap" type="com.quhong.operation.share.mysql.VideoLogData">
        <result column="id" property="id"/>
        <result column="room_id" property="roomId"/>
        <result column="video_id" property="videoId"/>
        <result column="video_title" property="videoTitle"/>
        <result column="duration" property="duration"/>
        <result column="play_time" property="playTime"/>
        <result column="ctime" property="ctime"/>
    </resultMap>

    <resultMap id="statMap" type="com.quhong.operation.share.data.MysqlStatData">
        <result column="room_count" property="countNum"/>
        <result column="total_play_time" property="sumNum"/>
    </resultMap>

    <sql id="baseSql">
        room_id,video_id,video_title,duration,play_time,ctime
    </sql>

    <sql id="itemSql">
        #{item.roomId},#{item.videoId},#{item.videoTitle},#{item.duration},#{item.playTime},#{item.ctime}
    </sql>

    <select id="selectStatData" resultMap="statMap">
        select count(distinct room_id) as room_count,sum(play_time) total_play_time from s_video_log_${tableSuffix}
        where ctime &gt; #{start}
        and ctime &lt; #{end}
    </select>


</mapper>
