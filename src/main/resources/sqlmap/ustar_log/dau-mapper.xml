<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.DauMapper">
    <resultMap id="resultMap" type="com.quhong.operation.share.mysql.Dau">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="fb_gender" property="fbGender"/>
        <result column="is_new" property="isNew"/>
        <result column="os" property="os"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
    </resultMap>

    <select id="totalOnlineUid" resultType="String" parameterType="String">
        SELECT user_id FROM s_dau_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]>
        <if test="os != null">
            AND os = #{os}
        </if>
        <if test="isNew != null">
            AND is_new = #{isNew}
        </if>
        <if test="app != null">
            AND package_name = #{app}
        </if>
        <if test="gender != -1">
            AND fb_gender = #{gender}
        </if>
        GROUP BY user_id
    </select>

    <select id="totalOnlineActor" resultMap="resultMap" parameterType="String">
        SELECT
        id,
        user_id,
        fb_gender,
        is_new,
        os,
        ctime,
        mtime
        FROM s_dau_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]>
        <if test="isNew != null">
            AND is_new = #{isNew}
        </if>
        GROUP BY user_id
    </select>


    <select id="welcomeActiveNum" resultMap="resultMap" parameterType="String">
        SELECT
        user_id,
        os
        FROM s_dau_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]>
        <if test="list.size() > 0">
            AND user_id IN
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY user_id
    </select>

    <select id="selectOnlineUserCount" resultType="Integer">
        SELECT COUNT(DISTINCT user_id)
        FROM s_dau_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="os != -1">
            AND os = #{os}
        </if>
        <if test="uidSet != null and uidSet.size > 0">
            AND user_id IN
            <foreach item="item" collection="uidSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getDauCountByApp" resultType="Integer">
        SELECT COUNT(DISTINCT user_id)
        FROM s_dau_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="os != null">
            AND package_name = #{app}
        </if>
        <if test="gender != -1">
            AND fb_gender = #{gender}
        </if>
        <if test="uidSet != null and uidSet.size > 0">
            AND user_id IN
            <foreach item="item" collection="uidSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getUidListByGender" resultType="String">
        SELECT user_id
        FROM s_dau_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
        <if test="gender != -1">
            AND fb_gender = #{gender}
        </if>
        <if test="os != -1">
            AND os = #{os}
        </if>
    </select>

</mapper>
