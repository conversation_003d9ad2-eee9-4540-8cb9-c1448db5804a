<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.mapper.ustar_log.SendBeansLogMapper" >
    <resultMap id="resultMap" type="com.quhong.operation.share.mysql.SendBeanLog" >
        <result column="id" property="id"/>
        <result column="uid" property="uid"/>
        <result column="in_room_time" property="inRoomTime"/>
        <result column="send_bean" property="sendBean"/>
        <result column="status" property="status"/>
        <result column="ctime" property="ctime"/>
        <result column="mic_time " property="micTime"/>
        <result column="send_red_number " property="sendRedNumber"/>
    </resultMap>

    <resultMap id="statMap" type="com.quhong.operation.share.mysql.SendBeansStatData">
        <result column="uid" property="uid"/>
        <result column="inRoomTime" property="inRoomTime"/>
        <result column="sendBeanNum" property="sendBeanNum"/>
        <result column="onMicTime" property="onMicTime"/>
        <result column="sendRedNum" property="sendRedNum"/>
    </resultMap>

    <sql id="fieldsSql">
        uid, in_room_time, send_bean, status, ctime,mic_time,send_red_number
    </sql>

    <sql id="itemsSql">
        #{uid}, #{inRoomTime}, #{sendBean}, #{status}, #{ctime},#{micTime},#{sendRedNumber}
    </sql>

    <select id="selectByUid" resultMap="resultMap" parameterType="String">
        SELECT
            id, <include refid="fieldsSql"/>
        FROM s_send_bean_log
        WHERE uid = #{uid}
    </select>

    <insert id="insertSendBeanLog" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.quhong.operation.share.mysql.SendBeanLog">
        INSERT INTO s_send_bean_log (<include refid="fieldsSql"/>)
        VALUES (<include refid="itemsSql"/>)
    </insert>

    <select id="selectStatListBySuccessNum" resultType="com.quhong.operation.share.mysql.SendBeansStatData">
        SELECT
            uid, sum(in_room_time) inRoomTime , sum(send_bean) sendBeanNum ,sum(mic_time) onMicTime,sum(send_red_number) sendRedNum,COUNT(uid) sendCount
        FROM s_send_bean_log
        WHERE ctime &gt;= #{startTime}
        AND ctime &lt;= #{endTime}
        AND send_bean > 0
        GROUP BY uid
        <if test="successNum != -1">
            having COUNT(uid) = #{successNum}
        </if>
    </select>

    <select id="selectFailStatList" resultType="com.quhong.operation.share.mysql.SendBeansStatData">
        SELECT uid, sum(in_room_time) inRoomTime , sum(send_bean) sendBeanNum ,sum(mic_time) onMicTime,sum(send_red_number) sendRedNum,COUNT(uid) sendCount
        FROM s_send_bean_log
        WHERE ctime &gt;= #{startTime}
        AND ctime &lt;= #{endTime}
        AND send_bean = 0
        GROUP BY uid
        <if test="failedNum != -1">
            having COUNT(uid) = #{failedNum}
        </if>
    </select>

</mapper>
