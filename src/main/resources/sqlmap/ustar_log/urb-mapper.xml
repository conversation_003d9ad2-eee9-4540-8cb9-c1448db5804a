<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.URBehaviorMapper">

    <select id="getURBehaviorStat" resultType="com.quhong.operation.share.vo.UBRStatVO" parameterType="String">
        SELECT
        SUM(CASE WHEN `action` = "delfriend" THEN times ELSE 0 END) AS deleteCount,
        SUM(CASE WHEN `action` = "block_user" THEN times ELSE 0 END) AS blockCount
        FROM(
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT `action`,times
            FROM t_user_relation_behavior_${tableSuffix}
            WHERE
            <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]>
            and `action` in ("delfriend","block_user")
            <if test="uidSet != null and uidSet.size() > 0">
                and uid in
                <foreach item="item" collection="uidSet" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aidSet != null and aidSet.size() > 0">
                and aid in
                <foreach item="item" collection="aidSet" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </foreach>
        ) AS stat
    </select>

</mapper>
