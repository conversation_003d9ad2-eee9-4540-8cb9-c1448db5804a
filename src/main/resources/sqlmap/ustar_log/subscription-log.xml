<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.SubscriptionLogMapper" >
    <resultMap id="baseMap" type="com.quhong.operation.share.mysql.SubscriptionLogData" >
        <result column="id" property="id"/>
        <result column="original_order_id" property="originalOrderId"/>
        <result column="order_id" property="orderId"/>
        <result column="user_id" property="userId"/>
        <result column="product_id" property="productId"/>
        <result column="os" property="os"/>
        <result column="price_currency_code" property="priceCurrencyCode"/>
        <result column="price_amount_micros" property="priceAmountMicros"/>
        <result column="type" property="type"/>
        <result column="subscription_type" property="subscriptionType"/>
        <result column="start_at" property="startAt"/>
        <result column="end_at" property="endAt"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
    </resultMap>

    <sql id="baseSql">
         original_order_id,order_id,user_id,product_id,os,price_currency_code,price_amount_micros,type,subscription_type,start_at,end_at,ctime,mtime
    </sql>

    <sql id="itemSql">
          #{originalOrderId}, #{orderId}, #{userId}, #{productId},#{os},#{priceCurrencyCode},#{priceAmountMicros},#{type},
          #{subscriptionType},#{startAt},#{endAt},#{ctime},#{mtime}
    </sql>

    <select id="selectAscOneBySubType" resultMap="baseMap">
        select <include refid="baseSql"/>
        from s_subscription_log
        where user_id = #{uid}
        <if test="subType != -1">
            and subscription_type = #{subType}
        </if>
        order by ctime limit 1
    </select>

</mapper>
