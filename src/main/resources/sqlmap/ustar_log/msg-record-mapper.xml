<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.MsgRecordMapper">
    <resultMap id="baseResultMap" type="com.quhong.operation.share.mysql.MysqlMsgRecordData">
    </resultMap>

    <sql id="baseSql">
            msg_index,
            fromUid,
            toUid,
            msg,
            msgInfo,
            msgType,
            from_delete,
            to_delete,
            `timestamp`,
            msg_id,
            from_like,
            to_like,
            status
    </sql>

    <sql id="itemSql">
        #{item.msgIndex},#{item.fromUid},#{item.toUid},#{item.msg},#{item.msgInfo},#{item.msgType},#{item.fromDelete},#{item.toDelete},#{item.timestamp},#{item.msgId},#{item.fromLike},#{item.toLike},#{item.status}
    </sql>

    <select id="selectRecordList" resultMap="baseResultMap">
        select
        <include refid="baseSql"/>
        from s_msg_record_${tableSuffix}
        where `timestamp` &gt;= #{startTime}
        and `timestamp` &lt;= #{endTime}
        <if test="msgType != -1">
            and msgType = #{msgType}
        </if>
        <if test="uidSet != null and uidSet.size() > 0">
            and fromUid in
            <foreach item="item" collection="uidSet" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectRecordByIndex" resultMap="baseResultMap">
        select msg_index,fromUid,msg,toUid,msgType,timestamp
        from s_msg_record_${tableSuffix}
        where `timestamp` &gt;= #{startTime}
        and `timestamp` &lt;= #{endTime}
        <if test="msgIndexList != null and msgIndexList.size() > 0">
            and msg_index in
            <foreach item="item" collection="msgIndexList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
