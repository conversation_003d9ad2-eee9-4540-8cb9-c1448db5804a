<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.MicTimeMapper">

    <select id="upMicTotalInfo" resultType="com.quhong.operation.share.tool.TotalVO" parameterType="String">
        SELECT
        uid as uid,
        COUNT(*) AS countNum,
        SUM(mic_time) AS sumNum
        FROM t_mic_time_${tableSuffix}
        WHERE date=#{date}
        <if test="uidList.size() > 0">
            AND uid IN
            <foreach item="item" collection="uidList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roomList.size() > 0">
            AND room_id IN
            <foreach item="item" collection="roomList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY uid
    </select>

</mapper>
