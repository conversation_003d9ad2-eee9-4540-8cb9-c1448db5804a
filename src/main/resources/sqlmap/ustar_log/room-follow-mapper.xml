<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.RoomFollowMapper" >
    <resultMap id="resultMap" type="com.quhong.operation.share.mysql.RoomFollow" >
        <result column="id" property="id"/>
        <result column="user_id" property="roomId"/>
        <result column="room_id" property="userId"/>
        <result column="action" property="action"/>
        <result column="os" property="os"/>
        <result column="ctime" property="ctime"/>
    </resultMap>

    <select id="actorFollowRoomCount" resultType="Integer" parameterType="String">
        SELECT COUNT(*) FROM s_room_follow_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]> AND user_id = #{userId} AND `action` = 1
    </select>

    <select id="roomFollowPerson" resultType="String" parameterType="String">
        SELECT user_id FROM s_room_follow_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]> AND room_id = #{roomId} AND `action` = 1
        GROUP BY user_id
    </select>

    <select id="roomFollowCount" resultType="Integer" parameterType="String">
        SELECT COUNT(*) FROM s_room_follow_${tableSuffix}
        WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]> AND room_id = #{roomId} AND `action` = 1
    </select>

</mapper>
