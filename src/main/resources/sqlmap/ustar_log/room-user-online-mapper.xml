<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.RoomUserOnlineSlaveMapper">

    <select id="roomUserOnlineStat" resultType="com.quhong.operation.share.vo.RoomOnlineStatVO" parameterType="String">
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            <![CDATA[
                SELECT SUM(CASE WHEN online_time < 300 THEN 1 ELSE 0 END)                            AS lt5Min,
                       SUM(CASE WHEN 300 <= online_time AND online_time < 600 THEN 1 ELSE 0 END)     AS gte5MinTo10Min,
                       SUM(CASE WHEN 600 <= online_time AND online_time < 1200 THEN 1 ELSE 0 END)    AS gte10MinTo20Min,
                       SUM(CASE WHEN 1200 <= online_time AND online_time < 2400 THEN 1 ELSE 0 END)   AS gte20MinTo40Min,
                       SUM(CASE WHEN 2400 <= online_time AND online_time < 3600 THEN 1 ELSE 0 END)   AS gte40MinTo60Min,
                       SUM(CASE WHEN 3600 <= online_time AND online_time < 7200 THEN 1 ELSE 0 END)   AS gte1HourTo2Hour,
                       SUM(CASE WHEN 7200 <= online_time AND online_time < 14400 THEN 1 ELSE 0 END)  AS gte2HourTo4Hour,
                       SUM(CASE WHEN 14400 <= online_time AND online_time < 28800 THEN 1 ELSE 0 END) AS gte4HourTo8Hour,
                       SUM(CASE WHEN 28800 <= online_time AND online_time < 43200 THEN 1 ELSE 0 END) AS gte8HourTo12Hour,
                       SUM(CASE WHEN 43200 <= online_time THEN 1 ELSE 0 END)                         AS gte12Hour
            ]]>
            FROM (SELECT uid, SUM(online_time) AS online_time FROM s_room_user_online_${tableSuffix}
            <![CDATA[ WHERE ctime >= #{startTime} AND ctime < #{endTime} ]]>
            <if test="os != -1">
                AND os = #{os}
            </if>
            <if test="userType == 1">
                AND is_new = 1
            </if>
            <if test="userType == 2">
                AND fb_gender = 2
            </if>
            GROUP BY uid) as online_time_stat
        </foreach>
    </select>

    <select id="stayRoomInfo" resultType="com.quhong.operation.share.tool.TotalVO" parameterType="String">
        SELECT
        uid as uid,
        COUNT(*) AS countNum,
        SUM(online_time) AS sumNum
        FROM s_room_user_online_${tableSuffix}
        WHERE stat_date = #{dateStr}
        <if test="uidList.size() > 0">
            AND uid IN
            <foreach item="item" collection="uidList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="roomList.size() > 0">
            AND room_id IN
            <foreach item="item" collection="roomList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY uid
    </select>


    <select id="enterRookieRoomNewUsersTime" resultType="com.quhong.operation.share.tool.TotalVO" >
        SELECT
        uid as uid,
        COUNT(*) AS countNum,
        SUM(online_time) AS sumNum
        FROM s_room_user_online_${tableSuffix}
        WHERE stat_date = #{dateStr}
        <if test="roomList != null and roomList.size() > 0">
            AND room_id IN
            <foreach item="item" collection="roomList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY uid
        HAVING  <![CDATA[ sumNum > #{low} AND sumNum <= #{hight} ]]>
    </select>



</mapper>
