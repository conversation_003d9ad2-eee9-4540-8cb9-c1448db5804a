<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.ReportSlaveMapper">

    <select id="getStatReportList" resultType="com.quhong.operation.share.vo.ApiReportListVO" parameterType="String">
        SELECT `name`,
        avg(api_time) AS respTime,
        count(id) AS resultCount
        FROM t_report_${tableSuffix}
        <where>
            <if test="type != null and type != -1">
                AND `type` = #{type}
            </if>
            <if test="os != null and os != -1">
                AND `os` = #{os}
            </if>
            <if test="name != null and name!=''">
                AND `name` = #{name}
            </if>
            <if test="uid != null and uid!=''">
                AND `uid` = #{uid}
            </if>
        </where>
        GROUP BY
        `name`
        ORDER BY resultCount DESC
        LIMIT #{offset},#{pageSize}
    </select>

    <select id="getStatReportListTotal" resultType="Integer" parameterType="String">
        SELECT
        count(*)
        FROM
        (SELECT count(id) FROM t_report_${tableSuffix}
        <where>
            <if test="type != null and type != -1">
                AND `type` = #{type}
            </if>
            <if test="os != null and os != -1">
                AND `os` = #{os}
            </if>
            <if test="name != null and name!=''">
                AND `name` = #{name}
            </if>
            <if test="uid != null and uid!=''">
                AND `uid` = #{uid}
            </if>
        </where>
        GROUP BY
        `name`)
        AS stat
    </select>

    <select id="statInDay" resultType="com.quhong.operation.share.vo.ReportStatVO" parameterType="String">
        SELECT
        api_time AS respTime,
        stat_time AS statTime
        FROM t_report_${tableSuffix}
        <where>
            <if test="type != null and type != -1">
                AND `type` = #{type}
            </if>
            <if test="os != null and os != -1">
                AND `os` = #{os}
            </if>
            <if test="name != null and name!=''">
                AND `name` = #{name}
            </if>
            <if test="countryCode != null and countryCode!=''">
                AND `country_code` = #{countryCode}
            </if>
            <if test="uid != null and uid!=''">
                AND `uid` = #{uid}
            </if>
        </where>
        GROUP BY
        statTime
    </select>

    <select id="statByDay" resultType="com.quhong.operation.share.vo.ReportStatVO" parameterType="String">
        SELECT
        avg(api_time) AS respTime
        FROM t_report_${tableSuffix}
        <where>
            <if test="type != null and type != -1">
                AND `type` = #{type}
            </if>
            <if test="os != null and os != -1">
                AND `os` = #{os}
            </if>
            <if test="name != null and name!=''">
                AND `name` = #{name}
            </if>
            <if test="countryCode != null and countryCode!=''">
                AND `country_code` = #{countryCode}
            </if>
            <if test="uid != null and uid!=''">
                AND `uid` = #{uid}
            </if>
        </where>
    </select>

    <select id="statCountry" resultType="com.quhong.operation.share.vo.ReportCountryVO" parameterType="String">
        SELECT
        country_code AS countryCode,
        avg(api_time) AS respTime,
        count(country_code) AS resultCount
        FROM t_report_${tableSuffix}
        <where>
            <if test="type != null and type != -1">
                AND `type` = #{type}
            </if>
            <if test="os != null and os != -1">
                AND `os` = #{os}
            </if>
            <if test="name != null and name!=''">
                AND `name` = #{name}
            </if>
            <if test="countryCode != null and countryCode!=''">
                AND `country_code` = #{countryCode}
            </if>
            <if test="uid != null and uid!=''">
                AND `uid` = #{uid}
            </if>
        </where>
        GROUP BY
        country_code
    </select>
</mapper>
