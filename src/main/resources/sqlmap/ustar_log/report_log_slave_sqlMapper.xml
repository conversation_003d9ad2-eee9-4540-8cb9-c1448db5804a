<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.ReportLogSlaveMapper">
    <resultMap id="baseResultMap" type="com.quhong.mysql.data.ReportLogData">
        <result column="report_id" property="reportId"/>
        <result column="information" property="information"/>
        <result column="os" property="os"/>
        <result column="slang" property="slang"/>
        <result column="report_type" property="reportType"/>
        <result column="content_type" property="contentType"/>
        <result column="content" property="content"/>
        <result column="c_time" property="ctime"/>
        <result column="m_time" property="mtime"/>
        <result column="error" property="error"/>
    </resultMap>
    <sql id="baseSql">
        uid,report_id,information,os,slang,report_type,content_type,content,c_time,m_time,error
    </sql>
    <sql id="itemSql">
        #{item.reportId},#{item.information},#{item.os},#{item.slang},#{item.reportType},#{item.contentType},#{item.content},#{item.ctime},#{item.mtime},#{item.error}
    </sql>

    <select id="selectReportId" resultType="string">
        SELECT report_id
        FROM t_report_log_${tableSuffix}
        WHERE
        <![CDATA[ c_time >= #{startTime} AND c_time < #{endTime} ]]>
        group by report_id
        order by c_time desc
    </select>

    <select id="selectReports" resultMap="baseResultMap">
        <foreach collection="tableSuffixList" item="tableSuffix" separator="UNION ALL">
            SELECT
            <include refid="baseSql"/>
            FROM t_report_log_${tableSuffix}
            WHERE
            <![CDATA[ c_time >= #{startTime} AND c_time < #{endTime} ]]>
            <if test="reportIds != null and reportIds.size() > 0">
                AND report_id in
                <foreach collection="reportIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </foreach>
        order by c_time desc
    </select>

    <select id="selectReport" resultMap="baseResultMap">
            SELECT <include refid="baseSql"/>
            FROM t_report_log_${tableSuffix}
            WHERE report_id = #{reportId}
            order by c_time desc
    </select>
</mapper>
