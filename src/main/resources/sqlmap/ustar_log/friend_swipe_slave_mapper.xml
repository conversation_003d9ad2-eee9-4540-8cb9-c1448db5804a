<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.CrushOpMapper">
    <resultMap id="resultMap" type="com.quhong.operation.share.data.FriendSwipeInfo">
        <result column="uid" property="uid"/>
        <result column="aid" property="aid"/>
        <result column="type" property="type"/>
        <result column="is_unlock" property="unlock"/>
        <result column="ctime" property="ctime"/>
    </resultMap>

    <select id="friendSwipeList" resultMap="resultMap">
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_0 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_1 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_2 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_3 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_4 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_5 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_6 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_7 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_8 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
        UNION ALL
        SELECT uid,aid,type,is_unlock,ctime FROM t_friend_swipe_9 where
        ctime &gt;= #{startTime} AND ctime &lt; #{endTime}
    </select>

</mapper>
