<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.RoomUserStatMapper">
    <resultMap id="baseMap" type="com.quhong.operation.share.vo.RoomUserOnlineVO" >
        <result column="stat_time" property="statTime"/>
        <result column="count" property="count"/>
    </resultMap>

    <select id="roomUserOnlineByDay" resultMap="baseMap" parameterType="String">
        SELECT stat_time, sum(user_count) as count
        FROM s_room_user_stat_${tableSuffix}
       <![CDATA[ WHERE ctime >= #{startTime}
          AND ctime
            < #{endTime} ]]>
        GROUP BY stat_time
    </select>

</mapper>
