<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.PartyGirlTaskMapper">

    <select id="selectPtgReplayStat" resultType="com.quhong.operation.share.data.AggStatData">
        select count(distinct uid) userCount,sum(match_msg_count) count
        from t_partygirl_task_${tableSuffix}
        where date_key = #{date}
        and match_msg_count > 0
    </select>
</mapper>
