<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.quhong.mysql.slave_mapper.ustar_log.RoomReportLogMapper">
    <resultMap id="baseResultMap" type="com.quhong.operation.share.mysql.RoomReportLogData">
        <result column="id" property="id"/>
        <result column="room_type" property="roomType"/>
        <result column="child_type" property="childType"/>
        <result column="date" property="date"/>
        <result column="num" property="num"/>
        <result column="user_num" property="userNum"/>
        <result column="content_num" property="contentNum"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
    </resultMap>

    <resultMap id="avgStatMap" type="com.quhong.operation.share.data.RoomReportStatData">
        <result column="room_avg_num" property="roomAvgNum"/>
        <result column="user_avg_num" property="userAvgNum"/>
        <result column="content_avg_num" property="contentAvgNum"/>
    </resultMap>

    <sql id="baseSql">
        room_type,child_type,date,num,user_num,content_num,ctime,mtime
    </sql>

    <sql id="itemSql">
        #{item.roomType},#{item.childType},#{item.date},#{item.num},#{item.userNum},#{item.contentNum},#{item.ctime},#{item.mtime}
    </sql>

    <select id="selectAvgStatData" resultMap="avgStatMap">
        select avg(num) as room_avg_num,avg(user_num) as user_avg_num,avg(content_num) as content_avg_num
        from s_room_report_log_${tableSuffix}
        where date = #{date}
        <if test="roomType != -1">
            and room_type = #{roomType}
        </if>
        <if test="childType != -1">
            and child_type = #{childType}
        </if>
    </select>

</mapper>
