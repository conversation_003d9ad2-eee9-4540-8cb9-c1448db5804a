<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mongo="http://www.springframework.org/schema/data/mongo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/data/mongo
       http://www.springframework.org/schema/data/mongo/spring-mongo.xsd">

    <mongo:mongo-client id="mongoOperationClient" host="${operation.mongo.host}" port="${operation.mongo.port}"
                        credentials="${operation.mongo.user}:${operation.mongo.password}@${operation.mongo.database}">
        <mongo:client-options min-connections-per-host="5"/>
    </mongo:mongo-client>
    <mongo:db-factory id="operationMongoDbFactory" dbname="${operation.mongo.database}" mongo-ref="mongoOperationClient"/>

    <mongo:mongo-client id="movieOpClient" host="${operation.movie.host}" port="${operation.movie.port}"
                        credentials="${operation.movie.user}:${operation.movie.password}@${operation.movie.database}">
        <mongo:client-options min-connections-per-host="5"/>
    </mongo:mongo-client>

    <bean id="operationDefaultMongoTypeMapper"
          class="org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper">
        <constructor-arg name="typeKey">
            <null/>
        </constructor-arg>
    </bean>

    <bean id="operationMappingContext" class="org.springframework.data.mongodb.core.mapping.MongoMappingContext"/>

    <bean id="operationMappingMongoConverter"
          class="org.springframework.data.mongodb.core.convert.MappingMongoConverter">
        <constructor-arg name="mongoDbFactory" ref="operationMongoDbFactory"/>
        <constructor-arg name="mappingContext" ref="operationMappingContext"/>
        <property name="typeMapper" ref="operationDefaultMongoTypeMapper"/>
    </bean>

    <bean id="operation_mongo_bean" class="org.springframework.data.mongodb.core.MongoTemplate">
        <constructor-arg name="mongoDbFactory" ref="operationMongoDbFactory"/>
        <constructor-arg name="mongoConverter" ref="operationMappingMongoConverter"/>
    </bean>


</beans>
