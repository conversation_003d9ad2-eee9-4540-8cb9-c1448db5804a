<!DOCTYPE html>
<!-- saved from url=(0040)http://www.qmovies.tv/about/privacy.html -->
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head title="Contact us">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <meta property="og:type" content="article" />
    <meta property="og:title" content="YouStar.live" />
    <meta property="og:description" content="Voice Chat Room
https://www.youstar.live/" />
    <meta property="og:image" th:content="${icon}" />
    <meta property="og:image:width" content="200" />
    <meta property="og:image:height" content="200" />

    <style type="text/css">
        html {
            background: #000;
            padding: 0;
        }

        body {
            padding: 0;
            margin: 0;
        }

        #app,
        #abb {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            color: #ffffff;
            background: url('https://cdn3.qmovies.tv/youstar/op_sys_1668584395_bg.png');
            background-size: 100% 100%;
            font-family: Roboto;
            padding: 52px 0 0;
            margin: 0 auto;
        }

        #abb {
            direction: rtl;
        }

        .we {
            text-align: center;
            width: 80vw;
            height: 90px;
            margin: 0 auto;
            font-size: 1.3rem;
            font-weight: bold;
            line-height: 30px;
        }

        .come {
            width: 100%;
            height: 14px;
            margin: 14px auto 38px;
            text-align: center;
            font-size: 0.92rem;
            font-weight: 400;
            line-height: 14px;
        }

        .Join {
            width: 236px;
            height: 52px;
            margin: 0 auto;
            background: rgba(54, 235, 166, 1);
            border-radius: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            font-weight: 500;
            line-height: 21px;
            color: #F9FAFB;
        }

        .img {
            width: 32px;

        }

        .msg {
            transform: scaleX(-1);
        }

        .span {
            margin: 0 12px;
        }

        .youstar {
            position: fixed;
            bottom: 0;
            width: 100%;
            height: 54px;
            background: rgba(249, 250, 251, 0.16);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 14px;
        }

        .left {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 13px;
            font-weight: bold;
            line-height: 16px;
        }

        .imgs {
            width: 1.96rem;
            margin: 0 4px;
        }

        .p {
            margin: 0 6px;
            width: 120px;
        }

        .pp {
            font-size: 11px;
            margin: 0 6px;
            width: 120px;
        }


        .right {
            width: 6.3067rem;
            margin: 0 25px;

        }

        .name {
            width: 100%;
            height: 21px;
            font-size: 18px;
            font-weight: 500;
            line-height: 21px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: center;
        }

        .rid {
            width: 100%;
            height: 14px;
            font-size: 12px;
            font-weight: 400;
            line-height: 14px;
            text-align: center;
            margin: 2px 0 25px;
        }

        .head {
            position: relative;
            width: 180px;
            height: 180px;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #demoCanvas_,
        #demoCanvas_1 {
            direction: ltr;
            position: absolute;
            top: 0;
            width: 180px;
            height: 180px;
        }

        .img_ {
            position: relative;
            width: 107px;
            height: 107px;
            border-radius: 50%;
        }

        .dis {
            display: none;
        }

        .box {
            transform: scaleX(-1);
        }

        .herf {
            width: 100px;
            height: 100px;
            background: #000;
            text-align: center;
        }
    </style>
</head>
<script src="https://cdn.jsdelivr.net/npm/svgaplayerweb@2.3.1/build/svga.min.js"></script>

<body>
    <!-- tijiaosada  -->
    <div id="app" class="dis">
        <div class="we">
            We are enjoying an interesting game, come and join our party!
        </div>
        <p class="come">
            Come and enjoy voice chat with us!
        </p>
        <div class="head">
            <div id="demoCanvas_"></div>
            <img class="img_" onload="SVGA_S" th:src="${icon}" alt="" />
        </div>
        <p class="name" th:text="${name}">name</p>
        <p class="rid" th:text="'Room ID :' + ${rid}">Room ID :rid</p>
        <div class="Join" onclick="shutDown()">
            <img class="img" src="https://cdn3.qmovies.tv/youstar/op_sys_1668584395_join.png" alt="" />
            <span class="span">Join the party </span>
        </div>
        <div class="youstar">
            <div class="left">
                <img class="imgs" src="https://cdn3.qmovies.tv/youstar/op_sys_1668584395_icon.png" alt="" />
                <p class="p">
                    Download Youstar get rich rewards.
                </p>
            </div>
            <img id="msg1" class="right dis" onclick="App()"
                src="https://cdn3.qmovies.tv/youstar/op_sys_1669109763_google.png" alt="" />

            <img id="msgs2" class="right dis" onclick="App()"
                src="https://cdn3.qmovies.tv/youstar/op_sys_1668584395_app.png" alt="">
        </div>
    </div>
    <div id="abb" class="dis">
        <div class="we">
            نحن نستمتع باللعبة الرائعة، تعال وانضم الى حفلتنا!
        </div>
        <p class="come">
            تعال واستمتع بالدردشة الصوتية معنا!
        </p>
        <div class="head">
            <div id="demoCanvas_1"></div>
            <img class="img_" onload="SVGA_S" th:src="${icon}" alt="" />
        </div>
        <p class="name" th:text="${name}">name</p>
        <p class="rid" th:text="'الأيدي للغرفة:' + ${rid}">الأيدي للغرفة:rid</p>
        <div class="Join" onclick="shutDown()">
            <img class="img  box" src="https://cdn3.qmovies.tv/youstar/op_sys_1668584395_join.png" alt="" />
            <span class="span">انضم الى الحفلة </span>
        </div>
        <div class="youstar">
            <div class="left">
                <img class="imgs" src="https://cdn3.qmovies.tv/youstar/op_sys_1668584395_icon.png" alt="" />
                <p class="pp">
                    تنزيل برنامج اليوستار للحصول على المكافآت الغنية.
                </p>
            </div>
            <img id="msg" class="right dis" onclick="App()"
                src="https://cdn3.qmovies.tv/youstar/op_sys_1669109763_google.png" alt="" />

            <img id="msgs" class="right dis" onclick="App()"
                src="https://cdn3.qmovies.tv/youstar/op_sys_1668584395_app.png" alt="">
        </div>
    </div>

</body>
<script th:inline="javascript">
    var uid = getQueryString('uid')
    var slang = getQueryString('slang')
    // 获取url中的参数
    function getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        return null;
    }
    // 跳转
    function App() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        if (isAndroid) {
            location.href = [[${ android_link }]]
        } else {
            location.href = [[${ ios_link }]]
        }
    }
    // 初始化svga
    function SVGA_S() {
        let id = slang == 1 ? '#demoCanvas_' : '#demoCanvas_1'
        var player = new SVGA.Player(id);
        var parser = new SVGA.Parser(id);
        parser.load('https://cdn3.qmovies.tv/youstar/op_sys_1668569607_ripple.svga', function (videoItem) {
            // player.loops = 2; // 循环次数
            player.setVideoItem(videoItem);
            player.startAnimation();
            player.onFrame(function (i) {
            });
        })
    }
    var downloader
    // 初始化
    function dataList() {
        if (slang == 1) {
            document.getElementById('app').classList.remove("dis")
            document.title = 'YouStar – Voice Chat Room'
        } else {
            document.getElementById('abb').classList.remove("dis")
            document.title = `يوستار - غرفة دردشة صوتية`
        }
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        if (isAndroid) {
            if (slang == 1) {
                document.getElementById('msg1').classList.remove("dis")
            } else {
                document.getElementById('msg').classList.remove("dis")
            }

        } else {
            if (slang == 1) {
                document.getElementById('msgs2').classList.remove("dis")
            } else {
                document.getElementById('msgs').classList.remove("dis")
            }
        }
    }
    function shutDown() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        location.href = [[${ action_url }]]
        window.setTimeout(function () {
            let hidden = window.document.hidden || window.document.mozHidden || window.document.msHidden || window.document.webkitHidden
            if (typeof hidden == "undefined" || hidden == false) {
                //应用宝下载地址
                App()
            }
        }, 2000)
    }


    //iOS 交互声明
    function connectWebViewJavascriptBridgeIOS(callback) {
        if (window.WebViewJavascriptBridge) {
            return callback(window.WebViewJavascriptBridge);
        }
        if (window.WVJBCallbacks) {
            return window.WVJBCallbacks.push(callback);
        }
        window.WVJBCallbacks = [callback];
        let WVJBIframe = document.createElement("iframe");
        WVJBIframe.style.display = "none";
        WVJBIframe.src = "wvjbscheme://__BRIDGE_LOADED__";
        document.documentElement.appendChild(WVJBIframe);
        setTimeout(() => {
            document.documentElement.removeChild(WVJBIframe);
        }, 0);
    }

    //Android 交互声明
    function connectWebViewJavascriptBridgeANDROID(callback) {
        if (window.WebViewJavascriptBridge) {
            callback(WebViewJavascriptBridge);
        } else {
            document.addEventListener(
                "WebViewJavascriptBridgeReady",
                function () {
                    callback(WebViewJavascriptBridge);
                },
                false
            );
        }
    }

    //H5调用Native
    function callhandler(name, data, callback) {
        //iOS的方法
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
            connectWebViewJavascriptBridgeIOS(function (bridge) {
                bridge.callHandler(name, data, callback)
            })
        }
        //Android方法
        if (/(Android)/i.test(navigator.userAgent)) {
            connectWebViewJavascriptBridgeANDROID(function (bridge) {
                bridge.callHandler(name, data, callback)
            })
        }
    }



    SVGA_S()
    dataList()
</script>

</html>