<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head title="Contact us">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <meta property="og:type" content="article" />
    <meta property="og:title" content="youstar.live" />
    <meta property="og:image" th:content="${icon}" />
    <meta property="og:image:width" content="200" />
    <meta property="og:image:height" content="200" />
</head>

<body>


</body>
<script th:inline="javascript">
    function shutDown() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
        const vm = this
        location.href = [[${ action_url }]]
        window.setTimeout(function () {
            let hidden = window.document.hidden || window.document.mozHidden || window.document.msHidden || window.document.webkitHidden
            if (typeof hidden == "undefined" || hidden == false) {
                //应用宝下载地址
                App()
            }
        }, 4000)
    }
    function App() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
        const vm = this
        if (isAndroid) {
            location.href = [[${ android_link }]]
        } else {
            location.href = [[${ ios_link }]]
        }
    }
    shutDown()

</script>

</html>