<!DOCTYPE html>
<!-- saved from url=(0040)http://www.qmovies.tv/about/privacy.html -->
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head title="Contact us">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <meta property="og:type" content="article" />
    <meta property="og:title" th:content="${name}" />
    <meta property="og:description" th:content="${desc}" />
    <meta property="og:image" th:content="${icon}" />
    <meta property="og:image:width" content="200" />
    <meta property="og:image:height" content="200" />

    <style type="text/css">

    </style>
</head>
<script src="https://cdn.jsdelivr.net/npm/svgaplayerweb@2.3.1/build/svga.min.js"></script>

<body>


</body>
<script th:inline="javascript">
    var uid = getQueryString('uid')
    var slang = getQueryString('slang')
    // 获取url中的参数
    function getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        return null;
    }
    // 跳转应用市场
    function App() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        if (isAndroid) {
            location.href = [[${ android_link }]]
        } else {
            location.href = [[${ ios_link }]]
        }
    }
    // 跳转h5活动
    function shutDown() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        try {
            location.href = [[${ action_url }]]
        } catch (error) {
            App()
        }

    }

    shutDown();

    //iOS 交互声明
    function connectWebViewJavascriptBridgeIOS(callback) {
        if (window.WebViewJavascriptBridge) {
            return callback(window.WebViewJavascriptBridge);
        }
        if (window.WVJBCallbacks) {
            return window.WVJBCallbacks.push(callback);
        }
        window.WVJBCallbacks = [callback];
        let WVJBIframe = document.createElement("iframe");
        WVJBIframe.style.display = "none";
        WVJBIframe.src = "wvjbscheme://__BRIDGE_LOADED__";
        document.documentElement.appendChild(WVJBIframe);
        setTimeout(() => {
            document.documentElement.removeChild(WVJBIframe);
        }, 0);
    }

    //Android 交互声明
    function connectWebViewJavascriptBridgeANDROID(callback) {
        if (window.WebViewJavascriptBridge) {
            callback(WebViewJavascriptBridge);
        } else {
            document.addEventListener(
                "WebViewJavascriptBridgeReady",
                function () {
                    callback(WebViewJavascriptBridge);
                },
                false
            );
        }
    }

    //H5调用Native
    function callhandler(name, data, callback) {
        //iOS的方法
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
            connectWebViewJavascriptBridgeIOS(function (bridge) {
                bridge.callHandler(name, data, callback)
            })
        }
        //Android方法
        if (/(Android)/i.test(navigator.userAgent)) {
            connectWebViewJavascriptBridgeANDROID(function (bridge) {
                bridge.callHandler(name, data, callback)
            })
        }
    }



</script>

</html>