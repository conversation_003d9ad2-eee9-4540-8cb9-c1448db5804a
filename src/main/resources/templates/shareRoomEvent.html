<!DOCTYPE html>
<!-- saved from url=(0040)http://www.qmovies.tv/about/privacy.html -->
<html>

<head title="Magic AI Privacy Policy">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport"
        content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:type" content="website">
    <meta property="og:title" th:content="'الحدث من معرف الغرفة:' + ${roomId}">
    <meta property="og:description" th:content="${eventDescription}">
    <meta property="og:image" th:content="${icon}"> <!-- 活动封面图片的URL -->
    <meta property="og:url" content="https://yourdomain.com/activity"> <!-- H5页面的链接地址 -->
    <meta property="og:site_name" content="Youstar">
    <meta property="og:image:width" content="1200"> <!-- 推荐大小为1200x630px -->
    <meta property="og:image:height" content="630">
    <script>
        // 设置vh
        !(function (n, e) {
            function setViewHeight() {
                // var windowVH = e.innerWidth / 375;
                var windowVH = n.documentElement.clientWidth / 375
                n.documentElement.style.setProperty('--px', windowVH + 'px');
                n.documentElement.style.setProperty('font-size', windowVH + 'px')
                n.body.style.fontSize = windowVH + 'px'
            }
            var i = 'orientationchange' in window ? 'orientationchange' : 'resize'
            n.addEventListener('DOMContentLoaded', setViewHeight)
            e.addEventListener(i, setViewHeight)
        })(document, window)
    </script>

    <title></title>

    <style>
        html,
        body,
        div,
        span,
        applet,
        object,
        iframe,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        blockquote,
        pre,
        a,
        abbr,
        acronym,
        address,
        big,
        cite,
        code,
        del,
        dfn,
        em,
        img,
        ins,
        kbd,
        q,
        s,
        samp,
        small,
        strike,
        strong,
        sub,
        sup,
        tt,
        var,
        b,
        u,
        i,
        center,
        dl,
        dt,
        dd,
        ol,
        ul,
        li,
        fieldset,
        form,
        label,
        legend,
        table,
        caption,
        tbody,
        tfoot,
        thead,
        tr,
        th,
        td,
        article,
        aside,
        canvas,
        details,
        embed,
        figure,
        figcaption,
        footer,
        header,
        hgroup,
        menu,
        nav,
        output,
        ruby,
        section,
        summary,
        time,
        mark,
        audio,
        video {
            margin: 0;
            padding: 0;
            border: 0;
            font-size: 100%;
            font: inherit;
            vertical-align: baseline;
        }

        /* HTML5 display-role reset for older browsers */
        article,
        aside,
        details,
        figcaption,
        figure,
        footer,
        header,
        hgroup,
        menu,
        nav,
        section {
            display: block;
        }

        body {
            line-height: 1;
        }

        ol,
        ul {
            list-style: none;
        }

        blockquote,
        q {
            quotes: none;
        }

        blockquote:before,
        blockquote:after,
        q:before,
        q:after {
            content: '';
            content: none;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .clearfix:after {
            content: '';
            display: block;
            height: 0;
            clear: both;
            visibility: hidden;
        }

        * {
            box-sizing: border-box;
        }

        @font-face {
            font-family: f-normal;
            src: url(https://cloudcdn.waho.live/resource/op_sys_1712456319_helveticaNeue.ttf);
        }

        @font-face {
            font-family: f-medium;
            src: url(https://cloudcdn.waho.live/resource/op_sys_1712456319_helveticaNeue-Medium.ttf);
        }

        body {
            font-family: f-normal;
            font-size: 12px;
        }
    </style>

    <style type="text/css">
        html {
            background: rgb(27, 53, 122);

        }

        body {
            background: RGBA(23, 50, 125, 1);
            padding: 0;
            margin: 0;
        }

        #app {
            /* width: 375rem; */
            min-height: 100vh;
            margin: 0 auto;
            background: url(https://cdn3.qmovies.tv/youstar/op_1726039089_bg.webp) no-repeat;
            background-size: 100% auto;
            background-position: 0  -88px;
            padding: 28rem 0 90rem;
            box-sizing: border-box;
        }

        .come {
            width: 248rem;
            height: 41rem;
            margin: 0 auto;
            background: url(https://cdn3.qmovies.tv/youstar/op_1726043128_come.webp) no-repeat;
            background-size: 100% auto;
        }

        .roomImg {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 320rem;
            margin: 90rem auto 0;
        }

        .roomImg_ {
            width: 100%;
            object-fit: contain;
        }

        .activityName {
            margin: 23rem 28rem 14rem;
            text-align: center;
            font-size: 20rem;
            color: #FFF7AA;
            line-height: 25rem;
            font-family: f-medium;
        }

        .activityText {
            width: 300rem;
            font-size: 12rem;
            color: #7682A5;
            line-height: 14rem;
            text-align: center;
            margin: 0 auto 20rem;
        }

        .mT {
            height: 160rem;
        }

        .activityTime {
            text-align: center;
            font-size: 16rem;
            color: #FFFBD1;
            line-height: 18rem;
            margin: 0 auto 4rem;
        }

        .activityDecs {
            font-family: f-medium;
            font-size: 12rem;
            color: #91A1CB;
            line-height: 18rem;
            text-align: center;
        }

        .ma {
            margin: 0 0 12rem;
        }

        .span {
            color: rgba(197, 211, 245, 1);
        }

        .viewActivity {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 217rem;
            height: 48rem;
            margin: 24rem auto 0;
            background: #FF9F28;
            border-radius: 25rem;
            font-family: f-medium;
            font-size: 18rem;
            color: #FFFFFF;
        }

        .viewActivity.hasMt {
            margin-top: 100rem;
        }

        .youstar {
            position: fixed;
            bottom: 0;
            width: 100vw;
            height: 74rem;
            background: #FFFFFF;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .youstar_left {
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 500;
            font-size: 18rem;
            color: #010101;
            line-height: 25rem;
            margin: 0 13rem;
            font-family: f-medium;
        }

        .font-box {
            margin: 0 12rem;
        }

        .two {
            font-weight: 500;
            font-size: 12rem;
            color: #7F7F7F;
            line-height: 17rem;
        }

        .youstarImg {
            width: 55rem;
            height: 55rem;

        }

        .youstar_right {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 79rem;
            height: 30rem;
            margin: 0 8rem;
            background: #FF9F28;
            border-radius: 15rem;
            font-weight: 500;
            font-size: 12rem;
            color: #FFFFFF;
            line-height: 17rem;
            font-family: f-medium;
        }
    </style>
</head>

<body>
    <div id="app">

    </div>
</body>
<script>

    var uid = getQueryString('uid')
    var slang = getQueryString('slang')
    // 获取url中的参数
    function getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        return null;
    }

    // 解析的数据
    var eventCoverUrl = "[[${ eventCoverUrl }]]"
    var eventName = "[[${ eventName }]]"
    var eventDescription = "[[${ eventDescription }]]"
    var eventStartTime = "[[${ eventStartTime }]]"
    var eventEndTime = "[[${ eventEndTime }]]"
    var roomName = "[[${ roomName }]]"
    var roomId = "[[${ roomId }]]"

    

    var htmlString = `<div class="come"></div>
        <div class="roomImg">
            <img class="roomImg_" src="${eventCoverUrl}" alt="">
        </div>
        <div class="activityName">
        ${eventName}
        </div>
        <div class="activityText"> ${eventDescription}</div>
        <div class="activityTime">Activity time</div>
        <div class="activityDecs ma">${format_s(eventStartTime)} — ${format_s(eventEndTime)} (GMT+3)</div>
        <div class="activityDecs ">Room: <span class="span">${roomName}</span></div>
        <div class="activityDecs ">Room ID: <span class="span">${roomId}</span></div>
        <div class="viewActivity" onclick="openActivity()">View activity</div>
        <div class="youstar">
            <div class="youstar_left">
                <img class="youstarImg" src="https://cdn3.qmovies.tv/youstar/op_1726047115_youstar.webp" alt="">
                <div  class="font-box">
                    <div>Youstar</div>
                    <div class="two">Group Voice Chat Room</div>
                </div>
            </div>
            <div class="youstar_right" onclick="shutDown()">Download</div>
        </div>`

    var errorString = ` 
    <div class="come"></div>
    <div class="mT"></div>
    <div class="activityName">
        Sorry，The event has expired
        </div>
        <div class="viewActivity hasMt" onclick="openHallActivity()">View other events</div>
        <div class="youstar">
            <div class="youstar_left">
                <img class="youstarImg" src="https://cdn3.qmovies.tv/youstar/op_1726047115_youstar.webp" alt="">
                <div>
                    <div>Youstar</div>
                    <div class="two">Group Voice Chat Room</div>
                </div>
            </div>
            <div class="youstar_right" onclick="shutDown()">Download</div>
        </div>`
    if (!eventStartTime) {
        document.getElementById("app").innerHTML = errorString
    } else {
        document.getElementById("app").innerHTML = htmlString
    }

    function format_s(data) {
        if (data == null || data == '') {
            return '0'
        } else {

            let date = new Date((Number(data)) * 1000) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
            const options = {
                timeZone: 'Europe/Moscow', // UTC+3
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false // 24小时制
            };

            // 格式化为所需的形式
            const utcPlus3Time = date.toLocaleString('en-US', options).replace(',', '');
            return utcPlus3Time
        }
    }

    // 编写一个通过时间戳转utc+3时间的函数
    

    // 跳转
    function App() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        if (isAndroid) {
            location.href = "[[${ android_link }]]"
        } else {
            location.href = "[[${ ios_link }]]"
        }
    }
    // 下载
    function shutDown() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        location.href = "[[${ action_url }]]"
        window.setTimeout(function () {
            let hidden = window.document.hidden || window.document.mozHidden || window.document.msHidden ||
                window.document.webkitHidden
            if (typeof hidden == "undefined" || hidden == false) {
                //应用宝下载地址
                App()
            }
        }, 2000)
    }
    // 跳转房间活动
    function openActivity() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        location.href = "[[${ action_url }]]"
        window.setTimeout(function () {
            let hidden = window.document.hidden || window.document.mozHidden || window.document.msHidden ||
                window.document.webkitHidden
            if (typeof hidden == "undefined" || hidden == false) {
                //应用宝下载地址
                App()
            }
        }, 2000)
    }

    // 跳转活动广场页面
    function openHallActivity() {
        let u = navigator.userAgent
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //判断是否是 android终端
        let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //判断是否是 iOS终端
        const vm = this
        location.href = "[[${ action_hall_url }]]"
        window.setTimeout(function () {
            let hidden = window.document.hidden || window.document.mozHidden || window.document.msHidden ||
                window.document.webkitHidden
            if (typeof hidden == "undefined" || hidden == false) {
                //应用宝下载地址
                App()
            }
        }, 2000)
    }

    //iOS 交互声明
    function connectWebViewJavascriptBridgeIOS(callback) {
        if (window.WebViewJavascriptBridge) {
            return callback(window.WebViewJavascriptBridge);
        }
        if (window.WVJBCallbacks) {
            return window.WVJBCallbacks.push(callback);
        }
        window.WVJBCallbacks = [callback];
        let WVJBIframe = document.createElement("iframe");
        WVJBIframe.style.display = "none";
        WVJBIframe.src = "wvjbscheme://__BRIDGE_LOADED__";
        document.documentElement.appendChild(WVJBIframe);
        setTimeout(() => {
            document.documentElement.removeChild(WVJBIframe);
        }, 0);
    }

    //Android 交互声明
    function connectWebViewJavascriptBridgeANDROID(callback) {
        if (window.WebViewJavascriptBridge) {
            callback(WebViewJavascriptBridge);
        } else {
            document.addEventListener(
                "WebViewJavascriptBridgeReady",
                function () {
                    callback(WebViewJavascriptBridge);
                },
                false
            );
        }
    }

    //H5调用Native
    function callhandler(name, data, callback) {
        //iOS的方法
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
            connectWebViewJavascriptBridgeIOS(function (bridge) {
                bridge.callHandler(name, data, callback)
            })
        }
        //Android方法
        if (/(Android)/i.test(navigator.userAgent)) {
            connectWebViewJavascriptBridgeANDROID(function (bridge) {
                bridge.callHandler(name, data, callback)
            })
        }
    }

</script>

</html>