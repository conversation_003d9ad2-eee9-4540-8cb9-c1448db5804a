baseUrl: /activity/
server:
  port: 8080
spring:
  application:
    name: ustar-java-activity
  messages:
    basename: i18n/activity
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-file-size: 10MB
      max-request-size: 50MB
      location: /data/upload_tmp
  thymeleaf:
    prefix: classpath:/templates/
    encoding: UTF-8
    cache: false
    suffix: .html
    servlet:
      content-type: text/html
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 3000
