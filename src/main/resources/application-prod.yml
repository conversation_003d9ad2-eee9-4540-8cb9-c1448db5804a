# 系统参数配置
online: true
beans: 2000,4000,7000,8000,10000
server:
  port: 8080
  servlet:
    context-path: /operation/
spring:
  application:
    name: youstar-operation
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 3000
        readTimeout: 60000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 3000
logback:
  configurationFile: ./logback.xml
# 禁用elasticsearch健康检查
management:
  health:
    elasticsearch:
      enabled: false
dubbo:
  application:
    name: ${spring.application.name}
    qos-enable: false
    metadata-service-port: 20885
  registry:
    address: kubernetes://DEFAULT_MASTER_HOST?registry-type=service&duplicate=false&trustCerts=true&namespace=devops
  protocol:
    name: dubbo
    port: 20880
  metadata-report:
    report-metadata: false
  service:
    shutdown:
      wait: 5000
