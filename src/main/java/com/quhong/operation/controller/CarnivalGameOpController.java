package com.quhong.operation.controller;

import com.alibaba.fastjson.JSONObject;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.CarnivalGameService;
import com.quhong.operation.share.condition.BaseCondition;
import com.quhong.operation.share.vo.CarnivalGameConfig;
import com.quhong.operation.utils.AWSUploadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


@RestController
@RequestMapping(value ="/carnivalGame", produces = MediaType.APPLICATION_JSON_VALUE)
public class CarnivalGameOpController extends BaseController {

    private final static Logger logger = LoggerFactory.getLogger(CarnivalGameOpController.class);
    private final static String filePath = "game/";

    @Resource
    private CarnivalGameService carnivalGameService;

    @RequireRole
    @RequestMapping("/config")
    public String carnivalGameConfig(@RequestBody BaseCondition condition) {
        logger.info("get carnivalGameConfig {}", condition);
        return createResult(HttpCode.SUCCESS, carnivalGameService.carnivalGameConfig(condition));
    }

    @RequireRole
    @PostMapping("/updateConfig")
    public String updateConfig(@RequestParam String uid, @RequestBody CarnivalGameConfig dto) {
        logger.info("uid: {}, updateConfig {}", uid, JSONObject.toJSONString(dto));
        carnivalGameService.updateBaseCarnivalGameConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/addPrizeConfig")
    public String addPrizeConfig(@RequestParam String uid, @RequestBody CarnivalGameConfig dto) {
        logger.info("uid: {}, addPrizeConfig {}", uid, JSONObject.toJSONString(dto));
        carnivalGameService.addPrizeConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequireRole
    @PostMapping("/updatePrizeConfig")
    public String updatePrizeConfig(@RequestParam String uid, @RequestBody CarnivalGameConfig dto) {
        logger.info("uid: {}, updateConfig {}", uid, JSONObject.toJSONString(dto));
        carnivalGameService.updatePrizeConfig(dto);
        return createResult(HttpCode.SUCCESS, "");
    }

    /**
     * 图片上传
     */
    @RequireRole
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return AWSUploadUtils.upload(file, filePath);
    }
}
