package com.quhong.operation.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.ApiResult;
import com.quhong.operation.server.report.MoneyReportServer;
import com.quhong.operation.server.report.NewUserReportServer;
import com.quhong.operation.share.vo.*;
import com.quhong.operation.utils.DateHelper;
import com.quhong.operation.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 女用户数据报表
 */
@RestController
@RequestMapping("/reports")
public class FemaleUserReportsController {

    private static final Logger logger = LoggerFactory.getLogger(FemaleUserReportsController.class);

    @Autowired
    private NewUserReportServer newUserReportServer;
    @Autowired
    private MoneyReportServer moneyReportServer;

    /**
     * 礼物+充值报表下载
     *
     * @param response  response
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/femaleGiftAndPayReports")
    public void femaleGiftAndPayReports(HttpServletResponse response,
                                        String startDate, String endDate, Integer os, Integer app) {
        logger.info("femaleGiftAndPayReports param s={}, e={}, os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);

        List<GiftAndPlayVO> result = newUserReportServer.getGiftAndPayList(timeArr[0], --timeArr[1], 2, null == os ? -1 : os, null == app ? -1 : app);
        List<MonthlyRechargeVO> lists = moneyReportServer.getMonthlyRechargeInfo(timeArr[0], timeArr[1]);

        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.encodeFilename(response, "female_gift_and_pay");
            excelWriter = EasyExcel.write(response.getOutputStream()).registerWriteHandler(ExcelUtils.simpleStyle()).build();
            excelWriter.write(result, EasyExcel.writerSheet(0, "礼物+充值").head(FemaleGiftAndPlayVO.class).build());
            excelWriter.write(lists, EasyExcel.writerSheet(1, "月份线上充值用户详情数据").head(MonthlyRechargeVO.class).build());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
        logger.info("method femaleGiftAndPayReports done");
    }

    /**
     * 礼物+充值列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/femaleGiftAndPayList")
    public ApiResult<Object> femaleGiftAndPayList(String startDate, String endDate, Integer os, Integer app) {
        logger.info("femaleGiftAndPayList param s={}, e={}, os={}", startDate, endDate, os);
        String[] giftAndPayArrZh = {"日期", "女用户数", "新增女用户数", "进房人数", "进房次数", "人均进房次数", "心心领取人数", "心心领取次数",
                "心心发送人数", "心心发送次数", "红包领取人数", "红包领取次数", "红包领取钻石数", "礼物发送人数", "礼物发送次数",
                "礼物消耗钻石数", "购买VIP人数", "华为充值人数", "华为充值次数", "华为充值金额", "充值人数", "充值次数", "充值金额"};
        String[] giftAndPayArrEn = {"Date", "female users", "new female users", "enter room users", "enter room times", "per capita enter user",
                "receive heart users", "receive heart times", "send heart users", "send heart times", "receive lucky box users",
                "receive lucky box times", "receive diamonds from lucky box", "send gift users", "send gift times",
                "diamonds consumed from sending gift", "purchase VIP users", "huawei recharge users", "huawei recharge time", "huawei recharge amount",
                "recharge users", "recharge time", "recharge amount"};
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        List<GiftAndPlayVO> giftAndPayList = newUserReportServer.getGiftAndPayList(timeArr[0], --timeArr[1], 2, null == os ? -1 : os, null == app ? -1 : app);
        result.put("data", giftAndPayList);
        result.put("zh", giftAndPayArrZh);
        result.put("en", giftAndPayArrEn);
        return new ApiResult<>().ok(0, result);
    }

    /**
     * 房间核心玩法数据报表下载
     *
     * @param response  response
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/femaleRoomCorePlayReports")
    public void femaleRoomCorePlayReports(HttpServletResponse response,
                                          String startDate, String endDate, Integer os, Integer app) {
        logger.info("femaleRoomCorePlayReports param s={}, e={}, os={}", startDate, endDate, os);
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        List<RoomCorePlayVO> result = newUserReportServer.getRoomCorePlayList(timeArr[0], --timeArr[1], 2, null == os ? -1 : os, null == app ? -1 : app);
        ExcelUtils.exportExcel(response, result, FemaleRoomCorePlayVO.class, "female_room_core_play", "房间核心玩法数据");
        logger.info("method femaleRoomCorePlayReports done");
    }

    /**
     * 房间核心玩法数据列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequireRole
    @RequestMapping("/femaleRoomCorePlayList")
    public ApiResult<Object> femaleRoomCorePlayList(String startDate, String endDate, Integer os, Integer app) {
        logger.info("femaleRoomCorePlayList param s={}, e={}, os={}", startDate, endDate, os);
        String[] roomCorePlayArrZh = {"日期", "女用户数", "进房人数", "进房次数", "人均进房次数", "幸运转盘参与人数", "幸运转盘参与次数", "幸运转盘消耗钻石数",
                "参与猜拳人数", "参与猜拳次数", "猜拳消耗钻石数", "玩幸运数字人数", "玩幸运数字次数", "玩骰子人数", "玩骰子次数",
                "红包发送人数", "红包发送次数", "红包领取人数", "红包领取次数", "参与PK人数", "参与PK次数", "玩九宫格抽奖人数",
                "玩九宫格抽奖次数", "九宫格消耗钻石数", "玩幸运卡牌人数", "玩幸运卡牌次数", "幸运卡牌消耗钻石数"};
        String[] roomCorePlayArrEn = {"Date", "female users", "enter room users", "enter room times", "per capita enter user", "join lucky wheel users",
                "join lucky wheel times", "diamonds consumed at lucky wheel", "join roshambo users", "join roshambo times",
                "diamonds consumed at roshambo", "play lucky number users", "play lucky number times", "play dice users",
                "play dice times", "send lucky box users", "send lucky box times", "receive lucky box users",
                "receive lucky box times", "join PK users", "join PK times", "play BIG WIN users", "play BIG WIN times",
                "diamonds consumed at BIG WIN", "play lucky card users", "play lucky card times", "diamonds consumed at lucky card"};
        Integer[] timeArr = DateHelper.ARABIAN.getStartOrEndSeconds(startDate, endDate);
        Map<String, Object> result = new HashMap<>();
        List<RoomCorePlayVO> roomCorePlayList = newUserReportServer.getRoomCorePlayList(timeArr[0], --timeArr[1], 2, null == os ? -1 : os, null == app ? -1 : app);
        result.put("data", roomCorePlayList);
        result.put("zh", roomCorePlayArrZh);
        result.put("en", roomCorePlayArrEn);
        return new ApiResult<>().ok(0, result);
    }

}
