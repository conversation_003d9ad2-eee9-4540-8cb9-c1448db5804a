package com.quhong.operation.controller;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.OtherRankingActivityDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.ActivityTemplateCondition;
import com.quhong.operation.share.dto.OtherActivityTemplateDTO;
import com.quhong.operation.share.vo.PageResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/activity_other_template")
public class OtherRankingActivityTemplateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(OtherRankingActivityTemplateController.class);
    private static final Set<String> SUPPORT_SET = new HashSet<>(Arrays.asList("gift", "mic", "buddle", "ride", "ripple", "diamond", "badge", "float_screen", "heart", "honor_title", "entry_effect", "background", "other"));
    // wenmiaofang、yufengxia、guogs、chenluyao、yicui、 运营-陈顺婵、测试-伍幸运,lijun、 许梦华
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5dba92651e23cd00c80b32bc", "62b9780a1fb34e1c3520cb90",
            "62d0dd77c9ada8168e6f77bd", "667a3cccd530ac0dd025e113", "6131ec0335eefc68288895bb", "67da97db2acea1ef176d13bc"
            , "67bc665b7771058b47bdfee5","5db2b8ff1e23cd007f2c9d7e", "67288d1b8bfeda82d33a4993"));
    @Value("${online:true}")
    private boolean online;
    @Resource
    private OtherRankingActivityDao otherRankingActivityDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;

    @Resource
    private MonitorSender monitorSender;

    @Resource
    private GiftDao giftDao;


    private HttpResult checkParams(OtherRankingActivityData template) {
        HttpResult result = new HttpResult();
        if (null == template.getActivityGiftList()
                || null == template.getStartTime() || null == template.getEndTime()
                || null == template.getAcNameAr() || null == template.getAcNameEn()
        ) {
            return result.error(ApiCode.PARAM_ERROR.getMsg());
        }
        for (Integer giftId : template.getActivityGiftList()) {
            if (null == giftId) {
                return result.error("活动礼物不能为空");
            }
            if (-1 != giftId) {
                GiftData giftData = giftDao.getGiftFromDb(giftId);
                if (giftData == null ) {
                    return result.error("活动礼物不存在或者无效");
                }
            }
        }
        if (-1 == template.getActivityGiftList().get(0) && template.getActivityGiftList().size() != 1) {
            return result.error("选择了全部礼物，不能再选择其它礼物");
        }
        if (template.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
            return result.error("活动结束时间距离现在太近，请检查");
        }
        return result.ok();
    }

    /**
     * 保存活动模板
     */
    @RequireRole(3)
    @RequestMapping("/save_template")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody OtherRankingActivityData template) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(template));
            String uid = request.getParameter("uid");
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            result = checkParams(template);
            if (result.getCode() != 0) {
                return result;
            }
            template.setRankingRewardList(Collections.emptyList());
            template.setReachingConfigList(Collections.emptyList());
            otherRankingActivityDao.save(template);
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "正式服-用户成功创建常规活动", "活动名：" + template.getAcNameEn());
            }

        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 更新活动模板
     */
    @RequireRole(3)
    @RequestMapping("/update_template")
    public HttpResult updateTemplate(HttpServletRequest request, @RequestBody OtherActivityTemplateDTO dto) {
        HttpResult result = new HttpResult();
        try {
            String uid = request.getParameter("uid");
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (Objects.isNull(dto.getActivityId())) {
                logger.error("The activityId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            OtherRankingActivityData template = new OtherRankingActivityData();
            BeanUtils.copyProperties(dto, template);
            result = checkParams(template);
            if (result.getCode() != 0) {
                return result;
            }
            logger.info("update template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));
            OtherRankingActivityData templateToUpdate = otherRankingActivityDao.findData(dto.getActivityId());
            if (Objects.isNull(templateToUpdate)) {
                logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            if (templateToUpdate.getStatus() == 1) {
                return result.error("活动已结束，无法更新");
            }
            int now = DateHelper.getNowSeconds();
            if (ServerConfig.isProduct() && templateToUpdate.getStartTime() < now && templateToUpdate.getEndTime() > now) {
                return result.error("活动已开始，无法修改");
            }
            if (dto.getEndTime() - now <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }
            String activityUrl ="";
            if (!StringUtils.isEmpty(dto.getAcUrl()) && !dto.getAcUrl().equals(templateToUpdate.getAcUrl())) {
                UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(dto.getAcUrl());
                urlBuilder.replaceQueryParam("activityId", dto.getActivityId());
                activityUrl = urlBuilder.build(false).encode().toUriString();
            }

            Update update = new Update();
            update.set("acNameEn", dto.getAcNameEn());
            update.set("acNameAr", dto.getAcNameAr());
            update.set("startTime", dto.getStartTime());
            update.set("endTime", dto.getEndTime());
            update.set("activityGiftList", dto.getActivityGiftList());
            update.set("mtime", DateHelper.getNowSeconds());
            if (!StringUtils.isEmpty(activityUrl)) {
                update.set("acUrl", activityUrl);
            }
            otherRankingActivityDao.updateData(templateToUpdate, update);
        } catch (Exception e) {
            logger.error("update template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 活动模板分页查询
     */
    @RequireRole(3)
    @RequestMapping("/page")
    public String selectPage(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<PageResultVO<OtherActivityTemplateDTO>> result = new HttpResult<>();
        PageResultVO<OtherActivityTemplateDTO> pageVO = new PageResultVO<>();
        try {
            List<OtherRankingActivityData> rankingActivities = otherRankingActivityDao
                    .selectPage(condition.getAcNameEn(), (condition.getPage() - 1) * 10, 10);
            List<OtherActivityTemplateDTO> dtoList = new ArrayList<>();
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
            rankingActivities.forEach(a -> {
                OtherActivityTemplateDTO dto = new OtherActivityTemplateDTO();
                dto.setActivityId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);

                List<RankingActivity.ActivityGift> activityGiftInfoList = new ArrayList<>();

                for (Integer giftId : a.getActivityGiftList()) {
                    if (giftId == -1) {
                        RankingActivity.ActivityGift activityGift = new RankingActivity.ActivityGift();
                        activityGift.setGiftId(-1);
                        activityGift.setGiftName("");
                        activityGift.setGiftNameAr("");
                        activityGift.setGiftPrice(0);
                        activityGift.setGiftPictureUrl("");
                        break;
                    }
                    GiftData giftData = giftDao.getGiftFromDb(giftId);
                    RankingActivity.ActivityGift activityGift = new RankingActivity.ActivityGift();
                    activityGift.setGiftId(giftData.getRid());
                    activityGift.setGiftName(giftData.getGname());
                    activityGift.setGiftNameAr(giftData.getGnamear());
                    activityGift.setGiftPrice(giftData.getPrice());
                    activityGift.setGiftPictureUrl(giftData.getGicon());
                    activityGiftInfoList.add(activityGift);
                }
                dto.setActivityGiftInfoList(activityGiftInfoList);

                dto.setUid(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
                dto.setToken(token);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(otherRankingActivityDao.selectCount());
        } catch (Exception e) {
            logger.info("e", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }


    /**
     * 删除活动模板
     */
    @RequireRole(3)
    @RequestMapping("/delete_template")
    public HttpResult deleteTemplate(HttpServletRequest request, @RequestBody OtherActivityTemplateDTO dto) {
        HttpResult result = new HttpResult();
        try {
            String uid = request.getParameter("uid");
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (Objects.isNull(dto.getActivityId())) {
                logger.error("The activityId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            logger.info("delete template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));
            OtherRankingActivityData templateToUpdate = otherRankingActivityDao.findData(dto.getActivityId());
            if (Objects.isNull(templateToUpdate)) {
                logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
                return result.error("指定的活动已不存在，请刷新页面");
            }
            if (templateToUpdate.getStatus() == 1) {
                return result.error("活动已结束，无法删除");
            }
            int now = DateHelper.getNowSeconds();
            if (templateToUpdate.getStartTime() < now && templateToUpdate.getEndTime() > now) {
                return result.error("活动已开始，无法删除");
            }
            otherRankingActivityDao.removeFromDb(dto.getActivityId());
        } catch (Exception e) {
            logger.error("update template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 活动模板详情
     */
    @RequireRole(3)
    @RequestMapping("/select_one")
    public String selectOne(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<OtherActivityTemplateDTO> result = new HttpResult<>();
        if (Objects.isNull(condition.getActivityId())) {
            logger.error("The activityId cannot be empty.");
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        OtherRankingActivityData data = otherRankingActivityDao.findData(condition.getActivityId());
        OtherActivityTemplateDTO dto = new OtherActivityTemplateDTO();
        dto.setActivityId(data.get_id().toString());
        BeanUtils.copyProperties(data, dto);
        return JSON.toJSONString(result.ok(dto), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 图片上传
     * @see RankingActivityTemplateController#uploadPicture(org.springframework.web.multipart.MultipartFile)
     */
//    @RequireRole(3)
//    @RequestMapping("/upload")
//    public String uploadPicture(MultipartFile file) {
//        return AWSUploadUtils.upload(file);
//    }

//    @RequireRole(2)
//    @RequestMapping("/uploadOSS")
//    public String uploadOSSPicture(MultipartFile file) {
//        return OSSUploadUtils.upload(file);
//    }


//    @RequireRole(2)
//    @RequestMapping("/downloadOSS")
//    public void downloadOSS(HttpServletResponse response, @RequestParam String bucketName, @RequestParam String path) throws IOException {
//        logger.info("downloadOSS bucketName={}, path={}", bucketName, path);
//        if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(path)) {
//            return;
//        }
//        OSSUploadUtils.download(response, bucketName, path);
//    }
}
