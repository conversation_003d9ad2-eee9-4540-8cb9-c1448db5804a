package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.ApiCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.RankingActivityDao;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.monitor.MonitorSender;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.common.HttpResult;
import com.quhong.operation.share.condition.ActivityTemplateCondition;
import com.quhong.operation.share.dto.ActivityTemplateDTO;
import com.quhong.operation.share.dto.RankActivityDTO;
import com.quhong.operation.share.dto.RankingActivityDto;
import com.quhong.operation.share.vo.PageResultVO;
import com.quhong.operation.utils.AWSUploadUtils;
import com.quhong.operation.utils.OSSUploadUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping("/activity_template")
public class RankingActivityTemplateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RankingActivityTemplateController.class);
    private static final Set<String> SUPPORT_SET = new HashSet<>(Arrays.asList("gift", "mic", "buddle", "ride", "ripple", "diamond", "badge", "float_screen", "heart", "honor_title", "entry_effect", "background", "other"));
    // wenmiaofang、yufengxia、guogs、chenluyao、yicui、 运营-陈顺婵、测试-伍幸运、运营-黄心怡
    private static final Set<String> ADMIN_SET = new HashSet<>(Arrays.asList("5dba92651e23cd00c80b32bc", "62b9780a1fb34e1c3520cb90",
            "62d0dd77c9ada8168e6f77bd", "667a3cccd530ac0dd025e113", "6131ec0335eefc68288895bb", "67da97db2acea1ef176d13bc"
            , "67bc665b7771058b47bdfee5", "68269e05aa4b7415d63174a2"));
    @Value("${online:true}")
    private boolean online;
    @Resource
    private RankingActivityDao rankingActivityDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;

    @Resource
    private MonitorSender monitorSender;

    /**
     * 保存活动模板
     */
    @RequireRole(3)
    @RequestMapping("/save_template")
    public HttpResult saveTemplate(HttpServletRequest request, @RequestBody RankActivityDTO dto) {
        HttpResult result = new HttpResult();
        try {
            logger.info("saveTemplate data={}", JSON.toJSONString(dto));
            RankingActivity template = new RankingActivity();
            BeanUtils.copyProperties(dto, template);
            String uid = request.getParameter("uid");
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (null == template.getActivityGiftList()
                    || null == template.getConfig() || null == template.getStartTime()
                    || null == template.getEndTime() || null == template.getAcNameAr()
                    || null == template.getAcNameEn()
                    || (null == template.getRankingConfigList() && template.getConfig().getNoRankReward() != 1)) {
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            for (RankingActivity.ActivityGift activityGift : template.getActivityGiftList()) {
                if (null == activityGift.getGiftId()) {
                    return result.error("活动礼物不能为空");
                }
            }
            if (template.getConfig().getNoRankReward() != 1) {
                for (RankingActivity.RankingConfig rankingConfig : template.getRankingConfigList()) {
                    for (RankingActivity.RankingRewardConfig rankingRewardConfig : rankingConfig.getRankingRewardConfigList()) {
                        if (!StringUtils.isEmpty(rankingRewardConfig.getRewardResourceKey())) {
                            continue;
                        }

                        for (RankingActivity.RewardConfigDetail reward : rankingRewardConfig.getRewardConfigDetailList()) {
                            if (!SUPPORT_SET.contains(reward.getRewardType())) {
                                return result.error("不支持的礼物资源");
                            }
                            if (ResourceConstant.OTHER.equals(reward.getRewardType())) {
                                continue;
                            }
                            if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
                                if (reward.getRewardNum() <= 0) {
                                    return result.error("钻石数量不能为空");
                                }
                            } else if (ResourceConstant.HEART.equals(reward.getRewardType())) {
                                if (reward.getRewardNum() <= 0) {
                                    return result.error("金币数量不能为空");
                                }
                            } else {
                                if (reward.getSourceId() <= 0) {
                                    return result.error("资源id不能为空");
                                }
                                if (!ResourceConstant.BADGE.equals(reward.getRewardType()) && reward.getRewardTime() <= 0) {
                                    return result.error("资源时长不能为空");
                                }
                            }
                        }
                    }
                }
            }
            if (template.getConfig().getHasGradeReward() == 1) {
                for (RankingActivity.ReachingConfig reachingConfig : template.getReachingConfigList()) {
                    if (null == reachingConfig.getReachingRewardType()) {
                        return result.error("等级奖励属性不能为空");
                    }
                    if (null != reachingConfig.getReachingRewardList()) {
                        for (RankingActivity.ReachingReward reachingReward : reachingConfig.getReachingRewardList()) {
                            if (null == reachingReward.getGiftNum()) {
                                return result.error("礼物数量不能为空");
                            }
                            if (null == reachingReward.getRewardConfigDetailList()) {
                                return result.error("等级奖励不能为空");
                            }
                            for (RankingActivity.RewardConfigDetail reward : reachingReward.getRewardConfigDetailList()) {
                                if (!SUPPORT_SET.contains(reward.getRewardType())) {
                                    return result.error("不支持的礼物资源");
                                }
                                if (ResourceConstant.OTHER.equals(reward.getRewardType())) {
                                    continue;
                                }
                                if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
                                    if (reward.getRewardNum() <= 0) {
                                        return result.error("钻石数量不能为空");
                                    }
                                } else if (ResourceConstant.HEART.equals(reward.getRewardType())) {
                                    if (reward.getRewardNum() <= 0) {
                                        return result.error("金币数量不能为空");
                                    }
                                } else {
                                    if (reward.getSourceId() <= 0) {
                                        return result.error("资源id不能为空");
                                    }
                                    if (!ResourceConstant.BADGE.equals(reward.getRewardType()) && reward.getRewardTime() <= 0) {
                                        return result.error("资源时长不能为空");
                                    }
                                }
                            }
                        }
                        reachingConfig.getReachingRewardList().sort(Comparator.comparing(RankingActivity.ReachingReward::getGiftNum));
                    }
                }
            }
            if (template.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }

            if (template.getTemplateType() == 0) {
                template.setAcUrl(ServerConfig.isProduct() ? "https://static.youstar.live/ranking/" : "https://test2.qmovies.tv/ranking/");
            }
            if (template.getTemplateType() == 1) {
                if (StringUtils.isEmpty(template.getAcUrl()) ) {
                    return result.error("活动链接配置有误");
                }
                String acUrl = template.getAcUrl().trim();
                if (StringUtils.isEmpty(acUrl) || !acUrl.contains("https://")) {
                    return result.error("活动链接配置有误");
                }
                template.setAcUrl(acUrl);
            }

            rankingActivityDao.save(template);

            if (dto.getIsCopyToGrayTest() == 1) {
                if (!template.getAcNameEn().startsWith("test")) {
                    String acNameEn = String.format("test_%s_%s", template.get_id().getCounter(), template.getAcNameEn());
                    rankingActivityDao.updateData(template, new Update().set("acNameEn", acNameEn));
                }
            }
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "正式服-用户成功创建冲榜活动", "活动名：" + template.getAcNameEn());
            }

        } catch (Exception e) {
            logger.error("save template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 更新活动模板
     */
    @RequireRole(3)
    @RequestMapping("/update_template")
    public HttpResult updateTemplate(HttpServletRequest request, @RequestBody ActivityTemplateDTO dto) {
        HttpResult result = new HttpResult();
        try {
            String uid = request.getParameter("uid");
            if (online && (!ADMIN_SET.contains(uid))) {
                logger.error("save_template no right to operate uid={}", uid);
                return result.error("您无操作权限，如有需求请联系技术人员!");
            }
            if (Objects.isNull(dto.getActivityId())) {
                logger.error("The activityId cannot be empty.");
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            if (null == dto.getActivityGiftList()
                    || null == dto.getConfig() || null == dto.getStartTime()
                    || null == dto.getEndTime() || null == dto.getAcNameAr()
                    || null == dto.getAcNameEn()
                    || (null == dto.getRankingConfigList() && dto.getConfig().getNoRankReward() != 1)) {
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            for (RankingActivity.ActivityGift activityGift : dto.getActivityGiftList()) {
                if (null == activityGift.getGiftId()) {
                    return result.error("活动礼物不能为空");
                }
            }
            if (dto.getConfig().getNoRankReward() != 1) {
                for (RankingActivity.RankingConfig rankingConfig : dto.getRankingConfigList()) {
                    for (RankingActivity.RankingRewardConfig rankingRewardConfig : rankingConfig.getRankingRewardConfigList()) {
                        if (!StringUtils.isEmpty(rankingRewardConfig.getRewardResourceKey())) {
                            continue;
                        }

                        for (RankingActivity.RewardConfigDetail reward : rankingRewardConfig.getRewardConfigDetailList()) {
                            if (!SUPPORT_SET.contains(reward.getRewardType())) {
                                return result.error("不支持的礼物资源");
                            }
                            if (ResourceConstant.OTHER.equals(reward.getRewardType())) {
                                continue;
                            }
                            if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
                                if (reward.getRewardNum() <= 0) {
                                    return result.error("钻石数量不能为空");
                                }
                            } else if (ResourceConstant.HEART.equals(reward.getRewardType())) {
                                if (reward.getRewardNum() <= 0) {
                                    return result.error("金币数量不能为空");
                                }
                            } else {
                                if (reward.getSourceId() <= 0) {
                                    return result.error("资源id不能为空");
                                }
                                if (!ResourceConstant.BADGE.equals(reward.getRewardType()) && reward.getRewardTime() <= 0) {
                                    return result.error("资源时长不能为空");
                                }
                            }
                        }
                    }
                }
            }
            if (dto.getConfig().getHasGradeReward() == 1) {
                for (RankingActivity.ReachingConfig reachingConfig : dto.getReachingConfigList()) {
                    if (null == reachingConfig.getReachingRewardType()) {
                        return result.error("等级奖励属性不能为空");
                    }
                    if (null != reachingConfig.getReachingRewardList()) {
                        for (RankingActivity.ReachingReward reachingReward : reachingConfig.getReachingRewardList()) {
                            if (null == reachingReward.getGiftNum()) {
                                return result.error("礼物数量不能为空");
                            }
                            if (null == reachingReward.getRewardConfigDetailList()) {
                                return result.error("等级奖励不能为空");
                            }
                            for (RankingActivity.RewardConfigDetail reward : reachingReward.getRewardConfigDetailList()) {
                                if (!SUPPORT_SET.contains(reward.getRewardType())) {
                                    return result.error("不支持的礼物资源");
                                }
                                if (ResourceConstant.OTHER.equals(reward.getRewardType())) {
                                    continue;
                                }
                                if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
                                    if (reward.getRewardNum() <= 0) {
                                        return result.error("钻石数量不能为空");
                                    }
                                } else if (ResourceConstant.HEART.equals(reward.getRewardType())) {
                                    if (reward.getRewardNum() <= 0) {
                                        return result.error("金币数量不能为空");
                                    }
                                } else {
                                    if (reward.getSourceId() <= 0) {
                                        return result.error("资源id不能为空");
                                    }
                                    if (!ResourceConstant.BADGE.equals(reward.getRewardType()) && reward.getRewardTime() <= 0) {
                                        return result.error("资源时长不能为空");
                                    }
                                }
                            }
                        }
                        reachingConfig.getReachingRewardList().sort(Comparator.comparing(RankingActivity.ReachingReward::getGiftNum));
                    }
                }
            }
            logger.info("update template activityId={} data={}", dto.getActivityId(), JSON.toJSONString(dto));
            RankingActivity templateToUpdate = rankingActivityDao.findData(dto.getActivityId());
            if (Objects.isNull(templateToUpdate)) {
                logger.error("templateToUpdate is empty. id={}", dto.getActivityId());
                return result.error(ApiCode.PARAM_ERROR.getMsg());
            }
            if (templateToUpdate.getStatus() == 1 && ServerConfig.isProduct()) {
                return result.error("活动已结束，无法更新");
            }
            if (dto.getEndTime() - DateHelper.getNowSeconds() <= 10 * 60) {
                return result.error("活动结束时间距离现在太近，请检查");
            }
            if (templateToUpdate.getTemplateType() == 1) {
                if (StringUtils.isEmpty(dto.getAcUrl())) {
                    return result.error("活动链接配置有误");
                }
                String acUrl = dto.getAcUrl().trim();
                if (StringUtils.isEmpty(acUrl) || !acUrl.contains("https://")|| !acUrl.contains("activityId=")) {
                    return result.error("活动链接配置有误");
                }
                dto.setAcUrl(acUrl);
            }

            Update update = new Update();
            update.set("acNameEn", dto.getAcNameEn());
            update.set("acNameAr", dto.getAcNameAr());
            update.set("startTime", dto.getStartTime());
            update.set("endTime", dto.getEndTime());
            update.set("config", dto.getConfig());
            update.set("conquerConfig", dto.getConquerConfig());
            update.set("activityGiftList", dto.getActivityGiftList());
            update.set("rankingConfigList", dto.getRankingConfigList());
            update.set("reachingConfigList", dto.getReachingConfigList());
            update.set("mtime", DateHelper.getNowSeconds());
            if (dto.getTemplateType() == 1) {
                update.set("acUrl", dto.getAcUrl());
            }
            rankingActivityDao.updateData(templateToUpdate, update);
        } catch (Exception e) {
            logger.error("update template error. {}", e.getMessage(), e);
            return result.error();
        }
        return result.ok();
    }

    /**
     * 活动模板分页查询
     */
    @RequireRole(3)
    @RequestMapping("/page")
    public String selectPage(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<PageResultVO<ActivityTemplateDTO>> result = new HttpResult<>();
        PageResultVO<ActivityTemplateDTO> pageVO = new PageResultVO<>();
        try {
            List<RankingActivity> rankingActivities = rankingActivityDao
                    .selectPage(condition.getAcNameEn(), (condition.getPage() - 1) * 10, 10, condition.getTemplateType());
            List<ActivityTemplateDTO> dtoList = new ArrayList<>();
            String token = basePlayerRedis.getToken(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
            rankingActivities.forEach(a -> {
                ActivityTemplateDTO dto = new ActivityTemplateDTO();
                dto.setActivityId(a.get_id().toString());
                BeanUtils.copyProperties(a, dto);
                dto.setUid(ServerConfig.isProduct() ? "5cdf784961d047a4adf44064" : "6006a874644f8e00374fca1d");
                dto.setToken(token);
                dtoList.add(dto);
            });
            pageVO.setList(dtoList);
            pageVO.setTotal(rankingActivityDao.selectCount(condition.getTemplateType()));
        } catch (Exception e) {
            logger.info("e", e);
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        return JSON.toJSONString(result.ok(pageVO), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 活动模板详情
     */
    @RequireRole(3)
    @RequestMapping("/select_one")
    public String selectOne(@RequestBody ActivityTemplateCondition condition) {
        HttpResult<ActivityTemplateDTO> result = new HttpResult<>();
        if (Objects.isNull(condition.getActivityId())) {
            logger.error("The activityId cannot be empty.");
            return JSON.toJSONString(result.error(HttpCode.SERVER_ERROR.getMsg()));
        }
        RankingActivity data = rankingActivityDao.findData(condition.getActivityId());
        ActivityTemplateDTO dto = new ActivityTemplateDTO();
        dto.setActivityId(data.get_id().toString());
        BeanUtils.copyProperties(data, dto);
        return JSON.toJSONString(result.ok(dto), SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 图片上传
     */
    @RequireRole(3)
    @RequestMapping("/upload")
    public String uploadPicture(MultipartFile file) {
        return AWSUploadUtils.upload(file);
    }

    @RequireRole(2)
    @RequestMapping("/uploadOSS")
    public String uploadOSSPicture(MultipartFile file) {
        return OSSUploadUtils.upload(file);
    }

    @RequireRole(2)
    @RequestMapping("/downloadOSS")
    public void downloadOSS(HttpServletResponse response, @RequestParam String bucketName, @RequestParam String path) throws IOException {
        logger.info("downloadOSS bucketName={}, path={}", bucketName, path);
        if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(path)) {
            return;
        }
        OSSUploadUtils.download(response, bucketName, path);
    }
}
