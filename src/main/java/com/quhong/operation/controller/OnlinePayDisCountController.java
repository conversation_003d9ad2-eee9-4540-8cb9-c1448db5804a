package com.quhong.operation.controller;

import com.alibaba.fastjson.JSON;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.BaseController;
import com.quhong.operation.annotation.RequireRole;
import com.quhong.operation.server.OnlinePayDisCountService;
import com.quhong.operation.share.vo.OnlineDisCountVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 线上折扣配置
 */

@RestController
@RequestMapping(value ="/onlinePayDisCount", produces = MediaType.APPLICATION_JSON_VALUE)
public class OnlinePayDisCountController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(OnlinePayDisCountController.class);

    @Resource
    private OnlinePayDisCountService onlinePayDisCountService;


    @RequireRole
    @RequestMapping("/config")
    public String getDataList() {
        return createResult(HttpCode.SUCCESS, onlinePayDisCountService.getConfigData());

    }

    @RequireRole
    @PostMapping("/updateData")
    public String updateData(@RequestParam String uid, @RequestBody OnlineDisCountVO dto) {
        try {
            logger.info("updateOnlinePayDisCount {}", JSON.toJSONString(dto));
            onlinePayDisCountService.updateData(uid, dto);
            return createResult(HttpCode.SUCCESS, "");
        }catch (CommonException e){
            return createResult(HttpCode.PARAM_ERROR, e.getHttpCode().getMsg());
        } catch (Exception e){
            throw e;
        }
    }
}
