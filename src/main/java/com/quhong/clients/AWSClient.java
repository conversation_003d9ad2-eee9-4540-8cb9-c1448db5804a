package com.quhong.clients;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class AWSClient {
    private static final Logger logger = LoggerFactory.getLogger(AWSClient.class);

    static {
        System.setProperty("aws.accessKeyId", "********************");
        System.setProperty("aws.secretKey", "LFhEf1fBkTGHsGKqTpmS0gswEKIsJk94skS0jn7W");
    }

    private AmazonS3 amazonS3;
    private String region = "ap-southeast-1";
    private String bucketName = "qhcf";

    private AWSUploader uploader;

    public AWSClient() {

    }

    @PostConstruct
    public void postInit() {
        try {
            amazonS3 = AmazonS3ClientBuilder.standard().withRegion(Regions.fromName(region)).build();
        } catch (Exception e) {
            logger.error("region:{} {}", region, e.getMessage(), e);
            throw e;
        }
    }

    public AmazonS3 getAmazonS3() {
        return amazonS3;
    }

    public String getRegion() {
        return region;
    }

    public String getBucketName() {
        return bucketName;
    }

    public AWSUploader getUploader() {
        return uploader;
    }
}
