package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.VoteOptionData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
public interface VoteOptionMapper extends BaseMapper<VoteOptionData> {

    @Insert("<script>" +
            "INSERT INTO t_vote_option (vote_id, option_order, option_content, votes_num) VALUES" +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.voteId}, #{item.optionOrder}, #{item.optionContent}, #{item.votesNum})" +
            "</foreach>"+
            "</script>")
    void batchInsert(List<VoteOptionData> list);

    @Update("<script>" +
            "UPDATE t_vote_option set votes_num = votes_num + 1 WHERE vote_id = #{voteId} AND id in (" +
            "<foreach collection='optionList' item='id' separator=','> #{id} </foreach> )" +
            "</script>")
    void incOptionVotesNum(@Param("voteId") int voteId, @Param("optionList") List<Integer> optionList);
}
