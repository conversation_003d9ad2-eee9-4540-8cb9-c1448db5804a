package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_national_day_v2_record")
public class NationalDayV2RecordData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String activityId;
    private String uid;
    private String rewardType;
    private String rewardIcon; // 奖励资源介绍图片
    private String rewardNameEn; // 奖励名称
    private String rewardNameAr; // 奖励名称阿语
    private int sourceId;
    private int rewardNum;
    private int rewardTime;

    private Integer ctime;    // 排序用

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public String getRewardIcon() {
        return rewardIcon;
    }

    public void setRewardIcon(String rewardIcon) {
        this.rewardIcon = rewardIcon;
    }

    public String getRewardNameEn() {
        return rewardNameEn;
    }

    public void setRewardNameEn(String rewardNameEn) {
        this.rewardNameEn = rewardNameEn;
    }

    public String getRewardNameAr() {
        return rewardNameAr;
    }

    public void setRewardNameAr(String rewardNameAr) {
        this.rewardNameAr = rewardNameAr;
    }

    public int getSourceId() {
        return sourceId;
    }

    public void setSourceId(int sourceId) {
        this.sourceId = sourceId;
    }

    public int getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(int rewardNum) {
        this.rewardNum = rewardNum;
    }

    public int getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(int rewardTime) {
        this.rewardTime = rewardTime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
