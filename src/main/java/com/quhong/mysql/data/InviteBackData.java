package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


@TableName("t_invite_back")
public class InviteBackData {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邀请者
     */
    private String uid;

    /**
     * 被邀请者，键唯一
     */
    private String aid;

    /**
     * 设备id，键唯一
     */
    private String deviceId;

    /**
     * 总奖励邀请者钻石
     */
    private Integer totalReward;

    /**
     * 回归奖励邀请者钻石(被邀请者填写code)
     */
    private Integer backReward;

    /**
     * 玩克罗姆游戏奖励邀请者钻石(和被邀方玩1局克罗姆)
     */
    private Integer playGameReward;

    /**
     * 被邀请方回归时间
     */
    private Integer backTime;

    /**
     * 创建时间（绑定code）
     */
    private Integer ctime;

    /**
     * 修改时间
     */
    private Integer mtime;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getTotalReward() {
        return totalReward;
    }

    public void setTotalReward(Integer totalReward) {
        this.totalReward = totalReward;
    }

    public Integer getPlayGameReward() {
        return playGameReward;
    }

    public void setPlayGameReward(Integer playGameReward) {
        this.playGameReward = playGameReward;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getBackReward() {
        return backReward;
    }

    public void setBackReward(Integer backReward) {
        this.backReward = backReward;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getBackTime() {
        return backTime;
    }

    public void setBackTime(Integer backTime) {
        this.backTime = backTime;
    }
}
