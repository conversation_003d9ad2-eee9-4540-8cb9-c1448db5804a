package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_activity_ramadan")
public class RamadanEndData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String foodKey;
    private String foodNameEn;
    private String foodNameAr;
    private String foodDescEn;
    private String foodDescAr;
    private String foodIcon;
    private String foodIconBack;
    private String rateNum;
    private Integer showStatus;
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFoodKey() {
        return foodKey;
    }

    public void setFoodKey(String foodKey) {
        this.foodKey = foodKey;
    }

    public String getFoodNameEn() {
        return foodNameEn;
    }

    public void setFoodNameEn(String foodNameEn) {
        this.foodNameEn = foodNameEn;
    }

    public String getFoodNameAr() {
        return foodNameAr;
    }

    public void setFoodNameAr(String foodNameAr) {
        this.foodNameAr = foodNameAr;
    }

    public String getFoodDescEn() {
        return foodDescEn;
    }

    public void setFoodDescEn(String foodDescEn) {
        this.foodDescEn = foodDescEn;
    }

    public String getFoodDescAr() {
        return foodDescAr;
    }

    public void setFoodDescAr(String foodDescAr) {
        this.foodDescAr = foodDescAr;
    }

    public String getFoodIcon() {
        return foodIcon;
    }

    public void setFoodIcon(String foodIcon) {
        this.foodIcon = foodIcon;
    }

    public String getFoodIconBack() {
        return foodIconBack;
    }

    public void setFoodIconBack(String foodIconBack) {
        this.foodIconBack = foodIconBack;
    }

    public String getRateNum() {
        return rateNum;
    }

    public void setRateNum(String rateNum) {
        this.rateNum = rateNum;
    }

    public Integer getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Integer showStatus) {
        this.showStatus = showStatus;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
