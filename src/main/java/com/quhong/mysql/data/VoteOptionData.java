package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
@TableName("t_vote_option")
public class VoteOptionData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 投票id
     */
    private Integer voteId;

    /**
     * 选项顺序
     */
    private Integer optionOrder;

    /**
     * 选项内容
     */
    private String optionContent;

    /**
     * 票数
     */
    private Integer votesNum;

    public VoteOptionData() {
    }

    public VoteOptionData(Integer voteId, Integer optionOrder, String optionContent, Integer votesNum) {
        this.voteId = voteId;
        this.optionOrder = optionOrder;
        this.optionContent = optionContent;
        this.votesNum = votesNum;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getVoteId() {
        return voteId;
    }

    public void setVoteId(Integer voteId) {
        this.voteId = voteId;
    }

    public Integer getOptionOrder() {
        return optionOrder;
    }

    public void setOptionOrder(Integer optionOrder) {
        this.optionOrder = optionOrder;
    }

    public String getOptionContent() {
        return optionContent;
    }

    public void setOptionContent(String optionContent) {
        this.optionContent = optionContent;
    }

    public Integer getVotesNum() {
        return votesNum;
    }

    public void setVotesNum(Integer votesNum) {
        this.votesNum = votesNum;
    }
}
