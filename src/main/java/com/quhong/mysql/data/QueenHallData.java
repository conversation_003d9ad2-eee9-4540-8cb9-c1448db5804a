package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_queen_hall")
public class QueenHallData {
    // 女王名人堂

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;
    private String titleEn;  // 称号英语
    private String titleAr;
    private String declareEn;  // 宣言英语
    private String declareAr;
    private String radioUrl;
    private Integer orderSort;    // 排序用

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getTitleEn() {
        return titleEn;
    }

    public void setTitleEn(String titleEn) {
        this.titleEn = titleEn;
    }

    public String getTitleAr() {
        return titleAr;
    }

    public void setTitleAr(String titleAr) {
        this.titleAr = titleAr;
    }

    public String getDeclareEn() {
        return declareEn;
    }

    public void setDeclareEn(String declareEn) {
        this.declareEn = declareEn;
    }

    public String getDeclareAr() {
        return declareAr;
    }

    public void setDeclareAr(String declareAr) {
        this.declareAr = declareAr;
    }

    public String getRadioUrl() {
        return radioUrl;
    }

    public void setRadioUrl(String radioUrl) {
        this.radioUrl = radioUrl;
    }

    public Integer getOrderSort() {
        return orderSort;
    }

    public void setOrderSort(Integer orderSort) {
        this.orderSort = orderSort;
    }
}
