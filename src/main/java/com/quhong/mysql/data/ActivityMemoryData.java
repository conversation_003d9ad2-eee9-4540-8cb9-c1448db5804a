package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_activity_memory")
public class ActivityMemoryData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private int memoryType;
    private String fromUid;
    private String toUid;
    private long roomOnline;
    private int onlineHour;
    private long costDiamond;
    private int ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public int getMemoryType() {
        return memoryType;
    }

    public void setMemoryType(int memoryType) {
        this.memoryType = memoryType;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getToUid() {
        return toUid;
    }

    public void setToUid(String toUid) {
        this.toUid = toUid;
    }

    public long getRoomOnline() {
        return roomOnline;
    }

    public void setRoomOnline(long roomOnline) {
        this.roomOnline = roomOnline;
    }

    public int getOnlineHour() {
        return onlineHour;
    }

    public void setOnlineHour(int onlineHour) {
        this.onlineHour = onlineHour;
    }

    public long getCostDiamond() {
        return costDiamond;
    }

    public void setCostDiamond(long costDiamond) {
        this.costDiamond = costDiamond;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
