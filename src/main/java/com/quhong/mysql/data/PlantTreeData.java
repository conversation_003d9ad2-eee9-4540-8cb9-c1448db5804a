package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_activity_plant_tree")
public class PlantTreeData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;
    private int waterNum;
    private int waterProcess;
    private int treeStatus;
    private int signPush;
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getWaterNum() {
        return waterNum;
    }

    public void setWaterNum(int waterNum) {
        this.waterNum = waterNum;
    }

    public int getWaterProcess() {
        return waterProcess;
    }

    public void setWaterProcess(int waterProcess) {
        this.waterProcess = waterProcess;
    }

    public int getTreeStatus() {
        return treeStatus;
    }

    public void setTreeStatus(int treeStatus) {
        this.treeStatus = treeStatus;
    }

    public int getSignPush() {
        return signPush;
    }

    public void setSignPush(int signPush) {
        this.signPush = signPush;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
