package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 投票表
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
@TableName("t_vote")
public class VoteData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 投票标题
     */
    private String title;

    /**
     * 投票类型 0礼物投票 1问答投票
     */
    private Integer type;

    /**
     * 选项类型 0单选 1多选
     */
    private Integer optionType;

    /**
     * 礼物id
     */
    private Integer giftId;

    /**
     * 投票持续时间 单位：分钟
     */
    private Integer duration;

    /**
     * 投票结束时间
     */
    private Integer endTime;

    /**
     * 状态 0进行中 1自动结束 2手动结束
     */
    private Integer status;

    /**
     * 投票费用类型 0金币 1钻石
     */
    private Integer feeType;

    /**
     * 投票费用
     */
    private Integer costBeans;

    /**
     * 投票用户数
     */
    private Integer votingUserNum;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getOptionType() {
        return optionType;
    }

    public void setOptionType(Integer optionType) {
        this.optionType = optionType;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public Integer getCostBeans() {
        return costBeans;
    }

    public void setCostBeans(Integer costBeans) {
        this.costBeans = costBeans;
    }

    public Integer getVotingUserNum() {
        return votingUserNum;
    }

    public void setVotingUserNum(Integer votingUserNum) {
        this.votingUserNum = votingUserNum;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFeeType() {
        return feeType;
    }

    public void setFeeType(Integer feeType) {
        this.feeType = feeType;
    }
}
