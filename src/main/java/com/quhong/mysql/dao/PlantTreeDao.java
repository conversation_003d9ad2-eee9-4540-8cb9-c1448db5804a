package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.PlantTreeData;
import com.quhong.mysql.mapper.ustar.PlantTreeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class PlantTreeDao {

    private static final Logger logger = LoggerFactory.getLogger(PlantTreeDao.class);
    @Resource
    private PlantTreeMapper plantTreeMapper;



    public PlantTreeData selectOne(String uid) {
        QueryWrapper<PlantTreeData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        return plantTreeMapper.selectOne(queryWrapper);
    }

    public List<PlantTreeData> selectSignPushList() {
        QueryWrapper<PlantTreeData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sign_push", 1);
        return plantTreeMapper.selectList(queryWrapper);
    }


    public void updateOne(PlantTreeData treeData){
        plantTreeMapper.updateById(treeData);
    }


    public int insert(PlantTreeData treeData){
        return plantTreeMapper.insert(treeData);
    }

}
