package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.ShootRecordData;
import com.quhong.mysql.mapper.ustar_log.ShootRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class ShootRecordDao {

    private static final Logger logger = LoggerFactory.getLogger(ShootRecordDao.class);
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private final CacheMap<String, List<ShootRecordData>> cacheMap;
    @Resource
    private ShootRecordMapper shootRecordMapper;


    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public ShootRecordDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }


    public List<ShootRecordData> selectList(String activityId, String uid, int page, int pageSize) {
        QueryWrapper<ShootRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.eq("uid", uid).or().eq("friend_id", uid);
        queryWrapper.orderByDesc("ctime");
        IPage<ShootRecordData> recordPage = new Page<>(page == 0 ? 1 : page, pageSize);
        recordPage = shootRecordMapper.selectPage(recordPage, queryWrapper);
        return recordPage.getRecords();
    }


    public int getRedStatus(String activityId, String uid) {
        QueryWrapper<ShootRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.eq("friend_id", uid);
        queryWrapper.eq("card_type", "guard");
        int totalCount = shootRecordMapper.selectCount(queryWrapper);
        return totalCount > 0 ? 1 : 0;
    }


    public int insertOne(ShootRecordData data) {
        return shootRecordMapper.insert(data);
    }

    public ShootRecordData selectOne(int id) {
        return shootRecordMapper.selectById(id);
    }

    public int updateOne(ShootRecordData shootRecordData) {
        return shootRecordMapper.updateById(shootRecordData);
    }

}
