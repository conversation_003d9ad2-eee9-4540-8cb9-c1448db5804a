package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.ShootDrawData;
import com.quhong.mysql.data.TreasureData;
import com.quhong.mysql.data.VoteData;
import com.quhong.mysql.mapper.ustar.TreasureMapper;
import com.quhong.mysql.mapper.ustar.VoteMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * @date 2023/3/9
 */
@Component
public class TreasureDao {
    private static final Logger logger = LoggerFactory.getLogger(TreasureDao.class);

    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private static final String TREASURE_LIST_KEY = "treasure_list_key";
    private static final String TREASURE_ONE_KEY = "treasure_one_key";
    private final CacheMap<String, List<TreasureData>> cacheListMap;
    private final CacheMap<String, TreasureData> cacheMap;

    @PostConstruct
    public void postInit() {
        cacheListMap.start();
        cacheMap.start();
    }

    public TreasureDao(){
        this.cacheListMap = new CacheMap<>(CACHE_TIME_MILLIS);
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @Resource
    private TreasureMapper treasureMapper;


    public List<TreasureData> selectList() {
        QueryWrapper<TreasureData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("service_type", 0);

        return treasureMapper.selectList(queryWrapper);
    }
    public List<TreasureData> getTreasureDataFromCache() {
        List<TreasureData> data = cacheListMap.getData(TREASURE_LIST_KEY);
        if (!CollectionUtils.isEmpty(data)) {
            return data;
        }
        data = selectList();
        cacheListMap.cacheData(TREASURE_LIST_KEY, data);
        return data;
    }
}
