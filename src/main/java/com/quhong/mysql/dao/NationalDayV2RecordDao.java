package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.NationalDayV2RecordData;
import com.quhong.mysql.mapper.ustar_log.NationalDayV2RecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class NationalDayV2RecordDao {

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV2RecordDao.class);
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private final CacheMap<String, List<NationalDayV2RecordData>> cacheMapList;
    @Resource
    private NationalDayV2RecordMapper nationalDayV2RecordMapper;


    @PostConstruct
    public void postInit() {
        cacheMapList.start();
    }

    public NationalDayV2RecordDao() {
        this.cacheMapList = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    private String getNationalDayListKey() {
        return "nationalDayList";
    }


    public List<NationalDayV2RecordData> scrollList(String activityId) {
        List<NationalDayV2RecordData> recordList = cacheMapList.getData(getNationalDayListKey());
        if (!CollectionUtils.isEmpty(recordList)) {
            return recordList;
        }

        QueryWrapper<NationalDayV2RecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.orderByDesc("ctime");
        IPage<NationalDayV2RecordData> recordPage = new Page<>(0, 10);
        recordList = nationalDayV2RecordMapper.selectPage(recordPage, queryWrapper).getRecords();

        cacheMapList.cacheData(getNationalDayListKey(), recordList);
        return recordList;

    }


    public NationalDayV2RecordData selectRecordOne(String uid, String activityId) {
        QueryWrapper<NationalDayV2RecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("activity_id", activityId);
        return nationalDayV2RecordMapper.selectOne(queryWrapper);
    }



    public int insertOne(NationalDayV2RecordData data) {
        return nationalDayV2RecordMapper.insert(data);
    }

    public NationalDayV2RecordData selectOne(int id) {
        return nationalDayV2RecordMapper.selectById(id);
    }

    public int updateOne(NationalDayV2RecordData recordData) {
        return nationalDayV2RecordMapper.updateById(recordData);
    }

}
