package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.QueenHallData;
import com.quhong.mysql.data.QueenHallExpressData;
import com.quhong.mysql.data.ShootDrawData;
import com.quhong.mysql.mapper.ustar_log.ShootDrawMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class ShootDrawDao {

    private static final Logger logger = LoggerFactory.getLogger(ShootDrawDao.class);
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private static final String SHOOT_KEY = "shoot_record_key";
    private final CacheMap<String, List<ShootDrawData>> cacheMap;
    @Resource
    private ShootDrawMapper shootDrawMapper;


    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public ShootDrawDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }


    public List<ShootDrawData> selectList() {
        QueryWrapper<ShootDrawData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("ctime");
        IPage<ShootDrawData> recordPage = new Page<>(0, 10);
        return shootDrawMapper.selectPage(recordPage, queryWrapper).getRecords();
    }
    public List<ShootDrawData> getShootDrawDataFromCache() {
        List<ShootDrawData> data = cacheMap.getData(SHOOT_KEY);
        if (!CollectionUtils.isEmpty(data)) {
            return data;
        }

        data = selectList();
        cacheMap.cacheData(SHOOT_KEY, data);
        return data;
    }


    public List<ShootDrawData> selectList(String uid, int page, int pageSize) {
        QueryWrapper<ShootDrawData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        IPage<ShootDrawData> recordPage = new Page<>(page == 0 ? 1 : page, pageSize);
        recordPage = shootDrawMapper.selectPage(recordPage, queryWrapper);
        return recordPage.getRecords();
    }



    public int insertOne(ShootDrawData data) {
        return shootDrawMapper.insert(data);
    }

}
