package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.VoteData;
import com.quhong.mysql.mapper.ustar.VoteMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
@Component
public class VoteDao {

    @Resource
    private VoteMapper voteMapper;

    public void insert(VoteData data) {
        voteMapper.insert(data);
    }

    public VoteData findData(int voteId) {
        return voteMapper.selectById(voteId);
    }

    public VoteData findData(String roomId, int voteId) {
        QueryWrapper<VoteData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("room_id", roomId);
        queryWrapper.eq("id", voteId);
        return voteMapper.selectOne(queryWrapper);
    }

    public void update(VoteData voteData) {
        voteMapper.updateById(voteData);
    }

    public List<VoteData> getVoteRecordPage(int type, String roomId, int timestamp, int page, int pageSize) {
        IPage<VoteData> dataPage = new Page<>(page, pageSize);
        QueryWrapper<VoteData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        queryWrapper.eq("room_id", roomId);
        queryWrapper.lt("end_time", DateHelper.getNowSeconds());
        queryWrapper.gt("ctime", timestamp);
        queryWrapper.orderByDesc("ctime");
        dataPage = voteMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }
}
