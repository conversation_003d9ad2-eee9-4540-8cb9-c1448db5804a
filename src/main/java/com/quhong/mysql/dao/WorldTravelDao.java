package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.WorldTravelData;
import com.quhong.mysql.mapper.ustar.WorldTravelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @date 2023/3/9
 */
@Component
public class WorldTravelDao {
    private static final Logger logger = LoggerFactory.getLogger(WorldTravelDao.class);


    @Resource
    private WorldTravelMapper worldTravelMapper;


    public List<WorldTravelData> selectList() {
        QueryWrapper<WorldTravelData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("world_key");
        return worldTravelMapper.selectList(queryWrapper);
    }
}
