package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.PlantTreeSignData;
import com.quhong.mysql.mapper.ustar.PlantTreeSignMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class PlantTreeSignDao {

    private static final Logger logger = LoggerFactory.getLogger(PlantTreeSignDao.class);
    @Resource
    private PlantTreeSignMapper plantTreeSignMapper;


    public List<PlantTreeSignData> selectList(String uid) {
        QueryWrapper<PlantTreeSignData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        return plantTreeSignMapper.selectList(queryWrapper);
    }

    public PlantTreeSignData selectOne(String uid, String signDate) {
        QueryWrapper<PlantTreeSignData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("sign_date", signDate);
        return plantTreeSignMapper.selectOne(queryWrapper);
    }

    public int insert(PlantTreeSignData signData){
        return plantTreeSignMapper.insert(signData);
    }

}
