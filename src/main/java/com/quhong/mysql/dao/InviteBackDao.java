package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.data.HotSearchListData;
import com.quhong.mysql.data.InviteBackData;
import com.quhong.mysql.mapper.ustar.InviteBackMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Lazy
@Component
public class InviteBackDao {

    private static final Logger logger = LoggerFactory.getLogger(InviteBackDao.class);

    @Resource
    private InviteBackMapper inviteBackMapper;

    public int updateById(InviteBackData data) {
        return inviteBackMapper.updateById(data);
    }

    public int insert(InviteBackData data) {
        return inviteBackMapper.insert(data);
    }

    public InviteBackData getOneByAid(String aid) {
        QueryWrapper<InviteBackData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("aid", aid);
        return inviteBackMapper.selectOne(queryWrapper);
    }

    public List<InviteBackData> getDetailByUid(String uid) {
        QueryWrapper<InviteBackData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        return inviteBackMapper.selectList(queryWrapper);
    }

    public List<InviteBackData> getDetailByUidToday(String uid, Integer startTime) {
        QueryWrapper<InviteBackData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.ge("ctime", startTime);
        queryWrapper.orderByDesc("ctime");
        return inviteBackMapper.selectList(queryWrapper);
    }

    public List<InviteBackData> getDetailByUidPage(String uid, int page, int pageSize) {
        QueryWrapper<InviteBackData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        IPage<InviteBackData> recordPage = new Page<>(page <= 0 ? 1 : page, pageSize);
        recordPage = inviteBackMapper.selectPage(recordPage, queryWrapper);
        return recordPage.getRecords();
    }

    public Integer getCountByUid(String uid, Integer startTime) {
        QueryWrapper<InviteBackData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        if (startTime != null) {
            queryWrapper.ge("ctime", startTime);
        }
        Integer count = inviteBackMapper.selectCount(queryWrapper);
        return count == null ? 0 : count;
    }

    public List<InviteBackData> getRollList() {
        QueryWrapper<InviteBackData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("ctime");
        queryWrapper.last("limit 30");
        return inviteBackMapper.selectList(queryWrapper);
    }


    public List<HotSearchListData> getRankListData(Integer startTime) {
        return inviteBackMapper.getRankListData(startTime);
    }

    public HotSearchListData getMyTotalData(String uid) {
        return inviteBackMapper.getMyTotalData(uid);
    }

    public InviteBackData getInviteByAidOrDev(String aid, String deviceId) {
        QueryWrapper<InviteBackData> queryWrapper = Wrappers.query();
        queryWrapper.eq("aid", aid);
        queryWrapper.or().eq("device_id", deviceId);
        return inviteBackMapper.selectOne(queryWrapper);
    }


    public Integer getCountByUidToday(String uid, Integer startTime) {
        QueryWrapper<InviteBackData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.ge("ctime", startTime);
        queryWrapper.gt("play_game_reward", 0);
        return inviteBackMapper.selectCount(queryWrapper);
    }
}
