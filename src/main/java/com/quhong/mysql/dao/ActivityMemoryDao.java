package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.ActivityMemoryData;
import com.quhong.mysql.mapper.ustar.ActivityMemoryMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Lazy
public class ActivityMemoryDao {

    private static final Logger logger = LoggerFactory.getLogger(ActivityMemoryDao.class);

    public static final Integer MEMORY_TYPE_TIME = 0;
    public static final Integer MEMORY_TYPE_SEND = 1;
    public static final Integer MEMORY_TYPE_RECEIVE = 2;

    @Resource
    private ActivityMemoryMapper activityMemoryMapper;



    public ActivityMemoryData selectOne(String uid, int memoryType) {
        QueryWrapper<ActivityMemoryData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("from_uid", uid);
        queryWrapper.eq("memory_type", memoryType);
        queryWrapper.last("limit 1");
        return activityMemoryMapper.selectOne(queryWrapper);
    }

    public ActivityMemoryData selectReceiveOne(String uid, int memoryType) {
        QueryWrapper<ActivityMemoryData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("to_uid", uid);
        queryWrapper.eq("memory_type", memoryType);
        queryWrapper.last("limit 1");
        return activityMemoryMapper.selectOne(queryWrapper);
    }
}
