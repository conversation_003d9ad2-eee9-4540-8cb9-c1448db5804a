package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.quhong.mysql.data.VoteOptionData;
import com.quhong.mysql.mapper.ustar.VoteOptionMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
@Component
public class VoteOptionDao {

    @Resource
    private VoteOptionMapper voteOptionMapper;

    public void insert(VoteOptionData data) {
        voteOptionMapper.insert(data);
    }

    public void batchInsert(List<VoteOptionData> optionDataList) {
        for (VoteOptionData data : optionDataList) {
            voteOptionMapper.insert(data);
        }
    }

    public List<VoteOptionData> findList(Integer voteId) {
        QueryWrapper<VoteOptionData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("vote_id", voteId);
        queryWrapper.orderByAsc("option_order");
        return voteOptionMapper.selectList(queryWrapper);
    }

    public void update(VoteOptionData voteOptionData) {
        voteOptionMapper.updateById(voteOptionData);
    }

    public void incOptionVotesNum(int voteId, List<Integer> optionList) {
        voteOptionMapper.incOptionVotesNum(voteId, optionList);
    }

    public VoteOptionData selectOne(int optionId) {
        return voteOptionMapper.selectById(optionId);
    }
}
