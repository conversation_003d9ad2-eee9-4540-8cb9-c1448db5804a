package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.QueenHallExpressData;
import com.quhong.mysql.mapper.ustar_log.QueenHallExpressMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Component
@Lazy
public class QueenHallExpressDao {

    private static final Logger logger = LoggerFactory.getLogger(QueenHallExpressDao.class);
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private final CacheMap<String, Set<String>> cacheMap;

    @Resource
    private QueenHallExpressMapper queenHallExpressMapper;


    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public QueenHallExpressDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }


    public List<QueenHallExpressData> selectList() {

        QueryWrapper<QueenHallExpressData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("likes").orderByAsc("ctime");
        IPage<QueenHallExpressData> dataPage = new Page<>(0, 6);
        dataPage = queenHallExpressMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public QueenHallExpressData selectOne(int id) {
        return queenHallExpressMapper.selectById(id);
    }

    public int updateOne(int id, int likes) {
        QueenHallExpressData expressData = new QueenHallExpressData();
        expressData.setId(id);
        expressData.setLikes(likes);
        return queenHallExpressMapper.updateById(expressData);
    }

    public int insertOne(QueenHallExpressData expressData) {
        return queenHallExpressMapper.insert(expressData);
    }

    public List<QueenHallExpressData> selectPageList(int page, int size) {
        QueryWrapper<QueenHallExpressData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("likes").orderByAsc("ctime");
        IPage<QueenHallExpressData> dataPage = new Page<>(page,size);
        dataPage = queenHallExpressMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }

    public QueenHallExpressData selectOne(String uid) {
        QueryWrapper<QueenHallExpressData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        queryWrapper.last("limit 1");
        return queenHallExpressMapper.selectOne(queryWrapper);
    }

}
