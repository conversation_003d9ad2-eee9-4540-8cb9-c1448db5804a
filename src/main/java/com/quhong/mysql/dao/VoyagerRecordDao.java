package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.mysql.data.QueenHallExpressData;
import com.quhong.mysql.data.VoyagerRecordData;
import com.quhong.mysql.data.WorldTravelRecordData;
import com.quhong.mysql.mapper.ustar.VoyagerRecordMapper;
import com.quhong.mysql.mapper.ustar.WorldTravelRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @date 2023/3/9
 */
@Component
public class VoyagerRecordDao {
    private static final Logger logger = LoggerFactory.getLogger(VoyagerRecordDao.class);


    @Resource
    private VoyagerRecordMapper voyagerRecordMapper;

    public int insertData(VoyagerRecordData data) {
        return voyagerRecordMapper.insert(data);
    }


    public List<VoyagerRecordData> selectList() {
        QueryWrapper<VoyagerRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("ctime");
        IPage<VoyagerRecordData> dataPage = new Page<>(0, 10);
        dataPage = voyagerRecordMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }
}
