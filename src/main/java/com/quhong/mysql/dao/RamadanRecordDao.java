package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.RamadanRecordData;
import com.quhong.mysql.data.ShootRecordData;
import com.quhong.mysql.mapper.ustar_log.RamadanRecordMapper;
import com.quhong.mysql.mapper.ustar_log.ShootRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class RamadanRecordDao {

    private static final Logger logger = LoggerFactory.getLogger(RamadanRecordDao.class);
    @Resource
    private RamadanRecordMapper ramadanRecordMapper;



    public List<RamadanRecordData> selectList(String uid, int page, int pageSize) {
        QueryWrapper<RamadanRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        IPage<RamadanRecordData> recordPage = new Page<>(page == 0 ? 1 : page, pageSize);
        recordPage = ramadanRecordMapper.selectPage(recordPage, queryWrapper);
        return recordPage.getRecords();
    }


    public int insertOne(RamadanRecordData data) {
        return ramadanRecordMapper.insert(data);
    }
}
