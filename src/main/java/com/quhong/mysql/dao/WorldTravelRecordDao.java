package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.mysql.data.SmashEggData;
import com.quhong.mysql.data.WorldTravelData;
import com.quhong.mysql.data.WorldTravelRecordData;
import com.quhong.mysql.mapper.ustar.WorldTravelMapper;
import com.quhong.mysql.mapper.ustar.WorldTravelRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @date 2023/3/9
 */
@Component
public class WorldTravelRecordDao {
    private static final Logger logger = LoggerFactory.getLogger(WorldTravelRecordDao.class);


    @Resource
    private WorldTravelRecordMapper worldTravelRecordMapper;

    public WorldTravelRecordData selectOneByKey(String uid, String worldKey) {
        QueryWrapper<WorldTravelRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("world_key", worldKey);
        return worldTravelRecordMapper.selectOne(queryWrapper);
    }

    public int insertData(WorldTravelRecordData data) {
        return worldTravelRecordMapper.insert(data);
    }

    public int updateData(WorldTravelRecordData data) {
        return worldTravelRecordMapper.updateById(data);
    }


    public List<WorldTravelRecordData> selectPageList(String uid, int start, int pageSize) {
        QueryWrapper<WorldTravelRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("mtime");
        IPage<WorldTravelRecordData> dataPage = new Page<>(start, pageSize);
        dataPage = worldTravelRecordMapper.selectPage(dataPage, queryWrapper);
        return dataPage.getRecords();
    }
}
