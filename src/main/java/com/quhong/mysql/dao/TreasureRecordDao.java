package com.quhong.mysql.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.LuckyNumData;
import com.quhong.mysql.data.TreasureRecordData;
import com.quhong.mysql.mapper.ustar_log.LuckyNumMapper;
import com.quhong.mysql.mapper.ustar_log.TreasureRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Lazy
@Component
public class TreasureRecordDao extends MonthShardingDao<TreasureRecordMapper> {
    private static final Logger logger = LoggerFactory.getLogger(TreasureRecordDao.class);

    @Resource
    private TreasureRecordMapper treasureRecordMapper;

    public TreasureRecordDao() {
        super("t_treasure_record");
    }

    public void insert(TreasureRecordData treasureRecordData) {
        String suffix = DateHelper.ARABIAN.getTableSuffix(DateHelper.formatDate(treasureRecordData.getCtime()));
        createTable(suffix);
        treasureRecordMapper.insert(suffix, treasureRecordData);
    }


    public List<TreasureRecordData> getRecords(String uid, int page, int size) {
        List<String> suffixList = getTableSuffixList(-2);
        suffixList = suffixList.stream().filter(this::checkExist).collect(Collectors.toList());
        if (suffixList.isEmpty()) {
            return Collections.emptyList();
        }
        int start = (page - 1) * size;
        logger.info("TreasureRecordDao getRecords uid:{}, start={}, size={}, suffixList={}", uid, start, size, suffixList);
        return treasureRecordMapper.getRecords(uid, start, size, suffixList);
    }

    public List<TreasureRecordData> getLastRecords(int size) {
        List<String> suffixList = getTableSuffixList(-1);
        suffixList = suffixList.stream().filter(this::checkExist).collect(Collectors.toList());
        if (suffixList.isEmpty()) {
            return Collections.emptyList();
        }
        return treasureRecordMapper.getLastRecords(size, suffixList);
    }


    public TreasureRecordData getUserRecordOne(String uid) {
        List<String> suffixList = getTableSuffixList(-1);
        suffixList = suffixList.stream().filter(this::checkExist).collect(Collectors.toList());
        if (suffixList.isEmpty()) {
            return null;
        }
        return treasureRecordMapper.getUserRecordOne(uid, suffixList);
    }

}
