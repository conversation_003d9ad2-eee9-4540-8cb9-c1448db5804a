package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.QuizResultData;
import com.quhong.mysql.mapper.ustar.QuizResultMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/5
 */
@Component
@Lazy
public class QuizResultDao {

    private static final Logger logger = LoggerFactory.getLogger(QuizResultDao.class);

    private final CacheMap<Integer, List<QuizResultData>> cacheMap;
    private static final long CACHE_TIME_MILLIS = 5 * 60 * 1000L;

    @Resource
    private QuizResultMapper quizResultMapper;

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public QuizResultDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    /**
     * 获取答题成绩排行榜前10名
     */
    public List<QuizResultData> selectTopTenRanking(Integer activityId) {
        List<QuizResultData> dataList = cacheMap.getData(activityId);
        if(!CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>(dataList);
        }
        dataList = selectTopTenList(activityId);
        if (!CollectionUtils.isEmpty(dataList)) {
            cacheMap.cacheData(activityId, dataList);
        }
        return dataList;
    }

    public QuizResultData selectOne(Integer activityId, String uid) {
        QueryWrapper<QuizResultData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.eq("uid", uid);
        return quizResultMapper.selectOne(queryWrapper);
    }

    /**
     * 获取答题成绩排行榜前10名
     */
    private List<QuizResultData> selectTopTenList(Integer activityId) {
        QueryWrapper<QuizResultData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.ne("best_time", 0);
        queryWrapper.orderByDesc("best_score");
        queryWrapper.orderByAsc("best_time");
        queryWrapper.orderByAsc("ctime");
        queryWrapper.orderByDesc("user_level");
        queryWrapper.last("limit 10");
        return quizResultMapper.selectList(queryWrapper);
    }

    public void update(QuizResultData data) {
        quizResultMapper.updateById(data);
        if (isBetterThanTenth(data)) {
            updateCacheMap(data.getActivityId());
        }
    }

    public void insert(QuizResultData data) {
        try {
            quizResultMapper.insert(data);
            if (isBetterThanTenth(data)) {
                updateCacheMap(data.getActivityId());
            }
        } catch (Exception e) {
            logger.error("insert quiz result data error. {}", e.getMessage(), e);
        }
    }

    /**
     * 判断是否比第十名成绩更好
     */
    private boolean isBetterThanTenth(QuizResultData data) {
        List<QuizResultData> topTenList = selectTopTenRanking(data.getActivityId());
        int ten = 10;
        if (CollectionUtils.isEmpty(topTenList) || topTenList.size() < ten) {
            return true;
        }
        QuizResultData lastData = topTenList.get(9);
        return data.getBestScore() > lastData.getBestScore() || (data.getBestScore().equals(lastData.getBestScore()) && data.getBestTime() < lastData.getBestTime());
    }

    /**
     * 更新排行榜前10缓存
     */
    private void updateCacheMap(Integer activityId) {
        List<QuizResultData> dataList = selectTopTenList(activityId);
        if (!CollectionUtils.isEmpty(dataList)) {
            cacheMap.cacheData(activityId, dataList);
        }
    }

    public List<QuizResultData> selectByActivityId(Integer activityId) {
        QueryWrapper<QuizResultData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        return quizResultMapper.selectList(queryWrapper);
    }
}
