package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.QueenHallData;
import com.quhong.mysql.mapper.ustar_log.QueenHallMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class QueenHallDao {

    private static final Logger logger = LoggerFactory.getLogger(QueenHallDao.class);
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    // public static final int START_TIME = 1659042000;
    // public static final int END_TIME = 1659560400;
    private final CacheMap<String, List<QueenHallData>> cacheMap;
    private static final String QUEEN_HALL_ALL = "list:queen_hall";

    @Resource
    private QueenHallMapper queenHallMapper;


    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public QueenHallDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }


    public List<QueenHallData> selectList() {
        QueryWrapper<QueenHallData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("order_sort");
        return queenHallMapper.selectList(queryWrapper);
    }


    public List<QueenHallData> getQueenHallDataFromCache() {
        List<QueenHallData> data = cacheMap.getData(QUEEN_HALL_ALL);
        if (!CollectionUtils.isEmpty(data)) {
            return data;
        }

        data = selectList();
        cacheMap.cacheData(QUEEN_HALL_ALL, data);
        return data;
    }

}
