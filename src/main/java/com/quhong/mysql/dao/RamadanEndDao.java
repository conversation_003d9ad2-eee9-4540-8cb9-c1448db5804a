package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.RamadanEndData;
import com.quhong.mysql.mapper.ustar.RamadanEndMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Lazy
public class RamadanEndDao {

    private static final Logger logger = LoggerFactory.getLogger(RamadanEndDao.class);
    @Resource
    private RamadanEndMapper ramadanEndMapper;



    public List<RamadanEndData> selectList() {
        QueryWrapper<RamadanEndData> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("ctime");
        return ramadanEndMapper.selectList(queryWrapper);
    }

}
