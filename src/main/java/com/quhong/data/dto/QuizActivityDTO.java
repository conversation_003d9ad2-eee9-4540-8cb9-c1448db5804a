package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/6
 */
public class QuizActivityDTO extends HttpEnvData {

    /**
     * 活动id
     */
    private Integer activityId;

    /**
     * 关卡
     */
    private Integer checkpointNo;

    /**
     * 答题回答
     */
    private Map<Integer, String> quizAnswer;

    /**
     * 答题耗时
     */
    private Long time;

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public Map<Integer, String> getQuizAnswer() {
        return quizAnswer;
    }

    public void setQuizAnswer(Map<Integer, String> quizAnswer) {
        this.quizAnswer = quizAnswer;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Integer getCheckpointNo() {
        return checkpointNo;
    }

    public void setCheckpointNo(Integer checkpointNo) {
        this.checkpointNo = checkpointNo;
    }
}
