package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class PainterPictureDTO extends HttpEnvData {
    private String picture;
    private String momentText;

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getMomentText() {
        return momentText;
    }

    public void setMomentText(String momentText) {
        this.momentText = momentText;
    }

    @Override
    public String toString() {
        return "PainterPictureDTO{" +
                "picture='" + picture + '\'' +
                ", uid='" + uid + '\'' +
                ", slang=" + slang +
                '}';
    }
}
