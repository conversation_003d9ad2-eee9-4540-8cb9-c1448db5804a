package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class UserReportDTO extends HttpEnvData {

    private String origin;               // 举报入口 roomCover、 roomScreen、userPage、 userCard、 moment、 momentComment
    private String reasonSelect;         // 原因选择
    private String subType;              // 子类型
    private String problemInfo;          // 问题详情信息
    private String publicText;          // 问题详情信息
    private String targetId;             // 房间roomId、 公屏uid、 用户主页uid、 资料卡uid、 朋友圈id、 朋友圈评论id
    private String msgId;                // 举报私信时带的消息id
    private Integer msgType;             // 消息类型


    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getReasonSelect() {
        return reasonSelect;
    }

    public void setReasonSelect(String reasonSelect) {
        this.reasonSelect = reasonSelect;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getProblemInfo() {
        return problemInfo;
    }

    public void setProblemInfo(String problemInfo) {
        this.problemInfo = problemInfo;
    }

    public String getPublicText() {
        return publicText;
    }

    public void setPublicText(String publicText) {
        this.publicText = publicText;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public Integer getMsgType() {
        return msgType;
    }

    public void setMsgType(Integer msgType) {
        this.msgType = msgType;
    }
}
