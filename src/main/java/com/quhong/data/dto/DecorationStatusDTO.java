package com.quhong.data.dto;


import com.quhong.handler.HttpEnvData;

/**
 * <AUTHOR>
 */
public class DecorationStatusDTO extends HttpEnvData {

    private int cardStatus;      // 登录活动页面5天(插牌)
    private int candyStatus;     // 在房间里发送“周年庆”礼物给五个朋友(糖果)
    private int giftStatus;      // 在私聊里发送“周年庆”礼物给五个朋友(礼物堆)
    private int balloonStatus;   // 发朋友圈带话题#祝YouStar周年庆生日快乐(气球)
    private int candleStatus;    // 关注官方号id 10000(蜡烛)

    public int getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(int cardStatus) {
        this.cardStatus = cardStatus;
    }

    public int getCandyStatus() {
        return candyStatus;
    }

    public void setCandyStatus(int candyStatus) {
        this.candyStatus = candyStatus;
    }

    public int getGiftStatus() {
        return giftStatus;
    }

    public void setGiftStatus(int giftStatus) {
        this.giftStatus = giftStatus;
    }

    public int getBalloonStatus() {
        return balloonStatus;
    }

    public void setBalloonStatus(int balloonStatus) {
        this.balloonStatus = balloonStatus;
    }

    public int getCandleStatus() {
        return candleStatus;
    }

    public void setCandleStatus(int candleStatus) {
        this.candleStatus = candleStatus;
    }

    @Override
    public String toString() {
        return "DecorationStatusDTO{" +
                "cardStatus=" + cardStatus +
                ", candyStatus=" + candyStatus +
                ", giftStatus=" + giftStatus +
                ", balloonStatus=" + balloonStatus +
                ", candleStatus=" + candleStatus +
                '}';
    }
}
