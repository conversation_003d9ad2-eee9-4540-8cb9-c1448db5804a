package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class ShareActivityDTO extends HttpEnvData {
    private String aid;
    private Integer shareId;
    private String activity_id;
    private String activity_name;
    private String activity_desc;
    private String activity_icon;
    private String activity_banner;
    private String activity_url;

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public Integer getShareId() {
        return shareId;
    }

    public void setShareId(Integer shareId) {
        this.shareId = shareId;
    }

    public String getActivity_id() {
        return activity_id;
    }

    public void setActivity_id(String activity_id) {
        this.activity_id = activity_id;
    }

    public String getActivity_name() {
        return activity_name;
    }

    public void setActivity_name(String activity_name) {
        this.activity_name = activity_name;
    }

    public String getActivity_desc() {
        return activity_desc;
    }

    public void setActivity_desc(String activity_desc) {
        this.activity_desc = activity_desc;
    }

    public String getActivity_icon() {
        return activity_icon;
    }

    public void setActivity_icon(String activity_icon) {
        this.activity_icon = activity_icon;
    }

    public String getActivity_banner() {
        return activity_banner;
    }

    public void setActivity_banner(String activity_banner) {
        this.activity_banner = activity_banner;
    }

    public String getActivity_url() {
        return activity_url;
    }

    public void setActivity_url(String activity_url) {
        this.activity_url = activity_url;
    }
}
