package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class DiTreasureDrawDTO extends HttpEnvData {
    /**
     * 活动码
     */
    private String activityId;
    /**
     * 是否抽剩下所有 true是 false(单抽)
     */
    private boolean drawAll;

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public boolean isDrawAll() {
        return drawAll;
    }

    public void setDrawAll(boolean drawAll) {
        this.drawAll = drawAll;
    }
}
