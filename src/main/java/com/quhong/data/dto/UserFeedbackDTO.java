package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class UserFeedbackDTO extends HttpEnvData {
    private int id;
    private String feedbackId;
    private String origin;
    private String problemSelect;        // 问题选择
    private String subType;              // 子类型
    private String contactInfo;          // 联系方式
    private String problemInfo;          // 问题详情信息
    private String loginType;            // 登录方式
    private String loginFailInfo;        // 登录页失败错误信息
    private Integer platform;            // 登录平台  1: ios  0: android

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getFeedbackId() {
        return feedbackId;
    }

    public void setFeedbackId(String feedbackId) {
        this.feedbackId = feedbackId;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getProblemSelect() {
        return problemSelect;
    }

    public void setProblemSelect(String problemSelect) {
        this.problemSelect = problemSelect;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getProblemInfo() {
        return problemInfo;
    }

    public void setProblemInfo(String problemInfo) {
        this.problemInfo = problemInfo;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public String getLoginFailInfo() {
        return loginFailInfo;
    }

    public void setLoginFailInfo(String loginFailInfo) {
        this.loginFailInfo = loginFailInfo;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }
}
