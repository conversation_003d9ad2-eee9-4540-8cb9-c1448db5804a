package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class NewYearExpectDTO extends HttpEnvData {
    private String activityId;
    private String message;
    private String messageUrl;

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessageUrl() {
        return messageUrl;
    }

    public void setMessageUrl(String messageUrl) {
        this.messageUrl = messageUrl;
    }

    @Override
    public String toString() {
        return "NewYearExpectDTO{" +
                "message='" + message + '\'' +
                ", messageUrl='" + messageUrl + '\'' +
                '}';
    }
}
