package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
public class VoteDTO extends HttpEnvData {

    /**
     * 投票id
     */
    private int voteId;

    /**
     * 投票类型 0礼物投票 1问答投票
     */
    private int type;

    /**
     * 选项类型 0单选 1多选
     */
    private int optionType;

    /**
     * 投票主题内容
     */
    private String title;

    /**
     * 投票指定的礼物id
     */
    private int giftId;

    /**
     * 投票持续时间
     */
    private int duration;

    /**
     * 投票费用类型 0金币 1钻石
     */
    private int feeType;

    /**
     * 投票费用
     */
    private int costBeans;

    /**
     * 投票选项内容
     */
    private List<String> optionContentList;

    /**
     * 页数
     */
    private int page;

    /**
     * 选择的投票选项
     */
    private List<Integer> optionList;

    public int getVoteId() {
        return voteId;
    }

    public void setVoteId(int voteId) {
        this.voteId = voteId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getOptionType() {
        return optionType;
    }

    public void setOptionType(int optionType) {
        this.optionType = optionType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getGiftId() {
        return giftId;
    }

    public void setGiftId(int giftId) {
        this.giftId = giftId;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getFeeType() {
        return feeType;
    }

    public void setFeeType(int feeType) {
        this.feeType = feeType;
    }

    public int getCostBeans() {
        return costBeans;
    }

    public void setCostBeans(int costBeans) {
        this.costBeans = costBeans;
    }

    public List<String> getOptionContentList() {
        return optionContentList;
    }

    public void setOptionContentList(List<String> optionContentList) {
        this.optionContentList = optionContentList;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public List<Integer> getOptionList() {
        return optionList;
    }

    public void setOptionList(List<Integer> optionList) {
        this.optionList = optionList;
    }
}
