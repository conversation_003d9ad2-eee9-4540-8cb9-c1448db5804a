package com.quhong.data;


/**
 * <AUTHOR>
 * @date 2024/8/23
 */
public class LotteryPrizeData {

    private Integer resType; // 资源类型 1 勋章 2 麦位框 3 坐骑 4 背包礼物  5 房间锁 6 聊天气泡 7 声波纹 8 浮屏 9 个人背景资源 10靓号 11进房通知 12荣誉称号 100金币 101财富等级 102vip
    private Integer resId; // 资源id
    private String icon; // 奖励图标
    private Integer num; // 奖励数量，天数（除特殊资源都是天数）、金币数（金币resType=100时）、个数(背包礼物resType=4时)、等级(背包礼物resType=101时)、-1永久
    private Integer days; // 背包礼物有效天数
    /**
     * 中奖概率（费付费用户）
     */
    private double prob;
    /**
     * 中奖概率（付费用户）
     */
    private double payUserProb;

    public LotteryPrizeData() {
    }

    public LotteryPrizeData(Integer resType, Integer resId, String icon, Integer num, Integer days, double prob, double payUserProb) {
        this.resType = resType;
        this.resId = resId;
        this.icon = icon;
        this.num = num;
        this.days = days;
        this.prob = prob;
        this.payUserProb = payUserProb;
    }

    public double getProb() {
        return prob;
    }

    public void setProb(double prob) {
        this.prob = prob;
    }

    public Integer getResType() {
        return resType;
    }

    public void setResType(Integer resType) {
        this.resType = resType;
    }

    public Integer getResId() {
        return resId;
    }

    public void setResId(Integer resId) {
        this.resId = resId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public double getPayUserProb() {
        return payUserProb;
    }

    public void setPayUserProb(double payUserProb) {
        this.payUserProb = payUserProb;
    }
}
