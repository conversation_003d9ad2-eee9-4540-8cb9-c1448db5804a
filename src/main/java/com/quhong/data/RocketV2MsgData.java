package com.quhong.data;

import com.quhong.mongo.data.RocketRewardConfigData;


public class RocketV2MsgData {

    String nowDay;
    String roomId;
    int rocketLevel;
    int updateLevel;
    int energyRate;
    RocketRewardConfigData configData;

    public RocketV2MsgData() {
    }

    public RocketV2MsgData(String nowDay, String roomId, int rocketLevel, int updateLevel, int energyRate, RocketRewardConfigData configData) {
        this.nowDay = nowDay;
        this.roomId = roomId;
        this.rocketLevel = rocketLevel;
        this.updateLevel = updateLevel;
        this.energyRate = energyRate;
        this.configData = configData;
    }

    public String getNowDay() {
        return nowDay;
    }

    public void setNowDay(String nowDay) {
        this.nowDay = nowDay;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public int getRocketLevel() {
        return rocketLevel;
    }

    public void setRocketLevel(int rocketLevel) {
        this.rocketLevel = rocketLevel;
    }

    public int getUpdateLevel() {
        return updateLevel;
    }

    public void setUpdateLevel(int updateLevel) {
        this.updateLevel = updateLevel;
    }

    public int getEnergyRate() {
        return energyRate;
    }

    public void setEnergyRate(int energyRate) {
        this.energyRate = energyRate;
    }

    public RocketRewardConfigData getConfigData() {
        return configData;
    }

    public void setConfigData(RocketRewardConfigData configData) {
        this.configData = configData;
    }
}
