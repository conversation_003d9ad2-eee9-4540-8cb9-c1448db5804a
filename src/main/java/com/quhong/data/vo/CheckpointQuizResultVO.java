package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/30
 */
public class CheckpointQuizResultVO {

    /**
     * 复活条件 0钻/次 1金币/次
     */
    private Integer againConditionType;

    /**
     * 复活需要的钻石或金币的数量
     */
    private Integer againCostNum;

    /**
     * 用户余额
     */
    private Integer balance;

    /**
     * 剩余答题次数
     */
    private Integer freeAnswerCount;

    /**
     * 是否成功通关 0否 1是
     */
    private Integer passSuccess;

    /**
     * 下一关卡
     */
    private Integer nextCheckpointNo;

    /**
     * 是否通关全部关卡
     */
    private Integer passAll;

    /**
     * 通关总耗时
     */
    private Integer costTime;

    /**
     * 用户排名
     */
    private Integer rank;

    /**
     * 获得的称谓
     */
    private String title;

    /**
     * 我已获得的奖励
     */
    private List<QuizGainRewardVO> myGainRewards;

    /**
     * 剩余关卡奖励
     */
    private List<QuizGainRewardVO> leftRewards;

    public Integer getAgainConditionType() {
        return againConditionType;
    }

    public void setAgainConditionType(Integer againConditionType) {
        this.againConditionType = againConditionType;
    }

    public Integer getAgainCostNum() {
        return againCostNum;
    }

    public void setAgainCostNum(Integer againCostNum) {
        this.againCostNum = againCostNum;
    }

    public Integer getBalance() {
        return balance;
    }

    public void setBalance(Integer balance) {
        this.balance = balance;
    }

    public Integer getFreeAnswerCount() {
        return freeAnswerCount;
    }

    public void setFreeAnswerCount(Integer freeAnswerCount) {
        this.freeAnswerCount = freeAnswerCount;
    }

    public Integer getPassSuccess() {
        return passSuccess;
    }

    public void setPassSuccess(Integer passSuccess) {
        this.passSuccess = passSuccess;
    }

    public Integer getNextCheckpointNo() {
        return nextCheckpointNo;
    }

    public void setNextCheckpointNo(Integer nextCheckpointNo) {
        this.nextCheckpointNo = nextCheckpointNo;
    }

    public Integer getPassAll() {
        return passAll;
    }

    public void setPassAll(Integer passAll) {
        this.passAll = passAll;
    }

    public List<QuizGainRewardVO> getMyGainRewards() {
        return myGainRewards;
    }

    public void setMyGainRewards(List<QuizGainRewardVO> myGainRewards) {
        this.myGainRewards = myGainRewards;
    }

    public List<QuizGainRewardVO> getLeftRewards() {
        return leftRewards;
    }

    public void setLeftRewards(List<QuizGainRewardVO> leftRewards) {
        this.leftRewards = leftRewards;
    }

    public Integer getCostTime() {
        return costTime;
    }

    public void setCostTime(Integer costTime) {
        this.costTime = costTime;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
