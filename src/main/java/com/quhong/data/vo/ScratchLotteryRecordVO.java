package com.quhong.data.vo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
public class ScratchLotteryRecordVO {

    private List<PrizeConfigVO> rewardList;
    private List<String> consumeList;
    private Integer ctime;

    public ScratchLotteryRecordVO() {
    }

    public ScratchLotteryRecordVO(List<PrizeConfigVO> rewardList, Integer ctime) {
        this.rewardList = rewardList;
        this.ctime = ctime;
    }

    public ScratchLotteryRecordVO(List<PrizeConfigVO> rewardList, List<String> consumeList, Integer ctime) {
        this.rewardList = rewardList;
        this.consumeList = consumeList;
        this.ctime = ctime;
    }

    public List<PrizeConfigVO> getRewardList() {
        return rewardList;
    }

    public void setRewardList(List<PrizeConfigVO> rewardList) {
        this.rewardList = rewardList;
    }

    public List<String> getConsumeList() {
        return consumeList;
    }

    public void setConsumeList(List<String> consumeList) {
        this.consumeList = consumeList;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
