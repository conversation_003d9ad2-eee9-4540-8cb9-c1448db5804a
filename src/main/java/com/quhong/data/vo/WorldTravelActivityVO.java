package com.quhong.data.vo;

import java.util.List;

public class WorldTravelActivityVO extends OtherRankConfigVO{
    private List<WorldConfig> worldList;
    private List<OtherRankingListVO> travelRankingList;
    private OtherMyRankVO myTravelRank;

    private List<OtherRankingListVO> collectRankingList;
    private OtherMyRankVO myCollectRank;

    public static class WorldConfig{
        private String worldKey;
        private String icon;
        private String nameEn;
        private String nameAr;
        private int status;
        private int likes;
        private int likeStatus;
        private int award;
        private int click;

        public String getWorldKey() {
            return worldKey;
        }

        public void setWorldKey(String worldKey) {
            this.worldKey = worldKey;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getLikes() {
            return likes;
        }

        public void setLikes(int likes) {
            this.likes = likes;
        }

        public int getLikeStatus() {
            return likeStatus;
        }

        public void setLikeStatus(int likeStatus) {
            this.likeStatus = likeStatus;
        }

        public int getAward() {
            return award;
        }

        public void setAward(int award) {
            this.award = award;
        }

        public int getClick() {
            return click;
        }

        public void setClick(int click) {
            this.click = click;
        }
    }

    public List<WorldConfig> getWorldList() {
        return worldList;
    }

    public void setWorldList(List<WorldConfig> worldList) {
        this.worldList = worldList;
    }

    public List<OtherRankingListVO> getTravelRankingList() {
        return travelRankingList;
    }

    public void setTravelRankingList(List<OtherRankingListVO> travelRankingList) {
        this.travelRankingList = travelRankingList;
    }

    public OtherMyRankVO getMyTravelRank() {
        return myTravelRank;
    }

    public void setMyTravelRank(OtherMyRankVO myTravelRank) {
        this.myTravelRank = myTravelRank;
    }

    public List<OtherRankingListVO> getCollectRankingList() {
        return collectRankingList;
    }

    public void setCollectRankingList(List<OtherRankingListVO> collectRankingList) {
        this.collectRankingList = collectRankingList;
    }

    public OtherMyRankVO getMyCollectRank() {
        return myCollectRank;
    }

    public void setMyCollectRank(OtherMyRankVO myCollectRank) {
        this.myCollectRank = myCollectRank;
    }
}
