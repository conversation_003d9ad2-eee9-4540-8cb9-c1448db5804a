package com.quhong.data.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class QuizActivityRankingVO {

    /**
     * 用户rid
     */
    private Integer rid;
    /**
     * 用户名
     */
    private String name;
    /**
     * 用户头像
     */
    private String head;
    /**
     * 答题分数
     */
    private Integer score;
    /**
     * 答题耗时
     */
    private BigDecimal time;
    /**
     * 排行榜
     */
    private Integer rank;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public BigDecimal getTime() {
        return time;
    }

    public void setTime(BigDecimal time) {
        this.time = time;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }
}
