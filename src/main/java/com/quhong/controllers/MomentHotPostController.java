package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.MomentHotPostV2Service;
import com.quhong.service.NewbieStarService;
import com.quhong.service.NewbieStarV2Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 杂七杂八的接口
 */


@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class MomentHotPostController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(MomentHotPostController.class);
    @Resource
    private NewbieStarService newbieStarService;
    @Resource
    private MomentHotPostV2Service momentHotPostV2Service;


    /**
     * 每周热帖
     */
    @RequestMapping(value = "momentHotPostV2Config")
    private String momentHotPostConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("momentHotPostV2Config activityId:{}, uid:{}", activityId, uid);
        return createResult(HttpCode.SUCCESS, momentHotPostV2Service.momentHotPostConfig(activityId, uid));
    }

    @RequestMapping(value = "momentHotPostV2DailyGet")
    private String momentHotPostV2DailyGet(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskType, @RequestParam String taskKey) {
        logger.info("momentHotPostGet activityId:{}, uid:{}, taskKey:{}", activityId, uid, taskKey);
        momentHotPostV2Service.momentHotPostV2DailyGet(activityId, uid, taskType, taskKey);
        HttpCode httpCode = new HttpCode(0, "تم الاستلام بنجاح", "تم الاستلام بنجاح");
        return createResult(httpCode, null);
    }

    @RequestMapping(value = "momentHotPostV2WeeklyGet")
    private String momentHotPostV2WeeklyGet(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskType, @RequestParam String taskKey) {
        logger.info("momentHotPostV2WeeklyGet activityId:{}, uid:{}, taskType:{}, taskKey:{}", activityId, uid, taskType, taskKey);
        momentHotPostV2Service.momentHotPostV2WeeklyGet(activityId, uid, taskType, taskKey);
        HttpCode httpCode = new HttpCode(0, "تم الاستلام بنجاح", "تم الاستلام بنجاح");
        return createResult(httpCode, null);
    }


}
