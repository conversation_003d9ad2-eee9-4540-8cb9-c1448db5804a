package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.FootballCarnivalService;
import com.quhong.service.MiningService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 足球狂欢
 *
 */
@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class FootballCarnivalController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private FootballCarnivalService footballCarnivalService;

    /**
     * 足球狂欢配置
     */
    @RequestMapping("footballCarnivalConfig")
    public HttpResult<FootballCarnivalVO> footballCarnivalConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("footballCarnivalConfig activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(footballCarnivalService.footballCarnivalConfig(activityId, uid));
    }

    /**
     * 足球狂欢团队榜单
     */
    @RequestMapping("footballCarnivalTeamRank")
    public HttpResult<FootballCarnivalVO> footballCarnivalTeamRank(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("footballCarnivalTeamRank activityId: {}, uid:{}", activityId, uid);
        return HttpResult.getOk(footballCarnivalService.footballCarnivalTeamRank(activityId, uid));
    }

    /**
     * 足球狂欢抽奖
     */
    @RequestMapping("footballCarnivalDraw")
    public HttpResult<FootballCarnivalVO> footballCarnivalDraw(@RequestParam String activityId, @RequestParam String uid,
                                                               @RequestParam int amount, @RequestParam int goal) {
        logger.info("footballCarnivalDraw activityId: {}, uid:{}, amount={}", activityId, uid, amount);
        return HttpResult.getOk(footballCarnivalService.footballCarnivalDraw(activityId, uid, amount,goal));
    }

    /**
     * 足球狂欢抽奖记录
     */
    @RequestMapping("footballCarnivalRecord")
    public HttpResult<FootballCarnivalVO> footballCarnivalRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        logger.info("footballCarnivalRecord activityId:{}, uid: {}, page:{}", activityId, uid, page);
        return HttpResult.getOk(footballCarnivalService.footballCarnivalRecord(activityId, uid, page));
    }


    /**
     * 足球狂欢加入组队
     */
    @RequestMapping("footballCarnivalJoinTeam")
    public HttpResult<?> footballCarnivalJoinTeam(@RequestParam String activityId, @RequestParam String uid, @RequestParam String captainUid) {
        logger.info("footballCarnivalJoinTeam activityId:{}, uid: {}, captainUid:{}", activityId, uid, captainUid);
        footballCarnivalService.footballCarnivalJoinTeam(activityId, uid, captainUid);
        return HttpResult.getOk();
    }

    /**
     * 足球狂欢分享给好友
     */
    @RequestMapping("footballCarnivalShare")
    public HttpResult<?> footballCarnivalShare(@RequestBody ShareActivityDTO dto) {
        logger.info("footballCarnivalShare dto:{} ", JSONObject.toJSONString(dto));
        footballCarnivalService.footballCarnivalShare(dto);
        return HttpResult.getOk();
    }

}