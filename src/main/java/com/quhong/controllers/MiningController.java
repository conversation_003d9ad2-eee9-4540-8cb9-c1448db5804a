package com.quhong.controllers;

import com.quhong.data.vo.MiningInfoVO;
import com.quhong.data.vo.MiningRecordVO;
import com.quhong.data.vo.MiningVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.MiningService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 黄金矿工活动
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@RestController
@RequestMapping(value ="${baseUrl}miningActivity", produces = MediaType.APPLICATION_JSON_VALUE)
public class MiningController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private MiningService miningService;

    /**
     * 获取活动详情
     */
    @GetMapping("info")
    private HttpResult<MiningInfoVO> getInfo(@RequestParam String uid, @RequestParam String activityId) {
        long timeMillis = System.currentTimeMillis();
        MiningInfoVO vo = miningService.getInfo(activityId, uid);
        logger.info("getInfo. activityId={} uid={} cost={}", activityId, uid, System.currentTimeMillis() - timeMillis);
        return HttpResult.getOk(vo);
    }

    /**
     * 挖矿
     */
    @GetMapping("mining")
    private HttpResult<MiningVO> mining(@RequestParam String uid, @RequestParam String activityId, @RequestParam Integer position) {
        logger.info("getInfo. activityId={} uid={} position={}", activityId, uid, position);
        if (position == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(miningService.mining(activityId, uid, position));
    }

    /**
     * 挖矿记录
     */
    @RequestMapping("miningRecord")
    private HttpResult<PageVO<MiningRecordVO>> miningRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam(defaultValue = "1") Integer page) {
        logger.info("get mining record. activityId={} uid={} page={}", activityId, uid, page);
        if (page == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(miningService.miningRecord(activityId, uid, page));
    }

    /**
     * 兑换奖品
     */
    @GetMapping("exchange")
    private HttpResult<MiningVO> exchange(@RequestParam String uid, @RequestParam String activityId, @RequestParam String drawType) {
        logger.info("exchange. activityId={} uid={} drawType={}", activityId, uid, drawType);
        if (!StringUtils.hasLength(drawType)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(miningService.exchange(activityId, uid, drawType));
    }

    /**
     * 重置刷新
     */
    @GetMapping("reset")
    private HttpResult<MiningVO> reset(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("reset. activityId={} uid={}", activityId, uid);
        return HttpResult.getOk(miningService.reset(activityId, uid));
    }

    /**
     * 设置活动提醒
     */
    @GetMapping("setReminder")
    private HttpResult<Object> setReminder(@RequestParam String uid, @RequestParam String activityId, @RequestParam(defaultValue = "1") int status) {
        logger.info("setReminder activityId={} uid={} status={}", activityId, uid, status);
        miningService.setReminder(activityId, uid, status);
        return HttpResult.getOk();
    }
}