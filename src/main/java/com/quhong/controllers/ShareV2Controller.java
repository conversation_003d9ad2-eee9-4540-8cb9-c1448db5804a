package com.quhong.controllers;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.service.ShareService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/9
 */
@Controller
@RequestMapping(value = "/share/")
public class ShareV2Controller {

    private static final Logger logger = LoggerFactory.getLogger(ShareController.class);

    @Resource
    private ShareService shareService;


    /**
     * 分享房间页面
     */
    @GetMapping("shareRoom/{param}")
    private ModelAndView shareRoom(@PathVariable("param") String param, Model model) {
        logger.info("Generate H5 Shared shareRoom page. param={}", param);
        if (ObjectUtils.isEmpty(param)) {
            logger.error("share page param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return shareService.shareRoom(param, model);
    }

    /**
     * 分享房间页面
     */
    @GetMapping("shareGameRoom/{param}")
    private ModelAndView shareGameRoom(@PathVariable("param") String param, Model model) {
        logger.info("Generate H5 Shared shareGameRoom page. param={}", param);
        if (ObjectUtils.isEmpty(param)) {
            logger.error("share page shareGameRoom param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return shareService.shareGameRoom(param, model);
    }

    /**
     * 分享个人主页页面
     */
    @GetMapping("shareUser/{param}")
    private ModelAndView shareUser(@PathVariable("param") String param, Model model) {
        logger.info("Generate H5 Shared page. param={}", param);
        if (ObjectUtils.isEmpty(param)) {
            logger.error("share page param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return shareService.shareUser(param, model);
    }

    /**
     * 分享活动
     */
    @GetMapping("shareActivity/{param}")
    private ModelAndView shareActivity(@PathVariable("param") String param, Model model) {
        logger.info("Generate shareActivity. param={}", param);
        if (ObjectUtils.isEmpty(param)) {
            logger.error("share page param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return shareService.shareActivity(param, model);
    }

    /**
     * 分享房间活动
     */
    @GetMapping("shareRoomEvent/{param}")
    private ModelAndView shareRoomEvent(@PathVariable("param") String param, Model model) {
        logger.info("Generate shareRoomEvent. param={}", param);
        if (ObjectUtils.isEmpty(param)) {
            logger.error("share page param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return shareService.shareRoomEvent(param, model);
    }
}
