package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.OtherActivityService;
import com.quhong.service.WorldCupService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class WorldCupController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(WorldCupController.class);

    @Resource
    private WorldCupService worldCupService;

    // 竞猜列表
    @RequestMapping("worldCupConfig")
    private String worldCupConfig(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, worldCupService.worldCupConfig(uid));
    }

    // 下注
    @RequestMapping("worldCupBet")
    private String betWorldCup(@RequestParam String uid, @RequestParam String matchType, @RequestParam int ship, @RequestParam int teamKey) {
        logger.info("betWorldCup: uid={}, matchType={}, ship={}, teamKey={}", uid, matchType, ship, teamKey);

        worldCupService.betWorldCup(uid, matchType, ship, teamKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 下注记录
    @RequestMapping("worldCupBetRecord")
    private String worldCupBetRecord(@RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, worldCupService.worldCupBetRecordList(uid, page));
    }

    // 兑换筹码
    @RequestMapping("worldCupChange")
    private String worldCupChange(@RequestParam String uid, @RequestParam String changeType) {
        logger.info("worldCupChange: uid={}, changeType={}", uid, changeType);
        return createResult(HttpCode.SUCCESS, worldCupService.worldCupChange(uid, changeType));
    }

    // 兑换钻石
    @RequestMapping("worldCupChangeDiamond")
    private String worldCupChangeDiamond(@RequestParam String uid) {
        logger.info("worldCupChangeDiamond: uid={}", uid);
        worldCupService.worldCupChangeDiamond(uid);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 冠军列表
    @RequestMapping("worldCupChampion")
    private String worldCupChampion(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, worldCupService.worldCupChampion(uid));
    }

    // 下注冠军
    @RequestMapping("worldCupChampionBet")
    private String worldCupChampionBet(@RequestParam String uid, @RequestParam int ship,
                                       @RequestParam(value = "recordId") int teamKey) {

        logger.info("worldCupChampionBet: uid={}, ship={}, teamKey={}", uid, ship, teamKey);
        worldCupService.worldCupChampionBet(uid, ship, teamKey);
        return createResult(HttpCode.SUCCESS, "");
    }


    // 世界杯房间返钻活动
    @RequestMapping("worldCupRoomInfo")
    private String worldCupRoomInfo(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankType) {

        return createResult(HttpCode.SUCCESS, worldCupService.worldCupRoomInfo(uid, activityId, rankType));
    }

    @RequestMapping("worldCupRoomAdd")
    private String worldCupRoomAdd(@RequestParam String uid, @RequestParam String activityId) {
        worldCupService.worldCupRoomAdd(uid, activityId);
        return createResult(HttpCode.SUCCESS, "");
    }




}
