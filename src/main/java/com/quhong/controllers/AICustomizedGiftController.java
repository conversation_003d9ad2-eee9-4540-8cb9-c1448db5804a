package com.quhong.controllers;


import com.quhong.data.dto.SharingOfficerDTO;
import com.quhong.data.vo.AICustomizedGiftVO;
import com.quhong.data.vo.CrashExchangeShopVO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.H5Controller;
import com.quhong.service.AICustomizedGiftService;
import com.quhong.service.CrashExchangeShopService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * AI定制礼物活动
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class AICustomizedGiftController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(AICustomizedGiftController.class);

    @Resource
    private AICustomizedGiftService aiCustomizedGiftService;


    /**
     * 基本配置信息
     *
     * @return
     */
    @RequestMapping("aiCustomized/config")
    private HttpResult<AICustomizedGiftVO> aiCustomizedGiftConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(aiCustomizedGiftService.aiCustomizedGiftConfig(activityId, uid));
    }

    /**
     * 前几个月的排行
     *
     * @return
     */
    @RequestMapping("aiCustomized/honor")
    private HttpResult<AICustomizedGiftVO> aiCustomizedGiftHonor(@RequestParam String activityId, @RequestParam String uid, @RequestParam String month) {
        return HttpResult.getOk(aiCustomizedGiftService.aiCustomizedGiftHonor(activityId, uid, month));
    }

    /**
     * 提交AI头像
     *
     * @return
     */
    @RequestMapping("aiCustomized/submitAiHead")
    private HttpResult<?> submitAiHead(@RequestBody SharingOfficerDTO dto) {
        aiCustomizedGiftService.submitAiHead(dto);
        return HttpResult.getOk();
    }

}
