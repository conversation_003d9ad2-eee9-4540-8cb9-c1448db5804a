package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.MotherService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class MotherController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(MotherController.class);

    @Resource
    private MotherService motherService;

    @RequestMapping("motherConfig")
    private String motherConfig(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, motherService.motherConfig(uid, activityId));
    }


    // 分享朋友圈
    @RequestMapping(value ="motherMomentPush")
    private String motherMomentPush(@RequestParam String uid, @RequestParam String activityId, @RequestParam int slang) {
        logger.info("motherMomentPush uid {}, activityId={}", uid, activityId);
        motherService.motherMomentPush(uid, activityId, slang);
        return createResult(HttpCode.SUCCESS, null);
    }
}
