package com.quhong.controllers;


import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.GameEventOrganizerService;
import com.quhong.service.SuperQueen2025Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 派对活动
 */


@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class GameEventOrganizerController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(GameEventOrganizerController.class);

    @Resource
    private GameEventOrganizerService gameEventOrganizerService;

    /**
     * challengeInfo
     */
    @RequestMapping("gameEventChallengeInfo")
    private String gameEventChallengeInfo(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.challengeInfo(activityId, uid));
    }

    /**
     * adminList
     */
    @RequestMapping("gameEventAdminList")
    private String gameEventAdminList(@RequestParam String activityId, @RequestParam String uid,
                                      @RequestParam int level) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.adminList(activityId, uid, level));
    }

    /**
     * collect 获取资源
     */
    @RequestMapping("gameEventCollect")
    private String gameEventCollect(@RequestParam String activityId, @RequestParam String uid,
                                    @RequestParam(required = false) String aid,
                                    @RequestParam int cmd, @RequestParam(required = false) Integer level) {
        gameEventOrganizerService.collect(activityId, uid, aid, cmd, level);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * gameEventReportAndGiftInfo
     */
    @RequestMapping("gameEventReportAndGiftInfo")
    private String gameEventReportAndGiftInfo(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.reportAndGiftInfo(activityId, uid));
    }

    /**
     * event-new  event-hot event-search
     */
    @RequestMapping("gameEventList")
    private String gameEventList(@RequestParam String activityId, @RequestParam String uid,
                                 @RequestParam String searchRid, @RequestParam int slang) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.eventList(activityId, uid, searchRid, slang));
    }


    /**
     * gameEventRankList
     */
    @RequestMapping("gameEventRankList")
    private String gameEventRankList(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.rankList(activityId, uid));
    }

    /**
     * gameHistoryRecordList
     */
    @RequestMapping("gameHistoryRecordList")
    private String gameHistoryRecordList(@RequestParam String activityId, @RequestParam String uid,
                                         @RequestParam int type, @RequestParam int page, @RequestParam int slang) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.historyRecordList(activityId, type, uid, page, slang));
    }

    /**
     * 获取题目列表
     */
    @RequestMapping("gameGetQuestionList")
    private String gameEventTestUidDay(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.getQuestionList(uid, activityId));
    }

    /**
     * 提交答题回答
     */
    @RequestMapping("gameSubmitAnswer")
    private String submitAnswer(@RequestParam String activityId, @RequestParam String uid, @RequestBody QuizActivityDTO dto) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.submitAnswer(activityId,uid,dto));
    }


    /**
     * 测试用偏移天数
     */
    @RequestMapping("gameEventTestUidDay")
    private String gameEventTestUidDay(@RequestParam String uid, @RequestParam int cmd, @RequestParam int addDays
            , @RequestParam int addValue) {
        return createResult(HttpCode.SUCCESS, gameEventOrganizerService.testUidDay(cmd, uid, addDays, addValue));
    }

}
