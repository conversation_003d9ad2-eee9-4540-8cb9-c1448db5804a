package com.quhong.controllers;

import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.PainterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class PainterController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(PainterController.class);

    @Resource
    private PainterService painterService;

    @RequestMapping("painterConfig")
    private String motherConfig(@RequestParam String uid, @RequestParam(required = false, defaultValue = "2") int slang) {
        return createResult(HttpCode.SUCCESS, painterService.painterConfig(uid, slang));
    }


    // 图片分享朋友圈
    @RequestMapping(value ="painterPicture", method = RequestMethod.POST)
    private String painterPicturePush(@RequestBody PainterPictureDTO dto) {
        logger.info("painterPicturePush dto={}", dto);
        painterService.painterPicturePush(dto);
        return createResult(HttpCode.SUCCESS, null);
    }


    // 文案分享朋友圈
    @RequestMapping(value ="painterWritten")
    private String painterWrittenPush(@RequestParam String uid, @RequestParam String message, @RequestParam String picture, @RequestParam int slang) {
        logger.info("painterWrittenPush uid {}, message={}", uid, message);
        painterService.painterWrittenPush(uid, message, picture, slang);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 排行榜
    @RequestMapping(value ="painterRanking")
    private String painterRanking(@RequestParam String uid, @RequestParam String selectDate) {
        logger.info("painterWrittenPush uid {}, selectDate={}", uid, selectDate);
        return createResult(HttpCode.SUCCESS, painterService.painterRanking(uid, selectDate));
    }

    // 作品广场
    @RequestMapping(value ="painterHall")
    private String painterHall(@RequestParam String uid, @RequestParam String selectDate, @RequestParam int page) {
        logger.info("painterHall uid {}, selectDate={}   ", uid, selectDate);
        return createResult(HttpCode.SUCCESS, painterService.painterHall(uid, selectDate, page));
    }


    // 点赞作品
    @RequestMapping(value ="painterLike")
    private String painterLike(@RequestParam String uid, @RequestParam String momentId) {
        logger.info("painterHall uid {}, momentId={}   ", uid, momentId);
        painterService.painterLike(uid, momentId);
        return createResult(HttpCode.SUCCESS, null);
    }
}
