package com.quhong.controllers;

import com.quhong.data.vo.NationalDayV3VO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.NationalDayV2Service;
import com.quhong.service.NationalDayV3Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class NationalDayV3Controller extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV3Controller.class);

    @Resource
    private NationalDayV3Service nationalDayV3Service;

    // 国庆配置信息
    @RequestMapping("nationalDayV3Config")
    private String nationalDayV3Config(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, nationalDayV3Service.nationalDayV3Config(uid, activityId));
    }

    // 邀请好友助力,发私信消息
    @RequestMapping("nationalDayV3InviteHelp")
    private String nationalDayV3InviteHelp(@RequestParam String uid, @RequestParam String activityId, @RequestParam String aid) {
        nationalDayV3Service.nationalDayV3InviteHelp(uid, activityId, aid);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 助力
    @RequestMapping("nationalDayV3Help")
    private String nationalDayV3Help(@RequestParam String uid, @RequestParam String activityId, @RequestParam String aid) {
        NationalDayV3VO vo = nationalDayV3Service.nationalDayV3Help(uid, activityId, aid);
        return createResult(HttpCode.SUCCESS, vo);
    }

    // find friend list
    @RequestMapping("nationalDayV3FindFriendList")
    private String nationalDayV3FindFriendList(@RequestParam String uid, @RequestParam String activityId, @RequestParam int page) {
        NationalDayV3VO vo = nationalDayV3Service.nationalDayV3FindFriendList(activityId, uid, page);
        return createResult(HttpCode.SUCCESS, vo);
    }

    // 打开或隐身发现开关
    @RequestMapping("nationalDayV3State")
    private String nationalDayV3State(@RequestParam String uid, @RequestParam String activityId, @RequestParam int state) {
        nationalDayV3Service.nationalDayV3State(activityId, uid, state);
        return createResult(HttpCode.SUCCESS, null);
    }

    //添加好友
    @RequestMapping("nationalDayV3AddFriendApply")
    private String nationalDayV3AddFriendApply(@RequestParam String uid, @RequestParam String activityId
            , @RequestParam String aid, @RequestParam String msg) {
        nationalDayV3Service.addFriendApply(activityId, uid, aid, msg);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 获取助力奖励
    @RequestMapping("nationalDayV3ClickReward")
    private String userClickReward(@RequestParam String uid, @RequestParam String activityId) {
        NationalDayV3VO vo = nationalDayV3Service.userClickReward(activityId, uid);
        return createResult(HttpCode.SUCCESS, vo);
    }

    // 清助力
    @RequestMapping("clearNationalDayV3")
    private String clearNationalDayV2(@RequestParam(defaultValue = "0") String rid, @RequestParam String activityId
            , @RequestParam String uid) {
        nationalDayV3Service.clearNationalDayV3(rid, activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }


}
