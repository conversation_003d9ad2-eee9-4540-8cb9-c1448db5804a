package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.NationalDayV2Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class NationalDayV2Controller extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV2Controller.class);

    @Resource
    private NationalDayV2Service nationalDayV2Service;

    // 国庆配置信息
    @RequestMapping("nationalDayV2Config")
    private String nationalDayV2Config(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, nationalDayV2Service.nationalDayV2Config(uid, activityId));
    }

    // 加入
    @RequestMapping("nationalDayV2Join")
    private String nationalDayV2Join(@RequestParam String uid, @RequestParam String activityId) {
        nationalDayV2Service.nationalDayV2Join(uid, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 点赞
    @RequestMapping("nationalDayV2Like")
    private String nationalDayV2Like(@RequestParam String uid, @RequestParam String activityId) {
        nationalDayV2Service.nationalDayV2Like(uid, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 抽奖
    @RequestMapping("nationalDayV2Draw")
    private String nationalDayV2Draw(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, nationalDayV2Service.nationalDayV2Draw(uid, activityId));
    }

    // 助力
    @RequestMapping("nationalDayV2Help")
    private String nationalDayV2Help(@RequestParam String uid, @RequestParam String activityId,  @RequestParam(defaultValue = "0") String rid) {
        nationalDayV2Service.nationalDayV2Help(uid, activityId, rid);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 清助力
    @RequestMapping("clearNationalDayV2")
    private String clearNationalDayV2(@RequestParam(defaultValue = "0") String rid,  @RequestParam String activityId) {
        nationalDayV2Service.clearNationalDayV2(rid, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }


}
