package com.quhong.controllers;


import com.quhong.data.dto.SmashEggDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.handler.HttpEnvData;
import com.quhong.service.GiftRecommendService;
import com.quhong.service.SmashEggService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 杂七杂八的接口
 */


@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class SmashEggWebController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(SmashEggWebController.class);

    @Resource
    private SmashEggService smashEggService;

    @RequestMapping("smash_egg/home_info")
    private String smashEggHomeInfo(@RequestParam String uid, @RequestParam(required = false, defaultValue = "1") int slang) {

        HttpEnvData req = new HttpEnvData();
        req.setUid(uid);
        req.setSlang(slang);
        logger.info("webSmashEggHomeInfo: uid: {}", req.getUid());
        return createResult(HttpCode.SUCCESS, smashEggService.smashEggHomeInfo(req));
    }

    @RequestMapping("smash_egg/personal")
    private String webSmashEggPersonal(@RequestParam String uid, @RequestParam(required = false, defaultValue = "1") int slang, @RequestParam(required = false, defaultValue = "1") int page) {
        SmashEggDTO dto = new SmashEggDTO();
        dto.setUid(uid);
        dto.setSlang(slang);
        dto.setPage(page);
        logger.info("webSmashEggPersonal: uid: {}, page: {}", dto.getUid(), dto.getPage());
        return createResult(HttpCode.SUCCESS, smashEggService.iosSmashEggPersonal(dto));
    }

    @RequestMapping("smash_egg/ranking")
    private String webSmashEggRanking(@RequestParam String uid, @RequestParam(required = false, defaultValue = "1") int slang, @RequestParam(required = false, defaultValue = "1") int rank_type) {
        SmashEggDTO dto = new SmashEggDTO();
        dto.setUid(uid);
        dto.setSlang(slang);
        dto.setRank_type(rank_type);
        logger.info("webSmashEggPersonal: uid: {}, rank_type: {}", dto.getUid(), dto.getRank_type());
        return createResult(HttpCode.SUCCESS, smashEggService.iosSmashEggRanking(dto));
    }

    @RequestMapping("smash_egg/play")
    private String webSmashEggPlay(@RequestParam String uid, @RequestParam(required = false, defaultValue = "1") int amount) {
        SmashEggDTO dto = new SmashEggDTO();
        dto.setUid(uid);
        dto.setAmount(amount);
        logger.info("webSmashEggPlay: uid: {}, amount: {}", dto.getUid(), dto.getAmount());
        return createResult(HttpCode.SUCCESS, smashEggService.iosSmashEggPlay(dto));
    }
}
