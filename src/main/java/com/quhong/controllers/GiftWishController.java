package com.quhong.controllers;


import com.quhong.data.dto.TimeCapsuleDTO;
import com.quhong.data.vo.TimeCapsuleV2VO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 礼物心愿
 */


@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class GiftWishController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(GiftWishController.class);

    @Resource
    private GiftWishService giftWishService;
    @Resource
    private TimeCapsuleService timeCapsuleService;
    @Resource
    private TimeCapsuleServiceV2 timeCapsuleServiceV2;


    @RequestMapping("giftWishConfig")
    private String giftWishConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, giftWishService.giftWishConfig(activityId, uid));
    }


    @RequestMapping("giftWishSee")
    private String giftWishSee(@RequestParam String activityId, @RequestParam String uid, @RequestParam String showKey) {
        giftWishService.giftWishSee(activityId, uid, showKey);
        return createResult(HttpCode.SUCCESS, null);
    }


    // 时间胶囊
    // @RequestMapping("timeCapsuleConfig")
    // private String timeCapsuleConfig(@RequestParam String activityId, @RequestParam String uid) {
    //     return createResult(HttpCode.SUCCESS, timeCapsuleService.timeCapsuleConfig(activityId, uid));
    // }
    //
    // @RequestMapping("timeCapsuleJoin")
    // private String timeCapsuleJoin(@RequestParam String activityId, @RequestParam String uid) {
    //     timeCapsuleService.timeCapsuleJoinWish(activityId, uid);
    //     return createResult(HttpCode.SUCCESS, null);
    // }
    //
    // @RequestMapping("timeCapsuleWrite")
    // private String timeCapsuleWrite(@RequestBody TimeCapsuleDTO dto) {
    //     timeCapsuleService.timeCapsuleWriteWish(dto);
    //     return createResult(HttpCode.SUCCESS, null);
    // }
    //
    // @RequestMapping("timeCapsuleLike")
    // private String timeCapsuleLike(@RequestParam String activityId, @RequestParam String uid, @RequestParam String docId) {
    //     timeCapsuleService.timeCapsuleLike(activityId, uid, docId);
    //     return createResult(HttpCode.SUCCESS, null);
    // }

    // 时间胶囊V2
    @RequestMapping("timeCapsuleConfigV2")
    private String timeCapsuleConfigV2(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, timeCapsuleServiceV2.timeCapsuleV2Config(activityId, uid));
    }

    @RequestMapping("timeCapsuleShareV2")
    private String timeCapsuleShareV2(@RequestParam String activityId, @RequestParam String uid,  @RequestParam int shareType) {
        timeCapsuleServiceV2.timeCapsuleShare(activityId, uid, shareType);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("timeCapsuleWriteV2")
    private String timeCapsuleWriteV2(@RequestBody TimeCapsuleV2VO dto) {
        timeCapsuleServiceV2.timeCapsuleWriteWish(dto);
        return createResult(HttpCode.SUCCESS, null);
    }


}
