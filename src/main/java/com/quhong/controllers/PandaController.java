package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.PandaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 熊猫活动
 */
@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class PandaController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(PandaController.class);

    @Resource
    private PandaService pandaService;

    /**
     * 熊猫配置
     */
    @RequestMapping(value = "pandaConfig")
    private String pandaConfig(@RequestParam String uid, @RequestParam String activityId, @RequestParam(required = false, defaultValue = "1") int slang) {
        logger.info("pandaConfig uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, pandaService.pandaConfig(uid, activityId, slang));
    }

    /**
     * 熊猫签到
     */
    @RequestMapping(value = "pandaSign")
    private String pandaSign(@RequestParam String uid, @RequestParam String activityId, @RequestParam String signDay) {
        logger.info("pandaSign uid={} activityId={} signDay={}", uid, activityId, signDay);
        pandaService.pandaSign(uid, activityId, signDay);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 抽奖
     */
    @RequestMapping(value = "pandaDraw")
    private String pandaDraw(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("pandaDraw uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, pandaService.pandaDraw(uid, activityId));
    }
}
