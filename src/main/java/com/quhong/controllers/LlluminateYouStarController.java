package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.LlluminateYouStarService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class LlluminateYouStarController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(LlluminateYouStarController.class);

    @Resource
    private LlluminateYouStarService llluminateYouStarService;

    // 加入闪耀活动
    @RequestMapping("llluminateJoin")
    private String llluminateJoin(@RequestParam String uid, @RequestParam(defaultValue = "") String rid) {
        logger.info("llluminateJoin uid:{}, rid:{}", uid, rid);
        return createResult(HttpCode.SUCCESS, llluminateYouStarService.joinLlluminateActivity(uid, rid));
    }

    // 闪耀活动左tab
    @RequestMapping("llluminateMainTab")
    private String llluminateMainTab(@RequestParam String uid, @RequestParam Integer slang) {
        return createResult(HttpCode.SUCCESS, llluminateYouStarService.mainTab(uid, slang));
    }


    // 闪耀活动中间tab
    @RequestMapping("llluminateGetDetail")
    private String llluminateGetDetail(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, llluminateYouStarService.getDetailVO(uid));
    }

    // 闪耀活动抽奖
    @RequestMapping("llluminateDraw")
    private String llluminateDraw(@RequestParam String uid, @RequestParam int num,@RequestParam(defaultValue = "") String roomId) {
        logger.info("llluminateDraw uid:{}, num:{}", uid, num);
        return createResult(HttpCode.SUCCESS, llluminateYouStarService.llluminateDraw(uid, num,roomId));
    }

    // 闪耀活动右tab-抽奖基本信息
    @RequestMapping("llluminateDrawInfo")
    private String llluminateDrawInfo(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, llluminateYouStarService.llluminateDrawInfo(uid));
    }

    // 闪耀活动订阅
    @RequestMapping("llluminateSub")
    private String llluminateSub(@RequestParam String uid, @RequestParam Integer eventId) {
        llluminateYouStarService.llluminateSub(uid, eventId);
        return createResult(HttpCode.SUCCESS, new Object());
    }



    // 闪耀活动发总榜奖励
    @RequestMapping("llluminateTestTotal")
    private String llluminateJoin(@RequestParam String uid) {
        logger.info("llluminateTestTotal uid:{}", uid);
        return createResult(HttpCode.SUCCESS, llluminateYouStarService.testTotal());
    }
}
