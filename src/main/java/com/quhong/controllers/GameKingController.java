package com.quhong.controllers;

import com.quhong.data.vo.GameKingDetailVO;
import com.quhong.data.vo.GameKingRankingVO;
import com.quhong.datas.HttpResult;
import com.quhong.service.GameKingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 游戏王活动
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@RestController
@RequestMapping("${baseUrl}")
public class GameKingController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private GameKingService gameKingService;

    /**
     * 获取活动详情
     */
    @GetMapping("game_king/detail")
    private HttpResult<GameKingDetailVO> getDetailInfo(@RequestParam String uid, @RequestParam(required = false, defaultValue = "2") int slang) {
        long timeMillis = System.currentTimeMillis();
        GameKingDetailVO vo = gameKingService.getDetailInfo(slang);
        logger.info("getDetailInfo. uid={} cost={}", uid, System.currentTimeMillis() - timeMillis);
        return HttpResult.getOk(vo);
    }

    /**
     * 获取活动排行榜
     */
    @GetMapping("game_king/ranking")
    private HttpResult<GameKingRankingVO> getRankingList(@RequestParam String uid) {
        long timeMillis = System.currentTimeMillis();
        GameKingRankingVO vo = gameKingService.getRankingList(uid);
        logger.info("getRankingList. uid={} cost={}", uid, System.currentTimeMillis() - timeMillis);
        return HttpResult.getOk(vo);
    }
}
