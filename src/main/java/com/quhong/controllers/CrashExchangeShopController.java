package com.quhong.controllers;


import com.quhong.data.vo.CrashExchangeShopVO;
import com.quhong.data.vo.Eid2025VO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.H5Controller;
import com.quhong.service.CrashExchangeShopService;
import com.quhong.service.Eid2025Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * crash兑换商店
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class CrashExchangeShopController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(CrashExchangeShopController.class);

    @Resource
    private CrashExchangeShopService exchangeShopService;


    /**
     * 基本配置信息
     *
     * @return
     */
    @RequestMapping("crash/config")
    private HttpResult<CrashExchangeShopVO> crashExchangeShopConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("crashExchangeShopConfig activityId={} uid={} ", activityId, uid);
        return HttpResult.getOk(exchangeShopService.crashExchangeShopConfig(activityId, uid));
    }

    /**
     * 抽奖
     *
     * @return
     */
    @RequestMapping("crash/draw")
    private HttpResult<CrashExchangeShopVO.CrashExchangeDrawVO> draw(@RequestParam String activityId, @RequestParam String uid,
                                                                    @RequestParam int drawNum) {
        logger.info("draw activityId={} uid={} drawNum={} ", activityId, uid, drawNum);
        return HttpResult.getOk(exchangeShopService.draw(activityId, uid, drawNum, true));
    }

    /**
     * 兑换
     *
     * @return
     */
    @RequestMapping("crash/exchange")
    private HttpResult<?> exchange(@RequestParam String activityId, @RequestParam String uid,
                                   @RequestParam String metaId) {
        logger.info("exchange activityId={} uid={} metaId={} ", activityId, uid, metaId);
        exchangeShopService.exchange(activityId, uid, metaId);
        return HttpResult.getOk();
    }

    /**
     * 抽奖记录
     *
     * @return
     */
    @RequestMapping("crash/getHistoryList")
    private HttpResult<CrashExchangeShopVO.CrashExchangeHistoryVO> getHistoryList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        logger.info("getHistoryList activityId={} uid={} page={} ", activityId, uid, page);
        return HttpResult.getOk(exchangeShopService.getHistoryListPageRecord(activityId, uid, page));
    }

    /**
     * 设置自动抽奖
     * @param activityId
     * @param uid
     * @param status 0关闭 1开启
     * @return
     */
    @RequestMapping("crash/setAutoDraw")
    private HttpResult<?> setAutoDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int status) {
        logger.info("setAutoDraw activityId={} uid={} status={}", activityId, uid,status);
        exchangeShopService.setAutoDraw(activityId, uid,status);
        return HttpResult.getOk();
    }


}
