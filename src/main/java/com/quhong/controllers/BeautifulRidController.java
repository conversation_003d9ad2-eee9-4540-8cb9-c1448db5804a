package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.BeautifulRidService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class BeautifulRidController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(BeautifulRidController.class);

    @Resource
    private BeautifulRidService beautifulRidService;

    // 靓号列表首页
    @RequestMapping("beautiful/rid_list")
    private String beautifulRidList(@RequestParam String uid, @RequestParam int slang) {
        return createResult(HttpCode.SUCCESS, beautifulRidService.beautifulRidList(uid, slang));
    }


    @RequestMapping("beautiful/search_rid")
    private String beautifulRidSearch(@RequestParam String uid, @RequestParam(defaultValue = "0") String rid) {
        return createResult(HttpCode.SUCCESS, beautifulRidService.beautifulRidSearch(uid, rid));
    }

    @RequestMapping("beautiful/set_rid")
    private String beautifulRidSet(@RequestParam String uid, @RequestParam(defaultValue = "0") String rid) {
        return createResult(HttpCode.SUCCESS, beautifulRidService.beautifulRidSet(uid, rid));
    }


    // 靓号列表首页-860新版
    @RequestMapping("beautiful/ridInfo")
    private String beautifulRidInfo(@RequestParam String uid, @RequestParam int slang) {
        return createResult(HttpCode.SUCCESS, beautifulRidService.beautifulRidInfo(uid, slang));
    }

    // 靓号列表首页-860历史记录
    @RequestMapping("beautiful/ridHistory")
    private String beautifulRidHistory(@RequestParam String uid, @RequestParam int slang) {
        return createResult(HttpCode.SUCCESS, beautifulRidService.beautifulRidHistory(uid, slang));
    }
}
