package com.quhong.controllers;


import com.quhong.data.dto.BackUserDTO;
import com.quhong.data.dto.SharingOfficerDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.SharingOfficerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class SharingOfficerController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(SharingOfficerController.class);

    @Resource
    private SharingOfficerService sharingOfficerService;

    // 分享官活动基本信息
    @RequestMapping("sharingOfficerInfo")
    private String sharingOfficerInfo(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, sharingOfficerService.sharingOfficerInfo(activityId, uid));
    }

    // 分享官活动滚屏
    @RequestMapping("sharingOfficerRewardRecord")
    private String sharingOfficerRewardRecord(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, sharingOfficerService.sharingOfficerRewardRecord(activityId, uid, 1));
    }

    // 分享官活动提交
    @RequestMapping("submitSharingOfficer")
    private String submitSharingOfficer(@RequestBody SharingOfficerDTO dto) {
        sharingOfficerService.submitSharingOfficer(dto);
        return createResult(HttpCode.SUCCESS, new Object());
    }
}
