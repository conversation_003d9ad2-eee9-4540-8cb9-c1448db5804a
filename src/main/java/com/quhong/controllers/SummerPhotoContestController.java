package com.quhong.controllers;

import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.SummerPhotoContestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class SummerPhotoContestController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(SummerPhotoContestController.class);

    @Resource
    private SummerPhotoContestService summerPhotoContestService;

    @RequestMapping("summerPhotoConfig")
    private String summerPhotoConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, summerPhotoContestService.painterConfig(activityId, uid));
    }


    // 图片分享朋友圈
    @RequestMapping(value = "summerPhotoPicture", method = RequestMethod.POST)
    private String summerPhotoPicture(@RequestParam String activityId, @RequestParam String uid, @RequestBody PainterPictureDTO dto) {
        logger.info("painterPicturePush dto={}", dto);
        summerPhotoContestService.painterPicturePush(activityId, uid, dto);
        return createResult(HttpCode.SUCCESS, null);
    }


    // 开始鉴赏列表
    @RequestMapping(value = "summerPhotoDayCreateList")
    private String summerPhotoDayCreateList(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, summerPhotoContestService.recommendDayCreateList(activityId, uid));
    }


    // 点赞作品
    @RequestMapping(value = "summerPhotoLike")
    private String summerPhotoLike(@RequestParam String activityId, @RequestParam String uid, @RequestParam String momentId) {
        logger.info("summerPhotoLike uid {}, momentId={}  ", uid, momentId);
        summerPhotoContestService.painterLike(activityId, uid, momentId);
        return createResult(HttpCode.SUCCESS, null);
    }


    // 给今日推荐文案点赞
    @RequestMapping(value = "summerPhotoMomentText")
    private String summerPhotoMomentText(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("summerPhotoMomentText uid {}, activityId={}   ", uid, activityId);
        summerPhotoContestService.painterLikeMomentText(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 测试偏移天数
     */
    @RequestMapping("summerPhotoTestUidDay")
    private String summerPhotoTestUidDay(@RequestParam String activityId, @RequestParam String uid
            , @RequestParam int cmd, @RequestParam int addDays) {
        return createResult(HttpCode.SUCCESS, summerPhotoContestService.testUidDay(activityId,cmd, uid, addDays));
    }
}
