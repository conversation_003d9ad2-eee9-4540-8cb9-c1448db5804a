package com.quhong.controllers;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.vo.LikeVO;
import com.quhong.data.vo.WorldTravelActivityVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.WorldTravelService;
import com.quhong.service.WorldTravelV2Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 狮子王
 */
@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class WorldTravelController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(WorldTravelController.class);

    @Resource
    private WorldTravelService worldTravelService;
    @Resource
    private WorldTravelV2Service worldTravelV2Service;

    /**
     * worldTravel配置相关
     */
    @RequestMapping(value = "worldTravelConfig")
    private String worldTravelConfig(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("worldTravelConfig uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, worldTravelService.worldTravelConfig(uid, activityId));
    }

    @RequestMapping("myTravelRecord")
    private String myTravelRecord(@RequestParam int page, @RequestParam String uid) {
        logger.info("NewYearHallDTO page:{}, uid:{}", page, uid);
        return createResult(HttpCode.SUCCESS, worldTravelService.myTravelRecord(page, uid));
    }

    @RequestMapping("worldTravelHall")
    private String worldTravelHall(@RequestParam int page, @RequestParam(defaultValue = "") String uid) {
        logger.info("worldTravelHall page:{}, momentId:{}", page, uid);
        return createResult(HttpCode.SUCCESS, worldTravelService.worldTravelRanking(page, uid));
    }

    @RequestMapping(value = "likeWorld")
    private String likeWorld(@RequestParam String uid, @RequestParam String activityId, @RequestParam String worldKey, @RequestParam Integer slang) {
        logger.info("likeWorld uid={} activityId={} worldKey={}", uid, activityId, worldKey);
        return createResult(ActivityHttpCode.SUCCESS_RESULT, worldTravelService.likeWorld(uid, activityId, worldKey, slang));
    }

    @RequestMapping(value = "getWorldAward")
    private String getWorldAward(@RequestParam String uid, @RequestParam String activityId, @RequestParam String worldKey) {
        logger.info("likeWorld uid={} activityId={} worldKey={}", uid, activityId, worldKey);
        worldTravelService.getWorldAward(uid, activityId, worldKey);
        return createResult(ActivityHttpCode.SUCCESS_RESULT, null);
    }

    @RequestMapping(value = "clickWorldKey")
    private String clickWorldKey(@RequestParam String uid, @RequestParam String activityId, @RequestParam String worldKey) {
        logger.info("clickWorldKey uid={} activityId={} worldKey={}", uid, activityId, worldKey);
        worldTravelService.clickWorldKey(uid, activityId, worldKey);
        return createResult(ActivityHttpCode.SUCCESS_RESULT, null);
    }

    @RequestMapping("drawRecord")
    private String drawRecord(@RequestParam String uid, @RequestParam int page) {
        logger.info("drawRecord uid:{}, page:{}", uid, page);
        return createResult(HttpCode.SUCCESS, worldTravelService.worldTravelRecord(uid, page));
    }


    /**
     * 页面信息
     */
    @RequestMapping(value = "worldTravel/pageInfo")
    private String pageInfo(@RequestParam String uid, @RequestParam String activityId, @RequestParam(required = false, defaultValue = "2") int slang) {
        logger.info("pageInfo uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, worldTravelV2Service.pageInfo(uid, activityId, slang));
    }

    /**
     * 点赞
     */
    @RequestMapping(value = "worldTravel/like")
    private String like(@RequestParam String uid, @RequestParam String activityId, @RequestParam String worldKey, @RequestParam(required = false, defaultValue = "2") int slang) {
        logger.info("like uid={} activityId={} worldKey={}", uid, activityId, worldKey);
        return createResult(ActivityHttpCode.LIKE_SUCCESS, worldTravelV2Service.like(uid, activityId, worldKey, slang));
    }


}
