package com.quhong.controllers;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.dto.ShareDTO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.H5Controller;
import com.quhong.service.ShareService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/9
 */
@Controller
@RequestMapping(value ="${baseUrl}")
public class ShareController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(ShareController.class);

    @Resource
    private ShareService shareService;

    /**
     * 生成分享链接 h5
     */
    @ResponseBody
    @RequestMapping(value = "share_h5", method = RequestMethod.POST)
    private String shareH5(@RequestBody ShareDTO dto) {
        logger.info("Generate H5 Shared links. uid={} mid={}", dto.getUid(), dto.getMid());
        return createResult(HttpCode.SUCCESS, shareService.shareH5(dto));
    }

    /**
     * 生成h5页面
     */
    @GetMapping("share_page/{param}")
    private ModelAndView sharePage(@PathVariable("param") String param, Model model) {
        logger.info("Generate H5 Shared page. param={}", param);
        if (StringUtils.isEmpty(param)) {
            logger.error("share page param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return shareService.sharePage(param, model);
    }

    /**
     * 分享H5活动链接
     */
    @ResponseBody
    @GetMapping(value = "share_activity")
    private String shareActivity(@RequestParam(required = false) String uid, @RequestParam(required = false) Integer shareId, @RequestParam(required = false) Integer slang, @RequestParam(required = false) Integer containsAid) {
        logger.info("Generate H5 Activity Shared links. uid={} shareId={}", uid, shareId);
        if (uid == null || shareId == null) {
            logger.error("share page param error. uid={} shareId={}", uid, shareId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return createResult(HttpCode.SUCCESS, shareService.shareActivity(uid, shareId, slang,containsAid));
    }

    /**
     * 生成跳转h5活动的中间页面
     */
    @GetMapping("share_activity_page/{param}")
    private ModelAndView shareActivityPage(@PathVariable("param") String param, Model model) {
        logger.info("Generate H5 activity shared page. param={}", param);
        if (StringUtils.isEmpty(param)) {
            logger.error("share page param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return shareService.shareActivityPage(param, model);
    }

    /**
     * 分享H5活动链接
     */
    @ResponseBody
    @GetMapping(value = "activity_download_url")
    private String getActivityDownloadUrl(@RequestParam Integer shareId) {
        logger.info("Generate activity download links. shareId={}", shareId);
        if (shareId == null || shareId == 0) {
            logger.error("Generate activity download links. shareId={}", shareId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return createResult(HttpCode.SUCCESS, shareService.getActivityDownloadUrl(shareId));
    }


}
