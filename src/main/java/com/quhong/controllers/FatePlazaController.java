package com.quhong.controllers;


import com.quhong.data.dto.FataPlazaDTO;
import com.quhong.data.vo.Eid2025VO;
import com.quhong.data.vo.FatePlazaVO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.H5Controller;
import com.quhong.service.Eid2025Service;
import com.quhong.service.FatePlazaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 交友广场
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class FatePlazaController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(FatePlazaController.class);

    @Resource
    private FatePlazaService fatePlazaService;


    /**
     * 交友纸条列表
     *
     * @return
     */
    @RequestMapping("fatePlaza/noteList")
    private HttpResult<FatePlazaVO> noteList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return HttpResult.getOk(fatePlazaService.noteList(activityId, uid, page));
    }

    /**
     * 发送纸条
     *
     * @return
     */
    @RequestMapping("fatePlaza/sendNote")
    private HttpResult<?> sendNote(@RequestBody FataPlazaDTO reqDTO) {
        fatePlazaService.sendNote(reqDTO);
        return HttpResult.getOk();
    }

    /**
     * 打招呼
     *
     * @return
     */
    @RequestMapping("fatePlaza/greet")
    private HttpResult<?> greet(@RequestBody FataPlazaDTO reqDTO) {
        fatePlazaService.greet(reqDTO);
        return HttpResult.getOk();
    }

    /**
     * 我的每日任务
     *
     * @return
     */
    @RequestMapping("fatePlaza/myDailyTask")
    private HttpResult<FatePlazaVO.DailyTaskVO> myDailyTask(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(fatePlazaService.myDailyTask(activityId, uid));
    }

    /**
     * 我的友谊任务
     *
     * @return
     */
    @RequestMapping("fatePlaza/myFriendshipTask")
    private HttpResult<FatePlazaVO.FatePlazaListVO> myFriendshipTask(@RequestParam String activityId, @RequestParam String uid, @RequestParam int friendshipType, @RequestParam int page) {
        return HttpResult.getOk(fatePlazaService.myFriendshipTask(activityId, uid, friendshipType, page));
    }

}
