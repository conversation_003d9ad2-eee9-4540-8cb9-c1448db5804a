package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.H5Controller;
import com.quhong.service.ShootActivityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class ShootActivityController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(ShootActivityController.class);

    @Resource
    private ShootActivityService shootActivityService;

    @RequestMapping("shootConfig")
    private String shootConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, shootActivityService.shootConfig(activityId, uid));
    }
    // 清除登录签到
    // @RequestMapping(value = "shootClearLogin")
    // private String shootClearLogin(@RequestParam String uid) {
    //     shootActivityService.shootClearLogin(uid);
    //     return createResult(HttpCode.SUCCESS, "");
    // }

    // 赠送守卫卡
    @RequestMapping(value ="shootSendCard")
    private String shootSendCard(@RequestParam String activityId, @RequestParam String uid, @RequestParam String giveRid) {
        return createResult(HttpCode.SUCCESS, shootActivityService.sendCard(activityId, uid, giveRid));
    }

    @RequestMapping("shootRecord")
    private String shootRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, shootActivityService.shootRecord(activityId, uid, page));
    }


    // 使用卡片抽奖
    @RequestMapping(value = "shootCardDraw")
    private String shootCardDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int zone, @RequestParam String cardType, @RequestParam int amount) {
        logger.info("shootCardDraw activityId:{}, uid:{}, zone:{}, cardType:{}, amount:{}", activityId, uid, zone, cardType, amount);
        return createResult(HttpCode.SUCCESS, shootActivityService.shootCardDraw(activityId, uid, zone, cardType, amount));
    }

    // 射门抽奖记录
    @RequestMapping("shootDrawRecord")
    private String shootDrawRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, shootActivityService.shootDrawRecord(activityId, uid, page));
    }

    @RequestMapping("friendList")
    private String friendList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, shootActivityService.shootFriendList(activityId, uid, page));
    }

}
