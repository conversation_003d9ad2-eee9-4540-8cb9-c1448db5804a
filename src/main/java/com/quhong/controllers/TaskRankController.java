package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.TaskRankTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 任务榜单模板
 */
@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class TaskRankController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(TaskRankController.class);

    @Resource
    private TaskRankTemplateService taskRankTemplateService;

    /**
     * 任务榜单模板配置
     */
    @RequestMapping(value = "taskRankTemplateConfig")
    private String taskRankTemplateConfig(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("taskRankTemplateConfig activityId={} uid={}", activityId, uid);
        return createResultNotRef(HttpCode.SUCCESS, taskRankTemplateService.taskRankTemplateConfig(activityId, uid));
    }

    /**
     * 获取日、里程碑任务奖励
     */
    @RequestMapping(value = "taskRankTemplateReward")
    private String taskRankTemplateReward(@RequestParam String activityId, @RequestParam String uid,
                                          @RequestParam int taskType, @RequestParam String taskKey,
                                          @RequestParam(required = false) String extraParam,
                                          @RequestParam int totalProcess) {
        logger.info("taskRankTemplateReward activityId={} uid={}", activityId, uid);
        taskRankTemplateService.taskRankTemplateReward(activityId, uid, taskType, taskKey,extraParam,totalProcess);
        return createResultNotRef(HttpCode.SUCCESS, "");
    }
}
