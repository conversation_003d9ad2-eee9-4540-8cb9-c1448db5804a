package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.TomatoWarActivityService;
import com.quhong.service.VoyagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 航海家活动
 */
@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class TomatoWarController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(TomatoWarController.class);

    @Resource
    private TomatoWarActivityService tomatoWarActivityService;

    /**
     *
     */
    @RequestMapping(value = "tomatoAdminHomeList")
    private String tomatoAdminHomeList(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, tomatoWarActivityService.tomatoAdminHomeList(activityId, uid));
    }


    //
    @RequestMapping(value = "addTomatoAdminList")
    private String addTomatoAdminList(@RequestParam String uid, @RequestParam String activityId,
                                      @RequestParam(defaultValue = "") String aid,
                                      @RequestParam(defaultValue = "") String strRid) {
        return createResult(HttpCode.SUCCESS, tomatoWarActivityService.addTomatoAdminList(activityId, uid, aid, strRid));
    }

    //
    @RequestMapping(value = "delTomatoAdminList")
    private String delTomatoAdminList(@RequestParam String uid, @RequestParam String activityId, @RequestParam String aid) {
        return createResult(HttpCode.SUCCESS, tomatoWarActivityService.delTomatoAdminList(activityId, uid, aid));
    }

    //
    @RequestMapping(value = "submitTomatoAdminList")
    private String submitTomatoAdminList(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, tomatoWarActivityService.submitTomatoAdminList(activityId, uid));
    }


    //
    @RequestMapping(value = "tomatoFriendList")
    private String tomatoFriendList(@RequestParam String uid, @RequestParam String activityId, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, tomatoWarActivityService.tomatoFriendList(activityId, uid, page));
    }
}
