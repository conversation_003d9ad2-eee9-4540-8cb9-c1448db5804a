package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.PlantTreeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 2023植树活动
 * @date 2023/4/24
 */
@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class PlantTreeController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(PlantTreeController.class);

    @Resource
    private PlantTreeService plantTreeService;

    /**
     * 植树配置相关
     */
    @RequestMapping(value = "plantTreeConfig")
    private String plantTreeConfig(@RequestParam String uid, @RequestParam String activityId, @RequestParam int slang) {
        logger.info("ramadanConfig uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, plantTreeService.plantTreeConfig(uid, activityId, slang));
    }

    /**
     * 开启种树之旅
     */
    @RequestMapping(value = "plantTreeOpen")
    private String plantTreeOpen(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("plantTreeOpen uid={} activityId={}", uid, activityId);
        plantTreeService.plantTreeOpen(uid, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 签到
     */
    @RequestMapping(value = "plantTreeSign")
    private String plantTreeSign(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("plantTreeSign uid={} activityId={}", uid, activityId);
        plantTreeService.plantTreeSign(uid, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping(value = "plantTreeSignTest")
    private String plantTreeSignTest(@RequestParam String uid, @RequestParam String activityId, @RequestParam String signDate) {
        logger.info("plantTreeSign uid={} activityId={} signDate={}", uid, activityId, signDate);
        plantTreeService.plantTreeSignTest(uid, activityId, signDate);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 签到提醒
     */
    @RequestMapping(value = "plantTreeSignAlarm")
    private String plantTreeSignAlarm(@RequestParam String uid, @RequestParam String activityId, @RequestParam int signPush) {
        logger.info("plantTreeSign uid={} activityId={}", uid, activityId);
        plantTreeService.plantTreeSignAlarm(uid, activityId, signPush);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 浇水
     */
    @RequestMapping(value = "plantTreeWater")
    private String plantTreeWater(@RequestParam String uid, @RequestParam String activityId, @RequestParam int slang) {
        logger.info("plantTreeSign uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, plantTreeService.plantTreeWater(uid, activityId, slang));
    }


    /**
     *  图片分享朋友圈
     */
    @RequestMapping(value ="plantTreePicture")
    private String plantTreePicturePush(@RequestParam String uid, @RequestParam String activityId, @RequestParam String picture, @RequestParam int slang) {
        logger.info("plantTreePicturePush uid={}, activityId={}, picture={}", uid, activityId, picture);
        plantTreeService.plantTreePicturePush( uid, activityId, picture, slang);
        return createResult(HttpCode.SUCCESS, null);
    }


}
