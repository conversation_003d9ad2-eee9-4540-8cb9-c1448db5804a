package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.UserFeedbackDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.UserFeedbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 用户反馈h5接口
 */
@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class UserFeedbackController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(UserFeedbackController.class);

    @Resource
    private UserFeedbackService userFeedbackService;

    @RequestMapping(value = "feedback/feedbackConfig")
    public String feedbackConfig(@RequestParam(required = false, defaultValue = "") String feedbackId) {
        logger.info("feedbackConfig: feedbackId: {}", feedbackId);
        return createResult(HttpCode.SUCCESS, userFeedbackService.feedbackConfig(feedbackId));
    }

    /**
     * 用户反馈
     */
    @RequestMapping(value = "feedback/userFeedback", method = RequestMethod.POST)
    public String userFeedback(UserFeedbackDTO dto, MultipartFile[] images) {
        long millis = System.currentTimeMillis();
        logger.info("userFeedback: dto: {}", JSONObject.toJSONString(dto));
        userFeedbackService.userFeedback(dto, images);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping(value = "feedback/feedbackLog")
    public String feedbackLog(@RequestParam(required = false, defaultValue = "") String feedbackId, @RequestParam(required = false, defaultValue = "1") int page) {
        logger.info("feedbackLog: feedbackId: {}, page: {}", feedbackId, page);
        return createResult(HttpCode.SUCCESS, userFeedbackService.userFeedbackRecord(feedbackId, page));
    }

    @RequestMapping(value = "feedback/setUnread")
    public String feedbackSetUnread(@RequestBody UserFeedbackDTO dto) {
        logger.info("feedbackSetUnread: dto: {}", JSONObject.toJSONString(dto));
        userFeedbackService.feedbackSetUnread(dto);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping(value = "feedback/customerHome")
    public String customerHome(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, userFeedbackService.customerHome(uid));
    }

}
