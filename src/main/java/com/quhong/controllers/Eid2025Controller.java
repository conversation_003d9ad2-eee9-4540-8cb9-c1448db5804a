package com.quhong.controllers;


import com.quhong.data.vo.Eid2025VO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.H5Controller;
import com.quhong.service.Eid2025Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 宰牲节2025
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class Eid2025Controller extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(Eid2025Controller.class);

    @Resource
    private Eid2025Service eid2025Service;


    /**
     * 基本配置信息
     *
     * @return
     */
    @RequestMapping("eid2025/eid2025Config")
    private HttpResult<Eid2025VO> eid2025Config(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("eid2025Config activityId={} uid={} ", activityId, uid);
        return HttpResult.getOk(eid2025Service.eid2025Config(activityId, uid));
    }


    /**
     * 抽奖历史记录
     *
     * @return
     */
    @RequestMapping("eid2025/getHistoryList")
    private HttpResult<Eid2025VO.Eid2025HistoryVO> getHistoryList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        logger.info("getHistoryList activityId={} uid={} page={} ", activityId, uid, page);
        return HttpResult.getOk(eid2025Service.getHistoryListPageRecord(activityId, uid, page));
    }


    /**
     * 抓羊
     *
     * @return
     */
    @RequestMapping("eid2025/catchSheep")
    private HttpResult<Eid2025VO.Eid2025DrawVO> catchSheep(@RequestParam String activityId, @RequestParam String uid,
                                                              @RequestParam int zone, @RequestParam int sheepType,
                                                              @RequestParam int amount) {
        logger.info("getHistoryList activityId={} uid={} zone={} sheepType={} amount={} ", activityId, uid, zone, sheepType, amount);
        return HttpResult.getOk(eid2025Service.catchSheep(activityId, uid, zone, sheepType, amount));
    }

    /**
     * 分享 snapchat
     */
    @RequestMapping("eid2025/setShareSnapchat")
    public HttpResult<?> setShareSnapchat(@RequestParam String activityId, @RequestParam String uid) {
        eid2025Service.setShareSnapchat(activityId, uid);
        return HttpResult.getOk();
    }

}
