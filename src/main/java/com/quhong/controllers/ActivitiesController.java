package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.H5Controller;
import com.quhong.mongo.data.LuckyLotteryActivity;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.service.*;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * 活动
 */
@RestController
@RequestMapping("${baseUrl}")
public class ActivitiesController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(ActivitiesController.class);
    public static final long FILE_LIMIT_SIZE = 12 * 1024 * 1024; // 文件限制大小 12m
    @Resource
    private WeeklyStarService weeklyStarService;
    @Resource
    private RankActivityService rankActivityService;
    @Resource
    private CelebrityActivityService celebrityActivityService;
    @Resource
    private NationalDayActivityService nationalDayActivityService;
    @Resource
    private LuckyLotteryService luckyLotteryService;
    @Resource
    private QueenHallService queenHallService;
    @Resource
    private OtherActivityService otherActivityService;
    @Resource
    private DesertFalconService desertFalconService;
    @Resource
    private QuizActivityService quizActivityService;
    @Resource
    private HonorNationalService honorNationalService;
    @Resource
    private CamelService camelService;
    @Resource
    private CheckpointQuizService checkpointQuizService;
    @Resource
    private OSSUploadService ossUploadService;
    @Resource
    private ResourceKeyService resourceKeyService;
    @Resource
    private ShareJackpotActivityService shareJackpotActivityService;
    @Resource
    private BackUserCollectService backUserCollectService;
    @Resource
    private SharingOfficerService sharingOfficerService;
    @Resource
    private LuxuryCarService luxuryCarService;
    @Resource
    private OperationRoomWashService operationRoomWashService;

    /**
     * 冲榜活动首页
     */
    @GetMapping("rankActivity")
    private String rankActivity(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("rankActivity uid={} activityId={}", uid, activityId);
        RankingActivity rankingActivity = rankActivityService.getRankingActivity(activityId);
        RankingActivityVO vo = new RankingActivityVO();
        BeanUtils.copyProperties(rankingActivity, vo);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 冲榜活动排行榜
     */
    @GetMapping("rankRanking")
    private String rankRanking(@RequestParam String uid, @RequestParam String activityId,
                               @RequestParam int rankingAttribute, @RequestParam(required = false) Integer rankGiftId,
                               @RequestParam(required = false) Integer rankGender, @RequestParam(defaultValue = "0") int gloryInfo) {
        if (null == rankGiftId) {
            rankGiftId = 0;
        }

        if (rankGender == null) {
            rankGender = 0;
        }
        logger.info("rankRanking uid={} activityId={} rankingAttribute={} rankGiftId={} rankGender={}", uid, activityId, rankingAttribute, rankGiftId, rankGender);
        return createResult(HttpCode.SUCCESS, rankActivityService.rankRanking(uid, activityId, rankingAttribute, rankGiftId, rankGender, gloryInfo));
    }

    /**
     * 冲榜活动等级奖励个人数据
     */
    @GetMapping("rankReaching")
    private String rankReaching(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("rankReaching uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, rankActivityService.rankReaching(uid, activityId));
    }

    /**
     * 名人活动首页
     */
    @GetMapping("celebrityActivity")
    private String celebrityActivity(@RequestParam String uid, @RequestParam String activityId) {
        long timeMillis = System.currentTimeMillis();
        CelebrityActivityVO celebrityActivityVO = celebrityActivityService.celebrityRanking(uid, activityId);
        logger.info("celebrityActivity uid={} activityId={} cost={}", uid, activityId, System.currentTimeMillis() - timeMillis);
        return createResult(HttpCode.SUCCESS, celebrityActivityVO);
    }

    /**
     * 名人活动点赞
     */
    @GetMapping("celebrityLike")
    private String celebrityLike(@RequestParam String uid, @RequestParam String activityId, @RequestParam String giftId) {
        logger.info("celebrityLike uid={} activityId={} giftId={}", uid, activityId, giftId);
        return createResult(HttpCode.SUCCESS, celebrityActivityService.celebrityLike(uid, activityId, giftId));
    }

    /**
     * 周星活动首页
     */
    @GetMapping("weeklyStar")
    private String weeklyStar(@RequestParam String uid) {
        logger.info("weeklyStar uid={}", uid);
        return createResult(HttpCode.SUCCESS, weeklyStarService.weeklyStar(uid));
    }

    /**
     * 国庆活动首页
     */
    @GetMapping("nationalDay")
    private String nationalDay(@RequestParam String uid, @RequestParam int activityId) {
        logger.info("nationalDay uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, nationalDayActivityService.nationalDay(uid, activityId));
    }

    /**
     * 抽奖模板配置信息
     */
    @GetMapping("luckyLotteryConfig")
    private String luckyLottery(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("luckyLottery uid={} activityId={}", uid, activityId);
        LuckyLotteryActivity luckyLotteryActivity = luckyLotteryService.getLuckyLottery(activityId);

        LuckyLotteryActivityVO vo = new LuckyLotteryActivityVO();
        BeanUtils.copyProperties(luckyLotteryActivity, vo);
        // 初始化免费次数
        luckyLotteryService.initLuckyDrawData(activityId, uid, vo);
        return createResult(HttpCode.SUCCESS, vo);
    }

    @GetMapping("drawLuckyLottery")
    private String drawLuckyLottery(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("drawLuckyLottery uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, luckyLotteryService.drawLuckyLottery(activityId, uid));
    }

    @GetMapping("drawLuckyHistory")
    private String drawLuckyHistory(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("drawLuckyHistory uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, luckyLotteryService.drawLuckyHistory(activityId, uid));
    }

    /**
     * Queen Hall
     * 女性名人堂
     */
    @GetMapping("queenHallConfig")
    private String queenHallConfig(@RequestParam String uid) {
        logger.info("queenHallFameConfig uid={} ", uid);
        return createResult(HttpCode.SUCCESS, queenHallService.getQueenHall(uid));
    }

    @GetMapping(value = "queenExpressList")
    private String queenExpressList(@RequestParam String uid, @RequestParam int page) {
        logger.info("expressList uid={} page={}", uid, page);
        return createResult(HttpCode.SUCCESS, queenHallService.queenExpressList(uid, page));
    }

    @RequestMapping(value = "expressQueen", method = RequestMethod.POST)
    private String expressQueen(@RequestBody QueenLikeDTO queenLikeDTO) {
        String uid = queenLikeDTO.getUid();
        String aid = queenLikeDTO.getAid();
        String content = queenLikeDTO.getContent();
        logger.info("expressQueen uid={} aid={}  content={}", uid, aid, content);
        return createResult(HttpCode.SUCCESS, queenHallService.expressQueen(uid, aid, content));
    }

    @RequestMapping(value = "likeQueen", method = RequestMethod.POST)
    private String likeQueen(@RequestBody QueenLikeDTO queenLikeDTO) {
        String uid = queenLikeDTO.getUid();
        String aid = queenLikeDTO.getAid();
        logger.info("likeQueen uid={} aid={}", uid, aid);
        return createResult(ActivityHttpCode.SUCCESS_RESULT, queenHallService.likeQueen(uid, aid));
    }

    @RequestMapping(value = "likeExpress", method = RequestMethod.POST)
    private String likeExpress(@RequestBody QueenLikeDTO queenLikeDTO) {
        String uid = queenLikeDTO.getUid();
        int likeId = queenLikeDTO.getLikeId();
        logger.info("likeQueen uid={} likeId={}", uid, likeId);
        return createResult(HttpCode.SUCCESS, queenHallService.likeExpress(uid, likeId));
    }

    @GetMapping("queenDiscountList")
    private String queenDiscountList(@RequestParam String uid) {
        logger.info("queenDiscount uid={} ", uid);
        return createResult(HttpCode.SUCCESS, queenHallService.queenDiscountList(uid));
    }

    @GetMapping("getQueenDiscount")
    private String getQueenDiscount(@RequestParam String uid, @RequestParam String discountNum) {
        logger.info("queenDiscount uid={} discountNum={}", uid, discountNum);
        return createResult(HttpCode.SUCCESS, queenHallService.getQueenDiscount(uid, discountNum));
    }

    @GetMapping("giveQueenDiscount")
    private String giveQueenDiscount(@RequestParam String uid, @RequestParam String recordId, @RequestParam int giveRid) {
        logger.info("queenDiscount uid={} recordId={}, giveRid={}", uid, recordId, giveRid);
        return createResult(HttpCode.SUCCESS, queenHallService.giveQueenDiscount(uid, recordId, giveRid));
    }

    /**
     * 配置类活动排行榜
     */
    @GetMapping("otherRankConfig")
    private String otherRankConfig(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("otherRankTime uid={} activityId={}", uid, activityId);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, otherActivityService.otherRankConfig(uid, activityId));
    }

    @GetMapping("otherRanking")
    private String otherRanking(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute, @RequestParam(defaultValue = "10") int rankNum, @RequestParam(defaultValue = "0") int gloryInfo) {
        logger.info("otherRanking uid={} activityId={} rankingAttribute={} ", uid, activityId, rankingAttribute);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, otherActivityService.otherRanking(uid, activityId, rankingAttribute, rankNum, gloryInfo));
    }


    @GetMapping("otherDailyRanking")
    private String otherDailyRanking(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute, @RequestParam(defaultValue = "10") int rankNum) {
        logger.info("otherRanking uid={} activityId={} rankingAttribute={} ", uid, activityId, rankingAttribute);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, otherActivityService.otherDailyRanking(uid, activityId, rankingAttribute, rankNum));
    }

    /**
     * 配置类活动等级奖励个人数据
     */
    @GetMapping("otherRankReaching")
    private String otherRankReaching(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute) {
        logger.info("otherRankReaching uid={} activityId={} rankingAttribute={}", uid, activityId, rankingAttribute);
        return createResult(HttpCode.SUCCESS, otherActivityService.otherRankReaching(uid, activityId, rankingAttribute));
    }


    /**
     * 隐藏排行榜
     */
    @GetMapping("hiddenRanking")
    private String hiddenRanking(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute) {
        logger.info("hiddenRanking uid={} activityId={} rankingAttribute={} ", uid, activityId, rankingAttribute);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, otherActivityService.hiddenRanking(uid, activityId, rankingAttribute));
    }

    /**
     * 高低分排行榜
     */
    @GetMapping("baseHighRanking")
    private String baseHighRanking(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute) {
        logger.info("hiddenRanking uid={} activityId={} rankingAttribute={} ", uid, activityId, rankingAttribute);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, otherActivityService.baseHighRanking(uid, activityId, rankingAttribute));
    }


    /**
     * 沙漠猎鹰发送榜单独接口
     */
    @GetMapping("desertFalconSendRanking")
    private String desertFalconSendRanking(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute) {
        logger.info("otherRanking uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, desertFalconService.desertFalconRanking(uid, activityId, rankingAttribute));
    }


    /**
     * 国家荣誉
     */
    @GetMapping("honorNational")
    private String honorNationalConfig(@RequestParam String uid) {
        logger.info("honorNationalConfig uid={} ", uid);
        return createResult(HttpCode.SUCCESS, honorNationalService.getHonorNational(uid));
    }

    /**
     * 运营房活动
     */
    @GetMapping("activityRoomRanking")
    private String activityRoomRanking(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute) {
        logger.info("otherRanking uid={} activityId={} rankingAttribute={} ", uid, activityId, rankingAttribute);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, otherActivityService.hiddenRanking(uid, activityId, rankingAttribute));
    }

    /**
     * 骆驼竞赛
     */
    @GetMapping("camelLevel")
    private String camelLevel(@RequestParam String uid, @RequestParam String activityId, @RequestParam(defaultValue = "1") int zoneType) {
        logger.info("camelLevel uid={} activityId={} zoneType: {}", uid, activityId, zoneType);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, camelService.camelLevel(uid, activityId, 1, zoneType));
    }

    @GetMapping("camelRanking")
    private String camelRanking(@RequestParam String uid, @RequestParam String activityId, @RequestParam int rankingAttribute, @RequestParam(defaultValue = "10") int rankNum) {
        logger.info("camelRanking uid={} activityId={} rankingAttribute={} ", uid, activityId, rankingAttribute);
        activityId = activityId.trim();
        return createResult(HttpCode.SUCCESS, camelService.camelRanking(uid, activityId, rankingAttribute, rankNum));
    }

    /**
     * 答题活动相关配置
     */
    @GetMapping("quiz")
    private String quizActivity(@RequestParam String uid, @RequestParam Integer activityId) {
        logger.info("quiz activity. uid={} activityId={} ", uid, activityId);
        return createResult(HttpCode.SUCCESS, quizActivityService.quizActivity(uid, activityId));
    }

    /**
     * 获取题目列表
     */
    @GetMapping("getQuestionList")
    private String getQuestionList(@RequestParam String uid, @RequestParam Integer activityId, @RequestParam Integer slang) {
        long timeMillis = System.currentTimeMillis();
        QuizQuestionVO vo = quizActivityService.getQuestionList(uid, activityId, slang);
        logger.info("get quiz question list. uid={} activityId={} slang={} cost={}", uid, activityId, slang, System.currentTimeMillis() - timeMillis);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 提交回答
     */
    @RequestMapping(value = "submitQuiz", method = RequestMethod.POST)
    private String submitQuiz(@RequestBody QuizActivityDTO dto) {
        logger.info("submit answer. uid={} activityId={} time={}", dto.getUid(), dto.getActivityId(), dto.getTime());
        long time = System.currentTimeMillis();
        DistributeLock lock = new DistributeLock("quiz_" + dto.getActivityId() + "_" + dto.getUid());
        try {
            lock.lock();
            QuizResultVO vo = quizActivityService.submitAnswer(dto);
            logger.info("submit answer. cost={}", System.currentTimeMillis() - time);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (Exception e) {
            logger.error("submit answer error. uid={} activityId={} checkpoint={} time={}", dto.getUid(), dto.getActivityId(), dto.getCheckpointNo(), dto.getTime());
            return createResult(HttpCode.SERVER_ERROR, null);
        } finally {
            lock.unlock();
        }

    }

    /**
     * 获取闯关答题题目列表
     */
    @GetMapping("checkpoint/getQuestionList")
    private String getCheckpointQuestionList(@RequestParam String uid, @RequestParam Integer activityId, @RequestParam Integer checkpointNo, @RequestParam Integer slang, @RequestParam Integer quizRebirth) {
        logger.info("get quiz question list. uid={} activityId={} checkpointNo={} slang={} quizRebirth={}", uid, activityId, checkpointNo, slang, quizRebirth);
        QuizQuestionVO vo = checkpointQuizService.getQuestionList(uid, activityId, checkpointNo, slang, quizRebirth);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 获取闯关答题页详情
     */
    @GetMapping("checkpoint/getCheckpointPage")
    private String getCheckpointPage(@RequestParam String uid, @RequestParam Integer activityId) {
        long timeMillis = System.currentTimeMillis();
        CheckpointPageVO vo = checkpointQuizService.getCheckpointPage(uid, activityId);
        logger.info("get checkpoint page detail. uid={} activityId={} cost={}", uid, activityId, System.currentTimeMillis() - timeMillis);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 提交闯关答题回答
     */
    @RequestMapping(value = "checkpoint/submitQuiz", method = RequestMethod.POST)
    private String submitCheckpointQuiz(@RequestBody QuizActivityDTO dto) {
        logger.info("submit answer. uid={} activityId={} checkpoint={} time={}", dto.getUid(), dto.getActivityId(), dto.getCheckpointNo(), dto.getTime());
        long time = System.currentTimeMillis();
        DistributeLock lock = new DistributeLock("checkpointQuiz_" + dto.getActivityId() + "_" + dto.getUid());
        try {
            lock.lock();
            CheckpointQuizResultVO vo = checkpointQuizService.submitAnswer(dto);
            logger.info("submit answer. cost={}", System.currentTimeMillis() - time);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (Exception e) {
            logger.error("submit answer error. uid={} activityId={} checkpoint={} time={}", dto.getUid(), dto.getActivityId(), dto.getCheckpointNo(), dto.getTime());
            return createResult(HttpCode.SERVER_ERROR, null);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 钻石兑换金币
     */
    @GetMapping("checkpoint/buyCoins")
    private String buyCoins(@RequestParam String uid, @RequestParam Integer pid) {
        logger.info("buyCoins. uid={} pid={}", uid, pid);
        checkpointQuizService.buyCoins(uid, pid);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 答题活动分享到朋友圈
     */
    @GetMapping("checkpoint/share")
    private String quizShare(@RequestParam String uid, @RequestParam Integer activityId, @RequestParam String img, @RequestParam Integer slang) {
        logger.info("quizShare. uid={} activityId={} img={}", uid, activityId, img);
        checkpointQuizService.quizShare(uid, activityId, img, slang);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 闯关答题复活
     */
    @GetMapping("checkpoint/revive")
    private String quizRevive(@RequestParam String uid, @RequestParam Integer activityId, @RequestParam Integer checkpointNo) {
        logger.info("quizRevive. uid={} activityId={} checkpointNo={}", uid, activityId, checkpointNo);
        checkpointQuizService.quizRevive(uid, activityId, checkpointNo);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 闯关答题清数据(测试用)
     */
    @GetMapping("checkpoint/clear")
    private String quizClear(@RequestParam String uid, @RequestParam Integer activityId) {
        logger.info("quizClear. uid={} activityId={} ", uid, activityId);
        if (ServerConfig.isProduct()) {
            return createResult(HttpCode.SUCCESS, null);
        }
        checkpointQuizService.quizClear(uid, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 用户文件上传
     */
    @RequestMapping(value = "uploadOSS", method = RequestMethod.POST)
    private String uploadOSS(MultipartFile file) {
        long time = System.currentTimeMillis();
        String fileUrl = ossUploadService.upload(file);
        logger.info("uploadOSS. cost={}", System.currentTimeMillis() - time);
        return createResult(HttpCode.SUCCESS, fileUrl);
    }

    @RequestMapping(value = "uploadFileOSS", method = RequestMethod.POST)
    private String uploadFileOSS(HttpServletRequest request, MultipartFile file) {

        String token = request.getParameter("token");
        if (StringUtils.isEmpty(token)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        long time = System.currentTimeMillis();
        String fileUrl = ossUploadService.uploadWebFile(file, null, null);
        long costTime = System.currentTimeMillis() - time;

        logger.info("uploadFileOSS. cost={}", costTime);
        if (costTime > 16000L) {
            logger.info("uploadFileOSS fileUrl:{}", fileUrl);
        }

        return createResult(HttpCode.SUCCESS, fileUrl);
    }

    @RequestMapping(value = "uploadFileOSSLimit", method = RequestMethod.POST)
    private String uploadFileOSSLimit(HttpServletRequest request, MultipartFile file) {
        String token = request.getParameter("token");
        String uid = request.getParameter("uid");
        String fileType = request.getParameter("fileType"); // image  video
        String activityId = request.getParameter("activityId");
        String contentType = file.getContentType();
        logger.info("activityId:{} uid:{} fileType:{} " +
                        "fileSize:{}KB contentType:{} fileName:{} originalFilename:{}",
                activityId, uid, fileType,
                file.getSize() / 1024, file.getContentType(), file.getName(), file.getOriginalFilename());
        if (StringUtils.isEmpty(token)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        long fileLimitSize = FILE_LIMIT_SIZE;
        if (ArabicPainterService.ACTIVITY_ID.equals(activityId)) {
            fileLimitSize = 500 * 1024;
        }

        if (file.getSize() > fileLimitSize) {
            logger.info("file max return");
            throw new CommonH5Exception(ActivityHttpCode.ARABIC_PAINTER_MAX_FILE_LIMIT);
        }
//        if (SharingOfficerService.ACTIVITY_ID.equals(activityId)) {
//            if (sharingOfficerService.uploadLimit(activityId, uid, 2, fileType)) {
//                logger.info("file max return");
//                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
//            }
//        }
        long time = System.currentTimeMillis();
        String fileUrl = ossUploadService.uploadWebFile(file, contentType, OSSUploadService.WEB_PATH_2);
        long costTime = System.currentTimeMillis() - time;

        logger.info("uploadFileOSS.uid={} cost={} fileUrl:{}", uid, costTime, fileUrl);
        if (costTime > 16000L) {
            logger.info("long time -->uploadFileOSS fileUrl:{}", fileUrl);
        }
//        if (SharingOfficerService.ACTIVITY_ID.equals(activityId)) {
//            sharingOfficerService.uploadLimit(activityId, uid, 1, fileType);
//        }
        return createResult(HttpCode.SUCCESS, fileUrl);
    }


    @RequestMapping(value = "uploadBaseEncode", method = RequestMethod.POST)
    private String uploadBaseCode(@RequestBody UploadFileDTO dto) {
        long time = System.currentTimeMillis();
        String fileUrl = ossUploadService.uploadBaseCode(dto);
        long costTime = System.currentTimeMillis() - time;
        logger.info("uploadBaseEncode. cost={}", costTime);

        if (costTime > 16000L) {
            logger.info("uploadBaseCode fileUrl:{}", fileUrl);
        }
        return createResult(HttpCode.SUCCESS, fileUrl);
    }

    @RequestMapping("resourceKeyList")
    private String getResourceKeyList(@RequestParam String uid, @RequestBody ResourceKeyDTO dto) {
        logger.info("getResourceKeyList uid={} dto={} ", uid, JSONObject.toJSONString(dto));
        return createResult(HttpCode.SUCCESS, resourceKeyService.getResourceKeyList(uid, dto));
    }

    /**
     * 瓜分奖池活动页面信息
     */
    @GetMapping("/shareJackpot/pageInfo")
    private HttpResult<PrizePoolSharingVO> pageInfo(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("share jackpot activity page info. uid={} activityId={}", uid, activityId);
        return HttpResult.getOk(shareJackpotActivityService.pageInfo(activityId, uid));
    }

    /**
     * 瓜分奖池活动抽奖
     */
    @GetMapping("/shareJackpot/draw")
    private HttpResult<DrawResultVO> draw(@RequestParam String uid,
                                          @RequestParam String activityId,
                                          @RequestParam int drawNum,
                                          @RequestParam(required = false, defaultValue = "2") int slang,
                                          @RequestParam(required = false, defaultValue = "") String roomId
    ) {
        logger.info("share jackpot activity draw. uid={} activityId={} drawNum={} roomId={}", uid, activityId, drawNum, roomId);
        return HttpResult.getOk(shareJackpotActivityService.draw(activityId, uid, drawNum, slang, roomId));
    }

    /**
     * 瓜分奖池活动抽奖历史记录
     */
    @GetMapping("/shareJackpot/drawRecord")
    private HttpResult<PageVO<DrawHistoryVO>> drawRecord(@RequestParam String uid, @RequestParam String activityId, @RequestParam(required = false, defaultValue = "1") int page) {
        logger.info("get share jackpot activity draw record. uid={} activityId={} page={}", uid, activityId, page);
        return HttpResult.getOk(shareJackpotActivityService.getDrawRecord(activityId, uid, page));
    }

    /**
     * 回归活动，展示信息
     *
     * @param dto
     * @return
     */
    @RequestMapping("/backUser/getBackUserInfo")
    private String getBackUserInfo(@RequestBody BackUserDTO dto) {
        logger.info("getBackUserInfo uid={} dto={} ", dto.getUid(), JSONObject.toJSONString(dto));
        return createResult(HttpCode.SUCCESS, backUserCollectService.getBackUserInfo(dto));
    }

    /**
     * 回归活动，获取奖励
     *
     * @param dto
     * @return
     */
    @RequestMapping("/backUser/signBackUserDau")
    private String signBackUserDau(@RequestBody BackUserDTO dto) {
        logger.info("signBackUserDau uid={} dto={} ", dto.getUid(), JSONObject.toJSONString(dto));
        return createResult(HttpCode.SUCCESS, backUserCollectService.signBackUserDau(dto));
    }

    /**
     * 豪车俱乐部活动
     */
    @RequestMapping("luxuryCarInfo")
    private HttpResult<LuxuryCarVO> luxuryCarInfo(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("luxuryCarInfo. activityId={} uid={}", activityId, uid);
        return HttpResult.getOk(luxuryCarService.getInfo(activityId, uid));
    }

    /**
     * 恢复数据
     */
    @GetMapping("/fix/fixRankReaching")
    private String fixRankReaching(@RequestParam String uid, @RequestParam String activityId, @RequestParam String roomId,
                                   @RequestParam int isCollect) {
        long timeMillis = System.currentTimeMillis();
        operationRoomWashService.sendGiftHandleFix(roomId, isCollect);
        logger.info("fixRankReaching uid={} activityId={} cost={}", uid, activityId, System.currentTimeMillis() - timeMillis);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    @RequestMapping("/out/resourceKeyList")
    private String outGetResourceKeyList(@RequestParam String uid, @RequestBody ResourceKeyDTO dto) {
        logger.info("getResourceKeyList uid={} dto={} ", uid, JSONObject.toJSONString(dto));
        return createResult(HttpCode.SUCCESS, resourceKeyService.getResourceKeyList(uid, dto));
    }
}
