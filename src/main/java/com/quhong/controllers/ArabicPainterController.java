package com.quhong.controllers;

import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.ArabicPainterService;
import com.quhong.service.PainterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class ArabicPainterController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(ArabicPainterController.class);

    @Resource
    private ArabicPainterService arabicPainterService;

    @RequestMapping("arabicPainterConfig")
    private String painterConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, arabicPainterService.painterConfig(activityId, uid));
    }


    // 图片分享朋友圈
    @RequestMapping(value = "arabicPainterPicture", method = RequestMethod.POST)
    private String painterPicturePush(@RequestParam String activityId, @RequestParam String uid, @RequestBody PainterPictureDTO dto) {
        logger.info("painterPicturePush dto={}", dto);
        arabicPainterService.painterPicturePush(activityId, uid, dto);
        return createResult(HttpCode.SUCCESS, null);
    }


    // 开始鉴赏列表
    @RequestMapping(value = "arabicPainterDayCreateList")
    private String painterRanking(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, arabicPainterService.recommendDayCreateList(activityId, uid));
    }


    // 点赞作品
    @RequestMapping(value = "arabicPainterLike")
    private String arabicPainterLike(@RequestParam String activityId, @RequestParam String uid, @RequestParam String momentId) {
        logger.info("arabicPainterLike uid {}, momentId={}  ", uid, momentId);
        arabicPainterService.painterLike(activityId, uid, momentId);
        return createResult(HttpCode.SUCCESS, null);
    }


    // 给今日推荐文案点赞
    @RequestMapping(value = "arabicPainterMomentText")
    private String arabicPainterMomentText(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("arabicPainterMomentText uid {}, activityId={}   ", uid, activityId);
        arabicPainterService.painterLikeMomentText(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 测试偏移天数
     */
    @RequestMapping("arabicPainterTestUidDay")
    private String carromMasterTestUidDay(@RequestParam String activityId, @RequestParam String uid
            , @RequestParam int cmd, @RequestParam int addDays) {
        return createResult(HttpCode.SUCCESS, arabicPainterService.testUidDay(activityId,cmd, uid, addDays));
    }
}
