package com.quhong.controllers;


import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.H5Controller;
import com.quhong.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 2025女王活动
 */


@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class SuperQueen2025Controller extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(SuperQueen2025Controller.class);

    @Resource
    private SuperQueen2025Service superQueen2025Service;

    /**
     * 2025女王活动加入
     */
    @RequestMapping("queen2025Join")
    private String joinSuperQueenActivity(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.joinSuperQueenActivity(activityId, uid));
    }

    /**
     * 2025女王活动信息
     */
    @RequestMapping("queen2025Info")
    private String queen2025Info(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.queen2025Info(activityId, uid));
    }

    /**
     * 2025女王活动惊喜奖励通知开关
     */
    @RequestMapping("queen2025changeNoticeSw")
    private String changeNoticeSw(@RequestParam String activityId, @RequestParam String uid,
                                  @RequestParam int type, @RequestParam int state) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.changeNoticeSw(activityId, type, uid, state));
    }

    /**
     * 2025女王活动积分历史记录
     */
    @RequestMapping("queen2025HistoryRecord")
    private String historyRecordList(@RequestParam String activityId, @RequestParam String uid,
                                     @RequestParam int type, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.historyRecordList(activityId, type, uid, page));
    }

    /**
     * 2025女王榜单列表
     */
    @RequestMapping("queen2025List")
    private String queen2025List(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.queen2025List(activityId, uid));
    }


    /**
     * 2025女王活动季度荣誉榜单
     */
    @RequestMapping("queen2025HonorHallList")
    private String honorHallList(@RequestParam String activityId, @RequestParam String uid,
                                 @RequestParam int rankType, @RequestParam int month, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.honorHallList(activityId, uid, rankType, month, page));
    }


    /**
     * 2025女王活动季度荣誉个人信息
     */
    @RequestMapping("queen2025HonorHallMyInfo")
    private String honorHallMyInfo(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.honorHallMyInfo(activityId, uid));
    }

    /**
     * 2025女王活动季度荣誉获取奖励
     */
    @RequestMapping("queen2025HonorHallCollect")
    private String honorHallCollect(@RequestParam String activityId, @RequestParam String uid) {
        superQueen2025Service.honorHallCollect(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    /**
     * 2025女王活动季度荣誉分享朋友圈
     */
    @RequestMapping("queen2025HonorHallPicturePush")
    private String pictureQueenHallPush(@RequestParam String activityId, @RequestParam String uid,@RequestBody PainterPictureDTO dto) {
        superQueen2025Service.pictureQueenHallPush(activityId, uid, dto);
        return createResult(HttpCode.SUCCESS, null);
    }



    /**
     * 2025女王偏移天数
     */
    @RequestMapping("queen2025TestUidDay")
    private String queen2025TestUidDay(@RequestParam String uid, @RequestParam int cmd, @RequestParam int addDays
            , @RequestParam int addValue) {
        return createResult(HttpCode.SUCCESS, superQueen2025Service.testUidDay(cmd, uid, addDays, addValue));
    }

}
