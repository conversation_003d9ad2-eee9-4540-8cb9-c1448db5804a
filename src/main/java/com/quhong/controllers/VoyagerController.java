package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.RamadanActivityService;
import com.quhong.service.VoyagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 航海家活动
 */
@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class VoyagerController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(VoyagerController.class);

    @Resource
    private VoyagerService voyagerService;

    /**
     * 航海家配置
     */
    @RequestMapping(value = "voyagerConfig")
    private String voyagerConfig(@RequestParam String uid, @RequestParam String activityId, @RequestParam(defaultValue = "") String dateStr) {
        logger.info("voyagerConfig uid={} activityId={} dateStr={}", uid, activityId, dateStr);
        return createResult(HttpCode.SUCCESS, voyagerService.voyagerConfig(uid, activityId, dateStr));
    }


    // 修改船名称
    @RequestMapping(value = "updateBoatName")
    private String updateBoatName(@RequestParam String uid, @RequestParam String activityId, @RequestParam String boatName) {
        logger.info("updateBoatName uid={} activityId={} boatName={}", uid, activityId, boatName);
        voyagerService.updateBoatName(uid, activityId, boatName);
        return createResult(HttpCode.SUCCESS, null);
    }
}
