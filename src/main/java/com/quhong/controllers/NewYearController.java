package com.quhong.controllers;


import com.quhong.data.dto.NewYearExpectDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.NewYearService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class NewYearController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(NewYearController.class);

    @Resource
    private NewYearService newYearService;

    // 寄语首页
    @RequestMapping("newYearMessage")
    private String newYearMessage(@RequestParam String uid, @RequestParam String activityId) {
        return createResult(HttpCode.SUCCESS, newYearService.newYearMessage(uid, activityId));
    }

    // 寄语广场
    @RequestMapping("newYearHall")
    private String newYearHall(@RequestParam int page, @RequestParam(defaultValue = "") String momentId) {
        logger.info("NewYearHallDTO page:{}, momentId:{}", page, momentId);
        return createResult(HttpCode.SUCCESS, newYearService.newYearHall(page, momentId));
    }

    // 写寄语
    @RequestMapping(value ="newYearExpect", method = RequestMethod.POST)
    private String newYearExpect(@RequestBody NewYearExpectDTO dto) {
        logger.info("NewYearExpectDTO {}", dto);
        newYearService.newYearExpect(dto);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 寄语排行榜
    @RequestMapping("newYearExpectRanking")
    private String newYearExpectRanking(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, newYearService.newYearExpectRanking(uid));
    }

}
