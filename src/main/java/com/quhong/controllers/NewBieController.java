package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.H5Controller;
import com.quhong.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 杂七杂八的接口
 */


@RestController
@RequestMapping(value = "${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class NewBieController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(NewBieController.class);
    @Resource
    private NewbieStarService newbieStarService;
    @Resource
    private NewbieStarV2Service newbieStarV2Service;

    /**
     * 迎新房任务
     */
    @RequestMapping("newbieConfig")
    private String newbieConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, newbieStarService.newbieConfig(activityId, uid));
    }

    @RequestMapping("newbieSign")
    private String newbieSign(@RequestParam String activityId, @RequestParam String uid) {
        newbieStarService.newbieSign(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("newbieLiveConfig")
    private String newbieLiveConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, newbieStarService.newbieLiveConfig(activityId, uid));
    }

    // 领取奖励
    @RequestMapping("newbieLiveReward")
    private String newbieLiveReward(@RequestParam String activityId, @RequestParam String uid, @RequestParam int liveStatus, @RequestParam String resKey) {
        newbieStarService.newbieLiveReward(activityId, uid, liveStatus, resKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequestMapping("newbieLiveHighReward")
    private String newbieLiveHighReward(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskKey) {
        newbieStarService.newbieLiveHighReward(activityId, uid, taskKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 设置开播日期
    @RequestMapping("newbieSetLiveTime")
    private String newbieSetLiveTime(@RequestParam String activityId, @RequestParam String uid, @RequestParam int start, @RequestParam int end, @RequestParam int alarm) {
        newbieStarService.newbieSetLiveTime(activityId, uid, start, end, alarm);
        return createResult(HttpCode.SUCCESS, "");
    }



    @RequestMapping("newbieConfigV2")
    private String newbieConfigV2(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, newbieStarV2Service.newbieConfig(activityId, uid));
    }

    @RequestMapping("newbieSignV2")
    private String newbieSignV2(@RequestParam String activityId, @RequestParam String uid) {
        newbieStarV2Service.newbieSign(activityId, uid);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("newbieLiveConfigV2")
    private String newbieLiveConfigV2(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, newbieStarV2Service.newbieLiveConfig(activityId, uid));
    }

    // 领取每日任务奖励
    @RequestMapping("newbieLiveRewardV2")
    private String newbieLiveRewardV2(@RequestParam String activityId, @RequestParam String uid, @RequestParam int liveStatus, @RequestParam String resKey) {
        newbieStarV2Service.newbieLiveReward(activityId, uid, liveStatus, resKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    @RequestMapping("newbieLiveSetContinueNum")
    private String newbieLiveSetContinueNum(@RequestParam String activityId, @RequestParam String aid) {
        newbieStarV2Service.newbieLiveSetContinueNum(activityId, aid);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 领取进阶任务奖励
    @RequestMapping("newbieLiveHighRewardV2")
    private String newbieLiveHighRewardV2(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskKey) {
        newbieStarV2Service.newbieLiveHighReward(activityId, uid, taskKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 领取邀请新用户挑战奖励
    @RequestMapping("newbieNewUserRewardV2")
    private String newbieNewUserRewardV2(@RequestParam String activityId, @RequestParam String uid, @RequestParam String taskKey) {
        newbieStarV2Service.newbieNewUserRewardV2(activityId, uid, taskKey);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 设置开播日期
    @RequestMapping("newbieSetLiveTimeV2")
    private String newbieSetLiveTimeV2(@RequestParam String activityId, @RequestParam String uid, @RequestParam String startHMS, @RequestParam String endHMS, @RequestParam String alarm) {
        newbieStarV2Service.newbieSetLiveTime(activityId, uid, startHMS, endHMS, alarm);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 领取开播在麦位时长奖励
    @RequestMapping("newbieOnMicRewardV2")
    private String newbieOnMicRewardV2(@RequestParam String activityId, @RequestParam String uid) {
        newbieStarV2Service.newbieOnMicReward(activityId, uid);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 同国家新用户列表
    @RequestMapping("newbieNewUserListV2")
    private String newbieNewUserListV2(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, newbieStarV2Service.newbieNewUserListV2(activityId, uid, page));
    }

    @RequestMapping("newbieInviteV2")
    private String newbieInviteV2(@RequestParam String activityId, @RequestParam String uid, @RequestParam String aid) {
        newbieStarV2Service.newbieInviteV2(activityId, uid, aid);
        return createResult(HttpCode.SUCCESS, "");
    }

    // 在房主上麦超过5分钟的用户
    @RequestMapping("newbieNewDevoteUserListV2")
    private String newbieNewDevoteUserListV2(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, newbieStarV2Service.newbieNewDevoteUserListV2(activityId, uid));
    }

    // 房主下发奖励给用户
    @RequestMapping("newbieHostSendRewardV2")
    private String newbieHostSendRewardV2(@RequestParam String activityId, @RequestParam String uid, @RequestParam String aid) {
        newbieStarV2Service.newbieHostSendReward(activityId, uid, aid);
        return createResult(HttpCode.SUCCESS, "");
    }


}
