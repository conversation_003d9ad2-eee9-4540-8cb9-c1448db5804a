package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.KnightsActivityService;
import com.quhong.service.LionActivityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 狮子王
 */
@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class FightController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(FightController.class);

    @Resource
    private KnightsActivityService knightsActivityService;

    @RequestMapping(value = "fightConfig")
    private String fightConfig(@RequestParam String uid, @RequestParam String activityId) {
        logger.info("fightConfig uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, knightsActivityService.fightConfig(uid, activityId));
    }

    @RequestMapping(value = "fightCheck")
    private String fightCheck(@RequestParam String searchId, @RequestParam String activityId) {
        logger.info("fightCheck searchId={} activityId={}", searchId, activityId);
        knightsActivityService.fightCheck(searchId, activityId);
        return createResult(HttpCode.SUCCESS, null);
    }

}
