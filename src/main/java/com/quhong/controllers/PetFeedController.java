package com.quhong.controllers;

import com.quhong.data.vo.MiningInfoVO;
import com.quhong.data.vo.MiningRecordVO;
import com.quhong.data.vo.MiningVO;
import com.quhong.data.vo.PetFeedVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.MiningService;
import com.quhong.service.PetFeedService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 养宠活动
 */
@RestController
@RequestMapping(value = "${baseUrl}petFeed/", produces = MediaType.APPLICATION_JSON_VALUE)
public class PetFeedController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private PetFeedService petFeedService;

    /**
     * 获取宠物
     */
    @RequestMapping("getPet")
    private HttpResult<?> getPet(@RequestParam String activityId, @RequestParam String uid
            , @RequestParam String petName, @RequestParam int petType) {
        petFeedService.getPet(activityId, uid, petName, petType);
        return HttpResult.getOk();
    }

    /**
     * 宠物基本信息
     */
    @RequestMapping("petFeedConfig")
    private HttpResult<PetFeedVO> petFeedConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(petFeedService.petFeedConfig(activityId, uid));
    }

    /**
     * 喂养宠物
     * @param activityId
     * @param uid
     * @param feedType 1 喂养 2 玩耍
     * @param amount 数量  喂食只能-1或100 -1表示全部喂食(只有喂养自己宠物时有此选项)或100食物, 玩耍只能是1
     * @param aid 目标用户
     *
     */
    @RequestMapping("feedPet")
    private HttpResult<PetFeedVO> feedPet(@RequestParam String activityId, @RequestParam String uid
            , @RequestParam int feedType, @RequestParam int amount, @RequestParam(required = false) String aid) {
        return HttpResult.getOk(petFeedService.feedPet(activityId, uid, feedType, amount, aid));
    }


    /**
     * 放养宠物
     */
    @RequestMapping("releasePet")
    private HttpResult<?> releasePet(@RequestParam String activityId, @RequestParam String uid) {
        petFeedService.releasePet(activityId, uid);
        return HttpResult.getOk();
    }

    /**
     * 兑换奖励
     */
    @RequestMapping("exchange")
    private HttpResult<?> exchange(@RequestParam String activityId, @RequestParam String uid, @RequestParam String metaId) {
        petFeedService.exchange(activityId, uid, metaId);
        return HttpResult.getOk();
    }

    /**
     * 历史记录
     */
    @RequestMapping("historyRecord")
    private HttpResult<PetFeedVO.HistoryRecordPageVO> historyRecord(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return HttpResult.getOk(petFeedService.getHistoryListPageRecord(activityId, uid, page));
    }

    /**
     * 我的好友/随便逛逛
     */
    @RequestMapping("petUserList")
    private HttpResult<PageVO<PetFeedVO.PetUserVO>> petUserList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int type, @RequestParam int page) {
        return HttpResult.getOk(petFeedService.petUserList(activityId, uid, type, page));
    }

    /**
     * 分享到snapchat
     */
    @RequestMapping("setShareSnapchat")
    public HttpResult<?> setShareSnapchat(@RequestParam String activityId, @RequestParam String uid) {
        petFeedService.setShareSnapchat(activityId, uid);
        return HttpResult.getOk();
    }

    /**
     * 设置签到提醒
     */
    @RequestMapping("setSignReminder")
    public HttpResult<?> setSignReminder(@RequestParam String activityId, @RequestParam String uid, @RequestParam int status) {
        petFeedService.setSignReminder(activityId, uid, status);
        return HttpResult.getOk();
    }

}