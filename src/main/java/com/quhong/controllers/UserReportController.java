package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.clients.AWSUploader;
import com.quhong.data.dto.UserFeedbackDTO;
import com.quhong.data.dto.UserReportDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.mysql.dao.UserReportDao;
import com.quhong.mysql.dao.UserReportNumDao;
import com.quhong.service.UserReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


@RestController
@RequestMapping(value ="${baseUrl}")
public class UserReportController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(UserReportController.class);
    @Resource
    private UserReportService userReportService;

    /**
     * 举报统一接口
     */
    @RequestMapping(value = "userReport", method = RequestMethod.POST)
    public String userReport(UserReportDTO dto, MultipartFile[] images) {
        long millis = System.currentTimeMillis();
        logger.info("userReport: dto: {}", JSONObject.toJSONString(dto));
        userReportService.userReport(dto, images);
        return createResult(HttpCode.SUCCESS, null);
    }
}
