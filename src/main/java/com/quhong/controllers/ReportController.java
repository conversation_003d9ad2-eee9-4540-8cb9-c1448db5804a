package com.quhong.controllers;

import com.quhong.clients.AWSUploader;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.CommonReportDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.mongo.dao.MReportDao;
import com.quhong.mongo.data.MReportData;
import com.quhong.redis.FriendsListRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/14
 */
@RestController
@RequestMapping(value ="${baseUrl}")
public class ReportController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(ReportController.class);

    @Resource
    private AWSUploader awsUploader;
    @Resource
    private MReportDao mReportDao;

    /**
     * 举报其他用户
     */
    @RequestMapping(value = "/common/report/new", method = RequestMethod.POST)
    public String commonReportNew(CommonReportDTO dto, MultipartFile[] images) {
        logger.info("get common report. uid={} requestId={}", dto.getUid(), dto.getRequestId());
        String uid = dto.getUid();
        String roomId = dto.getRoom_id() != null ? dto.getRoom_id() : "";
        String mid = dto.getMid() != null ? dto.getMid() : "";
        String cid =  dto.getCid() != null ? dto.getCid() : "";
        String aid = dto.getAid() != null ? dto.getAid() : "";
        String targetId = aid + roomId + mid + cid;
        String remark = dto.getRemark() != null ? dto.getRemark().trim() : "";
        if (StringUtils.isEmpty(targetId)) {
            logger.info("Parameter error, no target specified. uid={} roomId={} targetId={}", uid, roomId, targetId);
            createResult(ActivityHttpCode.NO_TARGET_SPECIFIED, null);
        }
        if (StringUtils.isEmpty(remark)) {
            logger.info("Please explain the reason for the report. uid={} roomId={} remark={}", uid, roomId, remark);
            createResult(ActivityHttpCode.EXPLAIN_THE_REASON_FOR_THE_REPORT, null);
        }
        if (remark.length() > 200) {
            logger.info("Sorry, the text you posted is too long. uid={} roomId={} remark={}", uid, roomId, remark);
            createResult(ActivityHttpCode.TEXT_YOU_POSTED_IS_TOO_LONG, null);
        }
        if (images != null && images.length > 3) {
            logger.info("Image upload failed, please resubmit. uid={} roomId={}", uid, roomId);
            createResult(ActivityHttpCode.IMAGE_UPLOAD_FAILED, null);
        }
        MReportData reportData = mReportDao.findData(uid, targetId);
        // 两天内不能重复举报
        int twoDayTime = 2 * 24 * 3600;
        int nowTime = DateHelper.getNowSeconds();
        if (reportData != null && nowTime - reportData.getMtime() < twoDayTime) {
            logger.info("You have already reported this user. Thank you for your support for our work! uid={} roomId={}", uid, roomId);
            createResult(ActivityHttpCode.ALREADY_REPORTED_THIS_USER, null);
        }
        List<String> imgUrls = new ArrayList<>();
        if (images != null && images.length > 0) {
            for (MultipartFile file : images) {
                if (file == null || file.isEmpty()) {
                    continue;
                }
                String url = awsUploader.updateLoad(file, "common_report/" + uid + "/" + DateHelper.getNowSeconds() + file.getName());
                logger.info("url={}", url);
                if (!StringUtils.isEmpty(url)) {
                    url = createCdnUrl(url);
                    imgUrls.add(url);
                } else {
                    logger.info("Image upload failed, please resubmit. uid={} roomId={}", uid, roomId);
                    createResult(ActivityHttpCode.IMAGE_UPLOAD_FAILED, null);
                }
            }
        }

        int new_type = 0;

        if(!StringUtils.isEmpty(mid)){
            if(mid.length() == 24){
                new_type = 3;
            }else {
                new_type = 4;
            }
        } else if (StringUtils.isEmpty(mid) && !StringUtils.isEmpty(roomId)) {
            new_type = 2;

        }else if (StringUtils.isEmpty(mid) && StringUtils.isEmpty(roomId) && !StringUtils.isEmpty(aid)) {
            new_type = 1;
        }


        MReportData data = new MReportData();
        data.setUid(uid);
        data.setAid(aid);
        data.setReason(dto.getReason());
        data.setRemark(remark);
        data.setRoom_id(roomId);
        data.setMid(mid);
        data.setCid(cid);
        data.setImg_list(imgUrls);
        data.setNew_type(new_type);
        data.setTarget_id(targetId);
        data.setMtime(nowTime);
        mReportDao.save(data);
        return createResult(HttpCode.SUCCESS, null);
    }

    private String createCdnUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        url = url.replaceAll("https://qhcf.s3.ap-southeast-1.amazonaws.com", "https://cdn3.qmovies.tv");
        return url;
    }
}
