package com.quhong.controllers;

import com.quhong.data.vo.DrawRecordVO;
import com.quhong.data.vo.MonsterHuntingVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.service.MonsterHuntingService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 怪兽狩猎活动
 */
@RestController
@RequestMapping("${baseUrl}")
public class MonsterHuntingController {

    private static final Logger logger = LoggerFactory.getLogger(MonsterHuntingController.class);

    @Resource
    private MonsterHuntingService monsterHuntingService;

    /**
     * 活动信息
     */
    @RequestMapping("monsterHunting/info")
    private HttpResult<MonsterHuntingVO> getInfo(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("getInfo. activityId={} uid={}", activityId, uid);
        return HttpResult.getOk(monsterHuntingService.getInfo(activityId, uid));
    }

    /**
     * 攻击怪兽
     */
    @RequestMapping("monsterHunting/attackMonster")
    private HttpResult<MonsterHuntingVO> attackMonster(@RequestParam String activityId, @RequestParam String uid, @RequestParam Integer num) {
        logger.info("attackMonster. activityId={} uid={} num={}", activityId, uid, num);
        if (num == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (num != 1 && num != 10 && num != 50) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return HttpResult.getOk(monsterHuntingService.attackMonster(activityId, uid, num));
    }

    /**
     * 奖励记录
     */
    @RequestMapping("monsterHunting/rewardRecord")
    private HttpResult<PageVO<DrawRecordVO>> getRewardRecord(@RequestParam String activityId,
                                                                @RequestParam String uid,
                                                                @RequestParam(defaultValue = "1") Integer page) {
        logger.info("getRewardRecord. activityId={} uid={} page={}", activityId, uid, page);
        return HttpResult.getOk(monsterHuntingService.getRewardRecord(activityId, uid, page));
    }
}
