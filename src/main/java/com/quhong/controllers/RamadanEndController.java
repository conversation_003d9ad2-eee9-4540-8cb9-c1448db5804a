package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.RamadanActivityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 2023开斋活动
 * @date 2023/3/9
 */
@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class RamadanEndController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(RamadanEndController.class);

    @Resource
    private RamadanActivityService ramadanActivityService;

    /**
     * 开斋配置相关
     */
    @RequestMapping(value = "ramadanConfig")
    private String ramadanConfig(@RequestParam String uid, @RequestParam String activityId, @RequestParam int slang) {
        logger.info("ramadanConfig uid={} activityId={}", uid, activityId);
        return createResult(HttpCode.SUCCESS, ramadanActivityService.ramadanConfig(uid, activityId, slang));
    }

    // 点赞
    @RequestMapping("ramadanLike")
    private String ramadanLike(@RequestParam String uid, @RequestParam String activityId, @RequestParam String foodKey) {
        logger.info("ramadanLike uid={} activityId={} foodKey={}", uid, activityId, foodKey);
        ramadanActivityService.ramadanEndLike(uid, activityId, foodKey);
        return createResult(HttpCode.SUCCESS, null);
    }

    // 开宝箱
    @RequestMapping("ramadanBox")
    private String ramadanBox(@RequestParam String uid, @RequestParam String activityId, @RequestParam String boxKey, @RequestParam int slang) {
        logger.info("ramadanBox uid={} activityId={} boxKey={}", uid, activityId, boxKey);
        return createResult(HttpCode.SUCCESS, ramadanActivityService.ramadanBox(uid, activityId, boxKey, slang));
    }


    // 图片分享朋友圈
    @RequestMapping(value ="ramadanPicture")
    private String ramadanPicturePush(@RequestParam String uid, @RequestParam String activityId, @RequestParam String picture, @RequestParam int slang) {
        logger.info("ramadanPicturePush uid={}, activityId={}, picture={}", uid, activityId, picture);
        ramadanActivityService.ramadanPicturePush( uid, activityId, picture, slang);
        return createResult(HttpCode.SUCCESS, null);
    }

    @RequestMapping("ramadanRecord")
    private String ramadanRecord(@RequestParam String uid, @RequestParam int page, @RequestParam int slang) {
        return createResult(HttpCode.SUCCESS, ramadanActivityService.ramadanRecord(uid, page, slang));
    }


}
