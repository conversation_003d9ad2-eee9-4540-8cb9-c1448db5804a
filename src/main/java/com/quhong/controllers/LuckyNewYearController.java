package com.quhong.controllers;


import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.GiftWishService;
import com.quhong.service.LuckyNewYearService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 新年幸运色
 */


@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class LuckyNewYearController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(LuckyNewYearController.class);

    @Resource
    private LuckyNewYearService luckyNewYearService;


    @RequestMapping("luckyNewYearConfig")
    private String luckyNewYearConfig(@RequestParam String activityId, @RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, luckyNewYearService.luckyNewYearConfig(activityId, uid));
    }


    @RequestMapping("luckyNewYearSelectTeam")
    private String luckyNewYearSelectTeam(@RequestParam String activityId, @RequestParam String uid, @RequestParam String selectTeam) {
        luckyNewYearService.luckyNewYearSelectTeam(activityId, uid, selectTeam);
        return createResult(HttpCode.SUCCESS, null);
    }


}
