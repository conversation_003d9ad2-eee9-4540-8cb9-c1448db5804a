package com.quhong.controllers;

import com.quhong.data.dto.TreasureDrawDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.service.TreasureService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 开宝箱接口
 */

@RestController
@RequestMapping(value ="${baseUrl}", produces = MediaType.APPLICATION_JSON_VALUE)
public class TreasureController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(TreasureController.class);

    @Resource
    private TreasureService treasureService;

    @RequestMapping("treasureConfig")
    private String treasureConfig(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, treasureService.treasureConfig(uid));
    }


    @RequestMapping(value = "treasureDraw", method = RequestMethod.POST)
    private String treasureDraw(@RequestBody TreasureDrawDTO dto) {
        logger.info("treasureDraw uid={} treasureDrawType={}", dto.getUid(), dto.getDrawType());

        return createResult(HttpCode.SUCCESS, treasureService.treasureDraw(dto));
    }


    @RequestMapping("treasureDrawRecord")
    private String treasureDrawRecord(@RequestParam String uid, @RequestParam int page) {
        return createResult(HttpCode.SUCCESS, treasureService.treasureDrawRecord(uid, page));
    }

    @GetMapping("treasureRanking")
    private String treasureRanking(@RequestParam String uid) {
        return createResult(HttpCode.SUCCESS, treasureService.treasureRanking(uid));
    }

}
