package com.quhong.controllers;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.data.dto.VoteDTO;
import com.quhong.data.vo.VoteInfoVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.handler.WebController;
import com.quhong.service.VoteService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
@RestController
@RequestMapping(value = "${baseUrl}/vote/")
public class VoteController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(VoteController.class);

    @Resource
    private VoteService voteService;

    /**
     * 投票礼物列表
     */
    @RequestMapping("giftList")
    private String giftList(HttpServletRequest request) {
        VoteDTO req = RequestUtils.getSendData(request, VoteDTO.class);
        logger.info("get gift list. uid={} ", req.getUid());
        return createResult(req, HttpCode.SUCCESS, voteService.giftList());
    }

    /**
     * 创建投票
     */
    @RequestMapping("create")
    private String createVote(HttpServletRequest request) {
        VoteDTO req = RequestUtils.getSendData(request, VoteDTO.class);
        logger.info("create room vote. uid={} roomId={} type={}", req.getUid(), req.getRoomId(), req.getType());
        if (StringUtils.isEmpty(req.getRoomId())) {
            logger.error("roomId is empty.");
            return createError(req, ActivityHttpCode.PARAM_ERROR);
        }
        return createResult(req, HttpCode.SUCCESS, voteService.createVote(req));
    }

    /**
     * 加入房间时检查是否存在投票
     */
    @PostMapping("check")
    public String checkVote(HttpServletRequest request) {
        VoteDTO req = RequestUtils.getSendData(request, VoteDTO.class);
        return createResult(req, HttpCode.SUCCESS, voteService.checkVote(req));
    }

    /**
     * 问答投票
     */
    @PostMapping("quizVote")
    public String quizVote(HttpServletRequest request) {
        VoteDTO req = RequestUtils.getSendData(request, VoteDTO.class);
        logger.info("room quiz vote. roomId={} uid={} voteId={}", req.getRoomId(), req.getUid(), req.getVoteId());
        long time = System.currentTimeMillis();
        DistributeLock lock = new DistributeLock("quizVote_" + req.getVoteId());
        try {
            lock.lock();
            VoteInfoVO voteInfoVO = voteService.quizVote(req);
            logger.info("room quiz vote. cost={}", System.currentTimeMillis() - time);
            return createResult(req, HttpCode.SUCCESS, voteInfoVO);
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("room quiz vote error. roomId={} uid={} voteId={}", req.getRoomId(), req.getUid(), req.getVoteId());
            return createResult(HttpCode.SERVER_ERROR, null);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 结束投票
     */
    @PostMapping("close")
    public String closeVote(HttpServletRequest request) {
        VoteDTO req = RequestUtils.getSendData(request, VoteDTO.class);
        logger.info("close room vote. roomId={} uid={} ", req.getRoomId(), req.getUid());
        return createResult(req, HttpCode.SUCCESS, voteService.closeVote(req));
    }

    /**
     * 投票记录
     */
    @PostMapping("recordList")
    public String getVoteRecord(HttpServletRequest request) {
        VoteDTO req = RequestUtils.getSendData(request, VoteDTO.class);
        logger.info("get room vote record. roomId={} uid={} type={}", req.getRoomId(), req.getUid(), req.getType());
        return createResult(req, HttpCode.SUCCESS, voteService.getVoteRecord(req));
    }

    /**
     * 投票记录详情
     */
    @PostMapping("recordDetail")
    public String getVoteRecordDetail(HttpServletRequest request) {
        VoteDTO req = RequestUtils.getSendData(request, VoteDTO.class);
        logger.info("get room vote record detail. roomId={} uid={} voteId={}", req.getRoomId(), req.getUid(), req.getVoteId());
        return createResult(req, HttpCode.SUCCESS, voteService.getVoteRecordDetail(req));
    }
}
