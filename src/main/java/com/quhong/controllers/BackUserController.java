package com.quhong.controllers;


import com.quhong.data.dto.BackUserDTO;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.BackUserInfoVO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.H5Controller;
import com.quhong.service.BackUserCollectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 活动接口
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class BackUserController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(BackUserController.class);

    @Resource
    private BackUserCollectService backUserCollectService;



    /**
     * 回归活动，邀请者展示信息
     *
     * @return
     */
    @RequestMapping("backUser/inviterHomeInfo")
    private  HttpResult<BackUserInfoVO>  inviterHomeInfo(@RequestParam String activityId, @RequestParam String uid,@RequestParam int page) {
        return HttpResult.getOk(backUserCollectService.inviterHomeInfo(activityId, uid, page));
    }


    @RequestMapping("backUser/getInviterDetail")
    private  HttpResult<BackUserInfoVO>  getInviterDetail(@RequestParam String activityId, @RequestParam String uid,@RequestParam int page) {
        return HttpResult.getOk(backUserCollectService.getInviterDetail(activityId, uid, page));
    }

    /**
     * 获取邀请者排行榜
     *
     * @param activityId 活动ID
     * @param uid 用户ID
     * @param page 页码
     * @return 排行榜信息
     */
    @RequestMapping("backUser/getInviterRank")
    public HttpResult<BackUserInfoVO> getInviterRank(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        logger.info("getInviterRank activityId={} uid={} page={}", activityId, uid, page);
        BackUserInfoVO vo = backUserCollectService.getInviterRank(activityId, uid, page);
        return HttpResult.getOk(vo);
    }

    /**
     * 通过邀请码绑定用户
     *
     * @param req 请求参数，包含用户ID和邀请码
     * @return 绑定结果
     */
    @RequestMapping("backUser/bindUidByCode")
    public HttpResult<BackUserInfoVO> bindUidByCode(@RequestBody BackUserDTO req) {
        logger.info("bindUidByCode uid={} inviteCode={}", req.getUid(), req.getInviteCode());
        BackUserInfoVO vo = backUserCollectService.bindUidByCode(req);
        return HttpResult.getOk(vo);
    }

    /**
     * 分享活动链接给好友
     *
     * @param dto 分享活动数据
     * @return 分享结果
     */
    @RequestMapping("backUser/shareToPrivateMsg")
    public HttpResult<?> shareToPrivateMsg(@RequestBody ShareActivityDTO dto) {
        logger.info("shareToPrivateMsg uid={} aid={} activityId={}", dto.getUid(), dto.getAid(), dto.getActivity_id());
        backUserCollectService.shareToPrivateMsg(dto);
        return HttpResult.getOk();
    }

    @RequestMapping("backUser/setShareSnapchat")
    public HttpResult<?> setShareSnapchat(@RequestParam String activityId, @RequestParam String uid) {
        logger.info("getInviterRank activityId={} uid={} page={}", activityId, uid);
        backUserCollectService.setShareSnapchat(activityId, uid);
        return HttpResult.getOk();
    }

}
