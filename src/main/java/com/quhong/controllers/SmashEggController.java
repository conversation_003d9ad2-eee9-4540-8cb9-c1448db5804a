package com.quhong.controllers;

import com.quhong.data.dto.SmashEggDTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.service.SmashEggService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 砸蛋IOS的接口
 */


@RestController
@RequestMapping(value ="${baseUrl}")
public class SmashEggController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(SmashEggController.class);

    @Resource
    private SmashEggService smashEggService;

    @RequestMapping("ios_smash_egg/home_info")
    private String iosSmashEggHomeInfo(HttpServletRequest request) {
        HttpEnvData req = RequestUtils.getSendData(request, HttpEnvData.class);

        logger.info("iosSmashEggHomeInfo: uid: {}", req.getUid());
        return createResult(req, HttpCode.SUCCESS, smashEggService.smashEggHomeInfo(req));
    }

    @RequestMapping("ios_smash_egg/personal")
    private String iosSmashEggPersonal(HttpServletRequest request) {
        SmashEggDTO dto = RequestUtils.getSendData(request, SmashEggDTO.class);
        logger.info("iosSmashEggPersonal: uid: {}, page: {}", dto.getUid(), dto.getPage());
        return createResult(dto, HttpCode.SUCCESS, smashEggService.iosSmashEggPersonal(dto));
    }

    @RequestMapping("ios_smash_egg/ranking")
    private String iosSmashEggRanking(HttpServletRequest request) {
        SmashEggDTO dto = RequestUtils.getSendData(request, SmashEggDTO.class);
        logger.info("iosSmashEggPersonal: uid: {}, rank_type: {}", dto.getUid(), dto.getRank_type());
        return createResult(dto, HttpCode.SUCCESS, smashEggService.iosSmashEggRanking(dto));
    }

    @RequestMapping("ios_smash_egg/play")
    private String iosSmashEggPlay(HttpServletRequest request) {
        SmashEggDTO dto = RequestUtils.getSendData(request, SmashEggDTO.class);
        logger.info("iosSmashEggPlay: uid: {}, amount: {}", dto.getUid(), dto.getAmount());
        return createResult(dto, HttpCode.SUCCESS, smashEggService.iosSmashEggPlay(dto));
    }

}
