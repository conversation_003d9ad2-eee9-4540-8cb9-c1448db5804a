package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.data.ActorData;
import com.quhong.data.vo.GameKingDetailVO;
import com.quhong.data.vo.GameKingRankingVO;
import com.quhong.data.vo.RankVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.PackConfigDao;
import com.quhong.mongo.data.PackConfigData;
import com.quhong.mongo.data.PackData;
import com.quhong.redis.GameKingRedis;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
@Service
public class GameKingService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final int MIN_DIAMOND_LIMIT = 1000000; // 最小上榜和获得奖励的钻石数

    public static final Map<Integer, String> REWARD_CONFIG_MAP = new LinkedHashMap<>();

    private static final List<GameKingDetailVO.Game> GAME_LIST = Arrays.asList(
            // new GameKingDetailVO.Game("Horse Racing", "سباق الخيل", "https://cdn3.qmovies.tv/youstar/op_1709101470_Horse_Racing.png"),
            new GameKingDetailVO.Game("Crash", "تحطم", "https://cdn3.qmovies.tv/common/op_1730429628_op_1723630281_logo.png"),
            // new GameKingDetailVO.Game("Fruit Party", "حفل الفواكه", "https://cdn3.qmovies.tv/youstar/op_1709101470_Fruit_Party.png"),
            new GameKingDetailVO.Game("Slots", "سلوتس", "https://cdn3.qmovies.tv/youstar/op_1709101470_Lava_Slot.png"),
            new GameKingDetailVO.Game("Fishing Star", "نجم الصيد", "https://cdn3.qmovies.tv/youstar/op_1709101470_Fishing.png"),
            // new GameKingDetailVO.Game("Fast 3", "سريع 3", "https://cdn3.qmovies.tv/youstar/op_1709277766_Fast_3.png"),
            new GameKingDetailVO.Game("Greedy", "جشع", "https://cdn3.qmovies.tv/common/op_1716447470_room-banner.png"),
            // new GameKingDetailVO.Game("Lucky Fishing", "سمكة محظوظة", "https://cdn3.qmovies.tv/common/op_1750928872_icon.png")
            // new GameKingDetailVO.Game("Forest Party", "حزب الغابة", "https://cdn3.qmovies.tv/common/op_1718175095_icon.png")
             new GameKingDetailVO.Game("Lucky Fruit", "فاكهة الحظ", "https://cdn3.qmovies.tv/common/op_1752655940_icon.png")
    );

    @Resource
    private PackConfigDao packConfigDao;
    @Resource
    private GameKingRedis gameKingRedis;
    @Resource
    private ActorDao actorDao;

    @PostConstruct
    public void init() {
        REWARD_CONFIG_MAP.put(30000000, "game_king_3000w");
        REWARD_CONFIG_MAP.put(15000000, "game_king_1500w");
        REWARD_CONFIG_MAP.put(10000000, "game_king_1000w");
        REWARD_CONFIG_MAP.put(1000000, "game_king_100w");
    }

    /**
     * 获取活动详情
     */
    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            condition = "T(com.quhong.core.config.ServerConfig).isProduct()",
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public GameKingDetailVO getDetailInfo(int slang) {
        GameKingDetailVO vo = new GameKingDetailVO();
        vo.setLastWeekTop3(getRankingTop3(gameKingRedis.getLastWeekNum()));
        vo.setGameList(GAME_LIST);
        List<PackConfigData> packConfigList = packConfigDao.findListByKeys(new HashSet<>(REWARD_CONFIG_MAP.values()));
        Map<String, PackConfigData> packConfigMap = CollectionUtil.listToKeyMap(packConfigList, PackConfigData::getKey);
        List<GameKingDetailVO.Reward> rewardList = new ArrayList<>();
        for (String key : REWARD_CONFIG_MAP.values()) {
            PackConfigData packConfigData = packConfigMap.get(key);
            if (packConfigData == null) {
                continue;
            }
            rewardList.add(new GameKingDetailVO.Reward(
                    slang == SLangType.ENGLISH ? packConfigData.getTitle() : packConfigData.getTitleAr(),
                    getRewardDetailList(slang, packConfigData)
            ));
        }
        vo.setRewardList(rewardList);
        return vo;
    }

    /**
     * 获取活动排行榜
     */
    public GameKingRankingVO getRankingList(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        GameKingRankingVO vo = new GameKingRankingVO();
        int curWeekNum = gameKingRedis.getCurWeekNum();
        vo.setRefreshTime(getRefreshTime());
        vo.setRankingList(getRankingList(curWeekNum, 20));
        vo.setMyRanking(getMyRanking(uid, curWeekNum, actorData, vo.getRankingList()));
        return vo;
    }

    private RankVO getMyRanking(String uid, int weekNum, ActorData actorData, List<RankVO> rankList) {
        if (CollectionUtils.isEmpty(rankList)) {
            return new RankVO(uid, actorData.getName(), ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()), "-", "0");
        }
        Map<String, RankVO> rankMap = CollectionUtil.listToKeyMap(rankList, RankVO::getUid);
        String strRank = "-";
        String rankingScore;
        if (rankMap.containsKey(uid)) {
            rankingScore = rankMap.get(uid).getScore();
            strRank = rankMap.get(uid).getRank();
        } else {
            rankingScore = gameKingRedis.getRankingScore(uid, weekNum) + "";
        }
        return new RankVO(uid, actorData.getName(), ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()), strRank, rankingScore + "");
    }

    private int getRefreshTime() {
        //程序里一周的最后一天是周六，不符合中国人习惯，所以+1
        Calendar end = Calendar.getInstance(TimeZone.getTimeZone("GMT+3:00"));
        end.set(Calendar.DAY_OF_WEEK, 7);
        end.set(Calendar.HOUR_OF_DAY, 23);
        end.set(Calendar.MINUTE, 59);
        end.set(Calendar.SECOND, 59);
        return  (int) (end.getTime().getTime() / 1000);
    }

    private List<RankVO> getRankingTop3(int weekNum) {
        Map<String, Integer> rankingMap = gameKingRedis.getRankingMap(weekNum, 3);
        if (CollectionUtils.isEmpty(rankingMap)) {
            return Collections.emptyList();
        }
        List<RankVO> list = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            ActorData actorData = actorDao.getActorDataFromCache(entry.getKey());
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", entry.getKey());
                continue;
            }
            list.add(new RankVO(actorData.getUid(), actorData.getName(), ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()), rank + "", entry.getValue() + ""));
            rank ++;
        }
        return list;
    }

    private List<RankVO> getRankingList(int weekNum, int length) {
        Map<String, Integer> rankingMap = gameKingRedis.getRangeRankingMap(weekNum, MIN_DIAMOND_LIMIT, 0);
        if (rankingMap.size() < length) {
            // 100万钻石的用户≤20名时，只展示前20名用户，包括低于100万钻的用户
            rankingMap = gameKingRedis.getRankingMap(weekNum, length);
        }
        if (CollectionUtils.isEmpty(rankingMap)) {
            return Collections.emptyList();
        }
        List<RankVO> list = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            ActorData actorData = actorDao.getActorDataFromCache(entry.getKey());
            if (actorData == null) {
                logger.error("can not find actor data. uid={}", entry.getKey());
                continue;
            }
            list.add(new RankVO(actorData.getUid(), actorData.getName(), ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()), rank + "", entry.getValue() + ""));
            rank ++;
        }
        return list;
    }

    private List<GameKingDetailVO.RewardDetail> getRewardDetailList(int slang, PackConfigData packConfigData) {
        if (CollectionUtils.isEmpty(packConfigData.getPackList())) {
            return Collections.emptyList();
        }
        List<GameKingDetailVO.RewardDetail> list = new ArrayList<>();
        for (PackData packData : packConfigData.getPackList()) {
            ResTypeEnum resTypeEnum = ResTypeEnum.getByType(packData.getResType());
            String tag;
            if (resTypeEnum == ResTypeEnum.DIAMONDS) {
                tag = packData.getNum() + "";
            } else {
                tag = resTypeEnum != null ? resTypeEnum.formatTag(slang, packData.getNum()) : "";
            }
            String name = resTypeEnum != null ? resTypeEnum.getNameBySlang(slang) : "";
            list.add(new GameKingDetailVO.RewardDetail(name, packData.getIcon(), tag));
        }
        return list;
    }
}
