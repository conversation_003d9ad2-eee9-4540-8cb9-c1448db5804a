package com.quhong.service;

import com.quhong.constant.ActivityConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

@Component
public class CamelService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(CamelService.class);
    private static final Map<Integer, String> ZONE_MAP = new HashMap<>();
    static {
        ZONE_MAP.put(1, "100-499");
        ZONE_MAP.put(2, "500-1199");
        ZONE_MAP.put(3, "1200-2399");
        ZONE_MAP.put(4, "2400-4799");
        ZONE_MAP.put(5, "4800-1000000");
    }

    private void fillCamelLevelVO(CamelRankingVO vo, String activityId, int rankType, int zoneType){
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        String rangeFrom = ZONE_MAP.get(zoneType);
        if(StringUtils.isEmpty(rangeFrom)){
            return;
        }
        List<String> rangeFromList = Arrays.asList(rangeFrom.split("-"));
        int min = Integer.parseInt(rangeFromList.get(0));
        int max = Integer.parseInt(rangeFromList.get(1));

        Map<String, Integer> rankingMap = activityOtherRedis.getOtherReachingMapByScore(activityId, rankType, min, max, 0);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            String rankUid = entry.getKey();
            int curScore = entry.getValue();
            rankingListVO.setScore(curScore);
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(rankUid);
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", rankUid);
                    continue;
                }
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            }
            rankingList.add(rankingListVO);
        }
        vo.setRankingList(rankingList);
    }

    private void fillCamelMyRank(CamelMyRankVO myRank, String activityId, String uid) {
        myRank.setSendingScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RANK, 0));
        myRank.setReceiveScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.RECEIVE_RANK, 0));
        myRank.setTotalScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RECEIVE_RANK, 0));
        myRank.setRank(activityOtherRedis.getOtherRank(activityId, uid, ActivityConstant.SEND_RECEIVE_RANK, 0));
    }

    /**
     * 个人数据
     */
    protected CamelMyRankVO getCamelMyRank(String activityId, String uid, int rankType) {
        CamelMyRankVO myRank = new CamelMyRankVO();
        ActorData actor = actorDao.getActorDataFromCache(uid);
        if (null == actor) {
            logger.error("can not find actor. uid={}", uid);
            return myRank;
        }
        myRank.setName(actor.getName());
        myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        fillCamelMyRank(myRank, activityId, uid);
        return myRank;
    }


    /**
     * 骆驼个人等级数据
     */
    private void fillCamelLevelMyRank(CamelMyRankVO myRank, String activityId, String uid) {
        myRank.setSendingScore(activityOtherRedis.getOtherReachingScore(activityId, uid, ActivityConstant.SEND_RANK, 0));
        myRank.setRank(0);
    }

    protected CamelMyRankVO getCamelLevelMyRank(String activityId, String uid, int rankType) {
        CamelMyRankVO myRank = new CamelMyRankVO();
        ActorData actor = actorDao.getActorDataFromCache(uid);
        if (null == actor) {
            logger.error("can not find actor. uid={}", uid);
            return myRank;
        }
        myRank.setName(actor.getName());
        myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        fillCamelLevelMyRank(myRank, activityId, uid);
        return myRank;
    }

    public CamelRankingVO camelLevel(String uid, String activityId, int rankType, int zoneType) {
        CamelRankingVO vo = new CamelRankingVO();
        OtherRankingActivityData activity = getOtherRankingActivity(activityId);
        if(activity == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }


        for (OtherRankingActivityData.ReachingConfig reachingConfig : activity.getReachingConfigList()) {
            if (reachingConfig.getReachingRewardType() != rankType) {
                continue;
            }
            fillCamelLevelVO(vo, activityId, rankType, zoneType);
            vo.setMyRank(getCamelLevelMyRank(activityId, uid, rankType));
        }
        return vo;
    }


    /**
     * 排行榜
     */
    public CamelRankingVO camelRanking(String uid, String activityId, int rankType, int rankNum) {
        if(rankNum > 30){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        CamelRankingVO vo = new CamelRankingVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        if(activity == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        for (OtherRankingActivityData.RankingConfig rankingConfig : activity.getRankingRewardList()) {
            if (rankingConfig.getRankType() != rankType) {
                continue;
            }
            vo.setRankingList(getOtherRankingListVO(activityId, rankType, rankNum, activity.getRoundNum(), 0));
            vo.setMyRank(getCamelMyRank(activityId, uid, rankType));
        }

        List<OtherRankingListVO> rankingList = vo.getRankingList();
        int top1Score = rankingList.size() > 0 ? rankingList.get(0).getScore() : 5000;
        int currentTime = DateHelper.getNowSeconds();
        int rDay = (currentTime - activity.getStartTime()) / 86400;
        int camelDay = 0;

        if(rDay < 0){
            camelDay = 1;
        }else{
            camelDay = rDay < 7 ? rDay + 1: 7;
        }

        int totalLength = top1Score * 7 / camelDay;
        vo.setTotalLength(totalLength);
        return vo;
    }

}
