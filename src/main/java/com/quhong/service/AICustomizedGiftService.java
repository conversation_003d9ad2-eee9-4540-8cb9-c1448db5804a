package com.quhong.service;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.SharingOfficerDTO;
import com.quhong.data.vo.AICustomizedGiftVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.dto.ImageDTO;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mysql.dao.AiCustomizedGiftUploadDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.AiCustomizedGiftUploadData;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.CDNUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * AI定制礼物活动
 */
@Service
public class AICustomizedGiftService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(AICustomizedGiftService.class);
    private static final String ACTIVITY_TITLE_EN = "AI Customized Gift";
    public static String ACTIVITY_ID = "688acadc0718a5042fa74e31"; //
    private static final List<Integer> GIFT_LEVEL_LIST = Arrays.asList(0, 100000, 300000, 1000000, 2000000);
    private static final List<String> RES_LEVEL_LIST = Arrays.asList("", "AICustomizedGift100K", "AICustomizedGift300K", "AICustomizedGift1M", "AICustomizedGift2M");
    private static final List<String> EVENT_LEVEL_LIST = Arrays.asList("", "Monthly Gift Sending Event-reward", "Monthly Gift Sending Event-reward", "Monthly Gift Sending Event-reward", "Monthly Gift Sending Event-reward");

    private static final List<Integer> NOTICE_LEVEL_LIST = Arrays.asList(0, 90000, 290000, 900000, 1900000);

    private static final int TARGET_SEND_NUM = 2000000;
    private static final int TARGET_SEND_NEXT_NUM = 1000000;

    private static String ACTIVITY_URL = String.format("https://static.youstar.live/ai_customized_gift/?activityId=%s", ACTIVITY_ID);

    private static String NOTICE_NEXT_LEVEL_TITLE_EN = "\uD83C\uDF89 Go one step further and unlock rewards!";
    private static String NOTICE_NEXT_LEVEL_TITLE_AR = "\uD83C\uDF89 تابع التقدم وافتح المكافآت!";
    private static String NOTICE_NEXT_LEVEL_BODY_EN = "\uD83D\uDC8E Your gift-sending enthusiasm continues to soar! You are only one step away from the next reward threshold. \uD83D\uDE80 Don’t miss the critical moment of sprinting! \uD83D\uDCA5";
    private static String NOTICE_NEXT_LEVEL_BODY_AR = "\uD83D\uDC8E تقدمك في إرسال الهدايا يتزايد!  لم يتبقَ سوى خطوة واحدة للوصول إلى الحد التالي من المكافآت\uD83D\uDE80 لا تفوت لحظة الحاسمة! \uD83D\uDCA5";

    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    @Resource
    private WhiteTestDao whiteTestDao;

    @Resource
    private AiCustomizedGiftUploadDao aiCustomizedGiftUploadDao;

    @Resource
    private IDetectService detectService;

    @Resource
    private CacheDataService cacheDataService;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "686f6fe4fd1678188e38c846";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/ai_customized_gift/?activityId=%s", ACTIVITY_ID);
        }
    }


    private String getAiCustomizedGiftGetRewardKey(String activityId, String month) {
        return String.format("ai:customized:get:reward:%s:%s", activityId, month);
    }

    private String getAiCustomizedGiftKey(String activityId, String month) {
        return String.format("ai:customized:gift:%s:%s", activityId, month);
    }


    public AICustomizedGiftVO aiCustomizedGiftConfig(String activityId, String uid) {
        String month = getMonthByBase(activityId, uid, false);//yyyy_MM

        int[] startTimeEndTime = DateSupport.ARABIAN.getMonthStartAndEndTime(DateSupport.ARABIAN.getToday());
        // String preMonth = getMonthByBase(activityId, uid, false, false);
        // logger.info("aiCustomizedGiftConfig month:{} preMonth:{}", month,preMonth);
        AICustomizedGiftVO vo = new AICustomizedGiftVO();
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        String rankingKey = getAiCustomizedGiftKey(activityId, month);
        makeOtherRankingData(rankingList, myRank, rankingKey, uid, 10, true);
        vo.setRankingList(rankingList);
        vo.setMyRank(myRank);
        vo.setStartTime(startTimeEndTime[0]);
        vo.setEndTime(startTimeEndTime[1]);

        int getReward = activityCommonRedis.isCommonSetData(getAiCustomizedGiftGetRewardKey(activityId, month), uid);
        if (getReward == 1) {
            AiCustomizedGiftUploadData aiCustomizedGiftUploadData = aiCustomizedGiftUploadDao.selectOneByUidAndMonth(uid, month);
            if (aiCustomizedGiftUploadData != null) {
                int state = aiCustomizedGiftUploadData.getState();
                int failCount = aiCustomizedGiftUploadData.getFailCount() == null ? 0 : aiCustomizedGiftUploadData.getFailCount();
                int mtime = aiCustomizedGiftUploadData.getMtime();
                int uploadHeadStatus = 0;
                int now = DateHelper.getNowSeconds();
                if (state == AiCustomizedGiftUploadDao.STATE_REJECTED && failCount <= 1 && mtime + (int) TimeUnit.DAYS.toSeconds(2) > now) {
                    uploadHeadStatus = 1;
                }
                vo.setUploadHeadStatus(uploadHeadStatus);
                vo.setHeadStatus(state);
            } else {
                vo.setHeadStatus(-1);
                vo.setUploadHeadStatus(1);
            }
        } else {
            vo.setUploadHeadStatus(0);
            vo.setHeadStatus(-1);
        }

        return vo;
    }

    public AICustomizedGiftVO aiCustomizedGiftHonor(String activityId, String uid, String month) {
        AICustomizedGiftVO vo = new AICustomizedGiftVO();
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        String rankingKey = getAiCustomizedGiftKey(activityId, month);
        String getRewardKey = getAiCustomizedGiftGetRewardKey(activityId, month);
        makeHonorRankingData(rankingList, myRank, rankingKey, uid, 50, true, getRewardKey);

        vo.setRankingList(rankingList);
        vo.setMyRank(myRank);
        return vo;
    }

    public void makeHonorRankingData(List<OtherRankingListVO> rankingList, OtherRankingListVO myRank,
                                     String rankKey, String uid, int length, boolean gloryInfo, String getRewardKey) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankKey, length);
        Set<String> getRewardSet = cacheDataService.getAllSetCache(getRewardKey, 2);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            if (!getRewardSet.contains(aid)) {
                continue;
            }
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            rankingVO.setRidData(rankActor.getRidData());
            if (gloryInfo) {
                String countryCode = ActorUtils.getCountryCode(rankActor.getCountry());
                rankingVO.setBadgeList(badgeDao.getBadgeList(aid));
                rankingVO.setVipLevel(vipInfoDao.getIntVipLevel(aid));
            }

            if (aid.equals(uid)) {
                BeanUtils.copyProperties(rankingVO, myRank);
                myRank.setRidData(rankActor.getRidData());
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if (myRank.getRank() == null || myRank.getRank() == 0) {
            myRank.setName(actorData.getName());
            myRank.setUid(uid);
            myRank.setRidData(actorData.getRidData());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRank.setScore(activityCommonRedis.getCommonZSetRankingScore(rankKey, uid));
            myRank.setRank(-1);
            if (gloryInfo) {
                String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
                myRank.setBadgeList(badgeDao.getBadgeList(uid));
                myRank.setVipLevel(vipInfoDao.getIntVipLevel(uid));
            }
        }
    }


    public void submitAiHead(SharingOfficerDTO dto) {
        // uploadFileOSSLimit 这个接口上传资源
        if (StringUtils.isEmpty(dto.getImageUrl())) {
            logger.info("param error imageUrl:{}", dto.getImageUrl());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String uid = dto.getUid();
        String month = getMonthByBase(dto.getActivityId(), uid, false);
        int getReward = activityCommonRedis.isCommonSetData(getAiCustomizedGiftGetRewardKey(dto.getActivityId(), month), uid);
        if (getReward == 0) {
            logger.info("not get reward, can not submit. uid: {}, month: {}", uid, month);
            throw new CommonH5Exception(ActivityHttpCode.AUTH_ERROR);
        }
        // checkActivityTime(dto.getActivityId());
        AiCustomizedGiftUploadData aiCustomizedGiftUploadData = aiCustomizedGiftUploadDao.selectOneByUidAndMonth(uid, month);
        if (aiCustomizedGiftUploadData != null) {
            int failCount = aiCustomizedGiftUploadData.getFailCount() == null ? 0 : aiCustomizedGiftUploadData.getFailCount();
            int mtime = aiCustomizedGiftUploadData.getMtime();
            int now = DateHelper.getNowSeconds();
            if (aiCustomizedGiftUploadData.getState() == AiCustomizedGiftUploadDao.STATE_APPROVED) {
                logger.info("already pass, can not submit again. uid: {}, month: {}", uid, month);
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_PASS);
            } else if (aiCustomizedGiftUploadData.getState() == AiCustomizedGiftUploadDao.STATE_PENDING) {
                logger.info("already pending, can not submit again. uid: {}, month: {}", uid, month);
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_PENDING);
            } else if (!(aiCustomizedGiftUploadData.getState() == AiCustomizedGiftUploadDao.STATE_REJECTED
                    && failCount <= 1 && mtime + (int) TimeUnit.DAYS.toSeconds(2) > now)) {
                logger.info("already rejected, can not submit again. uid: {}, month: {} failCount: {} mtime: {} now: {}", uid, month, failCount, mtime, now);
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_REJECTED);
            }
        }
        String imageUrl = CDNUtils.PROD_CDN_PREFIX + dto.getImageUrl();
        if (detectService.detectImage(new ImageDTO(imageUrl, DetectOriginConstant.ACTIVITY_AICUSTOMIZEDGIFT_HEAD, dto.getUid())).getData().getIsSafe() == 0) {
            logger.info("dirty image. imageUrl:{}", imageUrl);
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_IMAGE);
        }
        int now = DateHelper.getNowSeconds();
        if (aiCustomizedGiftUploadData == null) {
            AiCustomizedGiftUploadData data = new AiCustomizedGiftUploadData();
            data.setUid(uid);
            data.setImageUrl(imageUrl);
            data.setMonth(month);
            data.setState(AiCustomizedGiftUploadDao.STATE_PENDING);
            data.setCtime(now);
            data.setMtime(now);
            aiCustomizedGiftUploadDao.insert(data);
            logger.info("insert success. uid: {}, month: {} imageUrl: {}", uid, month, imageUrl);
        } else {
            aiCustomizedGiftUploadData.setImageUrl(imageUrl);
            aiCustomizedGiftUploadData.setState(AiCustomizedGiftUploadDao.STATE_PENDING);
            aiCustomizedGiftUploadData.setMtime(now);
            aiCustomizedGiftUploadDao.update(aiCustomizedGiftUploadData);
            logger.info("update success. uid: {}, month: {} imageUrl: {}", uid, month, imageUrl);
        }

    }

    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        try {
            int totalPrice = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            String roomId = giftData.getRoomId();
            String uid = giftData.getFrom_uid();

            if (StringUtils.isEmpty(roomId)) {
                return;
            }

            if (giftData.getGiving_type() == 5 || giftData.getGiving_type() == 6) {
                logger.info("sendGiftHandle giving_type is 5 or 6, return gid:{} num:{} toAid:{}", giftData.getGid(), giftData.getNumber(), giftData.getAid_list());
                return;
            }

            if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                logger.info("sendGiftHandle whiteTestDao isMemberByType, return gid:{} num:{} roomId:{}", giftData.getGid(), giftData.getNumber(), roomId);
                return;
            }

            String month = getMonthByBase(activityId, uid, false);//yyyy_MM
            String aiCustomizedGiftKey = getAiCustomizedGiftKey(activityId, month);

            String preMonth = getMonthByBase(activityId, uid, false, false);//yyyy_MM

            // logger.info("sendGiftHandle month: {}, preMonth: {}", month, preMonth);
            String curGetRewardKey = getAiCustomizedGiftGetRewardKey(activityId, month);
            String preGetRewardKey = getAiCustomizedGiftGetRewardKey(activityId, preMonth);

            Set<String> preGetRewardSet = cacheDataService.getAllSetCache(preGetRewardKey, 2);

            int oldSendNum = activityCommonRedis.getCommonZSetRankingScore(aiCustomizedGiftKey, uid);
            int newSendNum = activityCommonRedis.incrCommonZSetRankingScoreSimple(aiCustomizedGiftKey, uid, totalPrice);
            int oldLevel = getNewBaseIndexLevel(oldSendNum, GIFT_LEVEL_LIST);
            int newLevel = getNewBaseIndexLevel(newSendNum, GIFT_LEVEL_LIST);
            // 如果等级提升，下发奖励
            if (newLevel > oldLevel) {
                // 跨级的，需要把之前等级的key都下发
                for (int level = oldLevel + 1; level <= newLevel; level++) {
                    if (level < RES_LEVEL_LIST.size() && StringUtils.hasLength(RES_LEVEL_LIST.get(level))) {
                        String resKey = RES_LEVEL_LIST.get(level);
                        String eventTitle = EVENT_LEVEL_LIST.get(level);
                        resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
                        logger.info("sendGiftHandle reward sent to uid: {}, level: {}, resKey: {}", uid, level, resKey);
                    }
                }

                if (newSendNum >= TARGET_SEND_NUM || (newSendNum >= TARGET_SEND_NEXT_NUM && preGetRewardSet.contains(uid))) {
                    activityCommonRedis.addCommonSetData(curGetRewardKey, uid); // 完成条件，可以上传头像
                    cacheDataService.delSetCache(curGetRewardKey, 2);
                    // cacheDataService.delSetCache(preGetRewardKey, 2);
                }

                logger.info("sendGiftHandle reward distribution message sent to uid: {}", uid);
            }

            noticeNextLevel(uid, oldSendNum, newSendNum);


        } catch (Exception e) {
            logger.error("sendGiftHandle error: {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }


    private void noticeNextLevel(String uid, int oldSendNum, int newSendNum) {
        int oldLevel = getNewBaseIndexLevel(oldSendNum, NOTICE_LEVEL_LIST);
        int newLevel = getNewBaseIndexLevel(newSendNum, NOTICE_LEVEL_LIST);
        if (newLevel > oldLevel) {
            int maxLevel = NOTICE_LEVEL_LIST.size() - 1;
            int nowLevel = Math.min(newLevel + 1, maxLevel);
            int nextSendNum = GIFT_LEVEL_LIST.get(nowLevel);
            logger.info("noticeNextLevel oldLevel: {}, newLevel: {}, maxLevel: {}, nowLevel: {}, nextSendNum: {}", oldLevel, newLevel, maxLevel, nowLevel, nextSendNum);
            if (newSendNum < nextSendNum) {
                sendOfficialMsg(uid, NOTICE_NEXT_LEVEL_TITLE_EN, NOTICE_NEXT_LEVEL_TITLE_AR,
                        NOTICE_NEXT_LEVEL_BODY_EN, NOTICE_NEXT_LEVEL_BODY_AR, ACTIVITY_URL);
            }
        }
    }

    /**
     * 发送系统消息
     */
    private void sendOfficialMsg(String uid, String titleEn, String titleAr, String bodyEn, String bodyAr, String url) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("sendOfficialMsg actor not found for uid: {}", uid);
            return;
        }
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleAr : titleEn;
        String body = slang == SLangType.ARABIC ? bodyAr : bodyEn;
        String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
        commonOfficialMsg(uid, "", 0, 0, actText, title, body, url);
    }


}
