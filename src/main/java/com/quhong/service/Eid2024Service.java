package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActiveDataEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.Eid2024VO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 宰牲节
 */
@Service
public class Eid2024Service extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(Eid2024Service.class);
    private static final String ACTIVITY_TITLE_EN = "Shining Eid al-Adha";
    private static final String ACTIVITY_TITLE_AR = "تألق بعيد الأضحى";
    private static final String ACTIVITY_PRIZE_ICON = "https://cdn3.qmovies.tv/youstar/op_1717596913_FJRK.png";
    private static final String ACTIVITY_DESC = "Shining Eid al-Adha reward";
    private static final String ACTIVITY_ID = "668fe1d25165cfe866d3545c";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/shining_eid2024/?activityId=%s", ACTIVITY_ID): String.format("https://test2.qmovies.tv/shining_eid2024/?activityId=%s", ACTIVITY_ID);
    private static final String TOTAL_INTEGRAL = "totalIntegral";

    // 统计玩嘉年华消费钻石数
    private static final List<Integer> PLAY_GAME_LEVEL_LIST = Arrays.asList(20, 200, 1000, 3000);
    private static final List<String> PLAY_GAME_KEY_LIST = Arrays.asList("shiningEidPlayGameLevel1", "shiningEidPlayGameLevel2", "shiningEidPlayGameLevel3", "shiningEidPlayGameLevel4");

    // 玩嘉年华收集指定礼物任务
    private static final List<Integer> GIFT_COLLECT_1_LIST = Arrays.asList(810, 690, 752);
    private static final List<Integer> GIFT_COLLECT_2_LIST = Arrays.asList(809, 749, 748);
    private static final List<Integer> GIFT_COLLECT_3_LIST = Arrays.asList(811, 812, 813);
    private static final String COPPER_BOX = "copperBox";   // 铜宝箱
    private static final String BLUE_BOX = "blueBox";   // 兰宝箱
    private static final String GOLD_BOX = "goldBox";       // 金宝箱
    private static final Map<String, String> GIFT_COLLECT_MAP = new HashMap<>();

    // 抓羊任务
    private static final List<Integer> CATCH_SHEEP_LEVEL_LIST = Arrays.asList(10, 100, 500, 1250, 2500);
    private static final List<String> CATCH_SHEEP_KEY_LIST = Arrays.asList("shiningEidCatchSleep10", "shiningEidCatchSleep100", "shiningEidCatchSleep500", "shiningEidCatchSleep1250", "shiningEidCatchSleep2500");

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    static {
        GIFT_COLLECT_MAP.put(COPPER_BOX, "shiningEidCopperBox");
        GIFT_COLLECT_MAP.put(BLUE_BOX, "shiningEidSilverBox");
        GIFT_COLLECT_MAP.put(GOLD_BOX, "shiningEidGoldBox");
    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;

    private String getHashActivityId(String activityId, String uid, String dateStr){
        return String.format("%s:%s:%s", activityId, uid, dateStr);
    }

    // 玩星动嘉年华每日key
    private String getCurrentDate(String activityId){
        return String.format("playGameDate:%s", activityId);
    }

    private String getPlayGameKey(String activityId, String dateStr){
        return String.format("playGame:%s:%s", activityId, dateStr);
    }

    // 每日抓羊key
    private String getCatchSleepKey(String activityId, String dateStr){
        return String.format("catchSleep:%s:%s", activityId, dateStr);
    }

    // 抓羊榜单key
    private String getTotalRankKey(String activityId){
        return String.format("totalRank:%s", activityId);
    }

    private String getGiftStatus(String box, Integer giftId){
        return String.format("%s:%s", box, giftId);
    }

    public Eid2024VO eid2024Config(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(activityId));
        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        Map<String, Integer> dailyConfigMap =  activityCommonRedis.getCommonHashAll(getHashActivityId(activityId, uid, currentDate));
        Eid2024VO vo = new Eid2024VO();
        vo.setTotalIntegral(dailyConfigMap.getOrDefault(TOTAL_INTEGRAL, 0));
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        vo.setTotalPlayBeans(activityCommonRedis.getCommonZSetRankingScore(getPlayGameKey(activityId, currentDate), uid));
        vo.setCatchSleepNum(activityCommonRedis.getCommonZSetRankingScore(getCatchSleepKey(activityId, currentDate), uid));
        vo.setRoomId(getPopularRoomId());

        // 设置收集礼物状态
        List<Integer> copperBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_1_LIST) {
            copperBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(COPPER_BOX, giftId), 0));
        }
        vo.setCopperBoxStatus(copperBoxStatus);
        vo.setCopperBox(dailyConfigMap.getOrDefault(COPPER_BOX, 0));

        List<Integer> blueBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_2_LIST) {
            blueBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(BLUE_BOX, giftId), 0));
        }
        vo.setBlueBoxStatus(blueBoxStatus);
        vo.setBlueBox(dailyConfigMap.getOrDefault(BLUE_BOX, 0));

        List<Integer> goldBoxStatus = new ArrayList<>();
        for (Integer giftId : GIFT_COLLECT_3_LIST) {
            goldBoxStatus.add(dailyConfigMap.getOrDefault(getGiftStatus(GOLD_BOX, giftId), 0));
        }
        vo.setGoldBoxStatus(goldBoxStatus);
        vo.setGoldBox(dailyConfigMap.getOrDefault(GOLD_BOX, 0));

        // 设置抓羊榜
        List<OtherRankingListVO> totalRankingList = new ArrayList<>();
        OtherRankingListVO mytTotalRank = new OtherRankingListVO();
        makeOtherRankingData(totalRankingList, mytTotalRank, getTotalRankKey(activityId), uid, 10);
        vo.setTotalRankingList(totalRankingList);
        vo.setMyTotalRank(mytTotalRank);

        return vo;
    }

    public void catchSheep(String activityId, String uid, int zone, int amount) {
        checkActivityTime(activityId);
        if(amount != 1 && amount != 10){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        int debutNum = amount == 1 ? 100 : 900;

        // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(activityId));
        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        String hashActivityId = getHashActivityId(activityId, uid, currentDate);
        Map<String, Integer> dailyConfigMap =  activityCommonRedis.getCommonHashAll(hashActivityId);
        int totalIntegral = dailyConfigMap.getOrDefault(TOTAL_INTEGRAL, 0);
        if(totalIntegral <= 0 || totalIntegral < debutNum){
            throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
        }
        synchronized (stringPool.intern(ACTIVITY_ID)) {
            activityCommonRedis.incCommonHashNum(hashActivityId, TOTAL_INTEGRAL, -debutNum);
            ActiveDataEvent activeDataEvent = new ActiveDataEvent();
            activeDataEvent.setActive_id(activityId);
            activeDataEvent.setDate(DateHelper.ARABIAN.formatDateInDay());
            activeDataEvent.setUid(uid);
            activeDataEvent.setActive_data_desc(zone > 0 ? "sheep_catch_success_number" : "sheep_catch_fail_number");
            activeDataEvent.setNumber(amount);
            activeDataEvent.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new EventDTO(activeDataEvent));

            if(zone > 0){
                // 总榜单
                String totalRankKey = getTotalRankKey(ACTIVITY_ID);
                activityCommonRedis.incrCommonZSetRankingScore(totalRankKey, uid, amount);

                // 每日刷新抓羊数量
                String dailyCatchSleepKey = getCatchSleepKey(activityId, currentDate);
                int currentNum = activityCommonRedis.getCommonZSetRankingScore(dailyCatchSleepKey, uid);
                while (amount > 0){
                    List<Integer> tempLevelNumList = new ArrayList<>(CATCH_SHEEP_LEVEL_LIST);
                    int currentLevelIndex = 0;
                    if(tempLevelNumList.contains(currentNum)){
                        currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                    }else {
                        tempLevelNumList.add(currentNum);
                        tempLevelNumList.sort(Integer::compare);
                        currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                    }

                    int upLevelIndex = currentLevelIndex + 1;
                    if(upLevelIndex >= CATCH_SHEEP_LEVEL_LIST.size()){
                        activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSleepKey, uid, amount);
                        amount = 0;
                    }else {
                        int upLevelNum = CATCH_SHEEP_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                        int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                        if(needUpNum <= amount){                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                            currentNum = currentNum + needUpNum;
                            amount  = amount - needUpNum;
                            activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSleepKey, uid, needUpNum);

                            // 下发奖励
                            String resKey = CATCH_SHEEP_KEY_LIST.get(upLevelIndex);
                            resourceKeyHandlerService.sendResourceData(uid, resKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_PRIZE_ICON);
                        }else {
                            activityCommonRedis.incrCommonZSetRankingScore(dailyCatchSleepKey, uid, amount);
                            amount = 0;
                        }
                    }
                }
            }
        }
    }

    // 总榜排行榜奖励
    public void distributionTotalRanking(String activityId){
        try{
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getTotalRankKey(activityId), 10);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                String rankUid = entry.getKey();
                String resourceKey = null;
                switch (rank){
                    case 1:
                        resourceKey = "shiningEidTop1";
                        break;
                    case 2:
                        resourceKey = "shiningEidTop2";
                        break;
                    case 3:
                        resourceKey = "shiningEidTop3";
                        break;
                    case 4:
                    case 5:
                    case 6:
                    case 7:
                    case 8:
                    case 9:
                    case 10:
                        resourceKey = "shiningEidTop4-10";
                        break;
                }
                resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_PRIZE_ICON);
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    private void distributeGiftCollect(String fromUid, Map<String, Integer> dailyConfigMap, String dailyBoxKey, String boxType, List<Integer> giftList){

        int total = 0;
        for (Integer giftId : giftList) {
            String giftStatusKey = getGiftStatus(boxType, giftId);
            total += dailyConfigMap.getOrDefault(giftStatusKey, 0);
        }

        if(total >= giftList.size()){
            activityCommonRedis.setCommonHashNum(dailyBoxKey, boxType, 1);
            String resourceKey = GIFT_COLLECT_MAP.get(boxType);
            resourceKeyHandlerService.sendResourceData(fromUid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_PRIZE_ICON);
        }

    }

    // 统计嘉年华收集礼物id
    public void handleCommonMqGiftMsg(CommonMqTopicData data) {
        try {

            String fromUid = data.getUid();
            if(!inActivityTime(ACTIVITY_ID)){
                return;
            }

            // if (!whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)){
            //     return;
            // }

            CommonMqTopicData.StarBeatGameInfo starBeatGameInfo = JSONObject.parseObject(data.getJsonData(), CommonMqTopicData.StarBeatGameInfo.class);
            int resourceType = starBeatGameInfo.getResourceType();
            if(resourceType != BaseDataResourcesConstant.TYPE_BAG_GIFT){
                return;
            }

            synchronized (stringPool.intern(ACTIVITY_TITLE_EN)) {

                // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(ACTIVITY_ID));
                String currentDate = DateHelper.ARABIAN.formatDateInDay();
                int giftId = starBeatGameInfo.getResourceId();
                String dailyBoxKey = getHashActivityId(ACTIVITY_ID, fromUid, currentDate);
                Map<String, Integer> dailyConfigMap =  activityCommonRedis.getCommonHashAll(dailyBoxKey);
                if(GIFT_COLLECT_1_LIST.contains(giftId) && dailyConfigMap.getOrDefault(COPPER_BOX, 0) <= 0){
                    String giftStatusKey = getGiftStatus(COPPER_BOX, giftId);
                    dailyConfigMap.put(giftStatusKey, 1);
                    activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
                    distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, COPPER_BOX, GIFT_COLLECT_1_LIST);
                }


                if(GIFT_COLLECT_2_LIST.contains(giftId) && dailyConfigMap.getOrDefault(BLUE_BOX, 0) <= 0){
                    String giftStatusKey = getGiftStatus(BLUE_BOX, giftId);
                    dailyConfigMap.put(giftStatusKey, 1);
                    activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
                    distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, BLUE_BOX, GIFT_COLLECT_2_LIST);
                }

                if(GIFT_COLLECT_3_LIST.contains(giftId) && dailyConfigMap.getOrDefault(GOLD_BOX, 0) <= 0){
                    String giftStatusKey = getGiftStatus(GOLD_BOX, giftId);
                    dailyConfigMap.put(giftStatusKey, 1);
                    activityCommonRedis.setCommonHashNum(dailyBoxKey, giftStatusKey, 1);
                    distributeGiftCollect(fromUid, dailyConfigMap, dailyBoxKey, GOLD_BOX, GIFT_COLLECT_3_LIST);
                }
            }
        }catch (Exception e){
            logger.error("handleCommonMqGiftMsg: {}", JSONObject.toJSONString(data));
        }


    }

    // 统计积分及下发等级奖励
    public void handleMqMsg(CommonMqTopicData data) {
        String fromUid = data.getUid();
        if(!inActivityTime(ACTIVITY_ID)){
            return;
        }

        // if (!whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)){
        //     return;
        // }

        synchronized (stringPool.intern(ACTIVITY_TITLE_EN)) {
            int value = data.getValue();
            // 增加积分值

            // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(ACTIVITY_ID));
            String currentDate = DateHelper.ARABIAN.formatDateInDay();

            activityCommonRedis.incCommonHashNum(getHashActivityId(ACTIVITY_ID, fromUid, currentDate), TOTAL_INTEGRAL, value);

            // 增加玩游戏耗钻数
            String playGameKey = getPlayGameKey(ACTIVITY_ID, currentDate);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(playGameKey, fromUid);
            while (value > 0){
                List<Integer> tempLevelNumList = new ArrayList<>(PLAY_GAME_LEVEL_LIST);
                int currentLevelIndex = 0;
                if(tempLevelNumList.contains(currentNum)){
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                }else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if(upLevelIndex >= PLAY_GAME_LEVEL_LIST.size()){
                    activityCommonRedis.incrCommonZSetRankingScore(playGameKey, fromUid, value);
                    value = 0;
                }else {
                    int upLevelNum = PLAY_GAME_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if(needUpNum <= value){                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        value  = value - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(playGameKey, fromUid, needUpNum);

                        // 下发奖励
                        String resKey = PLAY_GAME_KEY_LIST.get(upLevelIndex);
                        resourceKeyHandlerService.sendResourceData(fromUid, resKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_PRIZE_ICON);
                    }else {
                        activityCommonRedis.incrCommonZSetRankingScore(playGameKey, fromUid, value);
                        value = 0;
                    }
                }
            }
        }
    }


    public void handleGiftMqMsg(SendGiftData giftData, String activityId) {
        synchronized (stringPool.intern(activityId)) {
            String fromUid = giftData.getFrom_uid();
            int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            // TODO 修改日期
            // String currentDate = activityCommonRedis.getCommonStrValue(getCurrentDate(ACTIVITY_ID));
            String currentDate = DateHelper.ARABIAN.formatDateInDay();
            activityCommonRedis.incCommonHashNum(getHashActivityId(activityId, fromUid, currentDate), TOTAL_INTEGRAL, totalBeans);
        }
    }
}
