package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.LuckyDrawHttpCode;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.CupidVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.msg.obj.RankInfoListObject;
import com.quhong.msg.obj.RankInfoObject;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.msg.room.RankNotificationPushMsg;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.room.TestRoomService;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
public class CupidService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(CupidService.class);
    private static final String ACTIVITY_TITLE = "Cupid Activity";
    public static final String DAILY_ACTIVITY_ID = "65d73c9a32cb5689e2fb8dc9";
    public static final String TOTAL_ACTIVITY_ID = "65d739f932cb5689e2fb8dc8";
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    protected DataCenterService dataCenterService;
    @Resource
    private TestRoomService testRoomService;

    public String getRecordActivityKey(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }

    public String getCupidDailyRankingKey(String activityId, int roundNum){
        return String.format("cupidDailyRanking:%s:%s", activityId, roundNum);
    }

    public String getCupidTotalRankingKey(String activityId){
        return String.format("cupidTotalRanking:%s", activityId);
    }

    private void fillCupidVORanking(CupidVO vo, String rankingKey, String uid, ActorData actorData, String activityId){
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        Map<String, Integer> brainRankingMap = activityCommonRedis.getCommonRankingMap(rankingKey, 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            if(aid.equals(uid)){
                BeanUtils.copyProperties(rankingVO, myRank);
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if(myRank.getRank() == null || myRank.getRank() == 0){
            myRank.setName(actorData.getName());
            myRank.setUid(uid);
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRank.setScore(activityCommonRedis.getCommonZSetRankingScore(rankingKey, uid));
            myRank.setRank(-1);
        }

        String cupidKey = getCupidTotalRankingKey(activityId);
        if(cupidKey.equals(rankingKey)){
            vo.setTotalRankingList(rankingList);
            vo.setMyTotalRank(myRank);
        }else {
            vo.setDailyRankingList(rankingList);
            vo.setMyDailyRank(myRank);

        }
    }

    public CupidVO cupidConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        CupidVO vo = new CupidVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        // 设置奖池信息
        List<CupidVO.PrizeConfig> prizeConfigList = new ArrayList<>();
        List<ActivityCommonConfig.CommonAwardConfig> cupidConfigList = activityCommonConfig.getCupidConfigList();
        for (ActivityCommonConfig.CommonAwardConfig commonAwardConfig: cupidConfigList) {
            CupidVO.PrizeConfig prizeConfig = new CupidVO.PrizeConfig();
            BeanUtils.copyProperties(commonAwardConfig, prizeConfig);
            prizeConfigList.add(prizeConfig);
        }
        vo.setPrizeConfigList(prizeConfigList);
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        // 设置每日排行榜
        OtherRankingActivityData activityDaily = otherActivityService.getOtherRankingActivity(DAILY_ACTIVITY_ID);
        String cupidDailyKey = getCupidDailyRankingKey(DAILY_ACTIVITY_ID, activityDaily.getRoundNum());
        fillCupidVORanking(vo, cupidDailyKey, uid, actorData, DAILY_ACTIVITY_ID);

        // 设置总排行榜
        String cupidTotalKey = getCupidTotalRankingKey(activityId);
        fillCupidVORanking(vo, cupidTotalKey, uid, actorData, activityId);
        return vo;
    }

    // 抽奖
    public CupidVO cupidDraw(String activityId, String uid, int amount) {

        checkActivityTime(activityId);
        CupidVO vo = new CupidVO();
        List<CupidVO.PrizeConfig> prizeConfigList = new ArrayList<>();
        List<String> prizeKeyList = new ArrayList<>();

        synchronized (stringPool.intern(ACTIVITY_TITLE + uid)) {

            int changed = amount == 1 ? 100 : 900;
            cupidDrawDiamondCost(uid, changed);
            Map<String, ActivityCommonConfig.CommonAwardConfig> rewardConfigMap = activityCommonConfig.getCupidConfigList().stream().collect(Collectors.toMap(ActivityCommonConfig.CommonAwardConfig::getDrawType, Function.identity()));

            int currentTime = DateHelper.getNowSeconds();

            for (int i = 0; i < amount; i++) {
                initPoolSize(activityId);
                String drawKey = activityCommonRedis.leftPopCommonListKey(activityId);
                ActivityCommonConfig.CommonAwardConfig awardConfig = rewardConfigMap.get(drawKey);
                if(awardConfig != null){
                    CupidVO.PrizeConfig prizeConfig = new CupidVO.PrizeConfig();
                    BeanUtils.copyProperties(awardConfig, prizeConfig);
                    prizeConfigList.add(prizeConfig);

                    String drawKeyTime = drawKey + "-" + currentTime;
                    prizeKeyList.add(drawKeyTime);
                }
            }

            // 记录抽奖历史
            String recordListKey = getRecordActivityKey(activityId, uid);
            activityCommonRedis.leftPushAllCommonList(recordListKey, prizeKeyList);
        }

        // 4、奖励下发
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                cupidRewardDistribute(activityId, uid, amount, prizeConfigList);
            }
        });

        vo.setPrizeConfigList(prizeConfigList);
        return vo;
    }


    // 1、扣费
    private void cupidDrawDiamondCost(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(912);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(ACTIVITY_TITLE);
        moneyDetailReq.setDesc(ACTIVITY_TITLE);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_BEANS_OUT);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
    }


    // 2、初始化奖池
    public void commonInitPoolSize(String activityId){

        List<String> prizeList = Stream.generate(() -> "0").limit(1000).collect(Collectors.toList());
        List<String> indexList = IntStream.rangeClosed(0, 999).mapToObj(String::valueOf).collect(Collectors.toList());

        for (int i = 1; i <= 1000; i++) {

            int index = i - 1;
            boolean flag = false;
            if(i == 334 || i == 777){
                prizeList.set(index, "a");
                flag = true;
            }

            if (i % 250 == 0){
                prizeList.set(index, "b");
                flag = true;
            }

            if ((i - 9) % 10 == 0){
                prizeList.set(index, "f");
                flag = true;
            }

            if ((i - 3) % 5 == 0){
                prizeList.set(index, "h");
                flag = true;
            }

            if(flag){
                indexList.remove(String.valueOf(index));
            }
        }

        // 打乱索引列表
        Collections.shuffle(indexList);
        List<ActivityCommonConfig.CommonAwardConfig> cupidConfigList = activityCommonConfig.getCupidConfigList().stream().filter(item -> item.getRateNum() > 0).collect(Collectors.toList());

        for (ActivityCommonConfig.CommonAwardConfig awardConfig : cupidConfigList) {
            for (int i = 0; i < awardConfig.getRateNum(); i++) {
                String indexStr = indexList.remove(0);
                prizeList.set(Integer.parseInt(indexStr), awardConfig.getDrawType());
            }
        }

        activityCommonRedis.rightPushAllCommonList(activityId, prizeList);
    }

    private void initPoolSize(String activityId){
        int poolSize = activityCommonRedis.getCommonListSize(activityId);
        if(poolSize <= 20){
            commonInitPoolSize(activityId);
        }
    }

    // 奖励下发
    private void cupidRewardDistribute(String activityId, String uid, int amount, List<CupidVO.PrizeConfig> prizeConfigList){
        // 设置每日排行榜
        OtherRankingActivityData activityDaily = otherActivityService.getOtherRankingActivity(DAILY_ACTIVITY_ID);
        String cupidDailyKey = getCupidDailyRankingKey(DAILY_ACTIVITY_ID, activityDaily.getRoundNum());
        activityCommonRedis.incrCommonZSetRankingScore(cupidDailyKey, uid, amount);

        // 设置总排行榜
        String cupidTotalKey = getCupidTotalRankingKey(activityId);
        activityCommonRedis.incrCommonZSetRankingScore(cupidTotalKey, uid, amount);


        for (CupidVO.PrizeConfig prizeConfig : prizeConfigList) {
            distributionService.sendRewardResource(uid, prizeConfig.getSourceId(), ActivityRewardTypeEnum.getEnumByName(prizeConfig.getRewardType()),
                    prizeConfig.getRewardTime(), prizeConfig.getRewardNum(), ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
        }
    }

    public CupidVO cupidRecord(String activityId, String uid, int page) {

        CupidVO vo = new CupidVO();
        List<CupidVO.PrizeConfig> prizeConfigList = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        String recordListKey = getRecordActivityKey(activityId, uid);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        Map<String, ActivityCommonConfig.CommonAwardConfig> rewardConfigMap = activityCommonConfig.getCupidConfigList().stream().collect(Collectors.toMap(ActivityCommonConfig.CommonAwardConfig::getDrawType, Function.identity()));

        for (String prizeKeyTime : prizeKeyTimeList) {
            String[] prizeKeySplit = prizeKeyTime.split("-");
            String prizeKey = prizeKeySplit[0];
            String prizeTime = prizeKeySplit[1];

            ActivityCommonConfig.CommonAwardConfig awardConfig = rewardConfigMap.get(prizeKey);
            if(awardConfig != null){
                CupidVO.PrizeConfig prizeConfig = new CupidVO.PrizeConfig();
                prizeConfig.setIconEn(awardConfig.getIconEn());
                prizeConfig.setNameEn(awardConfig.getNameEn());
                prizeConfig.setNameAr(awardConfig.getNameAr());
                prizeConfig.setCtime(Integer.valueOf(prizeTime));
                prizeConfigList.add(prizeConfig);
            }
        }

        vo.setDrawRecordList(prizeConfigList);
        if (prizeConfigList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }


    // 日榜排行榜奖励
    public void distributionDailyCupid(int roundNum){
        try{
            if(!ServerConfig.isProduct()){
                return;
            }

            String cupidDailyKey = getCupidDailyRankingKey(DAILY_ACTIVITY_ID, roundNum);
            Map<String, Integer> brainRankingMap = activityCommonRedis.getCommonRankingMap(cupidDailyKey, 1);
            for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
                String rankUid = entry.getKey();
                distributionService.sendRewardResource(rankUid, 26, ActivityRewardTypeEnum.getEnumByName("honor_title"), 1, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
            }
        }catch (Exception e){
            logger.error("distributionDailyCupid error: {}", e.getMessage(), e);
        }
    }

    // 总榜排行榜奖励
    public void distributionTotalCupid(){
        try{
            if(!ServerConfig.isProduct()){
                return;
            }

            String cupidTotalKey = getCupidTotalRankingKey(TOTAL_ACTIVITY_ID);
            Map<String, Integer> brainRankingMap = activityCommonRedis.getCommonRankingMap(cupidTotalKey, 1);
            for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
                String rankUid = entry.getKey();
                distributionService.sendRewardResource(rankUid, 26, ActivityRewardTypeEnum.getEnumByName("honor_title"), 30, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                distributionService.sendRewardResource(rankUid, 2503, ActivityRewardTypeEnum.getEnumByName("badge"), 30, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
            }
        }catch (Exception e){
            logger.error("distributionTotalCupid error: {}", e.getMessage(), e);
        }
    }

    public void rankNotificationPushMsgTest(String activityId){
        try{
            if(ServerConfig.isProduct()){
                return;
            }
            OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

            RankNotificationPushMsg msg = new RankNotificationPushMsg();
            msg.setTitle_en(activity.getAcNameEn());
            msg.setTitle_ar(activity.getAcNameAr());
            msg.setRankList(new ArrayList<>());
            List<OtherRankingActivityData.RankingConfig> rankingConfigList = activity.getRankingRewardList();
            int roundNum = activity.getRoundNum();

            // 遍历每种奖励类型
            for (OtherRankingActivityData.RankingConfig rankingConfig : rankingConfigList) {
                int rankType = rankingConfig.getRankType();
                List<String> rankingList = activityOtherRedis.getOtherRankingList(activityId, rankType, 10, roundNum);
                // 排行榜消息

                RankInfoListObject rankInfoListObject = new RankInfoListObject();
                rankInfoListObject.setTagType(rankingConfig.getRankType());
                rankInfoListObject.setTitle_en(rankingConfig.getRankNameEn());
                rankInfoListObject.setTitle_ar(rankingConfig.getRankNameAr());
                List<RankInfoObject> rankInfoObjects = new ArrayList<>();
                rankInfoListObject.setRankInfo(rankInfoObjects);
                for (int i = 0; i < rankingList.size(); i++) {
                    String aid = rankingList.get(i);
                    RankInfoObject rankInfoObject = new RankInfoObject();
                    rankInfoObject.setRank(i + 1);
                    rankInfoObject.setTagType(rankingConfig.getRankType());
                    if (ActivityConstant.ROOM_RANK == rankingConfig.getRankType()) {
                        MongoRoomData roomData = mongoRoomDao.getDataFromCache(aid);
                        rankInfoObject.setRoom_id(aid);
                        rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                        rankInfoObject.setName(roomData.getName());
                        ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(aid));
                        rankInfoObject.setRid(actorData.getRid());
                        rankInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
                    } else {
                        ActorData actorData = actorDao.getActorDataFromCache(aid);
                        rankInfoObject.setRoom_id(aid);
                        rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                        rankInfoObject.setName(actorData.getName());
                        rankInfoObject.setRid(actorData.getRid());
                        rankInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
                    }
                    rankInfoObjects.add(rankInfoObject);
                }
                msg.getRankList().add(rankInfoListObject);
            }

            boolean test = activity.getAcNameEn().startsWith("test");
            roomWebSender.sendRoomWebMsg(test ? testRoomService.getTestRoom() : "all", null, msg, false);

        }catch (Exception e){
            logger.error("rankNotificationPushMsgTest error: {}", e.getMessage(), e);
        }
    }

}
