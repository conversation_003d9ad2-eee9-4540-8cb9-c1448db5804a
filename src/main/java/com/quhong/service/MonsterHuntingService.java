package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.DrawPrizeRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.DrawRecordVO;
import com.quhong.data.vo.MonsterHuntingVO;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.MonsterHuntingRedis;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;


/**
 * 怪兽狩猎活动
 */
@Service
public class MonsterHuntingService extends OtherActivityService {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final Interner<String> stringPool = Interners.newWeakInterner();

    // 活动参数
    public static final String BANNER = "https://cdn3.qmovies.tv/youstar/op_1754037962_hutbanner_ar.jpg";
    private static final int RECORD_PAGE_SIZE = 10;
    private static final int LIMIT_INIT_POOL = 100;
    private static final int INIT_POOL_SIZE = 1000;
    public static String ACTIVITY_ID = "688f5953c38edf1f8d2cd543";
    private static String ACTIVITY_NAME = "Monster Hunting";
    //    private static final String GAME_URL = ServerConfig.isProduct() ? "https://h5.bigluckygame.com/game_greedy_six/?appId=666666&gameName=greedy" : "https://api.springluckygame.com/appw/greedy/?appId=666666&gameName=greedy";
    private static String ACTIVITY_URL = "https://static.youstar.live/monster_hunt/?activityId=" + ACTIVITY_ID;
    private static final int MAX_MONSTER_HP = 489960;        // 怪兽最大血量 489960
    private static final int SWORD_DAMAGE_MIN = 1;         // 宝剑最小伤害
    private static final int SWORD_DAMAGE_MAX = 99;         // 宝剑最大伤害
    private static final int MAX_CRITICAL_COUNT = 5;         // 每日最大暴击次数
    private static final int GIFT_SWORD_RATIO = 100;     // 发礼物（100钻石:1宝剑）
    private static int ACTIVITY_START_TIME = 1747620000; // 活动开始时间 (因为不是沙特0点开始，所以需要手动调整)

    // 公共属性
    private static final String ALL = "all";
    private static final String COMMON = "common"; // 公共属性
    private static final String STAGE = "monster_stage_%s"; // 怪兽阶段 monster_stage_日期
    private static final String MONSTER_DAMAGE = "monster_damage_%s"; // 怪兽受到的伤害
    private static final String DAMAGE_RANKING = "damage_ranking_%s"; // 伤害排行榜
    private static final String NOTIFY = "attack_monster_notify_%s"; // 通知
    private static final String ATTACK_DAMAGE_POOL_KEY = "attack_damage_pool_key"; // 攻击伤害池子
    // 用户属性
    private static final String SWORD_NUM = "sword_num_%s";   // 用户宝剑数量 sword_num_日期
    private static final String EXTRA_POINTS = "extra_points_%s"; // 多余积分
    private static final String CRITICAL_COUNT = "critical_count_%s"; // 用户暴击次数 critical_count_日期
    private static final String USER_TASK_LEVEL = "user_task_level"; // 用户成就任务等级
    private static final String USER_REWARD_RECORD = "user_reward_record"; // 用户奖励记录
    private static final String TOTAL_DAMAGE = "total_damage_%s"; // 全服总伤害 total_damage_日期
    // 阶段奖励配置
    private static final List<Integer> STAGE_PERCENTAGES = Arrays.asList(25, 50, 75, 100);
    private static final List<Integer> TASK_LIST = Arrays.asList(5000, 25000, 50000);
    private static final List<Integer> requiredDamageList = new ArrayList<>(5);
    // 奖励配置
    private static final String STAGE_REWARD_KEY = "MonsterAttendStage%s"; // 阶段奖励
    private static final String STAGE_TOP1_REWARD_KEY = "MonsterAttendStageTop1"; // 阶段top1奖励
    private static final String DAILY_RANKING_REWARD_KEY = "MonsterTodayTop%s"; // 日榜奖励
    private static final String JOIN_REWARD_KEY = "MonsterHurtUser";  // 参与奖励
    private static final String ACHIEVEMENT_REWARD_KEY = "MonsterBadge%s";  // 成就奖励
    private static final String CRITICAL_REWARD_KEY = "MonsterHurt"; // 暴击奖励
    // 推送消息
    public static final String ACTION_EN = "View";
    public static final String ACTION_AR = "شاهد";
    public static final String STAGE_TITLE_EN = "The monster's health is only ##";
    public static final String STAGE_TITLE_AR = "نقاط حياة الوحش فقط ##";
    public static final String STAGE_MSG_EN =
            "The # stage of your participation in 'Monster Hunt' today has ended." +
                    "The top 1 of this stage is ##" +
                    "Reward  has been issued, go and kill monsters!";
    public static final String STAGE_MSG_AR = "لقد انتهت المرحلة رقم # من مشاركتك في \"صيد الوحوش\" اليوم." + "المركز الأول في هذه المرحلة هو ##" + "تم إصدار الجائزة، انطلق لقتل المزيد من الوحوش!";
    public static final String END_TITLE_EN = "The monster was defeated";
    public static final String END_TITLE_AR = "تم هزيمة الوحش";
    public static final String END_MSG_EN =
            "The monster you participated in \"Monster Hunt\" has been defeated. Congratulations to #1#, #2#, #3# for being the top 3 hero in this contribution." +
                    "And your reward has been issued today.";
    public static final String END_MSG_AR = "تم القضاء على الوحش الذي شاركت في \"صيد الوحوش\"." + "تهانينا لـ #1#, #2#, #3# على كونهم الأبطال الثلاثة الأوائل في هذه المساهمة." + "وقد تم إصدار جائزتك اليوم.";
    @Resource
    private MonsterHuntingRedis monsterHuntingRedis;
    @Resource
    private OtherActivityService otherActivityService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;


    private String getLocalEventUserKey(String uid) {
        return "lock:monster_hunting:user:" + uid;
    }

    private String getLocalEventAllKey() {
        return "lock:monster_hunting:user:" + ACTIVITY_ID;
    }

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "6881ff61b7dccb16ab055230";
            ACTIVITY_START_TIME = 1747620000;
            ACTIVITY_URL = "https://test2.qmovies.tv/monster_hunt/?activityId=" + ACTIVITY_ID;
        }
        requiredDamageList.clear();
        requiredDamageList.add(0);
        for (int i = 0; i < STAGE_PERCENTAGES.size(); i++) {
            int requiredDamage = getMaxMonsterHp() * STAGE_PERCENTAGES.get(i) / 100;
            requiredDamageList.add(requiredDamage);
        }
        logger.info("requiredDamageList:{}", requiredDamageList);
    }

    /**
     * 获取活动信息
     *
     * @param activityId 活动id
     * @param uid        用户uid
     * @return 活动信息
     */
    public MonsterHuntingVO getInfo(String activityId, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String strDate = getStrDate();
        return buildMonsterHuntingVO(activityId, strDate, uid, activityData);
    }

    /**
     * 攻击怪兽
     *
     * @param activityId 活동id
     * @param uid        用户uid
     * @param num        消耗宝剑数量
     * @return 活动信息
     */
    public MonsterHuntingVO attackMonster(String activityId, String uid, int num) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = checkActivityTime(activityId);

        String strDate;
        int totalDamage;
        List<PrizeConfigVO> rewardList;
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            strDate = getStrDate();
            // 检测怪兽血量
            long monsterDamage = monsterHuntingRedis.getUserData(activityId, COMMON, String.format(MONSTER_DAMAGE, strDate));
            long monsterHp = getMaxMonsterHp() - monsterDamage;
            int stage = getMonsterStage(monsterHp);
            if (monsterHp <= 0) {
                throw new CommonH5Exception(new HttpCode(1, "The monster has been defeated. Come back tomorrow at 5 A.M.!", "تم هزيمة الوحش، عد مرة أخرى غدًا في الساعة 5 صباحًا!"));
            }
            // 检查宝剑数量
            long userSwordNum = monsterHuntingRedis.getUserData(activityId, uid, String.format(SWORD_NUM, strDate));
            if (userSwordNum < num) {
                throw new CommonH5Exception(new HttpCode(1, "You don't have enough swords, go to send gifts", "ليس لديك عدد كافٍ من السيوف، توجّه لإرسال الهدايا"));
            }
            // 消耗宝剑
            monsterHuntingRedis.incUserData(activityId, uid, String.format(SWORD_NUM, strDate), -num);
            doReportItemsChangeEvent(activityId, uid, 2, num, 2);
            // 计算伤害
            AttackResult attackResult = calculateDamage(activityId, strDate, uid, num, actorData);
            totalDamage = attackResult.totalDamage;
            rewardList = attackResult.rewardList;


            // long oldDamage = monsterHuntingRedis.getUserData(activityId, COMMON, String.format(MONSTER_DAMAGE, strDate));
            // 更新怪兽血量
            long newTotalDamage = monsterHuntingRedis.incUserData(activityId, COMMON, String.format(MONSTER_DAMAGE, strDate), totalDamage);

            int oldIndex = getBaseIndexLevel((int) monsterDamage, requiredDamageList);
            int afterIndex = getBaseIndexLevel((int) newTotalDamage, requiredDamageList);

            // 更新伤害排行榜
            long newTotalUserDamage = monsterHuntingRedis.incrRankingScore(activityId, String.format(DAMAGE_RANKING, ALL), uid, totalDamage);
            monsterHuntingRedis.incrRankingScore(activityId, String.format(DAMAGE_RANKING, strDate), uid, totalDamage);
            monsterHuntingRedis.incrRankingScore(activityId, String.format(DAMAGE_RANKING, strDate + "_" + stage), uid, totalDamage);
            // 检查并发放阶段奖励
            if (afterIndex > oldIndex) {
                checkAndSendStageRewards(activityId, strDate, newTotalDamage);
            }
            // 发放成就奖励
            sendAchievementReward(activityId, uid, newTotalUserDamage);
        }
        MonsterHuntingVO result = buildMonsterHuntingVO(activityId, strDate, uid, activityData);
        result.setRewardList(showPrizeList(rewardList));
        result.setAttackDamage(totalDamage);
        return result;
    }

    /**
     * 获取奖励记录
     *
     * @param activityId 活동id
     * @param uid        用户uid
     * @param page       页码
     * @return 奖励记录
     */
    public PageVO<DrawRecordVO> getRewardRecord(String activityId, String uid, int page) {
        PageVO<DrawRecordVO> pageVO = new PageVO<>();
        List<DrawRecordVO> list = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        List<String> recordList = monsterHuntingRedis.getUserRecordList(activityId, USER_REWARD_RECORD, uid, start, end);
        for (String record : recordList) {
            list.add(JSONObject.parseObject(record, DrawRecordVO.class));
        }
        pageVO.setList(list);
        pageVO.setNextUrl(list.size() < RECORD_PAGE_SIZE ? "" : String.valueOf(page + 1));
        return pageVO;
    }

    /**
     * 处理礼物消息
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String uid = giftData.getFrom_uid();
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            String strDate = getStrDate();
            // 下注钻石数
            int sendBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            Map<String, Long> userMapData = monsterHuntingRedis.getUserData(ACTIVITY_ID, uid);
            long totalDiamonds = userMapData.getOrDefault(String.format(EXTRA_POINTS, strDate), 0L) + sendBeans;
            int swordNum = (int) (totalDiamonds / GIFT_SWORD_RATIO);
            int extraPoints = (int) (totalDiamonds % GIFT_SWORD_RATIO);
            monsterHuntingRedis.setUserData(ACTIVITY_ID, uid, String.format(EXTRA_POINTS, strDate), extraPoints);
            if (swordNum > 0) {
                // 增加用户宝剑数量
                monsterHuntingRedis.incUserData(ACTIVITY_ID, uid, String.format(SWORD_NUM, strDate), swordNum);
                doReportItemsChangeEvent(ACTIVITY_ID, uid, 1, swordNum, 1);
            }
        }
    }



    /**
     * 检查并发放阶段奖励
     */
    private void checkAndSendStageRewards(String activityId, String strDate, long totalDamage) {
        synchronized (stringPool.intern(getLocalEventAllKey())) {
            for (int i = 0; i < STAGE_PERCENTAGES.size(); i++) {
                int requiredDamage = getMaxMonsterHp() * STAGE_PERCENTAGES.get(i) / 100;
                int stage = i + 1;
                // 如果总伤害达到了该阶段要求，且该阶段奖励还未发放过
                if (totalDamage >= requiredDamage && monsterHuntingRedis.getUserData(activityId, COMMON, String.format(STAGE, strDate)) < stage) {
                    // 标记该阶段奖励已发放
                    monsterHuntingRedis.setUserData(activityId, COMMON, String.format(STAGE, strDate), stage);
                    // 发放阶段奖励
                    Set<String> allUsers = monsterHuntingRedis.getRankingMap(activityId, String.format(DAMAGE_RANKING, strDate + "_" + stage), 0, -1).keySet();
                    sendStageRewardAndMsg(activityId, strDate, stage, allUsers);
                    logger.info("Stage {} reward sent for activity {}, total damage: {}", stage, activityId, totalDamage);
                }
            }
        }
    }

    private void sendAchievementReward(String activityId, String uid, long totalDamage) {
        if (totalDamage >= TASK_LIST.get(0)) {
            int curLevel = (int) monsterHuntingRedis.getUserData(activityId, uid, USER_TASK_LEVEL);
            int newLevel = 0;
            for (int i = 0; i < TASK_LIST.size(); i++) {
                if (totalDamage >= TASK_LIST.get(i) && curLevel < i + 1) {
                    // 发送奖励
                    sendRewardAndSaveRecord(activityId, uid, String.format(ACHIEVEMENT_REWARD_KEY, i + 1), "MonsterHunting-badge");
                    newLevel = i + 1;
                }
            }
            if (newLevel > curLevel) {
                monsterHuntingRedis.setUserData(activityId, uid, USER_TASK_LEVEL, newLevel);
            }
        }
    }

    /**
     * 发放阶段奖励
     */
    private void sendStageRewardAndMsg(String activityId, String strDate, int stage, Set<String> allUsers) {
        executor.execute(() -> {
            try {
                String top1Uid = monsterHuntingRedis.getRankingMap(ACTIVITY_ID, String.format(DAMAGE_RANKING, strDate + "_" + stage), 1).keySet().iterator().next();
                ActorData top1User = actorDao.getActorDataFromCache(top1Uid);
                String top1Name = top1User != null ? top1User.getName() : "";
                if (stage == 4) {
                    sendBeatReward(activityId, strDate);
                }
                sendRewardAndSaveRecord(activityId, top1Uid, STAGE_TOP1_REWARD_KEY, "MonsterHunting-stage top1");
                // 获取所有参与的用户
                String rewardKey = String.format(STAGE_REWARD_KEY, stage);
                ResourceKeyConfigData configData = resourceKeyHandlerService.getConfigData(rewardKey);
                if (configData == null || CollectionUtils.isEmpty(configData.getResourceMetaList())) {
                    logger.error("sendResourceData not find. resourceKey={}", rewardKey);
                    return;
                }
                for (String uid : allUsers) {
                    try {
                        // 发送奖励
                        sendRewardAndSaveRecord(activityId, uid, configData, "MonsterHunting-every stage reward");
                        if (stage != 4) {
                            // 发送阶段消息
                            sendStageMsg(uid, stage, top1Name);
                        }
                    } catch (Exception e) {
                        logger.error("sendStageRewardAndMsg error. activityId={} stage={} uid={} stage={} {}", activityId, stage, uid, stage, e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                logger.error("Send stage reward to all users error, stage: {}", stage, e);
            }
        });
    }

    private List<PrizeConfigVO> sendRewardAndSaveRecord(String activityId, String uid, String rewardKey, String title) {
        if (!StringUtils.hasLength(rewardKey) || !StringUtils.hasLength(uid)) {
            return Collections.emptyList();
        }
        ResourceKeyConfigData configData = resourceKeyHandlerService.getConfigData(rewardKey);
        if (configData == null || CollectionUtils.isEmpty(configData.getResourceMetaList())) {
            logger.error("sendResourceData not find. resourceKey={}", rewardKey);
            return Collections.emptyList();
        }
        return sendRewardAndSaveRecord(activityId, uid, configData, title);
    }

    private List<PrizeConfigVO> sendRewardAndSaveRecord(String activityId, String uid, ResourceKeyConfigData configData, String title) {
        List<PrizeConfigVO> list = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : configData.getResourceMetaList()) {
            resourceKeyHandlerService.sendOneResourceData(uid, resourceMeta, 905, title, title, title, "", "", 1);
            // 记录奖励发放记录
            DrawRecordVO record = new DrawRecordVO();
            PrizeConfigVO reward = getPrizeConfigVO(resourceMeta);
            record.setReward(reward);
            record.setCtime(DateHelper.getNowSeconds());
            monsterHuntingRedis.saveUserRecord(activityId, USER_REWARD_RECORD, uid, JSONObject.toJSONString(record));
            list.add(reward);
        }
        return list;
    }

    public void sendStageMsg(String uid, int stage, String top1) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            boolean isEn = actorData.getSlang() == SLangType.ENGLISH;
            String actText = isEn ? ACTION_EN : ACTION_AR;
            String title = (isEn ? STAGE_TITLE_EN : STAGE_TITLE_AR).replace("##", (100 - stage * 25) + "%");
            String body = (isEn ? STAGE_MSG_EN : STAGE_MSG_AR).replace("##", top1).replace("#", stage + "");
            otherActivityService.commonOfficialMsg(uid, BANNER, 0, 0, actText, title, body, ACTIVITY_URL);
        } catch (Exception e) {
            logger.error("sendPushMsg error e={}", e.getMessage(), e);
        }
    }

    public void sendBeatMsg(String uid, List<String> topUserNameList) {
        try {

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            boolean isEn = actorData.getSlang() == SLangType.ENGLISH;
            String actText = isEn ? ACTION_EN : ACTION_AR;
            String title = isEn ? END_TITLE_EN : END_TITLE_AR;
            String body = (isEn ? END_MSG_EN : END_MSG_AR).replace("#1#", topUserNameList.get(0)).replace("#2#", topUserNameList.get(1)).replace("#3#", topUserNameList.get(2));
            otherActivityService.commonOfficialMsg(uid, BANNER, 0, 0, actText, title, body, ACTIVITY_URL);
        } catch (Exception e) {
            logger.error("sendPushMsg error e={}", e.getMessage(), e);
        }
    }

    private PrizeConfigVO getPrizeConfigVO(ResourceKeyConfigData.ResourceMeta resourceMeta) {
        return new PrizeConfigVO(resourceMeta.getMetaId(), resourceMeta.getResourceIcon(), resourceMeta.getResourceNameEn(), resourceMeta.getResourceNameAr(), String.valueOf(resourceMeta.getResourceType()), resourceMeta.getResourceId(), resourceMeta.getResourceTime(), resourceMeta.getResourceNumber());
    }

    /**
     * 计算攻击伤害
     */
    private AttackResult calculateDamage(String activityId, String strDate, String uid, int swordNum, ActorData actorData) {
        int criticalCount = 0;
        int totalDamage = 0;
        int rewardCount = 0;
        initDrawPrizePool(activityId);
        List<String> numList = monsterHuntingRedis.popRewardFromPool(activityId, ATTACK_DAMAGE_POOL_KEY, swordNum);
        List<PrizeConfigVO> rewardList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(numList)) {
            for (String num : numList) {
                int damage = Integer.parseInt(num);
                if (damage >= 90) {
                    if (monsterHuntingRedis.incUserData(activityId, uid, String.format(CRITICAL_COUNT, strDate), 1) <= MAX_CRITICAL_COUNT) {
                        // 发送暴击奖励
                        rewardList.addAll(sendRewardAndSaveRecord(activityId, uid, CRITICAL_REWARD_KEY, "MonsterHunting-hit90 reward"));
                    }
                    criticalCount++;
                    rewardCount++;
                }
                monsterHuntingRedis.addNotify(activityId, String.format(NOTIFY, strDate), JSONObject.toJSONString(new MonsterHuntingVO.Notify(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()), actorData.getName(), damage)));
                totalDamage += damage;
            }
        }
        doDrawPrizeRecordEvent(uid, swordNum, totalDamage, criticalCount);
        return new AttackResult(totalDamage, rewardCount, rewardList);
    }

    private void initDrawPrizePool(String activityId) {
        int poolSize = monsterHuntingRedis.getPoolSize(activityId, ATTACK_DAMAGE_POOL_KEY);
        if (poolSize <= 0) {
            expandPrizePool(activityId);
        } else if (poolSize <= LIMIT_INIT_POOL) {
            executor.execute(() -> expandPrizePool(activityId));
        }
    }

    private void expandPrizePool(String activityId) {
        List<String> prizePoolList = new ArrayList<>(INIT_POOL_SIZE);
        // 创建一个包含1-99的数字列表，每个数字10次
        for (int i = SWORD_DAMAGE_MIN; i <= SWORD_DAMAGE_MAX; i++) {
            for (int j = 0; j < 10; j++) {
                prizePoolList.add(i + "");
            }
        }
        // 打乱奖池顺序
        Collections.shuffle(prizePoolList);
        monsterHuntingRedis.pushRewardInPool(activityId, ATTACK_DAMAGE_POOL_KEY, prizePoolList);
    }

    /**
     * 构建活动VO
     */
    private MonsterHuntingVO buildMonsterHuntingVO(String activityId, String strDate, String uid, OtherRankingActivityData activityData) {
        Map<String, Long> userDataMap = monsterHuntingRedis.getUserData(activityId, uid);
        Map<String, Long> commonDataMap = monsterHuntingRedis.getUserData(activityId, COMMON);
        MonsterHuntingVO vo = new MonsterHuntingVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        int monsterHp = getMaxMonsterHp() - commonDataMap.getOrDefault(String.format(MONSTER_DAMAGE, strDate), 0L).intValue();
        int monsterStage = getMonsterStage(monsterHp);
        // 用户数据
        vo.setSwordNum(userDataMap.getOrDefault(String.format(SWORD_NUM, strDate), 0L).intValue());
        vo.setUserTotalDamage((int) monsterHuntingRedis.getRankingScore(activityId, String.format(DAMAGE_RANKING, ALL), uid));
        vo.setUserTodayDamage((int) monsterHuntingRedis.getRankingScore(activityId, String.format(DAMAGE_RANKING, strDate), uid));
        vo.setMyRank(buildMyRank(activityId, strDate, uid));
        vo.setMyStageRank(monsterHuntingRedis.getRankingRank(activityId, String.format(DAMAGE_RANKING, strDate + "_" + monsterStage), uid));
        // 全服数据
        vo.setMonsterHp(monsterHp);
        vo.setMaxMonsterHp(getMaxMonsterHp());
        vo.setTotalDamage((int) monsterHuntingRedis.getUserData(activityId, COMMON, String.format(TOTAL_DAMAGE, strDate)));
        vo.setActivityUserNum(monsterHuntingRedis.getRankingSize(activityId, String.format(DAMAGE_RANKING, ALL)));
        vo.setStage(monsterStage);
        vo.setGameUrl("");
        vo.setNotifyList(buildNotifyList(activityId, strDate));
        return vo;
    }

    private List<MonsterHuntingVO.Notify> buildNotifyList(String activityId, String strDate) {
        List<MonsterHuntingVO.Notify> notifyList = new ArrayList<>();
        List<String> notifyListStr = monsterHuntingRedis.getNotifyList(activityId, String.format(NOTIFY, strDate), 6);
        for (String notifyStr : notifyListStr) {
            notifyList.add(JSONObject.parseObject(notifyStr, MonsterHuntingVO.Notify.class));
        }
        return notifyList;
    }

    /**
     * 构建我的排名
     */
    private OtherMyRankVO buildMyRank(String activityId, String strDate, String uid) {
        OtherMyRankVO myRank = new OtherMyRankVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData != null) {
            myRank.setName(actorData.getName());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        }
        myRank.setRank(monsterHuntingRedis.getRankingRank(activityId, String.format(DAMAGE_RANKING, strDate), uid));
        myRank.setScore((int) monsterHuntingRedis.getRankingScore(activityId, String.format(DAMAGE_RANKING, strDate), uid));
        return myRank;
    }

    private String getStrDate() {
        return getStrDate(DateHelper.getNowSeconds());
    }

    private String getStrDate(int nowTime) {
        return (nowTime - ACTIVITY_START_TIME) / TimeUnit.DAYS.toSeconds(1) + "";
    }

    private int getMonsterStage(long monsterHp) {
        long maxMonsterHp = getMaxMonsterHp();
        if (monsterHp >= maxMonsterHp * STAGE_PERCENTAGES.get(2) / 100) {
            return 1;
        } else if (monsterHp >= maxMonsterHp * STAGE_PERCENTAGES.get(1) / 100) {
            return 2;
        } else if (monsterHp >= maxMonsterHp * STAGE_PERCENTAGES.get(0) / 100) {
            return 3;
        } else {
            return 4;
        }
    }

    public void sendBeatReward(String activityId, String strDate) {
        executor.execute(() -> {
            try {

                Map<String, Long> rankingMap = monsterHuntingRedis.getRankingMap(activityId, String.format(DAMAGE_RANKING, strDate), 0, -1);
                if (!CollectionUtils.isEmpty(rankingMap)) {
                    int rank = 1;
                    List<String> top3NameList = new ArrayList<>();
                    for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
                        String aid = entry.getKey();
                        String rewardKey = "";
                        if (rank <= 3) {
                            rewardKey = String.format(DAILY_RANKING_REWARD_KEY, "1-3");
                            ActorData actorData = actorDao.getActorDataFromCache(aid);
                            top3NameList.add(actorData != null ? actorData.getName() : "");
                        } else if (rank <= 6) {
                            rewardKey = String.format(DAILY_RANKING_REWARD_KEY, "4-6");
                        } else if (rank <= 10) {
                            rewardKey = String.format(DAILY_RANKING_REWARD_KEY, "7-10");
                        }
                        if (StringUtils.hasLength(rewardKey)) {
                            sendRewardAndSaveRecord(ACTIVITY_ID, aid, rewardKey, "MonsterHunting-hurt ranking");
                        }
                        // 参与奖励
                        sendRewardAndSaveRecord(ACTIVITY_ID, aid, JOIN_REWARD_KEY, "MonsterHunting-defeat");
                        rank++;
                    }
                    // 发送击败消息
                    rankingMap.keySet().forEach(aid -> sendBeatMsg(aid, top3NameList));
                }
            } catch (Exception e) {
                logger.error("sendBeatReward error. activityId={} strDate={} {}", activityId, strDate, e.getMessage(), e);
            }
        });
    }

    private void doReportItemsChangeEvent(String activityId, String uid, int action, int num, int source) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setActivity_name(ACTIVITY_NAME);
        event.setActive_id(activityId);
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void doDrawPrizeRecordEvent(String uid, int costNum, int totalDamage, int criticalCount) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_NAME);
        event.setCost_ticket(costNum);
        event.setDraw_nums(costNum);
        event.setDraw_success_nums(criticalCount);
        event.setTicket_type(0);
        event.setSence_detail(0);
        event.setDraw_detail("");
        event.setDraw_result(String.format("{\"hurt_value\":%s}", totalDamage));
        eventReport.track(new EventDTO(event));
    }

    private int getMaxMonsterHp() {
        return MAX_MONSTER_HP;
    }

    /**
     * 攻击结果内部类
     */
    private static class AttackResult {
        private final int totalDamage; // 总伤害
        private final int rewardCount; // 暴击奖励次数
        private final List<PrizeConfigVO> rewardList; // 奖励列表

        public AttackResult(int totalDamage, int rewardCount, List<PrizeConfigVO> rewardList) {
            this.totalDamage = totalDamage;
            this.rewardCount = rewardCount;
            this.rewardList = rewardList;
        }

        public int getTotalDamage() {
            return totalDamage;
        }

        public int getRewardCount() {
            return rewardCount;
        }

        public List<PrizeConfigVO> getRewardList() {
            return rewardList;
        }
    }
}
