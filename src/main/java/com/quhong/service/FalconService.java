package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityExchangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.ScoreRecordEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.FalconVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.FriendsListRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FalconService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(FalconService.class);
    private static final String ACTIVITY_TOTAL_INTEGRAL = "totalIntegral";
    private static final String ACTIVITY_TITLE_EN = "Falcon Master";
    private static final String ACTIVITY_TITLE_AR = "سيد الصقر";
    private static final String ACTIVITY_DESC_EXCHANGE = "Falcon Master-exchange";
    private static final String ACTIVITY_DESC_GIVE = "Falcon Master-give";
    private static final String ACTIVITY_DESC_WISH = "Falcon Master-wishing well";
    private static final String ACTIVITY_ICON = "https://cdn3.qmovies.tv/youstar/op_1724997484_FJRK.png";
    public static final String ACTIVITY_ID = "67dd3987cded2d5afd6bba23";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/falcon_master2024_new/?activityId=%s&shareId=50", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/falcon_master2024_new/?activityId=%s&shareId=12", ACTIVITY_ID);
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final String FALCON_EXCHANGE_KEY = "falconExchangeItem";
    private static final String ONLY_ONE_EXCHANGE_ID = "aaa"; // 2025活动已经不下发这个奖励，配置已经去除，配置保留

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Autowired(required = false)
    private EventReport eventReport;

    // 活动用户相关数据记录
    private String getHashActivityId(String activityId, String uid) {
        return String.format("falcon:%s:%s", activityId, uid);
    }

    // 活动用户相关每日数据记录
    private String getDailyHashActivityId(String activityId, String uid, String dateStr) {
        return String.format("falcon:%s:%s:%s", activityId, uid, dateStr);
    }

    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getExchangeHKey(String metaId) {
        return String.format("exchange:%s", metaId);
    }

    public String getRecordActivityKey(String activityId, String uid) {
        return String.format("historyRecord:%s:%s", activityId, uid);
    }

    // 许愿set-key
    private String getUserWishSetKey(String activityId, String drawType) {
        return String.format("userWishSet:%s:%s", activityId, drawType);
    }

    private String getUserWishReachKey(String activityId, String drawType) {
        return String.format("userReachWish:%s:%s", activityId, drawType);
    }

    public FalconVO falconConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        FalconVO vo = new FalconVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        String hashActivityId = this.getHashActivityId(activityId, uid);
        Map<String, Integer> userMapData = activityCommonRedis.getCommonHashAll(hashActivityId);

        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(FALCON_EXCHANGE_KEY);
        if (resourceKeyConfigData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "Not Find Exchange Item");
        }

        List<FalconVO.ExchangeConfig> falconConfigVOList = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta config : resourceKeyConfigData.getResourceMetaList()) {
            FalconVO.ExchangeConfig prizeConfigVO = new FalconVO.ExchangeConfig();
            String metaId = config.getMetaId();
            prizeConfigVO.setDrawType(metaId);
            prizeConfigVO.setNameEn(config.getResourceNameEn());
            prizeConfigVO.setNameAr(config.getResourceNameAr());
            prizeConfigVO.setIconEn(config.getResourceIcon());
            prizeConfigVO.setResourcePreview(config.getResourcePreview());
            prizeConfigVO.setResourcePrice(config.getResourcePrice());
            prizeConfigVO.setRewardType(String.valueOf(config.getResourceType()));
            prizeConfigVO.setSourceId(config.getResourceId());
            prizeConfigVO.setRewardTime(config.getResourceTime());
            prizeConfigVO.setRewardNum(config.getResourceNumber());
            prizeConfigVO.setRateNum(Integer.parseInt(config.getRateNumber()));

            prizeConfigVO.setLikeStatus(userMapData.getOrDefault(metaId, 0));
            if (ONLY_ONE_EXCHANGE_ID.equals(metaId)) {
                prizeConfigVO.setExchange(userMapData.getOrDefault(this.getExchangeHKey(config.getMetaId()), 0));
            }
            falconConfigVOList.add(prizeConfigVO);
        }
        vo.setExchangeConfigList(falconConfigVOList);


        // TODO 设置积分数
        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        String dailyHashActivityId = this.getDailyHashActivityId(activityId, uid, dateStr);
        Map<String, Integer> dailyUserMapData = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        vo.setTotalIntegral(dailyUserMapData.getOrDefault(ACTIVITY_TOTAL_INTEGRAL, 0));
        return vo;
    }

    // 许愿
    public void falconLike(String activityId, String uid, String drawType) {
        checkActivityTime(activityId);
        synchronized (stringPool.intern("falconLike" + uid)) {

            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(FALCON_EXCHANGE_KEY);
            if (resourceKeyConfigData == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "Not Find Exchange Item");
            }

            Map<String, ResourceKeyConfigData.ResourceMeta> falconConfigMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            if (falconConfigMap.get(drawType) == null) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userMapData = activityCommonRedis.getCommonHashAll(hashActivityId);
            int likeStatus = userMapData.getOrDefault(drawType, 0);
            if (likeStatus > 0) {
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            activityCommonRedis.setCommonHashNum(hashActivityId, drawType, 1);
            activityCommonRedis.addCommonSetData(getUserWishSetKey(activityId, drawType), uid);
        }
    }

    // 兑换
    public void falconExchange(String activityId, String uid, String drawType, String aid) {
        OtherRankingActivityData activityData = checkActivityTime(activityId);
        synchronized (stringPool.intern("falconExchange" + uid)) {
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(FALCON_EXCHANGE_KEY);
            if (resourceKeyConfigData == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "Not Find Exchange Item");
            }

            Map<String, ResourceKeyConfigData.ResourceMeta> falconConfigMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            if (falconConfigMap.get(drawType) == null) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            ResourceKeyConfigData.ResourceMeta drawConfig = falconConfigMap.get(drawType);
            if (drawConfig == null || StringUtils.isEmpty(aid)) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            // 搜索id、许愿池赠送、自己兑换
            String receiveAid;
            String ACTIVITY_DESC;
            int getWay = 0;
            int changedType = 0;
            if (aid.length() != 24) {
                ActorData actorData = actorDao.getActorByStrRid(aid);
                if (actorData == null) {
                    throw new CommonH5Exception(ActivityHttpCode.USER_NOT_FIND_EXIST);
                }
                receiveAid = actorData.getUid();
                if (receiveAid.equals(uid)) {
                    throw new CommonH5Exception(ActivityHttpCode.CANNOT_YOUR_SELF);
                }
                ACTIVITY_DESC = ACTIVITY_DESC_GIVE;
                getWay = 10;
                changedType = 3;

            } else {
                receiveAid = aid;
                ACTIVITY_DESC = receiveAid.equals(uid) ? ACTIVITY_DESC_EXCHANGE : ACTIVITY_DESC_WISH;
                getWay = receiveAid.equals(uid) ? 11 : 10;
                changedType = receiveAid.equals(uid) ? 2 : 4;
            }

            // 特殊奖品兑换处理
            int currentTime = DateHelper.getNowSeconds();
            String hashActivityId = this.getHashActivityId(activityId, uid);
            if (ONLY_ONE_EXCHANGE_ID.equals(drawType)) {
                Map<String, Integer> userMapData = activityCommonRedis.getCommonHashAll(hashActivityId);
                int onlyOneExchange = userMapData.getOrDefault(this.getExchangeHKey(drawType), 0);
                if (onlyOneExchange > 0) {
                    throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
                }
            }

            // 积分操作
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String dailyHashActivityId = this.getDailyHashActivityId(activityId, uid, dateStr);
            Map<String, Integer> dailyUserMapData = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int totalIntegral = dailyUserMapData.getOrDefault(ACTIVITY_TOTAL_INTEGRAL, 0);
            int rateNumber = Integer.parseInt(drawConfig.getRateNumber());
            if (totalIntegral < rateNumber) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER_POINT);
            }
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, ACTIVITY_TOTAL_INTEGRAL, -rateNumber);
            ActorData actorData = actorDao.getActorDataFromCache(receiveAid);
            resourceKeyHandlerService.sendOneResourceData(actorData, drawConfig, 905, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, activityData.getAcUrl(), ACTIVITY_ICON, getWay);
            this.doReportEvent(uid, -rateNumber, changedType, drawConfig.getResourceNameEn());

            // 删除许愿
            activityCommonRedis.removeCommonSetData(getUserWishSetKey(activityId, drawType), receiveAid);

            // 增加对优惠券只有一次兑换机会
            if (ONLY_ONE_EXCHANGE_ID.equals(drawType)) {
                activityCommonRedis.incCommonHashNum(hashActivityId, this.getExchangeHKey(drawType), 1);
            }

            // 增加历史记录
            FalconVO.ExchangeConfig prizeConfig = new FalconVO.ExchangeConfig();
            prizeConfig.setNameEn(drawConfig.getResourceNameEn());
            prizeConfig.setNameAr(drawConfig.getResourceNameAr());
            prizeConfig.setIconEn(drawConfig.getResourceIcon());
            prizeConfig.setRewardType(String.valueOf(drawConfig.getResourceType()));
            prizeConfig.setCtime(currentTime);
            if (uid.equals(aid)) {
                prizeConfig.setSendType(0);
                prizeConfig.setFriendId(uid);
                activityCommonRedis.addCommonListData(getRecordActivityKey(activityId, receiveAid), JSONObject.toJSONString(prizeConfig));

            } else {
                prizeConfig.setSendType(1);
                prizeConfig.setFriendId(receiveAid);
                activityCommonRedis.addCommonListData(getRecordActivityKey(activityId, uid), JSONObject.toJSONString(prizeConfig));


                prizeConfig.setSendType(2);
                prizeConfig.setFriendId(uid);
                activityCommonRedis.addCommonListData(getRecordActivityKey(activityId, receiveAid), JSONObject.toJSONString(prizeConfig));
                activityCommonRedis.addCommonListRecord(getUserWishReachKey(activityId, drawType), receiveAid);

                int slang = actorData.getSlang();
                String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
                String title = slang == SLangType.ARABIC ? "هناك هدية من صديقك." : "There's a gift from your friend";
                String body = slang == SLangType.ARABIC ? "صديق عام أرسل لك هدية، اذهب لتتحقق ممن أرسلها وما هي الهدية.\n" + "لا تنسى أن تعبر عن شكرك." : "A general friend send a gift to you, go to check who send you and what's the gift~\n" + "Don't forget to express your gratitude.";
                String picture = "https://cdn3.qmovies.tv/youstar/op_1742200583_Falcon_Master_banner.jpg";
                commonOfficialMsg(receiveAid, picture, 0, 0, actText, title, body, activityData.getAcUrl());
            }
        }
    }

    // 许愿搜索
    public FalconVO.WishUserInfo falconSearch(String activityId, String uid, String search) {
        FalconVO.WishUserInfo vo = new FalconVO.WishUserInfo();
        if (StringUtils.isEmpty(search)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorByStrRid(search);
        if (actorData == null) {
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_FIND_EXIST);
        }

        vo.setAid(actorData.getUid());
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    // 许愿user
    public FalconVO falconWishList(String activityId, String uid, String drawType, int page) {
        FalconVO vo = new FalconVO();

        // 记录已实现愿望
        if (page == 1) {
            List<FalconVO.WishUserInfo> reachUserList = new ArrayList<>();
            List<String> reachRecordList = activityCommonRedis.getCommonListRecord(getUserWishReachKey(activityId, drawType));
            for (String reachUid : reachRecordList) {
                ActorData actorData = actorDao.getActorDataFromCache(reachUid);
                FalconVO.WishUserInfo reachUserInfo = new FalconVO.WishUserInfo();
                reachUserInfo.setName(actorData.getName());
                reachUserList.add(reachUserInfo);
            }
            vo.setReachUserList(reachUserList);
        }

        // 推荐列表
        Set<String> wishUidSet = activityCommonRedis.getCommonSetMember(getUserWishSetKey(activityId, drawType));
        Set<String> friendUidSet = friendsListRedis.getFriendList(uid);
        friendUidSet.retainAll(wishUidSet);
        wishUidSet.remove(uid);
        wishUidSet.removeAll(friendUidSet);

        List<String> wishUidList = new ArrayList<>(wishUidSet);
        wishUidList.addAll(0, friendUidSet);
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = Math.min(page * RECORD_PAGE_SIZE, wishUidList.size());

        if (start >= wishUidList.size()) {
            vo.setWishUserList(Collections.emptyList());
            vo.setNextUrl(-1);
        }

        List<FalconVO.WishUserInfo> wishUserList = new ArrayList<>();
        List<String> pageUidList = wishUidList.subList(start, end);

        for (String wishUid : pageUidList) {
            try {
                FalconVO.WishUserInfo wishUserInfo = new FalconVO.WishUserInfo();
                ActorData actorData = actorDao.getActorDataFromCache(wishUid);
                if (actorData == null) {
                    logger.error("falconWishList error:{}", wishUid);
                    continue;
                }

                wishUserInfo.setAid(actorData.getUid());
                wishUserInfo.setName(actorData.getName());
                wishUserInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                wishUserList.add(wishUserInfo);
            } catch (Exception e) {
                logger.error("falconWishList wishUid:{}, error:{}", wishUid, e.getMessage());
                continue;
            }
        }

        vo.setWishUserList(wishUserList);
        if (wishUserList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    public FalconVO falconRecord(String activityId, String uid, int page) {

        FalconVO vo = new FalconVO();
        List<FalconVO.ExchangeConfig> prizeConfigList = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        String recordListKey = getRecordActivityKey(activityId, uid);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (String item : prizeKeyTimeList) {
            FalconVO.ExchangeConfig prizeConfig = JSONObject.parseObject(item, FalconVO.ExchangeConfig.class);

            if (prizeConfig.getFriendId().equals(uid)) {
                prizeConfig.setFriendId(actorData.getStrRid());
            } else {
                ActorData otherData = actorDao.getActorDataFromCache(prizeConfig.getFriendId());
                prizeConfig.setFriendId(otherData.getStrRid());
            }
            prizeConfigList.add(prizeConfig);
        }
        vo.setRecordList(prizeConfigList);
        if (prizeConfigList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    private void doReportEvent(String uid, int changed, int changedType, String changedDesc) {
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(10);
        event.setScore_changed_detail(changedType);
        event.setScore_changed_desc(changedDesc);
        eventReport.track(new EventDTO(event));
    }

    public void handleGiftMqMsg(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        synchronized (stringPool.intern(fromUid)) {
            // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String dailyHashActivityId = this.getDailyHashActivityId(activityId, fromUid, dateStr);

            int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, ACTIVITY_TOTAL_INTEGRAL, totalBeans);
            this.doReportEvent(fromUid, totalBeans, 1, giftData.getGname());
        }
    }
}
