package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.AsyncConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.FcmMsgTypeConstant;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.BackUserDTO;
import com.quhong.data.dto.SendFcmDTO;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.BackUserInfoVO;
import com.quhong.data.vo.RankVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.*;
import com.quhong.mysql.dao.InviteBackDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.InviteBackData;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.redis.InviteBackUserCodeRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.utils.PageUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 回归活动收齐
 */
@Service
public class BackUserCollectService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(BackUserCollectService.class);
    private static final String ACTIVITY_ID = "66e2cbbf756ffdd95488113b"; // 固定的id，回归活动是永久的
    private static String ACTIVITY_BACK_URL = String.format("https://static.youstar.live/back_user2025/?activityId=%s", ACTIVITY_ID);
    private static String ACTIVITY_INVITER_BACK_URL = String.format("https://static.youstar.live/back_friend2025/?activityId=%s", ACTIVITY_ID);

    private static final String DAU_2 = "dau2";
    private static final String DAU_3 = "dau3";
    private static final String DAU_5 = "dau5";
    private static final String DAU_7 = "dau7";

    private static final List<Integer> GIFT_BEANS_LIST = Arrays.asList(0, 59, 299, 1999, 9999);

    private static final List<Integer> FRIEND_NUM_LIST = Arrays.asList(0, 1, 5, 10, 20);

    private static final List<String> GIFT_KEY_LIST = Arrays.asList("gift1", "gift2", "gift3", "gift4");

    private static final List<String> FRIEND_KEY_LIST = Arrays.asList("friend1", "friend2", "friend3", "friend4");

    private static final List<String> CHARGE_KEY_LIST = Arrays.asList("charge1", "charge2", "charge3");

    private static final List<String> DAU_KEY_LIST = Arrays.asList(DAU_2, DAU_3, DAU_5, DAU_7);

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    public static final Map<String, String> TYPE_BACK_CONFIG_CHARGE_PRODUCT_MAP = new HashMap<String, String>() {
        {
            put("product_coins_diamonds1", "back_ac_reward_099_key"); //
            put("product_coins_diamonds3", "back_ac_reward_1999_key"); //
            put("product_coins_diamonds4", "back_ac_reward_4999_key"); //
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_DAU_MAP = new HashMap<String, String>() {
        {
            put(DAU_2, "back_ac_reward_day2_key");
            put(DAU_3, "back_ac_reward_day3_key");
            put(DAU_5, "back_ac_reward_day5_key");
            put(DAU_7, "back_ac_reward_day7_key");
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_CHARGE_MAP = new HashMap<String, String>() {
        {
            put("charge1", "back_ac_reward_099_key"); // product_coins_diamonds1
            put("charge2", "back_ac_reward_1999_key"); // product_coins_diamonds3
            put("charge3", "back_ac_reward_4999_key"); // product_coins_diamonds4
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_GIFT_MAP = new HashMap<String, String>() {
        {
            put("gift1", "back_ac_reward_gift1_key");
            put("gift2", "back_ac_reward_gift2_key");
            put("gift3", "back_ac_reward_gift3_key");
            put("gift4", "back_ac_reward_gift4_key");
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_FRIEND_MAP = new HashMap<String, String>() {
        {
            put("friend1", "back_ac_reward_friend1_key");
            put("friend2", "back_ac_reward_friend2_key");
            put("friend3", "back_ac_reward_friend3_key");
            put("friend4", "back_ac_reward_friend4_key");
        }
    };

    private static final List<String> TITLE_LIST = Arrays.asList("Congratulations on participating in the friend recall event and getting rewards.",
            "مبروك على المشاركة في نشاط استدعاء الأصدقاء والحصول على المكافآت");

    private static final List<String> BODY_LIST = Arrays.asList("Congratulations on participating in the friend recall event and getting rewards.",
            "مبروك على المشاركة في نشاط استدعاء الأصدقاء والحصول على المكافآت");

    private static final List<String> FCM_TITLE_LIST = Arrays.asList("fcm推送 老友回归活动",
            "fcm推送 老友回归活动");

    private static final String BIND_INVITER = "bind_inviter";
    private static final String PLAY_GAME = "play_game";

    private static final String SHARE_SNAPCHAT = "share_snapchat";

    private static final List<Integer> SEND_CALL_BACK_NUM_LIST = Arrays.asList(1, 5, 10);

    private static final List<String> SEND_CALL_BACK_KEY_LIST = Arrays.asList("RecallMessage1", "RecallMessage5", "RecallMessage10");


    public static final Map<String, String> TYPE_BACK_CONFIG_NEW_MAP = new HashMap<String, String>() {
        {
            put(BIND_INVITER, "RecallinviteeBind");
            put(PLAY_GAME, "RecallinviteeGame");
        }
    };

    public static final Map<String, String> TYPE_INVITER_RES_MAP = new HashMap<String, String>() {
        {
            put(BIND_INVITER, "RecallinviterBind");
            put(PLAY_GAME, "RecallinviterGame");
            put(SHARE_SNAPCHAT, "RecallShare");
        }
    };


    private static final List<String> TASK_ALL_LIST = Arrays.asList(
            CommonMqTaskConstant.DAILY_LOGIN,
            CommonMqTaskConstant.ADD_FRIEND,
            CommonMqTaskConstant.PLAY_CARROM_POOL
    );

    private static final int RECORD_PAGE_SIZE = 6;

    private static final int PAGE_SIZE = 20;

    private static final String BACK_AC_KEY = "back_ac_key";

    private static final int BIND_INVITER_BEAN = 20;

    private static final int INVITER_PLAY_GAME_BEAN = 5;

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource
    private FriendsDao friendsDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private InviteBackDao inviteBackDao;
    @Resource
    private BackUserCollectService backUserCollectService;
    @Resource
    private InviteBackUserCodeRedis inviteBackUserCodeRedis;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private PetFeedService petFeedService;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
//            ACTIVITY_ID = "66e2cbbf756ffdd95488113b";
            ACTIVITY_BACK_URL = String.format("https://test2.qmovies.tv/back_user2025/?activityId=%s", ACTIVITY_ID);
            ACTIVITY_INVITER_BACK_URL = String.format("https://test2.qmovies.tv/back_friend2025/?activityId=%s", ACTIVITY_ID);
            // https://test2.qmovies.tv/back_user_download2025/ 分享站外的网页
        }
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ALL_LIST.contains(item)) {
            return;
        }
        handleUserScore(null, data, null);
        // sudGameRedis sudGameDao
    }

    public void handleUserScore(SendGiftData data, CommonMqTopicData mqData, RechargeInfo rechargeInfo) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        String uid;
        if (data != null) {
            uid = data.getFrom_uid();
        } else if (mqData != null) {
            uid = mqData.getUid();
        } else {
            uid = rechargeInfo.getUid();
            String productId = TYPE_BACK_CONFIG_CHARGE_PRODUCT_MAP.get(rechargeInfo.getProductId());
            if (!StringUtils.hasLength(productId)) {
                logger.info("not support uid:{} productId:{}", uid, productId);
                return;
            }
        }
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTestUser = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTestUser) {
                // 灰度测试,只统计测试用户
                return;
            }
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        synchronized (stringPool.intern(getInviteLockKey(uid))) {
            if (mqData != null && CommonMqTaskConstant.DAILY_LOGIN.equals(mqData.getItem())) {
                // 日活check
                BackUserStateData backData = backUserStateRedis.checkBackUser(actorData, true);
                return;
            }

            BackUserStateData backData = backUserStateRedis.checkBackUser(actorData, true);
            if (backData == null) {
                return;
            }
            int backTime = backData.getBackTime();
            if (backTime + ResourceConstant.BACK_AC_DAY_TIME < DateHelper.getNowSeconds()) {
                return;
            }

            int giftBeans = backData.getGiftBeans();
            Set<String> friendsSet = backData.getFriendsSet() == null ? new HashSet<>() : backData.getFriendsSet();
            Map<String, Integer> giftMapStatus = backData.getGiftMapStatus() == null ? new HashMap<>() : backData.getGiftMapStatus();
            Map<String, Integer> friendsMapStatus = backData.getFriendsMapStatus() == null ? new HashMap<>() : backData.getFriendsMapStatus();

            if (data != null) {
                // 1 发礼物
                long totalPrice = ((long) data.getPrice() * data.getNumber() * data.getAid_list().size());
                giftBeans += (int) totalPrice;
                int giftIndex = getIndexLevel(giftBeans, GIFT_BEANS_LIST);
                if (giftIndex > 0) {
                    backData.setGiftMapStatus(fillMap(giftMapStatus, GIFT_KEY_LIST.subList(0, giftIndex)));
                }
                backData.setGiftBeans(giftBeans);
                backUserStateRedis.saveBackUserState(backData, backData.getUid());
            } else if (mqData != null) {
                if (CommonMqTaskConstant.ADD_FRIEND.equals(mqData.getItem())) {
                    // 2添加好友
                    int oldFriendsCount = friendsSet.size();
                    String aid = mqData.getAid(); // 添加的好友
                    friendsSet.add(aid);
                    int newFriendsCount = friendsSet.size();
                    if (oldFriendsCount == newFriendsCount || newFriendsCount > 20) {
                        // 同一个人重复添加，或者超过上限
                        logger.info("not handle friend newFriendsCount:{}", newFriendsCount);
                        return;
                    }
                    int friendIndex = getIndexLevel(newFriendsCount, FRIEND_NUM_LIST);
                    if (friendIndex > 0) {
                        backData.setFriendsMapStatus(fillMap(friendsMapStatus, FRIEND_KEY_LIST.subList(0, friendIndex)));
                    }
                    backData.setFriendsSet(friendsSet);
                    backUserStateRedis.saveBackUserState(backData, backData.getUid());
                } else if (CommonMqTaskConstant.PLAY_CARROM_POOL.equals(mqData.getItem())) {
                    if (backData.getPlayGameStatus() == 0) {
                        backData.setPlayGameStatus(2);
                        backUserStateRedis.saveBackUserState(backData, backData.getUid());
                        // 给被邀请者(回归用户)发奖励
                        String title = getDescString(PLAY_GAME);
                        resourceKeyHandlerService.sendResourceData(uid, TYPE_BACK_CONFIG_NEW_MAP.get(PLAY_GAME), MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                                title, title, title,
                                "", "", 1);


                    }
                    InviteBackData inviteBackData = inviteBackDao.getOneByAid(uid);
                    if (inviteBackData == null || inviteBackData.getPlayGameReward() != null) {
                        // 没有邀请者，或者已经领取过(一个人只能被邀请者领取一次)
                        return;
                    }
                    String yaoQingUid = inviteBackData.getUid();
                    String gameId = mqData.getHandleId();
//                    SudGameInfo sudGameInfo = sudGameRedis.getSudGameInfo(gameId);
                    SudGameData sudGameInfo = sudGameDao.findData(gameId);
                    if (sudGameInfo == null) {
                        return;
                    }
                    boolean isSudGame = false;
                    if (!CollectionUtils.isEmpty(sudGameInfo.getPlayerList())) {
                        for (SudGamePlayerData playerData : sudGameInfo.getPlayerList()) {
                            if (playerData.getUid().equals(yaoQingUid)) {
                                isSudGame = true;
                                break;
                            }
                        }
                    }
                    if (!isSudGame) {
                        return;
                    }
                    BackUserInfoVO.InviterUserStateData inviterUserStateData = getInviterUserStateInfo(yaoQingUid, getDayByBase(ACTIVITY_ID, yaoQingUid), false);
                    if (inviterUserStateData != null) {
                        if (inviterUserStateData.getPlayGameAidSet().size() >= 5 || inviterUserStateData.getPlayGameAidSet().contains(uid)) {
                            return;
                        }
                        inviterUserStateData.getPlayGameAidSet().add(uid);
                        saveInviterUserStateInfo(yaoQingUid, getDayByBase(ACTIVITY_ID, yaoQingUid), inviterUserStateData);

                        int totalReward = inviteBackData.getTotalReward() == null ? 0 : inviteBackData.getTotalReward();
                        inviteBackData.setPlayGameReward(INVITER_PLAY_GAME_BEAN);
                        inviteBackData.setTotalReward(totalReward + INVITER_PLAY_GAME_BEAN);
                        inviteBackData.setMtime(DateHelper.getNowSeconds());
                        inviteBackDao.updateById(inviteBackData);
                        // 给邀请者发奖励
                        String title = getInviterDescString(PLAY_GAME);
                        resourceKeyHandlerService.sendResourceData(yaoQingUid, TYPE_INVITER_RES_MAP.get(PLAY_GAME), MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                                title, title,title, "", "", 1);

                    }
                }
            } else {
                // 下发充值奖励
                backUserChargeReward(uid, rechargeInfo.getProductId(), backData);
            }
        }
    }


    public BackUserInfoVO getBackUserInfo(BackUserDTO req) {
        String uid = req.getUid();
        BackUserInfoVO vo = new BackUserInfoVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);

        }
        BackUserStateData backUserStateData = backUserStateRedis.checkBackUser(actorData, true);
        if (backUserStateData == null) {
            logger.error("not find back user uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_BACK);
        }
        InviteBackData inviteBackData = inviteBackDao.getOneByAid(uid);
        if (inviteBackData != null) {
            ActorData actorData2 = actorDao.getActorDataFromCache(inviteBackData.getUid());
            vo.setInviteName(actorData2.getName());
            vo.setInviteHead(ImageUrlGenerator.generateRoomUserUrl(actorData2.getHead()));
            vo.setInviteUid(inviteBackData.getUid());
        }

        List<BackUserInfoVO.BackUserItemBag> dauBagList = new ArrayList<>();
        List<BackUserInfoVO.BackUserItemBag> chargeBagList = new ArrayList<>();
        List<BackUserInfoVO.BackUserItemBag> giftBagList = new ArrayList<>();
        List<BackUserInfoVO.BackUserItemBag> friendsBagList = new ArrayList<>();
        fillBagList(backUserStateData, dauBagList, chargeBagList, giftBagList, friendsBagList);

        int regTime = new ObjectId(uid).getTimestamp();
        int lastLoginOutTime = backUserStateData.getLastLoginOutTime();
        int backTime = backUserStateData.getBackTime();
        int isDoneShowPop = backUserStateData.getIsDoneShowPop();
        int now = DateHelper.getNowSeconds();

        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setLossDays((backTime - lastLoginOutTime) / 86400);
        vo.setRegTime(regTime);
        vo.setAliveTime(lastLoginOutTime - regTime);
        vo.setFriendCount(friendsDao.getFriendCount(uid));
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        int endTime = Math.min(activityData.getEndTime(), backTime + ResourceConstant.BACK_AC_DAY_TIME);
        vo.setEndTime(endTime);

        vo.setDauBagList(dauBagList);
        vo.setChargeBagList(chargeBagList);
        vo.setGiftBagList(giftBagList);
        vo.setFriendsBagList(friendsBagList);

        vo.setIsShowPop(isDoneShowPop == 1 ? 0 : 1);
        if (isDoneShowPop == 0) {
            backUserStateData.setIsDoneShowPop(1);
            backUserStateRedis.saveBackUserState(backUserStateData, backUserStateData.getUid());
        }
        vo.setPlayGameStatus(backUserStateData.getPlayGameStatus());
        vo.setBindStatus(backUserStateData.getBackCodeStatus());


        return vo;
    }

    private void fillBagList(BackUserStateData backData, List<BackUserInfoVO.BackUserItemBag> dauBagList
            , List<BackUserInfoVO.BackUserItemBag> chargeBagList, List<BackUserInfoVO.BackUserItemBag> giftBagList
            , List<BackUserInfoVO.BackUserItemBag> friendsBagList) {
        for (String itemKey : DAU_KEY_LIST) {
            BackUserInfoVO.BackUserItemBag itemBag = new BackUserInfoVO.BackUserItemBag();
            itemBag.setItemName(itemKey);
            itemBag.setItemState(getState(itemKey, backData));
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.
                    findByKey(TYPE_BACK_CONFIG_DAU_MAP.getOrDefault(itemKey, ""));
            if (resourceKeyConfigData != null) {
                itemBag.setConfigDataList2(resourceKeyConfigData.getResourceMetaList());
            }
            dauBagList.add(itemBag);
        }
        for (String itemKey : CHARGE_KEY_LIST) {
            BackUserInfoVO.BackUserItemBag itemBag = new BackUserInfoVO.BackUserItemBag();
            itemBag.setItemName(itemKey);
            itemBag.setItemState(getState(itemKey, backData));
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.
                    findByKey(TYPE_BACK_CONFIG_CHARGE_MAP.getOrDefault(itemKey, ""));
            if (resourceKeyConfigData != null) {
                itemBag.setConfigDataList2(resourceKeyConfigData.getResourceMetaList());
            }
            chargeBagList.add(itemBag);
        }
        for (String itemKey : GIFT_KEY_LIST) {
            BackUserInfoVO.BackUserItemBag itemBag = new BackUserInfoVO.BackUserItemBag();
            itemBag.setItemName(itemKey);
            itemBag.setItemState(getState(itemKey, backData));
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.
                    findByKey(TYPE_BACK_CONFIG_GIFT_MAP.getOrDefault(itemKey, ""));
            if (resourceKeyConfigData != null) {
                itemBag.setConfigDataList2(resourceKeyConfigData.getResourceMetaList());
            }
            giftBagList.add(itemBag);

        }
        for (String itemKey : FRIEND_KEY_LIST) {
            BackUserInfoVO.BackUserItemBag itemBag = new BackUserInfoVO.BackUserItemBag();
            itemBag.setItemName(itemKey);
            itemBag.setItemState(getState(itemKey, backData));
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.
                    findByKey(TYPE_BACK_CONFIG_FRIEND_MAP.getOrDefault(itemKey, ""));
            if (resourceKeyConfigData != null) {
                itemBag.setConfigDataList2(resourceKeyConfigData.getResourceMetaList());
            }
            friendsBagList.add(itemBag);
        }
    }

    public BackUserInfoVO signBackUserDau(BackUserDTO req) {
        checkActivityTime(ACTIVITY_ID);
        String uid = req.getUid();
        String itemTag = req.getItemName();
        if (!StringUtils.hasLength(itemTag) || !StringUtils.hasLength(uid)) {
            logger.error("itemTag:{} or uid:{} is empty", itemTag, uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        BackUserInfoVO vo = new BackUserInfoVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("not find uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        BackUserStateData backUserStateData = backUserStateRedis.checkBackUser(actorData, true);
        if (backUserStateData == null) {
            logger.error("Not returning to users uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_BACK);
        }
        int now = DateHelper.getNowSeconds();
        if (backUserStateData.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME < now) {
            logger.error("Invalid back activity uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.INVALID_BACK);
        }
        int state = getState(itemTag, backUserStateData);
        String resKey = getResKey(itemTag);
        if (state == 1 && StringUtils.hasLength(resKey)) {
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.
                    findByKey(resKey);
            if (resourceKeyConfigData != null) {
                setState(itemTag, backUserStateData, 2);
                backUserStateRedis.saveBackUserState(backUserStateData, uid);
                String title = getDescString(itemTag);
                resourceKeyHandlerService.sendResourceData(uid, resKey, MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                        title, title, title,
                        "", "", 1);
            } else {
                logger.error("dau reward fail not find resourceKeyConfigData uid:{} itemTag:{} state:{} resKey:{}",
                        uid, itemTag, state, resKey);
                throw new CommonH5Exception(ActivityHttpCode.DAU_REWARD_FAIL);
            }

        } else {
            logger.error("dau reward fail uid:{} itemTag:{} state:{}", uid, itemTag, state);
            throw new CommonH5Exception(ActivityHttpCode.DAU_REWARD_FAIL);
        }
        return vo;
    }


    private Map<String, Integer> fillMap(Map<String, Integer> itemMapStatus, List<String> srcKeyList) {
        if (itemMapStatus == null) {
            itemMapStatus = new HashMap<>();
        }
        for (String itemKey : srcKeyList) {
            //  0 待完成 1 待领取 2 已完成
            int oldStatus = itemMapStatus.getOrDefault(itemKey, 0);
            if (oldStatus == 0) {
                itemMapStatus.put(itemKey, 1);
            }
        }
        return itemMapStatus;
    }

    private void backUserChargeReward(String uid, String productId, BackUserStateData backUserStateData) {
        if (StringUtils.hasLength(productId)) {
            String rewardKey = TYPE_BACK_CONFIG_CHARGE_PRODUCT_MAP.get(productId);
            if (StringUtils.hasLength(rewardKey)) {
                if (backUserStateData != null && backUserStateData.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME >= DateHelper.getNowSeconds()) {
                    Set<String> rechargeProductIdSet = backUserStateData.getRechargeProductIdSet();
                    if (rechargeProductIdSet == null) {
                        rechargeProductIdSet = new HashSet<>();
                    }
                    if (rechargeProductIdSet.contains(productId)) {
                        logger.info("backUserReward fail uid:{} productId:{} is consumer", uid, productId);
                        return;
                    }
                    resourceKeyHandlerService.sendResourceData(uid, rewardKey, MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                            ResourceConstant.BACK_AC_RECHARGE_DESC, ResourceConstant.BACK_AC_RECHARGE_DESC,
                            ResourceConstant.BACK_AC_RECHARGE_DESC,
                            "", "", 1);
                    rechargeProductIdSet.add(productId);
                    backUserStateData.setRechargeProductIdSet(rechargeProductIdSet);
                    backUserStateRedis.saveBackUserState(backUserStateData, uid);
                    logger.info("backUserReward success uid:{} productId:{}", uid, productId);
                }
            }
        }
    }


    private String getResKey(String itemTag) {
        String resKey = TYPE_BACK_CONFIG_DAU_MAP.getOrDefault(itemTag, "");
        if (StringUtils.isEmpty(resKey)) {
            resKey = TYPE_BACK_CONFIG_GIFT_MAP.getOrDefault(itemTag, "");
        }
        if (StringUtils.isEmpty(resKey)) {
            resKey = TYPE_BACK_CONFIG_FRIEND_MAP.getOrDefault(itemTag, "");
        }
        return resKey;
    }

    private String getDescString(String itemTag) {
        String desc = "Back User reward";
        if (TYPE_BACK_CONFIG_DAU_MAP.keySet().contains(itemTag)) {
            desc = ResourceConstant.BACK_AC_ACTIVE_DESC;
        } else if (TYPE_BACK_CONFIG_GIFT_MAP.keySet().contains(itemTag)) {
            desc = ResourceConstant.BACK_AC_GIFT_DESC;
        } else if (TYPE_BACK_CONFIG_FRIEND_MAP.keySet().contains(itemTag)) {
            desc = ResourceConstant.BACK_AC_FRIENDS_DESC;
        } else if (BIND_INVITER.equals(itemTag)) {
            desc = "Back User-bind inviter reward";
        } else if (PLAY_GAME.equals(itemTag)) {
            desc = "Back User-play game reward";
        }
        return desc;
    }

    private String getInviterDescString(String itemTag) {
        String desc = "Back User to inviter reward";
        if (SEND_CALL_BACK_KEY_LIST.contains(itemTag)) {
            desc = "Back User to inviter-msg reward";
        } else if (BIND_INVITER.equals(itemTag)) {
            desc = "Back User to inviter-bind reward";
        } else if (PLAY_GAME.equals(itemTag)) {
            desc = "Back User to inviter-play game reward";
        } else if (SHARE_SNAPCHAT.equals(itemTag)) {
            desc = "Back User to inviter-share snapchat reward";
        }
        return desc;
    }

    private int getIndexLevel(int score, List<Integer> srcList) {
        List<Integer> tempLevelNumList = new ArrayList<>(srcList);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

    private int getState(String itemName, BackUserStateData backUserStateData) {
        if (DAU_2.equals(itemName)) {
            return backUserStateData.getDau2State();
        } else if (DAU_3.equals(itemName)) {
            return backUserStateData.getDau3State();
        } else if (DAU_5.equals(itemName)) {
            return backUserStateData.getDau5State();
        } else if (DAU_7.equals(itemName)) {
            return backUserStateData.getDau7State();
        } else {
            if (GIFT_KEY_LIST.contains(itemName)) {
                Map<String, Integer> giftMapStatus = backUserStateData.getGiftMapStatus() == null
                        ? new HashMap<>() : backUserStateData.getGiftMapStatus();
                return giftMapStatus.getOrDefault(itemName, 0);
            } else if (FRIEND_KEY_LIST.contains(itemName)) {
                Map<String, Integer> friendsMapStatus = backUserStateData.getFriendsMapStatus() == null ?
                        new HashMap<>() : backUserStateData.getFriendsMapStatus();
                return friendsMapStatus.getOrDefault(itemName, 0);
            } else if (CHARGE_KEY_LIST.contains(itemName)) {
                return 0;
            }
            return 0;
        }
    }

    private void setState(String itemName, BackUserStateData backUserStateData, int state) {
        if (DAU_2.equals(itemName)) {
            backUserStateData.setDau2State(state);
        } else if (DAU_3.equals(itemName)) {
            backUserStateData.setDau3State(state);
        } else if (DAU_5.equals(itemName)) {
            backUserStateData.setDau5State(state);
        } else if (DAU_7.equals(itemName)) {
            backUserStateData.setDau7State(state);
        } else {
            if (GIFT_KEY_LIST.contains(itemName)) {
                Map<String, Integer> giftMapStatus = backUserStateData.getGiftMapStatus() == null
                        ? new HashMap<>() : backUserStateData.getGiftMapStatus();
                giftMapStatus.put(itemName, state);
                backUserStateData.setGiftMapStatus(giftMapStatus);
            } else if (FRIEND_KEY_LIST.contains(itemName)) {
                Map<String, Integer> friendsMapStatus = backUserStateData.getFriendsMapStatus() == null ?
                        new HashMap<>() : backUserStateData.getFriendsMapStatus();
                friendsMapStatus.put(itemName, state);
                backUserStateData.setFriendsMapStatus(friendsMapStatus);
            } else if (CHARGE_KEY_LIST.contains(itemName)) {

            }
        }
    }


    public BackUserInfoVO inviterHomeInfo(String activityId, String uid, int page) {
        BackUserInfoVO vo = new BackUserInfoVO();
        page = page <= 0 ? 1 : page;
        int start = (page - 1) * RECORD_PAGE_SIZE;
        List<RankVO> allOldFriendList = backUserCollectService.findAllLossFriend(uid);
        PageUtils.PageData<RankVO> pageData = PageUtils.getPageData(allOldFriendList, page, RECORD_PAGE_SIZE);
        BackUserInfoVO.InviterUserStateData inviterUserStateData = getInviterUserStateInfo(uid, getDayByBase(activityId, uid), false);
        BackUserInfoVO.InviterUserStateData inviterUserStateTotal = getInviterUserTotal(uid, false);

        int shareSnapchatStatus = inviterUserStateTotal.getShareSnapchatStatus();
        if (shareSnapchatStatus == 0 && inviteBackDao.getCountByUid(uid, null) > 100) {
            shareSnapchatStatus = 2;
            inviterUserStateTotal.setShareSnapchatStatus(shareSnapchatStatus);
            saveInviterUserTotal(uid, inviterUserStateTotal);
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setInviteName(actorData.getName());
        vo.setInviteHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setInviteUid(uid);

        vo.setShareSnapchatStatus(shareSnapchatStatus);
        vo.setInviteCode(inviteBackUserCodeRedis.getHostCodeByUid(uid));
        vo.setTodayMsgCount(inviterUserStateData.getToMsgAidSet().size());
        vo.setOldFriendList(pageData.list);
        vo.setNextUrl(pageData.nextPage == 0 ? -1 : pageData.nextPage);

        return vo;
    }

    @Cacheable(value = "findAllLossFriendCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<RankVO> findAllLossFriend(String uid) {
        List<FriendsData> dataList = friendsDao.findAllDataByLimit(uid);
        List<RankVO> oldFriendList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            int now = DateHelper.getNowSeconds();
            BackUserInfoVO.InviterUserStateData inviterUserStateData = getInviterUserStateInfo(uid, getDayByBase(ACTIVITY_ID, uid), false);
            for (FriendsData friendsData : dataList) {
                String friendAid = Objects.equals(uid, friendsData.getUidFirst()) ? friendsData.getUidSecond() : friendsData.getUidFirst();
                ActorData actorData = actorDao.getActorDataFromCache(friendAid);
                if (!backUserStateRedis.isReg(friendAid) && backUserStateRedis.isBackUser(actorData, true) > 0) {
                    if (inviterUserStateData.getToMsgAidSet().contains(friendAid)) {
                        // 已经发送过消息的，不再展示
                        continue;
                    }
                    RankVO rankVO = new RankVO();
                    rankVO.setUid(friendAid);
                    rankVO.setName(actorData.getName());
                    rankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                    rankVO.setScore(String.valueOf((now - friendsData.getCtime()) / 86400 + 1));

                    oldFriendList.add(rankVO);
                }
            }
        }
        return oldFriendList;
    }

    public BackUserInfoVO getInviterDetail(String activityId, String uid, int page) {
        BackUserInfoVO vo = new BackUserInfoVO();
        page = page <= 0 ? 1 : page;
        List<BackUserInfoVO.DetailItemVO> detailList = new ArrayList<>();
        List<InviteBackData> sourceDetailList = inviteBackDao.getDetailByUidPage(uid, page, PAGE_SIZE);
        if (!CollectionUtils.isEmpty(sourceDetailList)) {
            sourceDetailList.forEach(item -> {
                BackUserInfoVO.DetailItemVO detailItemVO = new BackUserInfoVO.DetailItemVO();
                String subAid = item.getAid();
                ActorData actorData1 = actorDao.getActorDataFromCache(subAid);
                detailItemVO.setInviteeUid(item.getAid());
                detailItemVO.setInviteeName(actorData1.getName());
                detailItemVO.setInviteeHead(ImageUrlGenerator.generateRoomUserUrl(actorData1.getHead()));
                detailItemVO.setBackTime(item.getBackTime());
                detailItemVO.setBindStatus(2);
                detailItemVO.setPlayGameStatus(item.getPlayGameReward() == null || item.getPlayGameReward() == 0 ? 0 : 2);
                if (item.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME > DateHelper.getNowSeconds()) {
                    if (detailItemVO.getPlayGameStatus() > 0) {
                        detailItemVO.setTaskStatus(1); // 已完成
                    } else {
                        detailItemVO.setTaskStatus(0); // 未完成
                    }
                } else {
                    detailItemVO.setTaskStatus(2); // 已过期
                }
                detailList.add(detailItemVO);
            });
        }
        vo.setDetailList(detailList);
        vo.setTotalCallBackCount(inviteBackDao.getCountByUid(uid, null));
        vo.setNextUrl(detailList.size() < PAGE_SIZE ? -1 : page + 1);
        return vo;
    }


    public BackUserInfoVO getInviterRank(String activityId, String uid, int page) {
        BackUserInfoVO vo = new BackUserInfoVO();
        int startTime = DateHelper.getNowSeconds() - (int) TimeUnit.DAYS.toSeconds(30);
        List<RankVO> rankList = new ArrayList<>();
        List<HotSearchListData> sourceDetailList = inviteBackDao.getRankListData(startTime);
        if (!CollectionUtils.isEmpty(sourceDetailList)) {
            int rank = 0;
            for (HotSearchListData item : sourceDetailList) {
                RankVO sub = new RankVO();
                String subUid = item.getSearchKey();
                ActorData actorData1 = actorDao.getActorDataFromCache(subUid);
                if (actorData1.getValid() == 0) {
                    continue;
                }
                rank += 1;
                sub.setUid(subUid);
                sub.setName(actorData1.getName());
                sub.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData1.getHead()));
                sub.setScore(String.valueOf(item.getTotalP()));
                sub.setRank(String.valueOf(rank));
                rankList.add(sub);
                if (rank >= 30) {
                    break;
                }
            }
        }
        vo.setRankList(rankList);

        return vo;
    }


    public BackUserInfoVO bindUidByCode(BackUserDTO req) {
        String aid = req.getUid();
        String inviteCode = req.getInviteCode();
        if (!StringUtils.hasLength(inviteCode) || !StringUtils.hasLength(aid)) {
            logger.error("inviteCode:{} or aid:{} is empty", inviteCode, aid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        if (actorData == null || !StringUtils.hasLength(actorData.getTn_id())) {
            logger.error("not find aid:{} or tn_id is empty", aid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }

        BackUserStateData backUserStateData = backUserStateRedis.checkBackUser(actorData, true);
        if (backUserStateData == null || backUserStateData.getBackTime() + ResourceConstant.BACK_AC_DAY_TIME < DateHelper.getNowSeconds()) {
            logger.error("Not returning to users uid:{}", aid);
            throw new CommonH5Exception(ActivityHttpCode.BACK_NOT_BACK_USER);
        }

        String uid = inviteBackUserCodeRedis.getUidByHostCode(inviteCode);
        if (!StringUtils.hasLength(uid)) {
            logger.error("uid not find inviteCode:{} ", inviteCode);
            throw new CommonH5Exception(ActivityHttpCode.BACK_INVITE_CODE_NOT_ALLOW);
        }
        if (uid.equals(aid)) {
            logger.error("uid equals aid uid:{} inviteCode:{} ", uid, inviteCode);
            throw new CommonH5Exception(ActivityHttpCode.BACK_MYSELF_NOT_ALLOW);
        }

        FriendsData friendsData = friendsDao.findData(uid, aid);
        if (friendsData == null) {
            logger.error("Not friend uid:{} aid:{}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.BACK_NOT_FRIEND);
        }
        if (friendsData.getCtime() > backUserStateData.getBackTime()) {
            logger.error("Not long friend uid:{} aid:{}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.BACK_NOT_LONG_FRIEND);
        }


        int startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int todayCount = inviteBackDao.getCountByUid(uid, startTime);
        if (todayCount >= 5) {
            logger.error("above limit uid:{} aid:{}  todayCount:{}", uid, aid, todayCount);
            throw new CommonH5Exception(ActivityHttpCode.BACK_MAX_NUM_NOT_ALLOW);
        }
        BackUserInfoVO vo = new BackUserInfoVO();
        synchronized (stringPool.intern(getInviteLockKey(aid))) {
            InviteBackData inviteBackData = null;
            if (ServerConfig.isProduct()) {
                inviteBackData = inviteBackDao.getInviteByAidOrDev(aid, actorData.getTn_id());
            } else {
                inviteBackData = inviteBackDao.getOneByAid(aid);
            }
            if (inviteBackData != null) {
                logger.error("aid or device is use already uid:{} aid:{} tn_id:{}", uid, aid, actorData.getTn_id());
                throw new CommonH5Exception(ActivityHttpCode.BACK_AID_DEV_NOT_ALLOW);
            }
            int now = DateHelper.getNowSeconds();
            InviteBackData data = new InviteBackData();
            data.setUid(uid);
            data.setAid(aid);
            if (ServerConfig.isProduct()) {
                data.setDeviceId(actorData.getTn_id());
            }
            data.setBackTime(backUserStateData.getBackTime());
            data.setBackReward(BIND_INVITER_BEAN);
            data.setTotalReward(BIND_INVITER_BEAN);
            data.setCtime(now);
            data.setMtime(now);
            try {
                inviteBackDao.insert(data);
            } catch (Exception e) {
                logger.error("insert error msg:{}", e.getMessage(), e);
                throw new CommonH5Exception(ActivityHttpCode.BACK_AID_DEV_NOT_ALLOW);
            }
            logger.info("bind success uid:{} aid:{} inviteUserData:{}", uid, aid, JSON.toJSONString(data));
        }

        BackUserInfoVO.InviterUserStateData inviterUserStateData = getInviterUserStateInfo(uid, getDayByBase(ACTIVITY_ID, uid), false);
        if (inviterUserStateData != null) {
            if (inviterUserStateData.getBindAidSet().size() >= 5 || inviterUserStateData.getBindAidSet().contains(aid)) {
                return vo;
            }
            inviterUserStateData.getBindAidSet().add(aid);
            saveInviterUserStateInfo(uid, getDayByBase(ACTIVITY_ID, uid), inviterUserStateData);
            // 给邀请者发奖励
            String title = getInviterDescString(BIND_INVITER);
            resourceKeyHandlerService.sendResourceData(uid, TYPE_INVITER_RES_MAP.get(BIND_INVITER), MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                    title, title, title, "", "", 1);
            // 发送官方消息
            sendOfficialMsg(TITLE_LIST, BODY_LIST, aid);
        }


        backUserStateData.setBackCodeStatus(2);
        backUserStateRedis.saveBackUserState(backUserStateData, backUserStateData.getUid());

        String title = getDescString(BIND_INVITER);
        String diamondDesc = String.valueOf(actorDao.getActorDataFromCache(uid).getRid());
        // 给被邀请者(回归用户)发奖励
        resourceKeyHandlerService.sendResourceData(aid, TYPE_BACK_CONFIG_NEW_MAP.get(BIND_INVITER), MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                title, title, title, diamondDesc, "", "", 1);

        doReportEvent(uid, aid);
        petFeedService.taskMsgProcess(new CommonMqTopicData(uid, "", aid, aid, PetFeedService.INVITE_BIND_BACK_USER, 1));
        return vo;
    }


    public void setShareSnapchat(String activityId, String uid) {
        checkActivityTime(activityId);
        BackUserInfoVO.InviterUserStateData inviterUserTotal = getInviterUserTotal(uid, false);
        if (inviterUserTotal.getShareSnapchatStatus() == 2) {
            return;
        }
        inviterUserTotal.setShareSnapchatStatus(2);
        saveInviterUserTotal(uid, inviterUserTotal);
        String title = getInviterDescString(SHARE_SNAPCHAT);
        resourceKeyHandlerService.sendResourceData(uid, TYPE_INVITER_RES_MAP.get(SHARE_SNAPCHAT), MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                title, title,title, "", "", 1);

    }

    /**
     * 分享链接信息给好友
     */
    public void shareToPrivateMsg(ShareActivityDTO dto) {
        if (StringUtils.isEmpty(dto.getActivity_id()) || StringUtils.isEmpty(dto.getAid())) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        checkActivityTime(dto.getActivity_id());
        String uid = dto.getUid();
        String activityId = dto.getActivity_id();
        String aid = dto.getAid();
        if (uid.equals(aid)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        BackUserInfoVO.InviterUserStateData inviterUserStateData = getInviterUserStateInfo(uid, getDayByBase(activityId, uid), false);
        if (inviterUserStateData.getToMsgAidSet().contains(aid)) {
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }
        inviterUserStateData.getToMsgAidSet().add(aid);
        int toMsgCount = inviterUserStateData.getToMsgAidSet().size();
        if (toMsgCount <= 15) {
            if (SEND_CALL_BACK_NUM_LIST.contains(toMsgCount)) {
                int index = SEND_CALL_BACK_NUM_LIST.indexOf(toMsgCount);
                String title = getInviterDescString(SEND_CALL_BACK_KEY_LIST.get(index));
                resourceKeyHandlerService.sendResourceData(uid, SEND_CALL_BACK_KEY_LIST.get(index),
                        MoneyTypeConstant.BACK_AC_DIAMOND_TYPE,
                        title, title,title, "", "", 1);
            }
        }
        saveInviterUserStateInfo(uid, getDayByBase(activityId, uid), inviterUserStateData);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("activity_id", dto.getActivity_id());
        jsonObject.put("activity_name", dto.getActivity_name());
        jsonObject.put("activity_desc", dto.getActivity_desc());
        jsonObject.put("activity_icon", dto.getActivity_icon());
        jsonObject.put("activity_banner", dto.getActivity_banner());
        jsonObject.put("activity_url", dto.getActivity_url());
        sendActivityShareMsg(dto.getUid(), dto.getAid(), jsonObject);
//        sendFcmMsg(uid, aid, dto.getActivity_desc());
    }

    private void sendFcmMsg(String uid, String aid, String body) {
        SendFcmDTO sendFcmDTO = new SendFcmDTO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        Map<String, String> paramMap = new HashMap<>();
        // 老版本
//        paramMap.put("t", FcmMsgTypeConstant.LIVE_ROOM);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("roomId", "");
//        paramMap.put("custom", JSONObject.toJSONString(jsonObject));

        paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, new ObjectId().toString());
        paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
        paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_6);

        paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, actorData.getName());
        paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.PRIVATE_DETAIL_MSG);

        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, uid);
        paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));

        sendFcmDTO.setParamMap(paramMap);
        sendFcmDTO.setToUid(aid);
        sendFcmDTO.setTitle(actorData.getName());
        sendFcmDTO.setTitleAr(actorData.getName());
        sendFcmDTO.setBody(body);
        sendFcmDTO.setBodyAr(body);
        sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
    }

    private void sendOfficialMsg(List<String> titleList, List<String> bodyList, String item) {
        ActorData actorData = actorDao.getActorDataFromCache(item);
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleList.get(1) : titleList.get(0);
        String body = slang == SLangType.ARABIC ? bodyList.get(1) : bodyList.get(0);
        String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
        commonOfficialMsg(item, "", 0, 0, actText,
                title, body, ACTIVITY_BACK_URL);
    }


    private BackUserInfoVO.InviterUserStateData getInviterUserStateInfo(String uid, String dayStr, boolean isNull) {
        String totalInfoKey = getHashTotalKey(BACK_AC_KEY, dayStr);
        BackUserInfoVO.InviterUserStateData inviterUserStateData;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(totalInfoKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            inviterUserStateData = JSONObject.parseObject(jsonValue, BackUserInfoVO.InviterUserStateData.class);
        } else if (isNull) {
            return null;
        } else {
            inviterUserStateData = new BackUserInfoVO.InviterUserStateData();
            inviterUserStateData.setUid(uid);
            inviterUserStateData.setBindAidSet(new HashSet<>());
            inviterUserStateData.setPlayGameAidSet(new HashSet<>());
            inviterUserStateData.setToMsgAidSet(new HashSet<>());
        }
        return inviterUserStateData;
    }

    public BackUserInfoVO.InviterUserStateData saveInviterUserStateInfo(String uid, String dayStr, BackUserInfoVO.InviterUserStateData info) {
        String totalInfoKey = getHashTotalKey(BACK_AC_KEY, dayStr);
        activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(info));
        return info;
    }

    private String getHashTotalKey(String activityId, String dayStr) {
        String aId = StringUtils.isEmpty(activityId) ? BACK_AC_KEY : activityId;
        return aId + ":back_inviter_user:day:" + dayStr;
    }


    private BackUserInfoVO.InviterUserStateData getInviterUserTotal(String uid, boolean isNull) {
        BackUserInfoVO.InviterUserStateData inviterUserStateData;
        String jsonValue = activityCommonRedis.getCommonStrValue(getBackInviterUserKey(BACK_AC_KEY, uid));
        if (StringUtils.hasLength(jsonValue)) {
            inviterUserStateData = JSONObject.parseObject(jsonValue, BackUserInfoVO.InviterUserStateData.class);
        } else if (isNull) {
            return null;
        } else {
            inviterUserStateData = new BackUserInfoVO.InviterUserStateData();
            inviterUserStateData.setShareSnapchatStatus(0);
        }
        return inviterUserStateData;
    }

    public BackUserInfoVO.InviterUserStateData saveInviterUserTotal(String uid, BackUserInfoVO.InviterUserStateData info) {
        activityCommonRedis.setCommonStrData(getBackInviterUserKey(BACK_AC_KEY, uid), JSONObject.toJSONString(info));
        return info;
    }

    private String getBackInviterUserKey(String activityId, String uid) {
        String aId = StringUtils.isEmpty(activityId) ? BACK_AC_KEY : activityId;
        return aId + ":back_inviter_user:total:" + uid;
    }

    private String getInviteLockKey(String aid) {
        return "lock:back:user:collect:" + aid;
    }


    private void doReportEvent(String uid, String aid) {
//        InviteRecordEvent event = new InviteRecordEvent();
//        event.setUid(uid);
//        event.setInvited_uid(aid);
//        event.setCtime(DateHelper.getNowSeconds());
//        eventReport.track(new EventDTO(event));
//
//        Map<String, Object> properties = new HashMap<>();
//        properties.put("invite_uid", uid);
//        eventReport.userSet(new EventDTO(aid, "", properties));
    }

}
