package com.quhong.service;

import com.quhong.constant.ActivityConstant;
import com.quhong.data.ActorData;
import com.quhong.data.vo.DesertFalconVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class DesertFalconService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(DesertFalconService.class);

    /**
     * 获取基础排行榜
     */
    private List<OtherRankingListVO> getBaseDesertFalcon(String activityId, int rankType) {
        List<OtherRankingListVO> rankingList = new ArrayList<>();

        Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingMapByScore(activityId, rankType, 10, 0, 99999, 0);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            rankingListVO.setScore(entry.getValue());
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", entry.getKey());
                    continue;
                }
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            }
            rankingList.add(rankingListVO);
        }
        return rankingList;
    }


    private void fillDesertFalconVO(DesertFalconVO vo, String activityId, int rankType, int roundNum){
        vo.setRankingList0k(getBaseDesertFalcon(activityId, rankType));

        List<OtherRankingListVO> rankingList100k = new ArrayList<>();
        List<OtherRankingListVO> rankingList200k = new ArrayList<>();
        List<OtherRankingListVO> rankingList300k = new ArrayList<>();
        List<OtherRankingListVO> rankingList500k = new ArrayList<>();
        List<OtherRankingListVO> rankingList1m = new ArrayList<>();
        Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingMapByScore(activityId, rankType, 100000, 10000000, roundNum);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            String rankUid = entry.getKey();
            int curScore = entry.getValue();
            rankingListVO.setScore(curScore);
            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                ActorData rankActor = actorDao.getActorDataFromCache(rankUid);
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", rankUid);
                    continue;
                }
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            }
            logger.info("rankUid: {}, curScore: {}", rankUid, curScore);
            if(curScore >= 100000 && curScore < 200000){
                rankingList100k.add(rankingListVO);
            }else if(curScore >= 200000 && curScore < 300000){
                rankingList200k.add(rankingListVO);
            }else if(curScore >= 300000 && curScore < 500000){
                rankingList300k.add(rankingListVO);
            }else if(curScore >= 500000 && curScore < 1000000){
                rankingList500k.add(rankingListVO);
            }else if(curScore >= 1000000 && curScore < 10000000){
                rankingList1m.add(rankingListVO);
            }
        }
        vo.setRankingList100k(rankingList100k);
        vo.setRankingList200k(rankingList200k);
        vo.setRankingList300k(rankingList300k);
        vo.setRankingList500k(rankingList500k);
        vo.setRankingList1m(rankingList1m);
    }

    public DesertFalconVO desertFalconRanking(String uid, String activityId, int rankType) {
        DesertFalconVO vo = new DesertFalconVO();
        OtherRankingActivityData activity = getOtherRankingActivity(activityId);

        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        int roundNum = activity.getRoundNum();
        for (OtherRankingActivityData.RankingConfig rankingConfig : activity.getRankingRewardList()) {
            if (rankingConfig.getRankType() != rankType) {
                continue;
            }
            fillDesertFalconVO(vo, activityId, rankType, roundNum);
            // vo.setRankingList(getOtherRankingListVO(activityId, rankType));
            vo.setMyRank(getOtherMyRank(activityId, uid, rankType, roundNum, 0));
        }
        return vo;
    }

}
