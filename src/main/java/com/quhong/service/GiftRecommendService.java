package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.ActorData;
import com.quhong.data.vo.GiftRecommendVO;
import com.quhong.data.vo.PosterVO;
import com.quhong.data.vo.WebSiteVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.GiftRecommendDao;
import com.quhong.mongo.dao.PosterDao;
import com.quhong.mongo.data.GiftRecommendData;
import com.quhong.mongo.data.Poster;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class GiftRecommendService{

    private static final Logger logger = LoggerFactory.getLogger(GiftRecommendService.class);
    private static final String APK_URL = "https://cdn3.qmovies.tv/common/op_sys_1655528412_youstar-v8.29.2-release.apk";
    private static final String APK_ADDRESS = "https://apps.apple.com/us/app/id1377028992,https://play.google.com/store/apps/details?id=in.dradhanus.liveher";

    @Resource
    private GiftRecommendDao giftRecommendDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private GiftRecommendService giftRecommendService;
    @Resource
    private PosterDao posterDao;


    @Cacheable(value = "giftRecommend", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public GiftRecommendData getGiftRecommend(String recommendId) {
        if (StringUtils.isEmpty(recommendId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        GiftRecommendData data = giftRecommendDao.findDataById(recommendId);
        if (null == data) {
            logger.error("cannot find getGiftRecommend activityId={}", recommendId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }

    public GiftRecommendVO giftConfig(String uid, String recommendId) {
        GiftRecommendData data = giftRecommendService.getGiftRecommend(recommendId);
        if(data == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        ActorData actor = actorDao.getActorDataFromCache(uid);
        if(actor == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        GiftRecommendVO vo = new GiftRecommendVO();
        BeanUtils.copyProperties(data, vo);
        vo.setRid(actor.getRid());
        return vo;
    }

    public WebSiteVO officialSite() {
        WebSiteVO vo = new WebSiteVO();
        vo.setPoster_url(APK_URL);
        vo.setAddress(APK_ADDRESS);
        return vo;
    }

    @Cacheable(value = "officialPoster", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public PosterVO officialPoster(String version) {
        PosterVO vo = new PosterVO();
        Poster poster = posterDao.findOne(version);
        if(poster == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        BeanUtils.copyProperties(poster, vo);
        return vo;
    }

}
