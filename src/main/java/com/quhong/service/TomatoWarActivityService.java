package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.ActorData;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.TomatoWarVO;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.data.FriendsData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.RankingActivityRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;


@Service
public class TomatoWarActivityService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(TomatoWarActivityService.class);
    private static final String ACTIVITY_ID = "682aa8bbd9e6e564572fc6cb";
    private static final String ACTIVITY_TITLE_EN = "Tomato War";
    private static final int RECORD_PAGE_SIZE = 20;

    public static final Map<Integer, String> ADMIN_NO_DIAMONDS_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(1, "TomatoFightingAdminTop1"); //
            put(2, "TomatoFightingAdminTop2"); //
            put(3, "TomatoFightingAdminTop3"); //
        }
    };
    private static final List<Integer> HOST_BEAN_LIST = Arrays.asList(
            10000, 10000, 10000);

    private static final List<Integer> ADMIN_BEAN_LIST = Arrays.asList(
            7000, 5000, 4000);

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private RankingActivityRedis rankingActivityRedis;
    @Resource
    private RankActivityService rankActivityService;

    public TomatoWarVO tomatoAdminHomeList(String activityId, String uid) {
        TomatoWarVO vo = new TomatoWarVO();
        RankingActivity data = rankActivityService.getRankingActivity(activityId);
        Integer giftId = data.getActivityGiftList().get(0).getGiftId();
        int rank = rankingActivityRedis.getRank(activityId, RoomUtils.formatRoomId(uid), 3, giftId == null ? 0 : giftId, 0);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (rank == 0 || rank > 3 || data.getStatus() != 1) {
            vo.setRank(0);
        } else {
            TomatoWarVO.TomatoWarAdminInfo info = getTomatoWarAdminInfo(activityId, uid, false);
            vo.setRank(info.getStatus() == 1 ? -1 : rank);
            vo.setRoomHostName(actorData.getName());
            vo.setRoomHostHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            List<OtherRankingListVO> adminList = new ArrayList<>();
            for (String aid : info.getAdminUidList()) {
                ActorData aidActorData = actorDao.getActorDataFromCache(aid);
                OtherRankingListVO itemVO = new OtherRankingListVO();
                itemVO.setUid(aid);
                itemVO.setName(aidActorData.getName());
                itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(aidActorData.getHead()));
                itemVO.setRidData(aidActorData.getRidData());
                adminList.add(itemVO);

            }
            vo.setRoomHostBean(HOST_BEAN_LIST.get(rank - 1));
            vo.setRoomAdminBean(ADMIN_BEAN_LIST.get(rank - 1));
            vo.setAdminList(adminList);
        }

        return vo;
    }

    public TomatoWarVO addTomatoAdminList(String activityId, String uid, String aid, String strRid) {
        if (StringUtils.isEmpty(aid) && StringUtils.isEmpty(strRid)) {
            logger.info("aid:{} or strRid:{} is empty", aid, strRid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        TomatoWarVO vo = new TomatoWarVO();
        RankingActivity data = rankActivityService.getRankingActivity(activityId);
        if (data.getStatus() != 1) {
            logger.info("activityId:{} is not over can not add", aid);
            throw new CommonH5Exception(ActivityHttpCode.TOMATO_IS_NOT_OVER);
        }
        Integer giftId = data.getActivityGiftList().get(0).getGiftId();
        int rank = rankingActivityRedis.getRank(activityId, RoomUtils.formatRoomId(uid), 3, giftId == null ? 0 : giftId, 0);

        if (StringUtils.isEmpty(aid)) {
            ActorData searchData = actorDao.getActorByStrRid(strRid);
            if (searchData == null) {
                logger.info("strRid:{} not exist", strRid);
                throw new CommonH5Exception(ActivityHttpCode.TOMATO_USER_NOT_EXIST);
            }
            aid = searchData.getUid();
        } else {
            if (aid.length() != 24) {
                logger.info("aid:{} is not 24", aid);
                throw new CommonH5Exception(ActivityHttpCode.TOMATO_USER_NOT_EXIST);
            }
            ActorData searchData = actorDao.getActorDataFromCache(aid);
            if (searchData == null) {
                logger.info("aid:{} not exist", aid);
                throw new CommonH5Exception(ActivityHttpCode.TOMATO_USER_NOT_EXIST);
            }
        }

        if (uid.equals(aid)) {
            logger.info("can not add myself uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.SHINING_NOT_ADD_MYSELF);
        }

        if (rank == 0 || rank > 3) {
            logger.info("uid:{} rank:{} out of list", uid, rank);
            throw new CommonH5Exception(ActivityHttpCode.TOMATO_IS_OUT_OF_RANK);
        }
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            TomatoWarVO.TomatoWarAdminInfo info = getTomatoWarAdminInfo(activityId, uid, false);
            if (info.getStatus() == 1) {
                logger.info("uid:{} aid:{} 已确认提交,不能再修改", uid, aid);
                throw new CommonH5Exception(ActivityHttpCode.TOMATO_IS_SUBMIT);
            }

            List<String> adminUidList = info.getAdminUidList();
            if (adminUidList.contains(aid)) {
                logger.info("aid:{} already exist", aid);
                throw new CommonH5Exception(ActivityHttpCode.TOMATO_IS_USER_EXIST);
            }
            int size = adminUidList.size();
            if (size >= 12) {
                logger.info("aid:{} already exist", aid);
                throw new CommonH5Exception(ActivityHttpCode.TOMATO_IS_MAX_NUM);
            }
            if (size == 0) {
                info.setRoomHostUid(uid);
                info.setRank(rank);
            }
            adminUidList.add(aid);
            saveTomatoWarAdminInfo(activityId, uid, info);
        }
        return vo;
    }

    public TomatoWarVO delTomatoAdminList(String activityId, String uid, String aid) {
        TomatoWarVO vo = new TomatoWarVO();
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            TomatoWarVO.TomatoWarAdminInfo info = getTomatoWarAdminInfo(activityId, uid, true);
            if (info == null) {
                logger.info("uid:{} not info data", aid);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            } else {
                if (info.getStatus() == 1) {
                    logger.info("uid:{} aid:{} 已确认提交,不能再删除", uid, aid);
                    throw new CommonH5Exception(ActivityHttpCode.TOMATO_IS_SUBMIT);
                } else {
                    List<String> adminUidList = info.getAdminUidList();
                    if (adminUidList.contains(aid)) {
                        adminUidList.remove(aid);
                        saveTomatoWarAdminInfo(activityId, uid, info);
                    } else {
                        logger.info("uid:{} aid:{} 删除失败，用户不在admin列表", uid, aid);
                        throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
                    }
                }
            }
        }
        return vo;
    }

    public TomatoWarVO submitTomatoAdminList(String activityId, String uid) {
        TomatoWarVO vo = new TomatoWarVO();
        TomatoWarVO.TomatoWarAdminInfo info = getTomatoWarAdminInfo(activityId, uid, true);
        if (info == null) {
            logger.info(" not info data");
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        } else {
            if (info.getStatus() == 1) {
                logger.info("确认提交,不能再次提交");
                throw new CommonH5Exception(ActivityHttpCode.TOMATO_IS_SUBMIT);
            } else {
                info.setStatus(1);
                saveTomatoWarAdminInfo(activityId, uid, info);
                int rank = info.getRank();
                List<String> adminUidList = info.getAdminUidList();
                String resKey = ADMIN_NO_DIAMONDS_KEY_MAP.getOrDefault(rank, "");
                for (String aid : adminUidList) {
                    resourceKeyHandlerService.sendResourceData(aid, resKey, ACTIVITY_TITLE_EN,
                            ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, "", "");
                }
            }
        }
        return vo;
    }

    public TomatoWarVO tomatoFriendList(String activityId, String uid, int page) {
        RankingActivity data = rankActivityService.getRankingActivity(activityId);
        page = page <= 0 ? 1 : page;
        int start = (page - 1) * RECORD_PAGE_SIZE;
        TomatoWarVO vo = new TomatoWarVO();
        TomatoWarVO.TomatoWarAdminInfo info = getTomatoWarAdminInfo(activityId, uid, false);
        List<FriendsData> dataList = friendsDao.findDataList(uid, start, RECORD_PAGE_SIZE);
        List<OtherRankingListVO> friendList = new ArrayList<>();
        for (FriendsData friendsData : dataList) {
            String friendAid = Objects.equals(uid, friendsData.getUidFirst()) ? friendsData.getUidSecond() : friendsData.getUidFirst();
            ActorData actorData = actorDao.getActorDataFromCache(friendAid);
            OtherRankingListVO itemVO = new OtherRankingListVO();
            itemVO.setUid(friendAid);
            itemVO.setName(actorData.getName());
            itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            itemVO.setRidData(actorData.getRidData());
            itemVO.setScore(info.getAdminUidList().contains(friendAid) ? 1 : 0);
            friendList.add(itemVO);
        }
        vo.setFriendList(friendList);
        vo.setNextUrl(dataList.size() < RECORD_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }


    private TomatoWarVO.TomatoWarAdminInfo getTomatoWarAdminInfo(String activityId, String uid, boolean isNull) {
        String totalInfoKey = getHashTotalKey(activityId);
        TomatoWarVO.TomatoWarAdminInfo tomatoWarAdminInfo;
        String jsonValue = activityCommonRedis.getCommonHashStrValue(totalInfoKey, uid);
        if (StringUtils.hasLength(jsonValue)) {
            tomatoWarAdminInfo = JSONObject.parseObject(jsonValue, TomatoWarVO.TomatoWarAdminInfo.class);
        } else if (isNull) {
            return null;
        } else {
            tomatoWarAdminInfo = new TomatoWarVO.TomatoWarAdminInfo();
            tomatoWarAdminInfo.setAdminUidList(new ArrayList<>());
        }
        return tomatoWarAdminInfo;
    }

    public TomatoWarVO.TomatoWarAdminInfo saveTomatoWarAdminInfo(String activityId, String uid, TomatoWarVO.TomatoWarAdminInfo info) {
        String totalInfoKey = getHashTotalKey(activityId);
        activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(info));
        return info;
    }

    private String getHashTotalKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":tomato_war_admin:total";
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:tomato_war_admin:user:" + uid;
    }
}
