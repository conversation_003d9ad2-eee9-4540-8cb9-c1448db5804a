package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.ScoreRecordEvent;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 礼物和平书
 */
@Service
public class GiftIllustrationBookService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(GiftIllustrationBookService.class);
    private static final String ACTIVITY_TITLE_EN = "Gift Illustration Book";

    public static final String ACTIVITY_ID = "684bc3090732402aea56f40d";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/peace_gift_book2025/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/peace_gift_book2025/?activityId=%s", ACTIVITY_ID);


    private static Map<String, String> COLLECT_KEY_MAP = new LinkedHashMap<>();

    private static Map<Integer, Integer> ORDER_GIFT_ID_MAP = new LinkedHashMap<>();

    private static List<Integer> SPECIAL_GIFT_LIST = Arrays.asList(1221, 1222, 1135); // 特殊礼物 价值从低到高

    // 发送不同种类任务
    private static final List<Integer> SEND_GIFT_LEVEL_LIST = Arrays.asList(0, 3, 6, 9);
    private static final List<String> SEND_GIFT_KEY_LIST = Arrays.asList("", "PeaceTask3", "PeaceTask6", "PeaceTask9");

//    private static final List<String> SEND_TOP_KEY_LIST = Arrays.asList("PeaceTop1", "PeaceTop2", "PeaceTop3"
//            , "PeaceTop4", "PeaceTop5", "PeaceTop6-10"
//            , "PeaceTop6-10", "PeaceTop6-10", "PeaceTop6-10", "PeaceTop6-10");

    private static final String PEACE_MASTER_REWARD_KEY = "PeaceMasterReward";

    private static final Interner<String> stringPool = Interners.newWeakInterner();


    static {
        COLLECT_KEY_MAP.put("1-2-3", "PeaceSynthesisReward1");
        COLLECT_KEY_MAP.put("4-5-6", "PeaceSynthesisReward2");
        COLLECT_KEY_MAP.put("7-8-9", "PeaceSynthesisReward3");
        COLLECT_KEY_MAP.put("1-4-7", "PeaceSynthesisReward4");
        COLLECT_KEY_MAP.put("2-5-8", "PeaceSynthesisReward5");
        COLLECT_KEY_MAP.put("3-6-9", "PeaceSynthesisReward6");

    }

    private static final int GIFT_ONE_BEANS = 10;
    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int HISTORY_PAGE_SIZE = 20;

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private CacheDataService cacheDataService;
//    @Resource
//    private ActivityCommonRedis activityCommonRedis;
//    @Resource
//    private ActorDao actorDao;
//    @Resource
//    private RankActivityService rankActivityService; //这个模板，最多只能配置6个礼物,所以放弃

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            SPECIAL_GIFT_LIST = Arrays.asList(938, 939, 112);
            ORDER_GIFT_ID_MAP.put(1, 110);
            ORDER_GIFT_ID_MAP.put(2, 840);
            ORDER_GIFT_ID_MAP.put(3, 934);
            ORDER_GIFT_ID_MAP.put(4, 935);
            ORDER_GIFT_ID_MAP.put(5, 936);
            ORDER_GIFT_ID_MAP.put(6, 937);
            ORDER_GIFT_ID_MAP.put(7, 938);
            ORDER_GIFT_ID_MAP.put(8, 939);
            ORDER_GIFT_ID_MAP.put(9, 112);
        } else {
            ORDER_GIFT_ID_MAP.put(1, 1220);
            ORDER_GIFT_ID_MAP.put(2, 602);
            ORDER_GIFT_ID_MAP.put(3, 716);
            ORDER_GIFT_ID_MAP.put(4, 590);
            ORDER_GIFT_ID_MAP.put(5, 659);
            ORDER_GIFT_ID_MAP.put(6, 717);
            ORDER_GIFT_ID_MAP.put(7, 1221);
            ORDER_GIFT_ID_MAP.put(8, 1222);
            ORDER_GIFT_ID_MAP.put(9, 1135);
        }

    }

    private String getLocalLockKey(String activityId, String uid) {
        return String.format("getLocalLockKey:%s:%s", activityId, uid);
    }

    private String getSubStateHashKey(Integer giftId) {
        return String.format("%s_state", giftId);
    }

    private String getSubAllStateHashKey() {
        return "all_state";
    }

    // 下面是redis的key

    /**
     * 特殊礼物（最贵的3个礼物）统计
     * zset
     *
     * @param giftId
     * @return
     */
    private String getGiftSpecialKey(String activityId, Integer giftId) {
        return String.format("gift:peace:id:%s:%s", activityId, giftId);
    }

    /**
     * 用户每种礼物的状态
     * hash
     *
     * @param activityId
     * @param uid
     * @return
     */
    private String getGiftPeaceKey(String activityId, String uid) {
        return String.format("gift:peace:state:%s:%s", activityId, uid);
    }

    /**
     * 兑换记录
     * list
     *
     * @param activityId
     * @param uid
     * @return
     */
    private String getListHistoryKey(String activityId, String uid) {
        return String.format("gift:peace:draw:%s:%s", activityId, uid);
    }


    public GiftPeaceBook2025VO giftPeaceBookConfig(String activityId, String uid) {
        OtherRankingActivityData activity = getOtherRankingActivity(activityId);
        String giftPeaceKey = getGiftPeaceKey(activityId, uid);
//        ActorData actorData = actorDao.getActorDataFromCache(uid);
        GiftPeaceBook2025VO vo = new GiftPeaceBook2025VO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setGiftRemainNumList(getRemainNumList(giftPeaceKey));
        vo.setAllGiftLightNum(activityCommonRedis.getCommonHashValue(giftPeaceKey, getSubAllStateHashKey()));
        vo.setSynthesisNumList(getCollectNumList(vo.getGiftRemainNumList()));

        List<OtherSupportUserVO> giftSupportOneUserList = new ArrayList<>(3);
        for (Integer giftId : SPECIAL_GIFT_LIST) {
            String supportUserKey = getGiftSpecialKey(activityId, giftId);
            Map<String, Integer> linkedRankMap = activityCommonRedis.getCommonRankingMap(supportUserKey, 1);
            OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
            if (CollectionUtils.isEmpty(linkedRankMap)) {
                supportUserVO.setName("");
                supportUserVO.setHead("");
                supportUserVO.setUid("");
                supportUserVO.setScore(0);
                giftSupportOneUserList.add(supportUserVO);
                continue;
            }
            for (Map.Entry<String, Integer> entry : linkedRankMap.entrySet()) {
                String aid = entry.getKey();
                ActorData rankActor = actorDao.getActorDataFromCache(aid);
                supportUserVO.setName(rankActor.getName());
                supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                supportUserVO.setUid(aid);
                supportUserVO.setScore(entry.getValue());
                giftSupportOneUserList.add(supportUserVO);
                break;
            }

        }
        vo.setGiftSupportOneUserList(giftSupportOneUserList);
        return vo;
    }

    public GiftPeaceBook2025VO synthesis(String activityId, String uid, String collectType) {
        OtherRankingActivityData activity = checkActivityTime(activityId);
        // 3.1 验证collectType参数
        if (!COLLECT_KEY_MAP.containsKey(collectType)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        // 3.2 解析collectType获取3个ORDER，通过ORDER_GIFT_ID_MAP获取对应的giftId
        String[] orders = collectType.split("-");
        if (orders.length != 3) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        List<Integer> giftIds = new ArrayList<>();
        for (String orderStr : orders) {
            try {
                int order = Integer.parseInt(orderStr);
                Integer giftId = ORDER_GIFT_ID_MAP.get(order);
                if (giftId == null) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
                giftIds.add(giftId);
            } catch (NumberFormatException e) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
        }

        String giftPeaceKey = getGiftPeaceKey(activityId, uid);
        synchronized (stringPool.intern(getLocalLockKey(activityId, uid))) {
            // 3.3 检查礼物卡片剩余次数
            boolean hasEnoughCards = true;
            Map<String, Integer> hashMap = activityCommonRedis.getCommonHashAll(giftPeaceKey);
            for (Integer giftId : giftIds) {
                int remainNum = hashMap.getOrDefault(String.valueOf(giftId), 0);
                if (remainNum <= 0) {
                    hasEnoughCards = false;
                    break;
                }
            }
            if (!hasEnoughCards) {
                // 失败返回国际化消息
                throw new CommonH5Exception(ActivityHttpCode.GIFT_SENDING_INSUFFICIENT);
            }
            // 成功：每个礼物卡片减1，下发奖励
            for (Integer giftId : giftIds) {
                activityCommonRedis.incCommonHashNum(giftPeaceKey, String.valueOf(giftId), -1);
            }
        }
        // 下发奖励，资源key按COLLECT_KEY_MAP的value
        String resourceKey = COLLECT_KEY_MAP.get(collectType);
        handleRes(uid, resourceKey, ACTIVITY_TITLE_EN);

        // 4 填充vo对象
        // 4.1 giftRemainNumList，按ORDER_GIFT_ID_MAP的value，依次从getGiftPeaceKey获取剩余卡片数
        GiftPeaceBook2025VO vo = new GiftPeaceBook2025VO();
        vo.setGiftRemainNumList(getRemainNumList(giftPeaceKey));

        // 4.2 SynthesisVO的resourceMeta按COLLECT_KEY_MAP的value对应配置的资源返回
        GiftPeaceBook2025VO.SynthesisVO synthesisVO = new GiftPeaceBook2025VO.SynthesisVO();
        ResourceKeyConfigData resourceConfig = resourceKeyHandlerService.getConfigData(resourceKey);
        if (resourceConfig != null && !resourceConfig.getResourceMetaList().isEmpty()) {
            // 取第一个资源配置
            synthesisVO.setResourceMeta(resourceConfig.getResourceMetaList().get(0));

            GiftPeaceBook2025VO.ResourceMetaTmp historyMeta = new GiftPeaceBook2025VO.ResourceMetaTmp();
            BeanUtils.copyProperties(synthesisVO.getResourceMeta(), historyMeta);
            historyMeta.setCtime(DateHelper.getNowSeconds());
            List<String> giftIconList = new ArrayList<>(3);
            for (Integer giftId : giftIds) {
                giftIconList.add(giftDao.getGiftFromCache(giftId).getGicon());
            }
            historyMeta.setGiftIconList(giftIconList);
            List<GiftPeaceBook2025VO.ResourceMetaTmp> srcList = Collections.singletonList(historyMeta);
            leftPushAllHistoryList(uid, srcList);
        }
        vo.setSynthesisVO(synthesisVO);
        vo.setSynthesisNumList(getCollectNumList(vo.getGiftRemainNumList()));
        return vo;
    }

    private List<Integer> getRemainNumList(String giftPeaceKey) {
        List<Integer> giftRemainNumList = new ArrayList<>();
        Map<String, Integer> hashMap = activityCommonRedis.getCommonHashAll(giftPeaceKey);
        for (Integer giftId : ORDER_GIFT_ID_MAP.values()) {
            int remainNum = hashMap.getOrDefault(String.valueOf(giftId), 0);
            giftRemainNumList.add(remainNum);
        }
        return giftRemainNumList;
    }

    // 获取要合成的子卡片数
    private List<Integer> getCollectNumList(List<Integer> giftRemainNumList) {
        List<Integer> collectNumList = new ArrayList<>(6);
        // 遍历6个抽奖类型
        for (String collectType : COLLECT_KEY_MAP.keySet()) {
            // 解析抽奖类型获取3个位置（如"1-2-3"中的1,2,3）
            String[] orders = collectType.split("-");
            // 计算该抽奖类型的最大可用次数
            int availableNum=0;
            for (String orderStr : orders) {
                int order = Integer.parseInt(orderStr);
                // 位置从1开始，数组索引从0开始，所以需要减1
                int cardNum = giftRemainNumList.get(order - 1);
                // 如果任何一张卡片数量<=1，则该抽奖类型不可用
                if (cardNum >= 1) {
                    availableNum++;
                }
            }
            collectNumList.add(availableNum);
        }
        return collectNumList;
    }

    public GiftPeaceBook2025VO.GiftPeaceBookHistoryVO getHistoryListPageRecord(String activityId, String uid, int page) {
        GiftPeaceBook2025VO.GiftPeaceBookHistoryVO vo = new GiftPeaceBook2025VO.GiftPeaceBookHistoryVO();
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        String key = getListHistoryKey(activityId, uid);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        List<GiftPeaceBook2025VO.ResourceMetaTmp> resultList = new ArrayList<>();
        for (String json : jsonList) {
            GiftPeaceBook2025VO.ResourceMetaTmp rewardData = JSON.parseObject(json, GiftPeaceBook2025VO.ResourceMetaTmp.class);
            resultList.add(rewardData);
        }
        vo.setMyHistoryList(resultList);
        vo.setNextPage(resultList.size() < HISTORY_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

    public void leftPushAllHistoryList(String uid, List<GiftPeaceBook2025VO.ResourceMetaTmp> srcList) {
        String key = getListHistoryKey(ACTIVITY_ID, uid);
        List<String> strList = new ArrayList<>();
        for (GiftPeaceBook2025VO.ResourceMetaTmp resourceMeta : srcList) {
            strList.add(JSONObject.toJSONString(resourceMeta));
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(key, strList, HISTORY_USER_MAX_SIZE);
    }

    public void sendGiftHandle(SendGiftData data, String activityId) {

        // 礼物
        if (StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }
        String uid = data.getFrom_uid();
        if (!checkAc(uid)) {
            return;
        }
        if(data.getGiving_type()==5||data.getGiving_type()==6){
            logger.info("sendGiftHandle giving_type is 5 or 6, return gid:{} num:{} toAid:{}",data.getGid(),data.getNumber(),data.getAid_list());
            return;
        }

//        int sendBeans = data.getNumber() * data.getPrice() * data.getAid_list().size();
        int incNum = data.getNumber();
        int giftId = data.getGid();
        String giftPeaceKey = getGiftPeaceKey(ACTIVITY_ID, uid);
        String giftStateKey = getSubStateHashKey(giftId);

        activityCommonRedis.incCommonHashNum(giftPeaceKey, String.valueOf(giftId), incNum);
        Map<String, Integer> hashMap = activityCommonRedis.getCommonHashAll(giftPeaceKey);
        if (hashMap.getOrDefault(giftStateKey, 0) == 0) {
            // 完成礼物
            activityCommonRedis.setCommonHashNum(giftPeaceKey, giftStateKey, 1);
            hashMap.put(giftStateKey, 1);
            int collectNum = 0;
            for (Integer collectGiftId : ORDER_GIFT_ID_MAP.values()) {
                if (hashMap.getOrDefault(getSubStateHashKey(collectGiftId), 0) == 1) {
                    collectNum += 1;
                }
            }
            // 统计完成的礼物种类数量
            activityCommonRedis.setCommonHashNum(giftPeaceKey, getSubAllStateHashKey(), collectNum);
            int index = SEND_GIFT_LEVEL_LIST.indexOf(collectNum);
            if (index > 0) {
                handleRes(uid, SEND_GIFT_KEY_LIST.get(index), ACTIVITY_TITLE_EN);
            }
        }

        if (SPECIAL_GIFT_LIST.contains(giftId)) {
            // 特殊礼物
            activityCommonRedis.incrCommonZSetRankingScore(getGiftSpecialKey(ACTIVITY_ID, giftId), uid, incNum);
        }

    }


    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    public void distributionRanking(String activityId) {
        try {
            for (Integer giftId : SPECIAL_GIFT_LIST) {
                distributionRanking(giftId);
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    // 下发特殊礼物榜单奖励
    private void distributionRanking(int giftId) {
        try {
            int length = 1;
            String typeScoreKey = getGiftSpecialKey(ACTIVITY_ID, giftId);
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(typeScoreKey, length);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = entry.getKey();
                handleRes(aid, PEACE_MASTER_REWARD_KEY, ACTIVITY_TITLE_EN);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }


    private void doActivityParticipationEvent(String uid, int type) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(type);
        eventReport.track(new EventDTO(event));
    }


}
