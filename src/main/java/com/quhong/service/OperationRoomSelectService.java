package com.quhong.service;

import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.OperationRoomWashVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.OtherSupportUserVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 运营房选拔赛
 */
@Service
public class OperationRoomSelectService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(OperationRoomSelectService.class);
    private static final String ACTIVITY_TITLE_EN = "Activity Room Select Competition";
    public static final String ACTIVITY_ID = "676a7295f140c43cb2973797";
    private static final List<String> ALL_LOOK_USER = Arrays.asList("5ddad30b0ca307a634ec01cb", "5cdf784961d047a4adf44064", "5cb6fc80644f8e002a90cfc2");
    // private static final List<String> OPERATION_ROOM_LIST = Arrays.asList("r:65e6c696368c6b63b860c6b8", "r:65e6c695368c6b63b860c6b5", "r:65e6c695368c6b63b860c6b6", "r:65e6c694368c6b63b860c6b4", "r:65e6c693368c6b63b860c6b2", "r:65e6c693368c6b63b860c6b3", "r:65e6c670368c6b63b860c6b1", "r:65e6c664368c6b63b860c6b0", "r:65e6c65c368c6b63b860c6af", "r:65e6c659368c6b63b860c6ae", "r:65e6c658368c6b63b860c6ad", "r:65e6c657368c6b63b860c6ac", "r:65e6c655368c6b63b860c6ab");
    private static final List<String> OPERATION_ROOM_LIST = Arrays.asList("r:60d0a3a691965616273453bf", "r:5c80e09c66dc63003dc4de58", "r:622e9cc874bb1a9f4c763f4f", "r:6063258e7a505b70e2bed1de", "r:5fafa436a3639186a55df9d8", "r:6111b324830ba10ccf89fe70", "r:5db8575dabb01a005f968a93", "r:60dd015bf4d233799ebac629", "r:600806c21b666cec90d405c0", "r:5cb2f98c66dc63003ebfe3f2", "r:633cb648fbdf9b5e1f285947", "r:600c00bf32a61091e2d5f187", "r:5fb920c22a8c6468a363dbd8", "r:5dcf5fb977cc7c7e1f8d4029", "r:5cc9473a66dc630025bf7645", "r:5f6769ac15a6c3af461ca69e", "r:5d8b945178cf5ea933183b5b", "r:5ea7a1c3abb01a00856e5520", "r:5c334f6b66dc63011a81a3a5", "r:5ee27069484060c13abb07e8", "r:5c371f5e66dc630027f609f6", "r:5bc8a56266dc6300f162f3eb", "r:60254fa3abb01a0032b55944", "r:5da4da74add87696168edb44", "r:5c6cd10f66dc63516adcb6b3", "r:5c65a64966dc63003ebe124d", "r:5db52b44ab4f1e4128e0fc96", "r:5c190cd066dc63009d6527df", "r:634454c6202c0143cdef7ba1", "r:5d527bd3abb01a004db9d300", "r:5aed7d7f1bad489359765888", "r:5c90662a66dc630026d3965a", "r:5c5cbfc066dc630026cb6262", "r:5cc388df66dc63002932e454", "r:63159048085609557104c254", "r:5e03fcd2abb01a006a4ee3db", "r:5f16d69babb01a004fdfe5d4", "r:5d38de23ad59835f1f8bbf49", "r:5d6b01e50ddfe7811341c5a7", "r:5c1b8b1d66dc6300a47f8590");

    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    private String getOperationRoomWashRankKey(String activityId) {
        return String.format("operationRoomSelect:%s", activityId);
    }

    private String getSupportRoomUserKey(String activityId, String roomId) {
        return String.format("supportRoomUser:%s:%s", activityId, roomId);
    }


    public OperationRoomWashVO operationRoomSelectConfig(String activityId, String uid) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        OperationRoomWashVO vo = new OperationRoomWashVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        Map<Integer, OtherRankingListVO> hiddenRankingMap = new HashMap<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        Map<String, Integer> scoreMap = new HashMap<>();
        String hostRoomId = RoomUtils.formatRoomId(uid);

        String operationRoomWashRankKey = getOperationRoomWashRankKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(operationRoomWashRankKey, 10);
        int rank = 1;
        int flag = -1;

        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            String roomId = entry.getKey();
            rankingListVO.setScoreStr(entry.getValue().toString());
            rankingListVO.setRoomId(roomId);
            rankingListVO.setRank(rank);
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
            rankingListVO.setName(roomData.getName());
            rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));

            String supportUserKey = getSupportRoomUserKey(activityId, roomId);
            List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 4);
            List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
            for (String supportUid : supportUserList) {
                if (supportUid.equals(RoomUtils.getRoomHostId(roomId))){
                    continue;
                }
                OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                supportUserVO.setName(supportActorData.getName());
                supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                supportUserVO.setUid(supportUid);
                supportUserVOList.add(supportUserVO);

                if (supportUserVOList.size() >= 3){
                    break;
                }
            }
            rankingListVO.setSupportUserList(supportUserVOList);
            if (roomId.equals(hostRoomId)) {
                flag = rank;
                BeanUtils.copyProperties(rankingListVO, myRank);
                List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
                for (OtherSupportUserVO srcSupportUser : supportUserVOList) {
                    OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                    BeanUtils.copyProperties(srcSupportUser, supportUserVO);
                    supportMyUserVOList.add(supportUserVO);
                }
                myRank.setSupportUserList(supportMyUserVOList);
            }else {
                if (!ALL_LOOK_USER.contains(uid)) {
                    rankingListVO.setScoreStr("*****");
                }
            }
            hiddenRankingMap.put(rank, rankingListVO);
            scoreMap.put(entry.getKey(), entry.getValue());
            rank += 1;
        }

        if (flag > 0) {
            OtherRankingListVO beforeMap = hiddenRankingMap.get(flag - 1);
            if (beforeMap != null) {
                beforeMap.setScoreStr(scoreMap.get(beforeMap.getRoomId()).toString());
            }

            OtherRankingListVO afterMap = hiddenRankingMap.get(flag + 1);
            if (afterMap != null) {
                afterMap.setScoreStr(scoreMap.get(afterMap.getRoomId()).toString());
            }
        } else {

            myRank.setScoreStr(String.valueOf(activityCommonRedis.getCommonZSetRankingScore(operationRoomWashRankKey, hostRoomId)));
            myRank.setRoomId(hostRoomId);
            myRank.setRank(-1);
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(hostRoomId);
            if (roomData != null) {
                myRank.setName(roomData.getName());
                myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                myRank.setName(actorData.getName());
                myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            }
            String supportUserKey = getSupportRoomUserKey(activityId, hostRoomId);
            List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 4);
            List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
            for (String supportUid : supportUserList) {
                if (supportUid.equals(RoomUtils.getRoomHostId(hostRoomId))){
                    continue;
                }
                OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                supportUserVO.setName(supportActorData.getName());
                supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                supportUserVO.setUid(supportUid);
                supportMyUserVOList.add(supportUserVO);
                if (supportMyUserVOList.size() >= 3){
                    break;
                }
            }
            myRank.setSupportUserList(supportMyUserVOList);
        }
        if (OPERATION_ROOM_LIST.contains(hostRoomId)) {
            myRank.setRank(-2);
        }
        vo.setMyRank(myRank);
        vo.setOtherRankingList(new ArrayList<>(hiddenRankingMap.values()));
        return vo;
    }

    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        int totalPrice = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        String roomId = giftData.getRoomId();
        String fromUid = giftData.getFrom_uid();

        if (StringUtils.isEmpty(roomId)) {
            return;
        }

        if(OPERATION_ROOM_LIST.contains(roomId) || RoomUtils.isGameRoom(roomId)){
            return;
        }
        String operationRoomWashRankKey = getOperationRoomWashRankKey(activityId);
        String supportRoomUserKey = getSupportRoomUserKey(activityId, roomId);
        activityCommonRedis.incrCommonZSetRankingScore(operationRoomWashRankKey, roomId, totalPrice);
        activityCommonRedis.incrCommonZSetRankingScore(supportRoomUserKey, fromUid, totalPrice);
    }


    // 总榜排行榜奖励
    public void distributionTotalRanking(String activityId) {
        try {
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getOperationRoomWashRankKey(activityId), 10);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                String rankUid = RoomUtils.getRoomHostId(entry.getKey());
                String resourceKey = null;
                switch (rank) {
                    case 1:
                        resourceKey = "ActivityRoomCompetitionTop1";
                        break;
                    case 2:
                        resourceKey = "ActivityRoomCompetitionTop2";
                        break;
                    case 3:
                        resourceKey = "ActivityRoomCompetitionTop3";
                        break;
                    default:
                        resourceKey = "ActivityRoomCompetitionTop4-10";
                }
                resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, "", "");
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

}
