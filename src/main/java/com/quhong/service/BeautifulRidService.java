package com.quhong.service;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.vo.BeautifulRidHistoryVO;
import com.quhong.data.vo.BeautifulRidListVO;
import com.quhong.data.vo.BeautifulRidSearchVO;
import com.quhong.data.vo.BeautifulRidSetVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataResourcesService;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.redis.BeautifulRidRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component
public class BeautifulRidService {

    private static final Logger logger = LoggerFactory.getLogger(BeautifulRidService.class);
    private static final List<String> BEAUTIFUL_RID_LIST = Arrays.asList("ABCABC", "ABABAB", "ABCCBA", "AAA", "AAABBB",
            "AABBCC", "AAAAA", "AAAAAA", "ABCD", "ABCDE", "ABCDEF");

    private static final int BEAUTIFUL_LENGTH = 6;

    @Resource
    private ActorDao actorDao;
    @Resource
    private BeautifulRidDao beautifulRidDao;
    @Resource
    private BeautifulRidChangeLogDao beautifulRidLogDao;
    @Resource
    private BeautifulRidListDao beautifulRidListDao;
    @Resource
    private SearchAlphaDao searchAlphaDao;
    @Resource
    private NewUserHonorDao newUserHonorDao;
    @Resource
    private BeautifulRidRedis beautifulRidRedis;
    @Resource
    private DataResourcesService dataResourcesService;
    @Resource
    private BeautifulRidChangeLogDao beautifulRidChangeLogDao;

    private List<String> getBeautifulRid(int level) {
        if (level >= 2 && level <= 3) {
            return Collections.singletonList("AABBCC");
        } else if (level == 4) {
            return Arrays.asList("AAABBB", "AABBCC");
        } else if (level >= 5) {
            return Collections.singletonList("ALL");
        } else {
            return Collections.emptyList();
        }
    }

    private int getBeautifulRidPattenType(int level) {
        if (level >= 2 && level <= 3) {
            return 1;
        } else if (level == 4) {
            return 2;
        } else if (level >= 5) {
            return 3;
        } else {
            return 0;
        }
    }

    /**
     * 通过荣耀等级获取靓号延续时长
     */
    private int getBeautifulRidContinueDay(int honorLevel) {
        int continueDay = 0;

        if (honorLevel == 1) {
            continueDay = 0;
        } else if (honorLevel == 2) {
            continueDay = 60;
        } else if (honorLevel == 3) {
            continueDay = 90;
        } else if (honorLevel == 4) {
            continueDay = 120;
        } else if (honorLevel >= 5 && honorLevel <= 7) {
            continueDay = 150;
        } else if (honorLevel >= 8) {
            continueDay = -1;
        }

        return continueDay;
    }


    public BeautifulRidListVO beautifulRidList(String uid, int slang) {

        BeautifulRidListVO vo = new BeautifulRidListVO();
        int currentTime = DateHelper.getNowSeconds();
        int isBeautiful = 0;
        int leftDays = 0;

        ActorData userInfo = actorDao.getActorDataFromCache(uid);
        if (userInfo == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        BeautifulRidData beautifulRidData = beautifulRidDao.findDataByTime(uid, currentTime);
        if (beautifulRidData != null) {
            isBeautiful = 1;
            long leftTime = beautifulRidData.getEnd_time() - currentTime;
            leftDays = leftTime > 0 ? (int) (leftTime / 86400) : 0;
        }


        SearchAlphaData searchAlpha = searchAlphaDao.findData(uid);
        if (searchAlpha != null) {
            isBeautiful = 1;
            long leftTime = searchAlpha.getEnd_time() - currentTime;
            leftDays = leftTime > 0 ? (int) (leftTime / 86400) : 0;
        }

        vo.setBadge_id(42);
        vo.setRid(userInfo.getRid());
        vo.setIs_beautiful(isBeautiful);
        vo.setLeft_days(leftDays);
        vo.setBadge_name(slang == 2 ? "نجم الشحن" : "Recharge Star");
        vo.setAlpha_rid("");
        vo.setAlpha_type(0);


        NewUserHonorData honorData = newUserHonorDao.findData(uid);
        int honorLevel = honorData != null ? honorData.getHonor_level() : 0;
        if (honorLevel < 2) {
            vo.setList(Collections.emptyList());
            return vo;

        }

        List<String> beautifulTypeList = getBeautifulRid(honorLevel);
        if (!beautifulTypeList.contains("ALL")) {
            vo.setList(beautifulTypeList);
        } else {
            vo.setList(BEAUTIFUL_RID_LIST);
        }

        return vo;
    }

    private List<Integer> getRecommendRidList(int honorLevel) {
        if (honorLevel < 2) {
            return Collections.emptyList();
        }

        List<BeautifulRidListData> listData = beautifulRidListDao.findRecommendRid(honorLevel);
        List<Integer> ridList = new ArrayList<>();
        for (BeautifulRidListData data : listData) {
            ridList.add(data.getRid());
        }

        return ridList;

    }

    public BeautifulRidSearchVO beautifulRidSearch(String uid, String rid) {

        if (rid.length() != BEAUTIFUL_LENGTH) {
            logger.info("rid length error return uid:{} rid:{} length:{}", uid, rid, rid.length());
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_LENGTH_NOY_ALLOW);
        }

        if (rid.startsWith("0")) {
            logger.info("rid startsWith return  uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_ENTER_VALID);
        }

        int currentTime = DateHelper.getNowSeconds();
        int searchRid;
        try {
            searchRid = Integer.parseInt(rid);
        } catch (Exception e) {
            logger.error("beautifulRidSearch PARAM_ERROR return uid:{}, rid: {}, error:{}", uid, rid, e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        // rid已被自己使用
        BeautifulRidData useRidData = beautifulRidDao.findData(uid);
        if (useRidData != null && useRidData.getBeautiful_rid().equals(rid)) {
            logger.info("useRidData return uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_USING_CODE);
        }

        // rid是否在靓号列表里
        BeautifulRidListData ridObj = beautifulRidListDao.findValidData(searchRid);
        if (ridObj == null) {
            logger.info("ridObj return uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_ENTER_VALID);
        }

        // rid是否隐藏的靓号
        if (beautifulRidRedis.isHideBeautifulRid(rid)) {
            logger.info("isHideBeautifulRid return uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_ENTER_VALID);
        }

//        int searchBeautifulRidStatus = ridObj.getStatus();  // 搜索的靓号当前状态  不等于0: 已使用

        int searchBeautifulRidStatus = actorDao.getActorByRid(searchRid) != null ? 1 : 0;
        int searchBeautifulRidLevel = ridObj.getLevel();  // 搜索的靓号所属等级

        NewUserHonorData honorData = newUserHonorDao.findData(uid);
        int honorLevel = honorData != null ? honorData.getHonor_level() : 0;
        List<Integer> recommendList = getRecommendRidList(honorLevel);

        BeautifulRidSearchVO vo = new BeautifulRidSearchVO();
        vo.setRid(searchRid);
        vo.setRecommend(recommendList);
        vo.setAlphaLevel(beautifulRidDao.getAlphaLevel(String.valueOf(searchRid)));

        if (searchBeautifulRidStatus != 0 || honorLevel < 2) {
            vo.setStatus(0);
            vo.setCan_take(0);
            return vo;
        }

        // 是否正在使用靓号
        BeautifulRidData beautifulRidUsing = beautifulRidDao.findDataByTime(uid, currentTime);
        if (honorLevel < 4) {
            if (beautifulRidUsing != null) {
                vo.setStatus(0);
                vo.setCan_take(0);
                return vo;
            } else if (searchBeautifulRidLevel >= 2) {
                vo.setStatus(1);
                vo.setCan_take(0);
                return vo;
            } else {
                vo.setStatus(1);
                vo.setCan_take(1);
                return vo;
            }
        } else {
            if (beautifulRidUsing != null && beautifulRidUsing.getCan_change_beautiful_rid() == 0) {
                vo.setStatus(1);
                vo.setCan_take(0);
                return vo;

            } else {
                vo.setStatus(1);
                vo.setCan_take(1);
                return vo;
            }
        }

    }

    private void addBeautifulRid(String uid, int realRid, int setRid, int honorLevel) {
        BeautifulRidData alreadyBeautiful = beautifulRidDao.findRealRidDataByUidRid(uid, realRid);
        int duration = getBeautifulRidContinueDay(honorLevel);
        ResourcesDTO resourcesDTO = new ResourcesDTO();
        resourcesDTO.setUid(uid);
        resourcesDTO.setResId(String.valueOf(setRid));
        resourcesDTO.setAlphaLevel(beautifulRidDao.getAlphaLevel(String.valueOf(setRid)));
        resourcesDTO.setResType(BaseDataResourcesConstant.TYPE_BEAUTIFUL_RID);
        resourcesDTO.setDays(duration);
        resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
        resourcesDTO.setDesc("wear beautiful rid");
        resourcesDTO.setmTime(DateHelper.getNowSeconds());
        resourcesDTO.setHonorLevel(honorLevel);
        resourcesDTO.setSetEndTime(0);
        resourcesDTO.setGetWay(BaseDataResourcesConstant.TYPE_CHARGE_GET);

        if (alreadyBeautiful == null) {
            int canChangeBeautifulRid = honorLevel >= 4 ? 1 : 0;
            resourcesDTO.setCanChangeBeautifulRid(canChangeBeautifulRid);
        } else {
            resourcesDTO.setCanChangeBeautifulRid(0);
        }
        logger.info("addBeautifulRid: uid:{}, realRid:{}, setRid:{}, honorLevel:{}, alreadyBeautiful:{}, resourcesDTO: {}", uid, realRid, setRid, honorLevel, alreadyBeautiful, resourcesDTO);

        ApiResult<String> result = dataResourcesService.handleRes(resourcesDTO);
        if (result.isError()) {
            logger.error("buy beautiful rid fail uid={} setRid={} code={} msg={}", uid, setRid, result.getCode(), result.getCode().getMsg());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
    }

    public BeautifulRidSetVO beautifulRidSet(String uid, String rid) {

        int setRid = Integer.parseInt(rid);
        int currentTime = DateHelper.getNowSeconds();

        if (rid.length() != BEAUTIFUL_LENGTH) {
            logger.info("rid length error return uid:{} rid:{} length:{}", uid, rid, rid.length());
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_LENGTH_NOY_ALLOW);
        }

        NewUserHonorData honorData = newUserHonorDao.findData(uid);
        if (honorData == null) {
            logger.info("newUserHonorDao not find return uid:{} rid:{}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_NOT_HONOR_USER);
        }

        // rid是否在靓号列表里
        BeautifulRidListData ridObj = beautifulRidListDao.findValidData(setRid);
        if (ridObj == null) {
            logger.info("ridObj not find return uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_ENTER_VALID);
        }
//        if(ridObj.getStatus() != 0){
//            logger.info("ridObj used return uid:{}, rid: {}", uid, rid);
//            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_RID_USED);
//        }

        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.info("actorData not exist return uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        // 是否被别人使用
        ActorData useRidActorData = actorDao.getActorByRid(setRid);
        if (useRidActorData != null) {
            logger.info("actorData exist return uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_RID_USED);
        }

        // rid是否隐藏的靓号
        if (beautifulRidRedis.isHideBeautifulRid(rid)) {
            logger.info("isHideBeautifulRid return uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_RID_USED);
        }

        int honorLevel = honorData.getHonor_level();
        int beautifulRidLevel = ridObj.getLevel();  // 靓号所属等级
        if (honorLevel <= 4) {
            if (beautifulRidLevel == 0) {
                logger.info(" beautifulRidLevel not conform return-1 uid:{} rid:{}, honorLevel:{}, beautifulRidLevel:{}", uid, rid, honorLevel, beautifulRidLevel);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_NOT_HONOR_USER);
            }

            if (honorLevel < 2) {
                logger.info(" beautifulRidLevel not conform return-2 uid:{} rid:{}, honorLevel:{}, beautifulRidLevel:{}", uid, rid, honorLevel, beautifulRidLevel);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_NOT_HONOR_USER);
            } else if (honorLevel == 3 && beautifulRidLevel >= 2) {
                logger.info(" beautifulRidLevel not conform return-3 uid:{} rid:{}, honorLevel:{}, beautifulRidLevel:{}", uid, rid, honorLevel, beautifulRidLevel);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_NOT_HONOR_USER);
            } else if (honorLevel == 4 && beautifulRidLevel >= 3) {
                logger.info(" beautifulRidLevel not conform return-4 uid:{} rid:{}, honorLevel:{}, beautifulRidLevel:{}", uid, rid, honorLevel, beautifulRidLevel);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_NOT_HONOR_USER);
            }
        }

        BeautifulRidData beautifulRidData = beautifulRidDao.findData(uid);
        if (beautifulRidData != null) {

            int canChangeBeautifulRid = beautifulRidData.getCan_change_beautiful_rid();
            if (honorLevel < 4 || beautifulRidData.getBeautiful_rid().equals(setRid + "")) {
                logger.info("use rid return-1 uid:{}, rid: {}", uid, rid);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_USING_CODE);
            } else if (honorLevel > 4 && canChangeBeautifulRid == 0) {
                logger.info("use rid return-2 uid:{}, rid: {}, canChangeBeautifulRid:{}", uid, rid, canChangeBeautifulRid);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_USING_CODE);
            }  else if (honorLevel == 4 && canChangeBeautifulRid == 0) {
                logger.info("use rid return-3 uid:{}, rid: {}, canChangeBeautifulRid:{}", uid, rid, canChangeBeautifulRid);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_USING_CODE);
            }else {
                addBeautifulRid(uid, beautifulRidData.getReal_rid(), setRid, honorLevel);
            }

        } else {
            int logHonorLevel = 0;
            List<BeautifulRidChangeLog> logs = beautifulRidLogDao.listByHonorLevel(uid, 7);
            if (!logs.isEmpty()) {
                logHonorLevel = logs.get(0).getHonorLevel();  // 该用户有过期时间的最大荣誉等级记录
            }

            if (logHonorLevel > 0 && honorLevel <= logHonorLevel) {
                logger.info("logHonorLevel return-1 uid:{}, rid: {}", uid, rid);
                throw new CommonH5Exception(ActivityHttpCode.BEAUTIFUL_USING_CODE);
            }

            addBeautifulRid(uid, actorData.getRid(), setRid, honorLevel);
        }

        BeautifulRidSetVO vo = new BeautifulRidSetVO();
        BeautifulRidData releaseRidData = beautifulRidDao.findData(uid);
        int leftDays = 0;
        if (releaseRidData != null) {
            long ridEndTime = releaseRidData.getEnd_time();
            long leftTime = ridEndTime - currentTime;

            leftDays = leftTime > 0 ? (int) (leftTime / 86400) : 0;
            vo.setUniqueEndTime((int) releaseRidData.getEnd_time());
        }
        vo.setRid(setRid);
        vo.setLeft_days(leftDays);

        String strRid = String.valueOf(setRid);
        vo.setStrRid(strRid);
        vo.setAlphaLevel(beautifulRidDao.getAlphaLevel(strRid));
        vo.setUniqueGetType(0);
        return vo;
    }


    public BeautifulRidListVO beautifulRidInfo(String uid, int slang) {
        BeautifulRidListVO vo = new BeautifulRidListVO();
        int currentTime = DateHelper.getNowSeconds();
        int leftDays = 0;
        ActorData userInfo = actorDao.getActorDataFromCache(uid);
        if (userInfo == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        BeautifulRidData beautifulRidData = beautifulRidDao.findDataByTime(uid, currentTime);
        String alpha_rid = "";
        if (beautifulRidData != null) {
            long leftTime = beautifulRidData.getEnd_time() - currentTime;
            leftDays = leftTime > 0 ? (int) (leftTime / 86400) : 0;
            alpha_rid = beautifulRidData.getBeautiful_rid();
            vo.setStrRid(alpha_rid);
            vo.setLeft_days(leftDays);
            vo.setAlphaLevel(beautifulRidDao.getAlphaLevel(alpha_rid));
            vo.setUniqueEndTime((int) beautifulRidData.getEnd_time());
            vo.setUniqueGetType(0);
        } else {
            vo.setStrRid(String.valueOf(userInfo.getRid()));
            vo.setAlphaLevel(0);
        }

        NewUserHonorData honorData = newUserHonorDao.findData(uid);
        int honorLevel = honorData != null ? honorData.getHonor_level() : 0;
        int pattenType = getBeautifulRidPattenType(honorLevel);
        vo.setRecommendUniquePatten(pattenType);
        vo.setRecommendUniqueList(recommendRidList(pattenType));
        return vo;
    }

    private List<Integer> recommendRidList(int level) {
        if (level == 0) {
            return Collections.emptyList();
        }
        List<BeautifulRidListData> listData = beautifulRidListDao.findRecommendRidByLevel(level);
        List<Integer> ridList = new ArrayList<>();
        for (BeautifulRidListData data : listData) {
            ridList.add(data.getRid());
        }
        return ridList;
    }

    public BeautifulRidHistoryVO beautifulRidHistory(String uid, int slang) {
        BeautifulRidHistoryVO vo = new BeautifulRidHistoryVO();
        List<BeautifulRidSetVO> historyList = new ArrayList<>();
        List<BeautifulRidChangeLog> logList = beautifulRidChangeLogDao.listByUid(uid);
        if (!CollectionUtils.isEmpty(logList)) {
            for (BeautifulRidChangeLog item : logList) {
                String strRid = item.getAfterRid();
                if (StringUtils.hasLength(strRid) && strRid.length() < 7) {
                    BeautifulRidSetVO itemVO = new BeautifulRidSetVO();
                    itemVO.setStrRid(item.getAfterRid());
                    itemVO.setUniqueGetType(0);
                    itemVO.setAlphaLevel(beautifulRidDao.getAlphaLevel(strRid));
                    itemVO.setUniqueGetTime(item.getcTime());
                    historyList.add(itemVO);
                }
            }
        }
        vo.setHistoryList(historyList);
        return vo;
    }

}
