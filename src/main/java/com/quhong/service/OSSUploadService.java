package com.quhong.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.UploadFileDTO;
import com.quhong.exception.CommonH5Exception;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

@Component
public class OSSUploadService {

    private static final Logger logger = LoggerFactory.getLogger(OSSUploadService.class);

    private static final String OSS_END_POINT = "https://oss-me-east-1.aliyuncs.com";
    private static final String CLOUDCDN_DOMAIN = "https://cloudcdn.qmovies.tv/";

    private static final String accessKeyId = "LTAI5tPAmfMkNHRaC4XMgHf1";
    private static final String accessKeySecret = "******************************";
    private static final String bucketName = "qhclient";
    private static final String WEB_PATH = "web/";
    private static final String pngReplace = "data:image/png;base64,";
    public static final String WEB_PATH_2 = "web2/";
    private static OSS ossClient = null;

    public OSSUploadService() {
        ossClient = new OSSClientBuilder().build(OSS_END_POINT, accessKeyId, accessKeySecret);
    }

    public String upload(MultipartFile file) {
        return upload(file, WEB_PATH);
    }

    public String upload(MultipartFile file, String path) {
        String fileUrl;
        try {
            // 设置存储路径
            String ossFilePath = path + "op_" + DateHelper.getNowSeconds() + "_" + file.getOriginalFilename();
            ossClient.putObject(bucketName, ossFilePath, file.getInputStream());
            fileUrl = CLOUDCDN_DOMAIN + ossFilePath;
        } catch (IOException e) {
            logger.error("uploading failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
        return fileUrl;
    }

    public String genFileName() {
        return UUID.randomUUID().toString().substring(0, 5);
    }


    public String uploadBaseCode(UploadFileDTO dto) {
        String fileUrl;
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            byte[] bytes = decoder.decodeBuffer(dto.getFileBaseCode().replace(pngReplace, ""));
            fileUrl = WEB_PATH + "op_" + DateHelper.getNowSeconds() + "_" + genFileName() + ".png";
            InputStream inputStream = new ByteArrayInputStream(bytes);
            ossClient.putObject(bucketName, fileUrl, inputStream);
            inputStream.close();
        } catch (IOException e) {
            logger.error("uploading failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
        return fileUrl;
    }


    public String uploadWebFile(MultipartFile file, String fileType, String webPath) {
        String fileUrl;
        webPath = webPath != null ? webPath : WEB_PATH;
        try {
            // 设置存储路径
            if (fileType != null && fileType.contains("video")) {
                fileUrl = webPath + "op_" + DateHelper.getNowSeconds() + "_" + genFileName() + ".mp4";
            } else {
                fileUrl = webPath + "op_" + DateHelper.getNowSeconds() + "_" + genFileName() + ".png";
            }
            ossClient.putObject(bucketName, fileUrl, file.getInputStream());
        } catch (IOException e) {
            logger.error("uploadWebFile failed {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
        return fileUrl;
    }
}
