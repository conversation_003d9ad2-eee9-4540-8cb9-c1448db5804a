package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.BlindBoxWeeklyVO;
import com.quhong.data.vo.PopularListVO;
import com.quhong.data.vo.ZipInfoVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BubbleDao;
import com.quhong.mongo.dao.ExtraMicFrameDao;
import com.quhong.mongo.dao.JoinCartonDao;
import com.quhong.mongo.data.BubbleData;
import com.quhong.mongo.data.ExtraMicFrameData;
import com.quhong.mongo.data.JoinCartonData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.BlindBoxRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;


/**
 * 大航海家
 */
@Service
public class BlindBoxWeeklyService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(BlindBoxWeeklyService.class);
    private static final String ACTIVITY_NAME = "BlindBox";
    private static final List<String> DAILY_TASK_LIST = Arrays.asList("a", "b");
    private static final List<String> ADVANCED_ONCE_LIST = Arrays.asList("c", "e", "h");  // 根据时长记录

    @Resource
    private ActorDao actorDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private GiftDao giftDao;
    @Resource
    private BlindBoxRedis blindBoxRedis;
    @Resource
    private BubbleDao bubbleDao;
    @Resource
    private ExtraMicFrameDao extraMicFrameDao;
    @Resource
    private JoinCartonDao joinCartonDao;

    public BlindBoxWeeklyVO blindBoxWeeklyConfig(String uid, String activityId) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        BlindBoxWeeklyVO vo = new BlindBoxWeeklyVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        // 设置礼物信息
        List<BlindBoxWeeklyVO.BlindBox> blindBoxList = new ArrayList<>();

        List<Integer> activityGiftIdList = activity.getActivityGiftList();
        int roundNum = activity.getRoundNum();
        for (Integer giftId: activityGiftIdList) {
            GiftData giftData = giftDao.getGiftFromCache(giftId);
            if(giftData == null){
                continue;
            }
            ZipInfoVO zipInfoVO = JSON.parseObject(giftData.getZipInfo(), ZipInfoVO.class);

            if (zipInfoVO == null || CollectionUtils.isEmpty(zipInfoVO.getRandomGiftList())) {
                logger.error("can not find random gift config. giftId={}", giftData.getRid());
                continue;
            }
            List<ZipInfoVO.RandomGift> randomGiftList = zipInfoVO.getRandomGiftList();

            BlindBoxWeeklyVO.BlindBox blindBox = new BlindBoxWeeklyVO.BlindBox();
            blindBox.setGiftId(giftData.getRid());
            blindBox.setGiftIcon(giftData.getGicon());
            blindBox.setGiftPrice(giftData.getPrice());

            List<String> rankingList = blindBoxRedis.getBlindBoxDailyRankingList(activityId, giftId, 1, roundNum);
            if(rankingList != null && !rankingList.isEmpty()){
                String topUid = rankingList.get(0);
                ActorData actorData = actorDao.getActorDataFromCache(topUid);
                blindBox.setTopHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                blindBox.setTopName(actorData.getName());
                blindBox.setTopUid(topUid);
            }else {
                blindBox.setTopHead("");
                blindBox.setTopName("");
            }

            List<BlindBoxWeeklyVO.BlindGift> blindGiftList = new ArrayList<>();
            for (ZipInfoVO.RandomGift randomGift: randomGiftList) {
                BlindBoxWeeklyVO.BlindGift blindGift = new  BlindBoxWeeklyVO.BlindGift();
                GiftData blindGiftData = giftDao.getGiftFromCache(randomGift.getGiftId());
                blindGift.setGiftId(blindGiftData.getRid());
                blindGift.setGiftIcon(blindGiftData.getGicon());
                blindGift.setGiftPrice(blindGiftData.getPrice());
                blindGiftList.add(blindGift);
            }
            blindBox.setBlindGiftList(blindGiftList);
            blindBoxList.add(blindBox);
        }
        vo.setBlindBoxList(blindBoxList);


        // 设置任务
        List<ActivityCommonConfig.BlindBoxConfig> blindBoxWeeklyConfigList = activityCommonConfig.getBlindBoxConfigList();
        List<BlindBoxWeeklyVO.TaskInfo> dailyTaskList = new ArrayList<>();
        List<BlindBoxWeeklyVO.TaskInfo> advancedTaskList = new ArrayList<>();

        // 设置房间id
        String roomId = otherActivityService.getPopularRoomId();
        for (ActivityCommonConfig.BlindBoxConfig config : blindBoxWeeklyConfigList) {
            BlindBoxWeeklyVO.TaskInfo taskInfo = new BlindBoxWeeklyVO.TaskInfo();
            taskInfo.setRoomId(roomId);
            BeanUtils.copyProperties(config, taskInfo);
            String taskId = config.getDrawType();
            int totalProcess = config.getTotalProcess();
            int currentProcess = 0;

            taskInfo.setJumpGiftId(config.getJumpGiftId());
            taskInfo.setFinishNum(blindBoxRedis.getBlindBoxFinishNumScore(activityId, uid, taskId));
            if(DAILY_TASK_LIST.contains(taskId)){
                currentProcess = blindBoxRedis.getBlindBoxDailyTaskScore(activityId, uid, taskId);
                taskInfo.setFinishProcess(Math.min(currentProcess, totalProcess));
                dailyTaskList.add(taskInfo);
            } else {
                currentProcess = blindBoxRedis.getBlindBoxAdvanceTaskScore(activityId, uid, taskId);
                taskInfo.setFinishProcess(Math.min(currentProcess, totalProcess));
                advancedTaskList.add(taskInfo);
            }
        }

        vo.setDailyTaskList(dailyTaskList);
        vo.setAdvancedTaskList(advancedTaskList);
        return vo;
    }

    public void handleBlindBoxGift(SendGiftData giftData, String activityId, int roundNum) {

        try {
            int sendGiftId = giftData.getGid();
            int receiveGiftId = giftData.getReceiveGiftId();
            int receivePrice = giftData.getReceivePrice();
            int totalReceivePrice = receivePrice * giftData.getNumber();
            int sendTotalNum = giftData.getNumber() * giftData.getAid_list().size(); // 送数量
            String fromUid = giftData.getFrom_uid();
            String toUid = "";
            Set<String> aidList = giftData.getAid_list();
            int sevenEndTime = DateHelper.getNowSeconds() + 604800;

            if(aidList != null && !aidList.isEmpty()){
                toUid = aidList.iterator().next().toString();
            }
            logger.info("fromUid:{}  sendGiftId: {}, receiveGiftId:{}, totalNum:{} toUid:{}", fromUid, sendGiftId, receiveGiftId, sendTotalNum, toUid);

            blindBoxRedis.incrBlindBoxDailyRankingScore(activityId, toUid, sendGiftId, totalReceivePrice, roundNum);

            List<ActivityCommonConfig.BlindBoxConfig> blindBoxWeeklyConfigList = activityCommonConfig.getBlindBoxConfigList();
            for (ActivityCommonConfig.BlindBoxConfig config : blindBoxWeeklyConfigList) {
                int direct = config.getDirect();
                int directUser = config.getDirectUser();
                int detectGiftId = direct == 0 ? sendGiftId : receiveGiftId;
                String directUserId = directUser == 0 ? fromUid : toUid;
                String taskId = config.getDrawType();
                int taskGiftId = config.getTaskGiftId();
                if(taskGiftId != detectGiftId){
                    continue;
                }

                int taskTotal = config.getTotalProcess();

                // 每日任务
                if(DAILY_TASK_LIST.contains(taskId)){
                    int currentTaskNum = blindBoxRedis.getBlindBoxDailyTaskScore(activityId, directUserId, taskId);
                    if(currentTaskNum >= taskTotal){
                        continue;
                    }
                    blindBoxRedis.incrBlindBoxDailyTaskScore(activityId, directUserId, taskId, sendTotalNum);
                    int afterTaskNum = currentTaskNum + sendTotalNum;
                    if(afterTaskNum >= taskTotal){
                        distributionService.sendRewardResource(directUserId, config.getSourceId(),
                                ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), config.getRewardTime(),
                                config.getRewardNum(), ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    }
                }else if (ADVANCED_ONCE_LIST.contains(taskId)){

                    // 道具最高7天有效
                    boolean flag = true;
                    if(ResourceConstant.BUDDLE.equals(config.getRewardType())){
                        BubbleData bubbleData = bubbleDao.findData(directUserId, config.getSourceId());
                        if (bubbleData != null && bubbleData.getEnd_time() > sevenEndTime){
                            flag = false;
                        }
                    }

                    if(ResourceConstant.MIC.equals(config.getRewardType())){
                        ExtraMicFrameData extraMicFrameData = extraMicFrameDao.findData(directUserId, config.getSourceId());
                        if (extraMicFrameData != null && extraMicFrameData.getEnd_time() > sevenEndTime){
                            flag = false;
                        }
                    }

                    if(ResourceConstant.RIDE.equals(config.getRewardType())){
                        JoinCartonData joinCartonData = joinCartonDao.findCartoonData(directUserId, config.getSourceId());
                        if (joinCartonData != null && joinCartonData.getEnd_time() > sevenEndTime){
                            flag = false;
                        }
                    }

                    if(flag){
                        distributionService.sendRewardResource(directUserId, config.getSourceId(),
                                ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), config.getRewardTime(),
                                config.getRewardNum(), ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    }

                    blindBoxRedis.incrBlindBoxFinishNumScore(activityId, directUserId, taskId, 1);

                }else {
                    int tempSendTotalNum = sendTotalNum;
                    while (tempSendTotalNum > 0){
                        int currentTaskNum = blindBoxRedis.getBlindBoxAdvanceTaskScore(activityId, directUserId, taskId);
                        int needTaskNum = taskTotal - currentTaskNum;
                        logger.info("blindBox other task currentTaskNum:{}, needTaskNum:{}, tempSendTotalNum:{}", currentTaskNum, needTaskNum, tempSendTotalNum);
                        if(tempSendTotalNum >= needTaskNum){
                            tempSendTotalNum = tempSendTotalNum - needTaskNum;       // 剩余数量
                            blindBoxRedis.setBlindBoxAdvanceTaskScore(activityId, directUserId, taskId, 0);
                            distributionService.sendRewardResource(directUserId, config.getSourceId(),
                                    ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), config.getRewardTime(),
                                    config.getRewardNum(), ACTIVITY_NAME, ACTIVITY_NAME, 0);

                            blindBoxRedis.incrBlindBoxFinishNumScore(activityId, directUserId, taskId, 1);
                        }else {
                            blindBoxRedis.incrBlindBoxAdvanceTaskScore(activityId, directUserId, taskId, tempSendTotalNum);
                            tempSendTotalNum = 0;
                        }
                    }

                }
            }
        }catch (Exception e){
            logger.error("handleBlindBoxGift: {}", e.getMessage(), e);
        }
    }




}
