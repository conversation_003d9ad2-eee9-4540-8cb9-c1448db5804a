package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.clients.AWSUploader;
import com.quhong.config.AsyncConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.ReportOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.TempToAidDTO;
import com.quhong.data.dto.UserReportDTO;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IMsgService;
import com.quhong.msg.room.ReportHiddenMsg;
import com.quhong.msg.room.UnlockGiftMsg;
import com.quhong.mysql.dao.UserReportDao;
import com.quhong.mysql.dao.UserReportNumDao;
import com.quhong.mysql.data.UserReportData;
import com.quhong.mysql.data.UserReportNumData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class UserReportService {
    private static final Logger logger = LoggerFactory.getLogger(UserReportService.class);
    private static final List<String> REPORT_ORIGIN_LIST = Arrays.asList("roomProfile", "publicChat", "userInfo", "userPage", "moment", "momentComment", "roomEvent");
    private static final List<String> MESSAGE_TYPE_LIST = Arrays.asList(ReportOriginConstant.PRIVATE_MESSAGE, ReportOriginConstant.PUBLIC_CHAT);

    @Resource
    private AWSUploader awsUploader;
    @Resource
    private UserReportDao userReportDao;
    @Resource
    private UserReportNumDao userReportNumDao;
    @Resource
    private IDetectService detectService;
    @Resource
    private IMsgService iMsgService;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;
    @Resource
    private CommonTaskService commonTaskService;



    private String createCdnUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        url = url.replaceAll("https://qhcf.s3.ap-southeast-1.amazonaws.com", "https://cdn3.qmovies.tv");
        return url;
    }

    public void userReport(UserReportDTO dto, MultipartFile[] images) {
        logger.info("get common report. uid={} requestId={}", dto.getUid(), dto.getRequestId());
        String uid = dto.getUid();
        String origin = dto.getOrigin();
        String targetId = dto.getTargetId();
        String problemInfo = dto.getProblemInfo() != null ? dto.getProblemInfo().trim() : "";
        String publicText = dto.getPublicText() != null ? dto.getPublicText().trim() : "";
        String msgId = dto.getMsgId();
        Integer msgType = dto.getMsgType();

        // if (!ReportOriginConstant.REPORT_ORIGIN_LIST.contains(origin)) {
        //     logger.error("Parameter error, report origin. uid={} targetId={}, origin={}", uid, targetId, origin);
        //     throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        // }

        if (StringUtils.isEmpty(targetId)) {
            logger.error("Parameter error, no target specified. uid={} targetId={}", uid, targetId);
            throw new CommonH5Exception(ActivityHttpCode.NO_TARGET_SPECIFIED);
        }
        if (StringUtils.isEmpty(problemInfo)) {
            logger.error("Please explain the reason for the report. uid={} problemInfo={}", uid, problemInfo);
            throw new CommonH5Exception(ActivityHttpCode.EXPLAIN_THE_REASON_FOR_THE_REPORT);
        }

        if (StringUtils.isEmpty(dto.getReasonSelect())) {
            logger.error("Please explain the reason for the report. uid={} reasonSelect={}", uid, dto.getReasonSelect());
            throw new CommonH5Exception(ActivityHttpCode.EXPLAIN_THE_REASON_FOR_THE_REPORT);
        }

        if (problemInfo.length() > 200) {
            logger.error("Sorry, the text you posted is too long. uid={} reportContent={}", uid, problemInfo);
            throw new CommonH5Exception(ActivityHttpCode.TEXT_YOU_POSTED_IS_TOO_LONG);
        }
        if (images != null && images.length > 3) {
            logger.error("Image upload failed, please resubmit. uid={} ", uid);
            throw new CommonH5Exception(ActivityHttpCode.IMAGE_UPLOAD_FAILED);
        }

        int nowTime = DateHelper.getNowSeconds();
        if(!MESSAGE_TYPE_LIST.contains(origin)){
            UserReportData reportData = userReportDao.selectOne(uid, origin, targetId);
            // 两天内不能重复举报
            int twoDayTime = 2 * 24 * 3600;
            if (reportData != null && nowTime - reportData.getCtime() < twoDayTime) {
                logger.error("You have already reported this operation. Thank you for your support for our work! uid={} origin={}, targetId={}", uid, origin, targetId);
                if(ReportOriginConstant.FAMILY.equals(origin)){
                    throw new CommonH5Exception(ActivityHttpCode.ALREADY_REPORTED_THIS_FAMILY);
                }else {
                    throw new CommonH5Exception(ActivityHttpCode.ALREADY_REPORTED_THIS_USER);
                }
            }
        }

        List<String> imgUrls = new ArrayList<>();
        if (images != null && images.length > 0) {
            for (MultipartFile file : images) {
                if (file == null || file.isEmpty()) {
                    continue;
                }
                String url = awsUploader.updateLoad(file, "common_report/" + uid + "/" + DateHelper.getNowSeconds() + file.getName());
                logger.info("url={}", url);
                if (!StringUtils.isEmpty(url)) {
                    url = createCdnUrl(url);
                    imgUrls.add(url);
                } else {
                    logger.error("Image upload failed, please resubmit. uid={} origin={}, targetId={}", uid, origin, targetId);
                    throw new CommonH5Exception(ActivityHttpCode.IMAGE_UPLOAD_FAILED);
                }
            }
        }

        String imageInfo = JSONObject.toJSONString(imgUrls);
        if(imageInfo.length() > 500){
            throw new CommonH5Exception(ActivityHttpCode.IMAGE_UPLOAD_FAILED.getCode(), "image is too long");
        }

        // 房间公屏消息和私信消息长按举报，增加数美鉴定, 鉴定为违规内容需扣除友好积分
        if(ReportOriginConstant.PRIVATE_MESSAGE.equals(origin) || ReportOriginConstant.PUBLIC_CHAT.equals(origin)){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    doDetectMsgTask(uid, targetId, origin, publicText, msgId, msgType);
                }
            });
        }


        try {
            UserReportData data = new UserReportData();
            data.setUid(uid);
            data.setOrigin(origin);
            data.setReasonSelect(dto.getReasonSelect());
            data.setSubType(dto.getSubType());
            data.setReportContent(problemInfo);
            data.setPublicText(dto.getPublicText());
            data.setImageInfo(imageInfo);
            data.setTargetId(targetId);
            data.setMsgId(msgId);
            data.setMsgType(msgType);
            data.setMtime(nowTime);
            data.setCtime(nowTime);
            userReportDao.insertOne(data);

            UserReportNumData userReportNumData = userReportNumDao.selectOne(targetId);
            if(userReportNumData == null){
                userReportNumData = new UserReportNumData();
                userReportNumData.setAid(targetId);
                userReportNumData.setTotal(1);
                userReportNumData.setMtime(nowTime);
                userReportNumData.setCtime(nowTime);
                userReportNumDao.insertOne(userReportNumData);
            }else {
                userReportNumData.setTotal(userReportNumData.getTotal() + 1);
                userReportNumData.setMtime(nowTime);
                userReportNumDao.updateOne(userReportNumData);
            }
        }catch (Exception e){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

    }

    public void doDetectMsgTask(String uid, String targetId, String origin, String reportContent, String msgId, Integer msgType){
        if(msgType == null || StringUtils.isEmpty(reportContent)){
            logger.info("doDetectMsgTask empty uid:{}, origin:{}, reportContent:{}, msgId:{}, msgType:{}", uid, origin, reportContent, msgId, msgType);
            return;
        }
        boolean msgFlag = false;
        if(msgType == MsgType.TEXT && detectService.detectText(new TextDTO(reportContent, origin, uid)).getData().getIsSafe() == 0){
            logger.info("doDetectMsgTask uid:{}, origin:{}, reportContent:{}", uid, origin, reportContent);
            // 私信消息鉴定为违规内容时，举报用户端隐藏该消息
            msgFlag = true;

        }

        if (msgType == MsgType.IMAGE && detectService.detectImage(new ImageDTO(reportContent, origin, uid)).getData().getIsSafe() == 0) {
            msgFlag = true;
            logger.info("doDetectMsgTask uid:{}, origin:{}, IMAGE:{}", uid, origin, reportContent);
        }

        if(msgFlag){
            //
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(targetId, "", uid, "", CommonMqTaskConstant.USER_MSG_VIOLATION, 1));
            if(ReportOriginConstant.PRIVATE_MESSAGE.equals(origin) && !StringUtils.isEmpty(msgId)){
                ReportHiddenMsg msg = new ReportHiddenMsg();
                msg.setMessageId(msgId);
                marsMsgActivityService.asyncSendPlayerMsg("", uid, uid, msg, true);

                TempToAidDTO tempToAidDTO = new TempToAidDTO();
                tempToAidDTO.setUid(uid);
                tempToAidDTO.setMsgId(msgId);
                iMsgService.deleteMsgRecord(tempToAidDTO);

            }
        }
    }
}
