package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.GuardEarthDTO;
import com.quhong.data.vo.GuardEarthVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 小丑与魔术师活动
 */
@Service
public class GuardEarthService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(GuardEarthService.class);
    private static final String ACTIVITY_TASK_ALL = "totalRewardStatus";
    private static final String ACTIVITY_TITLE = "Protect our earth";
    private static final String ACTIVITY_DESC = "Protect our earth reward";
    public static final List<Integer> GIFT_ID_LIST = ServerConfig.isProduct() ?  Arrays.asList(716, 717, 718) : Arrays.asList(864, 865, 866);
    public static final List<Integer> GIFT_MAX_LIST = Arrays.asList(99, 39, 19);
    public static final List<String> TASK_KEY_LIST = Arrays.asList("forest", "ocean", "earth");
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ActivityCommonConfig activityCommonConfig;

    private String getStatusKey(String drawKey){
        return String.format("status:%s", drawKey);
    }

    private String getHashActivityId(String activityId, String uid){
        return String.format("guardEarth:%s:%s", activityId, uid);
    }

    public GuardEarthVO guardEarthConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        GuardEarthVO vo = new GuardEarthVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        // 设置任务信息
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> guardEarthData = activityCommonRedis.getCommonHashAll(hashActivityId);
        List<GuardEarthVO.TaskConfig> guardEarthConfigVOList = new ArrayList<>();
        int index = 0;
        for (String taskKey: TASK_KEY_LIST) {
            GuardEarthVO.TaskConfig taskConfig = new GuardEarthVO.TaskConfig();
            taskConfig.setTaskKey(taskKey);
            taskConfig.setCurrentNum(Math.min(guardEarthData.getOrDefault(taskKey, 0), GIFT_MAX_LIST.get(index)));
            taskConfig.setTotalNum(GIFT_MAX_LIST.get(index));
            guardEarthConfigVOList.add(taskConfig);
            index += 1;
        }

        vo.setTaskConfigList(guardEarthConfigVOList);

        List<PrizeConfigVO> micConfigVOList = new ArrayList<>();
        List<ActivityCommonConfig.CommonAwardConfig> guardEarthConfigList = activityCommonConfig.getGuardEarthConfigList();
        for (ActivityCommonConfig.CommonAwardConfig config: guardEarthConfigList) {
            PrizeConfigVO prizeConfigVO = new PrizeConfigVO();
            BeanUtils.copyProperties(config, prizeConfigVO);
            micConfigVOList.add(prizeConfigVO);
        }
        vo.setGuardEarthConfigList(micConfigVOList);
        vo.setTotalRewardStatus(guardEarthData.getOrDefault(ACTIVITY_TASK_ALL, 0));

        // 设置总榜
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        List<OtherRankingListVO> totalRankingList = new ArrayList<>();
        OtherRankingListVO mytTotalRank = new OtherRankingListVO();
        Map<String, Integer> dailyRankingMap = activityCommonRedis.getCommonRankingMap(activityId, 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : dailyRankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            if(aid.equals(uid)){
                BeanUtils.copyProperties(rankingVO, mytTotalRank);
            }
            totalRankingList.add(rankingVO);
            rank += 1;
        }

        if(mytTotalRank.getRank() == null || mytTotalRank.getRank() == 0){
            mytTotalRank.setName(actorData.getName());
            mytTotalRank.setUid(uid);
            mytTotalRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            mytTotalRank.setScore(activityCommonRedis.getCommonZSetRankingScore(activityId, uid));
            mytTotalRank.setRank(-1);
        }
        vo.setTotalRankingList(totalRankingList);
        vo.setMyTotalRank(mytTotalRank);
        return vo;
    }


    // 总榜排行榜奖励
    public void distributionTotalGuardRanking(String activityId){
        try{
            if(!ServerConfig.isProduct()){
                return;
            }

            Map<String, Integer> brainRankingMap = activityCommonRedis.getCommonRankingMap(activityId, 3);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
                String rankUid = entry.getKey();
                switch (rank){
                    case 1:
                        distributionService.sendRewardResource(rankUid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 19999, ACTIVITY_TITLE, ACTIVITY_DESC, 0);
                        break;
                    case 2:
                        distributionService.sendRewardResource(rankUid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 15999, ACTIVITY_TITLE, ACTIVITY_DESC, 0);
                        break;
                    case 3:
                        distributionService.sendRewardResource(rankUid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 13999, ACTIVITY_TITLE, ACTIVITY_DESC, 0);
                        break;
                }
                rank += 1;

            }
        }catch (Exception e){
            logger.error("distributionTotalGuardRanking error: {}", e.getMessage(), e);
        }
    }

    public void guardEarthClaim(GuardEarthDTO dto) {

        synchronized (stringPool.intern(ACTIVITY_TITLE)) {
            String activityId = dto.getActivityId();
            String uid = dto.getUid();
            List<Integer> micList = dto.getMicList();
            checkActivityTime(activityId);

            if(CollectionUtils.isEmpty(micList)){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String userHashKey = getHashActivityId(activityId, uid);
            Map<String, Integer> userData = activityCommonRedis.getCommonHashAll(userHashKey);
            int totalRewardStatus = userData.getOrDefault(ACTIVITY_TASK_ALL, 0);
            if(totalRewardStatus != 1){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }

            if(micList.size() != 2){
                logger.error("guardEarthClaim micList error micList:{}", micList);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            activityCommonRedis.setCommonHashNum(userHashKey, ACTIVITY_TASK_ALL, 2);
            Map<Integer, ActivityCommonConfig.CommonAwardConfig> guardEarthConfigMap = activityCommonConfig.getGuardEarthConfigList().stream().collect(Collectors.toMap(ActivityCommonConfig.CommonAwardConfig::getSourceId, Function.identity()));
            for (Integer micId : micList) {
                ActivityCommonConfig.CommonAwardConfig config = guardEarthConfigMap.get(micId);
                if(config == null){
                    continue;
                }
                distributionService.sendRewardResource(uid, config.getSourceId(), ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), 7, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
            }
            distributionService.sendRewardResource(uid, 2586, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);

        }
    }

    public void handleUserSend(String activityId, String uid, int index, int totalNumber) {

        String taskKey = TASK_KEY_LIST.get(index);
        int maxNum = GIFT_MAX_LIST.get(index);
        String userHashKey = getHashActivityId(activityId, uid);
        Map<String, Integer> userData = activityCommonRedis.getCommonHashAll(userHashKey);
        int totalRewardStatus = userData.getOrDefault(ACTIVITY_TASK_ALL, 0);
        if(totalRewardStatus != 0){
            return;
        }

        int currentNum = userData.getOrDefault(taskKey, 0);
        if(currentNum > maxNum){
            return;
        }
        int afterNUm = activityCommonRedis.incCommonHashNum(userHashKey, taskKey, totalNumber);
        userData.put(taskKey, afterNUm);
        if(afterNUm > maxNum){

            int finishStatus = 0;
            for (int i = 0; i < TASK_KEY_LIST.size(); i++) {
                String item = TASK_KEY_LIST.get(i);
                int taskNum = userData.getOrDefault(item, 0);
                int taskMaxNum = GIFT_MAX_LIST.get(i);
                if(taskNum >= taskMaxNum){
                    finishStatus += 1;
                }
            }

            if(finishStatus >= TASK_KEY_LIST.size()){
                activityCommonRedis.setCommonHashNum(userHashKey, ACTIVITY_TASK_ALL, 1);
            }
        }
    }

    public void handleGiftMqMsg(SendGiftData giftData, String activityId) {
        synchronized (stringPool.intern(activityId)) {
            String fromUid = giftData.getFrom_uid();
            int giftId = giftData.getGid();
            int totalNumber = giftData.getNumber() * giftData.getAid_list().size();
            int totalBeans = totalNumber * giftData.getPrice();
            int giftIndex = GIFT_ID_LIST.indexOf(giftId);
            handleUserSend(activityId, fromUid, giftIndex, totalNumber);
            activityCommonRedis.incrCommonZSetRankingScore(activityId, fromUid, totalBeans);
        }
    }

    public void handleTotalGiftMqMsg(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        activityCommonRedis.incrCommonZSetRankingScore(activityId, fromUid, totalBeans);
    }


}
