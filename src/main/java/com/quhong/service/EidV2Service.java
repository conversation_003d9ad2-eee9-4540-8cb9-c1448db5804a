package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.EidV2VO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import java.util.*;

@Service
public class EidV2Service extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(EidV2Service.class);
    private static final String ACTIVITY_TITLE = "Eid Activity";
    public static final String ACTIVITY_ID = "6606b5bc26fdf2a19cb5ec07";
    private static final List<String> DATE_LIST = Arrays.asList("2024-03-31", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05",
            "2024-04-06", "2024-04-07", "2024-04-08", "2024-04-09", "2024-04-10");

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;

    private String getEidDate(String activityId){
        // String dateKey = "eidActivityDate:" + activityId;
        // return activityCommonRedis.getCommonStrValue(dateKey);
        return DateHelper.ARABIAN.formatDateInDay();
    }

    private String getDailyRankingKey(String activityId, String dateStr){
        return String.format("dailyRanking:%s:%s", activityId, dateStr);
    }

    public EidV2VO eidV2Config(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        EidV2VO vo = new EidV2VO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        // 设置日榜
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();

        String eidDate = getEidDate(activityId);
        String dailyRankingKey = getDailyRankingKey(activityId, eidDate);
        Map<String, Integer> dailyRankingMap = activityCommonRedis.getCommonRankingMap(dailyRankingKey, 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : dailyRankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            if(aid.equals(uid)){
                BeanUtils.copyProperties(rankingVO, myRank);
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if(myRank.getRank() == null || myRank.getRank() == 0){
            myRank.setName(actorData.getName());
            myRank.setUid(uid);
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRank.setScore(activityCommonRedis.getCommonZSetRankingScore(dailyRankingKey, uid));
            myRank.setRank(-1);
        }

        vo.setDailyRankingList(rankingList);
        vo.setMyDailyRank(myRank);

        // 设置每日头像
        List<OtherRankingListVO> dailyRankHeadList = new ArrayList<>();
        for (String rankDate : DATE_LIST) {
            String dayKey = getDailyRankingKey(activityId, rankDate);
            Map<String, Integer> top1RankingMap = activityCommonRedis.getCommonRankingMap(dayKey, 1);
            if(CollectionUtils.isEmpty(top1RankingMap)){
                continue;
            }
            for (Map.Entry<String, Integer> entry : top1RankingMap.entrySet()) {
                OtherRankingListVO rankingVO = new OtherRankingListVO();
                String aid = entry.getKey();
                ActorData rankActor = actorDao.getActorDataFromCache(aid);
                rankingVO.setUid(aid);
                rankingVO.setScore(entry.getValue());
                rankingVO.setName(rankActor.getName());
                rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                rankingVO.setRankDate(rankDate);
                dailyRankHeadList.add(rankingVO);
            }
        }
        vo.setDailyRankHeadList(dailyRankHeadList);
        return vo;
    }

    public void distributionDailyRankingAward(){
        try{
            String lastDate = DateHelper.ARABIAN.getYesterdayStr(new Date());
            String dayKey = getDailyRankingKey(ACTIVITY_ID, lastDate);
            Map<String, Integer> top1RankingMap = activityCommonRedis.getCommonRankingMap(dayKey, 1);
            if(CollectionUtils.isEmpty(top1RankingMap)){
                return;
            }
            for (Map.Entry<String, Integer> entry : top1RankingMap.entrySet()) {
                String aid = entry.getKey();
                distributionService.sendRewardResource(aid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 1000, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
            }
        }catch (Exception e){
            logger.error("distributionRankingAward error: {}", e.getMessage(), e);
        }
    }

    public void handleGiftMqMsg(SendGiftData giftData) {
        String fromUid = giftData.getFrom_uid();
        int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        String eidDate = getEidDate(ACTIVITY_ID);
        String dailyRankingKey = getDailyRankingKey(ACTIVITY_ID, eidDate);
        activityCommonRedis.incrCommonZSetRankingScore(dailyRankingKey, fromUid, totalBeans);
    }


}
