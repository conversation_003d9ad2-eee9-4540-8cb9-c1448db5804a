package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mysql.dao.QuestionAwardDao;
import com.quhong.mysql.dao.QuestionDao;
import com.quhong.mysql.dao.QuizActivityTemplateDao;
import com.quhong.mysql.dao.QuizResultDao;
import com.quhong.mysql.data.QuestionAwardData;
import com.quhong.mysql.data.QuestionData;
import com.quhong.mysql.data.QuizActivityTemplateData;
import com.quhong.mysql.data.QuizResultData;
import com.quhong.redis.QuizActivityRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 答题活动Service
 *
 * <AUTHOR>
 * @date 2022/9/2
 */
@Service
public class QuizActivityService {

    private static final Logger logger = LoggerFactory.getLogger(QuizActivityService.class);

    private static final String SEND_GIFT_TITLE = "Quiz Activity Rewards";
    private static final String SEND_GIFT_DESC = "quiz activity rewards";

    @Resource
    private QuestionDao questionDao;
    @Resource
    private QuizActivityTemplateDao templateDao;
    @Resource
    private QuestionAwardDao questionAwardDao;
    @Resource
    private QuizResultDao quizResultDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private QuizActivityRedis quizActivityRedis;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private CheckpointQuizService checkpointQuizService;

    /**
     * 答题活动相关配置
     */
    public Object quizActivity(String uid, Integer activityId) {
        QuizActivityVO vo = new QuizActivityVO();
        ActorData userData = actorDao.getActorDataFromCache(uid);
        if (userData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        vo.setUserName(userData.getName());
        vo.setUserHead(userData.getHead());
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        if (templateData.getQuizType() == 1) {
            // 闯关答题
            return checkpointQuizService.checkpointQuiz(uid, activityId, templateData, userData);
        }
        vo.setActivityId(templateData.getId());
        vo.setQuizType(templateData.getQuizType());
        // 模板配置
        QuizActivityConfigVO configVO = new QuizActivityConfigVO();
        BeanUtils.copyProperties(templateData, configVO);
        List<QuestionAwardData> questionAwardList = questionAwardDao.selectByActivityId(activityId);
        if (CollectionUtils.isEmpty(questionAwardList)) {
            questionAwardList = Collections.emptyList();
        }
        int joinNum = 0;
        if (configVO.getShowJoinNum() == 1) {
            joinNum = quizActivityRedis.getQuizJoinNum(activityId);
        }
        configVO.setAcBeginTime(dealTime(configVO.getAcBeginTime()));
        configVO.setAcEndTime(dealTime(configVO.getAcEndTime()));
        configVO.setJoinNum(joinNum);
        configVO.setReward(questionAwardList);
        vo.setConfigVO(configVO);
        // 活动排行榜
        List<QuizActivityRankingVO> rankingVOList = new ArrayList<>();
        List<QuizResultData> topTenRankingList = quizResultDao.selectTopTenRanking(activityId);
        if (!CollectionUtils.isEmpty(topTenRankingList)) {
           for (int i=0; i<topTenRankingList.size(); i++) {
               QuizActivityRankingVO rankingVO = new QuizActivityRankingVO();
               QuizResultData resultData = topTenRankingList.get(i);
               ActorData actorData = actorDao.getActorDataFromCache(resultData.getUid());
               rankingVO.setName(actorData.getName());
               rankingVO.setHead(actorData.getHead());
               rankingVO.setRank(i + 1);
               rankingVO.setScore(resultData.getBestScore());
               rankingVO.setTime(new BigDecimal(resultData.getBestTime()).divide(new BigDecimal(1000),1, BigDecimal.ROUND_HALF_UP));
               rankingVOList.add(rankingVO);
           }
        }
        vo.setRankingList(rankingVOList);
        vo.setMyGainRewards(getMyGainRewards(uid, activityId));
        // 剩余答题次数
        int deviceQuizCount = quizActivityRedis.getDeviceQuizCount(activityId, userData.getTn_id());
        vo.setFreeAnswerCount(templateData.getAnswersNum() - deviceQuizCount);
        return vo;
    }

    private Date dealTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, 2);
        calendar.add(Calendar.MINUTE, 30);
        return calendar.getTime();
    }

    private List<QuizActivityVO.MyGainReward> getMyGainRewards(String uid, Integer activityId) {
        Set<String> gainReward = quizActivityRedis.getGainReward(uid, activityId);
        List<QuizActivityVO.MyGainReward> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(gainReward)) {
            for (String jsonValue : gainReward) {
                QuestionAwardData awardData = JSONObject.parseObject(jsonValue, QuestionAwardData.class);
                QuizActivityVO.MyGainReward myGainReward = new QuizActivityVO.MyGainReward();
                BeanUtils.copyProperties(awardData, myGainReward);
                int showTimeOrNum = 0;
                if ("gift".equals(awardData.getRewardType()) || "diamond".equals(awardData.getRewardType())) {
                    showTimeOrNum = 1;
                } else if ("other".equals(awardData.getRewardType())) {
                    if (awardData.getRewardTimes() == 0) {
                        showTimeOrNum = 1;
                    }
                }
                myGainReward.setShowTimeOrNum(showTimeOrNum);
                list.add(myGainReward);
            }
        }
        return list;
    }

    /**
     * 获取题目列表
     */
    public QuizQuestionVO getQuestionList(String uid, Integer activityId, Integer slang) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizQuestionVO vo = new QuizQuestionVO();
        List<QuizQuestionVO.QuestionVO> list = new ArrayList<>();
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        logger.info("now={}, acBeginTime={}, acEndTime={}", new Date(), dealTime(templateData.getAcBeginTime()), dealTime(templateData.getAcEndTime()));
        if (new Date().before(dealTime(templateData.getAcBeginTime()))) {
            // 活动还未开始
            logger.error("The quiz activity has not started yet. uid={} activityId={}", uid, activityId);
            throw new CommonException(ActivityHttpCode.QUIZ_ACTIVITY_HAS_NOT_STARTED);
        }
        if (new Date().after(dealTime(templateData.getAcEndTime()))) {
            // 活动已经结束
            logger.error("The quiz activity has ended uid={} activityId={}", uid, activityId);
            throw new CommonException(ActivityHttpCode.QUIZ_ACTIVITY_HAS_ENDED);
        }
        int deviceQuizCount = quizActivityRedis.getDeviceQuizCount(activityId, actorData.getTn_id());
        if (templateData.getAnswersNum() - deviceQuizCount <= 0 ) {
            // 每日答题次数已用完
            logger.error("The number of answers has been used up uid={} activityId={}", uid, activityId);
            throw new CommonException(ActivityHttpCode.NUM_OF_ANSWERS_HAS_BEEN_USED_UP);
        }
        int questionNum = quizActivityRedis.getQuestionNumInRedis(uid, activityId, templateData.getAnswerType(), slang);
        if (questionNum == 0) {
            List<QuestionData> questionList = questionDao.selectList(slang == SLangType.ARABIC ? templateData.getArGid() : templateData.getGid());
            if (CollectionUtils.isEmpty(questionList)) {
                vo.setList(list);
                return vo;
            }
            for(QuestionData questionData : questionList) {
                quizActivityRedis.saveQuestionInRedis(uid, activityId, questionData.getId(), templateData.getAnswerType(), slang);
            }
        }
        List<Integer> tidList = quizActivityRedis.getQuestionFromRedis(uid, activityId, templateData.getOnceNum(), templateData.getAnswerType(), slang);
        if (CollectionUtils.isEmpty(tidList)) {
            vo.setList(list);
            return vo;
        }
        for (Integer tid : tidList) {
            QuestionData questionData = questionDao.selectOne(tid);
            QuizQuestionVO.QuestionVO questionVO = new QuizQuestionVO.QuestionVO();
            questionVO.setId(tid);
            questionVO.setPictureUrl(questionData.getPictureUrl());
            questionVO.setContent(questionData.getContent());
            Map<String, String> optionContentMap;
            String optionContent = questionData.getOptionContent();
            if (!StringUtils.isEmpty(optionContent)) {
                optionContentMap = JSON.parseObject(optionContent, new TypeReference<HashMap<String, String>>() {});
            } else {
                optionContentMap = new HashMap<>(4);
            }
            questionVO.setOptionContent(optionContentMap);
            questionVO.setCorrectOption(questionData.getCorrectOption());
            list.add(questionVO);
        }
        vo.setList(list);
        quizActivityRedis.saveDeviceQuizCount(activityId, actorData.getTn_id());
        quizActivityRedis.addSubmitQuizCount(uid, activityId);
        return vo;
    }

    /**
     * 提交答题回答
     */
    public QuizResultVO submitAnswer(QuizActivityDTO dto) {
        Integer activityId = dto.getActivityId();
        String uid = dto.getUid();
        if (dto.getTime() > 24 * 60 * 60 * 1000) {
            logger.error("submit answer time param error. uid={} time={}", dto.getUid(), dto.getTime());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        int time = Math.toIntExact(dto.getTime());
        if (activityId == null || StringUtils.isEmpty(uid) || dto.getTime() == null || dto.getTime() == 0) {
            logger.error("submit answer param error. activityId={} uid={} time={}", activityId, uid, dto.getTime());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizResultVO vo = new QuizResultVO();
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find quiz activity template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        int nowTime = DateHelper.getNowSeconds();
        Map<Integer, String> quizAnswer = dto.getQuizAnswer();
        // 获取用户剩余答题次数
        Integer quizCount = quizActivityRedis.getDeviceQuizCount(activityId, actorData.getTn_id());
        vo.setFreeAnswerCount(templateData.getAnswersNum() - quizCount);
        List<QuestionData> questionList = questionDao.selectList(dto.getSlang() == SLangType.ARABIC ? templateData.getArGid() : templateData.getGid());
        Map<Integer, String> correctOptionMap;
        if (!CollectionUtils.isEmpty(questionList)) {
            correctOptionMap = questionList.stream().collect(Collectors.toMap(QuestionData::getId, QuestionData::getCorrectOption));
        } else {
            correctOptionMap = new HashMap<>(4);
        }
        if (CollectionUtils.isEmpty(quizAnswer)) {
            quizAnswer = new HashMap<>(4);
        }
        QuizResultData resultData = quizResultDao.selectOne(activityId, uid);
        int score = 0;
        int bestScore = resultData != null ? resultData.getBestScore() : 0;
        int bestTime = resultData != null ? resultData.getBestTime() : 0;
        // 计数分数
        int oneScore = templateData.getScore();
        for (Map.Entry<Integer, String> entry: quizAnswer.entrySet()) {
            if (!correctOptionMap.containsKey(entry.getKey())) {
                continue;
            }
            String correctOption = correctOptionMap.get(entry.getKey());
            if (Objects.equals(entry.getValue(), correctOption)) {
                score += oneScore;
            }
        }
        vo.setRewardList(getRewardList(uid, activityId, score));
        // 对比本次成绩获取最好分数和最好时间
        int newBestScore = Math.max(score, bestScore);
        int newBestTime = getNewBestTime(score, bestScore, time, bestTime);
        int userLevel = userLevelDao.getUserLevel(uid);
        // 更新或保存最好成绩
        saveQuizResult(resultData, activityId, uid, score, bestScore, newBestScore, time, bestTime, newBestTime, userLevel, nowTime);
        BigDecimal oneThousand = new BigDecimal(1000);
        vo.setScore(score);
        vo.setTime(new BigDecimal(time).divide(oneThousand,1, BigDecimal.ROUND_HALF_UP));
        vo.setBestScore(newBestScore);
        vo.setBestTime(new BigDecimal(newBestTime).divide(oneThousand,1, BigDecimal.ROUND_HALF_UP));
        // 获取本次排名和最好排名
        int oldRanking = quizActivityRedis.getQuizRanking(uid, activityId);
        double oldRankingScore = quizActivityRedis.getQuizRankingScore(uid, activityId);
        quizActivityRedis.saveQuizRanking(uid, activityId, score, time, nowTime, userLevel);
        int curRanking = quizActivityRedis.getQuizRanking(uid, activityId);
        vo.setRanking(curRanking);
        vo.setBestRanking(getBestRanking(oldRanking, curRanking, oldRankingScore, activityId, uid));
        return vo;
    }

    private Integer getNewBestTime(int score, Integer bestScore, int time, Integer bestTime) {
        if (bestTime == null || bestScore == null || score > bestScore) {
            return time;
        } else if (score == bestScore) {
            return Math.min(time, bestTime);
        } else {
            return bestTime;
        }
    }

    /**
     * 答题活动奖励
     */
    private List<QuizResultVO.GainQuizReward> getRewardList(String uid, Integer activityId, Integer score) {
        List<QuizResultVO.GainQuizReward> rewardList = new ArrayList<>();
        List<QuestionAwardData> questionAwardList = questionAwardDao.selectByActivityId(activityId);
        int submitQuizCount = quizActivityRedis.getSubmitQuizCount(uid, activityId);
        if (!CollectionUtils.isEmpty(questionAwardList)) {
            for (QuestionAwardData awardData : questionAwardList) {
                if(awardData.getRewardLimit() > score) {
                    continue;
                }
                QuizResultVO.GainQuizReward gainQuizReward = new QuizResultVO.GainQuizReward();
                BeanUtils.copyProperties(awardData, gainQuizReward);
                // 下发资源(限制重复提交答题和同一设备多个账号重复答题获得奖励)
                if (submitQuizCount > 0) {
                    distributionService.sendRewardResource(uid, awardData.getSourceId(), ActivityRewardTypeEnum.getEnumByName(awardData.getRewardType()), awardData.getRewardTimes(),
                            awardData.getRewardNum(), SEND_GIFT_TITLE, SEND_GIFT_DESC, 0);
                }
                rewardList.add(gainQuizReward);
                quizActivityRedis.saveGainReward(uid, activityId, JSONObject.toJSONString(awardData));
            }
        }
        if (submitQuizCount > 0) {
            quizActivityRedis.reduceSubmitQuizCount(uid, activityId);
        }
        return rewardList;
    }

    /**
     * 获取最好排名
     */
    private Integer getBestRanking(int oldRanking, int curRanking, double oldRankingScore, Integer activityId, String uid) {
        if (oldRanking == 0) {
            return curRanking;
        }
        if (curRanking > oldRanking) {
            // 如果当前排名低于之前排名，还原回之前排名
            quizActivityRedis.saveQuizRanking(uid, activityId, oldRankingScore);
        }
        return Math.min(oldRanking, curRanking);
    }

    /**
     * 更新或保存最好成绩
     */
    private void saveQuizResult(QuizResultData resultData, Integer activityId, String uid, int score, int bestScore, int newBestScore, int time, int bestTime, int newBestTime, int userLevel, int nowTime) {
        if (resultData == null) {
            // 新增最好成绩
            resultData = new QuizResultData();
            resultData.setActivityId(activityId);
            resultData.setUid(uid);
            resultData.setBestScore(newBestScore);
            resultData.setBestTime(newBestTime);
            resultData.setUserLevel(userLevel);
            resultData.setCtime(nowTime);
            quizResultDao.insert(resultData);
        } else {
            boolean needUpdate = score > bestScore || (score == bestScore && time <= bestTime);
            if (needUpdate) {
                // 更新最好成绩
                resultData.setBestScore(newBestScore);
                resultData.setBestTime(newBestTime);
                resultData.setUserLevel(userLevel);
                resultData.setCtime(nowTime);
                quizResultDao.update(resultData);
            }
        }
    }
}
