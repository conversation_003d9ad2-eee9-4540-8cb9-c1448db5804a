package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.ActivityHandler;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.GiftRedis;
import com.quhong.redis.WeeklyStarRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class WeeklyStarService implements ActivityHandler {
    private static final Logger logger = LoggerFactory.getLogger(WeeklyStarService.class);
    private static boolean isTest = true;

    @Resource
    private ActorDao actorDao;
    //    @Resource
//    private GiftRedis giftRedis;
    @Resource
    private SysConfigDao configDao;
    @Resource
    private WeeklyStarRedis weeklyStarRedis;
    @Resource
    private WeeklyStarService weeklyStarService;
    @Resource
    private GiftDao giftDao;
    @Resource
    private WhiteTestDao whiteTestDao;

    @Override
    public void process(SendGiftData giftData) {
        if (giftData.getGift_cost_type() == 2) {
            // 金币礼物不统计
            return;
        }
//        if (!RoomUtils.isVoiceRoom(giftData.getRoomId())) {
//            // 非语音房礼物不统计
//            return;
//        }

        int giftId = giftData.getGid();

        if (!weeklyStarService.getWeeklyGiftList().contains(giftId)) {
            return;
        }

        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();
        Set<String> aidList = giftData.getAid_list();
        int giftNumberPrice = giftData.getNumber() * giftData.getPrice();
        int totalGiftPrice = giftNumberPrice * aidList.size();

        // 灰度测试过滤
//        if (ServerConfig.isProduct() && isTest && !whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
//            return;
//        }

        int weekCount = weeklyStarService.getWeekCount();
        weeklyStarRedis.incrWeeklyStarScore(fromUid, giftId, weekCount, totalGiftPrice);

    }

    public WeeklyStarVO weeklyStar(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (null == actorData) {
            logger.error("cannot find actor uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        WeeklyStarVO vo = new WeeklyStarVO();
        List<GiftRedis.RedisGiftData> thisWeekGiftList = weeklyStarService.getWeekGiftList(SysConfigDao.NEW_GIFT_KEY, SysConfigDao.NEW_GIFT_LIST_KEY);
        List<GiftRedis.RedisGiftData> nextWeekGiftList = weeklyStarService.getWeekGiftList(SysConfigDao.NEXT_WEEK_LIST, SysConfigDao.NEXT_WEEK_LIST);
        List<GiftRedis.RedisGiftData> lastWeekGiftList = weeklyStarService.getWeekGiftList(SysConfigDao.LAST_WEEK_LIST, SysConfigDao.LAST_WEEK_LIST);

        vo.setLastGiftList(lastWeekGiftList);
        vo.setWeekGiftList(thisWeekGiftList);
        vo.setNextWeekGiftList(nextWeekGiftList);

        vo.setThisWeekRankList(getWeekRank(thisWeekGiftList, 10, 0));
        vo.setLastWeekRankList(getWeekRank(lastWeekGiftList, 10, -1));
        vo.setMyData(weeklyStarService.getMyWeeklyData(actorData, thisWeekGiftList));
        return vo;
    }

    //    @Cacheable(value = "getMyWeeklyData", key = "#p0.uid", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public WeeklyStarMyVO getMyWeeklyData(ActorData actorData, List<GiftRedis.RedisGiftData> thisWeekGiftList) {
        WeeklyStarMyVO vo = new WeeklyStarMyVO();
        vo.setRid(actorData.getRid());
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setTop1Count(weeklyStarRedis.getTop1Count(actorData.getUid()));
        int show = weeklyStarRedis.getTop1ShowScore(actorData.getUid());
        vo.setShowPopupTop1(show);
        if (show > 0) {
            weeklyStarRedis.delTop1Show(actorData.getUid());
        }
        int week = weeklyStarService.getWeekCount();
        for (GiftRedis.RedisGiftData redisGiftData : thisWeekGiftList) {
            WeeklyStarGiftVO giftVO = new WeeklyStarGiftVO();
            giftVO.setGiftId(redisGiftData.getGiftId());
            giftVO.setGiftIcon(redisGiftData.getGiftIcon());
            giftVO.setCount(weeklyStarRedis.getScore(actorData.getUid(), redisGiftData.getGiftId(), week));
            giftVO.setRank(weeklyStarRedis.getRank(actorData.getUid(), redisGiftData.getGiftId(), week));
            vo.getRankList().add(giftVO);
        }
        return vo;
    }

    /**
     * 填充排行榜
     *
     * @param weekOffset 偏移量 0本周，-1上周
     */
    private List<WeeklyStarRankingVO> getWeekRank(List<GiftRedis.RedisGiftData> weekGiftList, int length, int weekOffset) {
        int week = weeklyStarService.getWeekCount() + weekOffset;
        List<WeeklyStarRankingVO> result = new ArrayList<>();
        for (GiftRedis.RedisGiftData redisGiftData : weekGiftList) {
            WeeklyStarRankingVO vo = new WeeklyStarRankingVO();
            WeeklyStarGiftVO giftVO = new WeeklyStarGiftVO();
            giftVO.setGiftId(redisGiftData.getGiftId());
            giftVO.setGiftIcon(redisGiftData.getGiftIcon());
            vo.setGift(giftVO);
            // 排行榜行信息
            Map<String, Integer> rankingMap = weeklyStarService.getRankingMap(redisGiftData.getGiftId(), week, length);
            int rank = 0;
            for (String uid : rankingMap.keySet()) {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (null == actorData) {
                    continue;
                }
                WeeklyStarRankVO rankVO = new WeeklyStarRankVO();
                rankVO.setRid(actorData.getRid());
                rankVO.setAid(uid);
                rankVO.setName(actorData.getName());
                rankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                rankVO.setCount(rankingMap.get(uid));
                rankVO.setRank(++rank);
                vo.getRanking().add(rankVO);
            }
            result.add(vo);
        }
        return result;
    }


    @Cacheable(value = "getRankingMap", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public Map<String, Integer> getRankingMap(int giftId, int week, int length) {
        return weeklyStarRedis.getRankingMap(giftId, week, length);
    }


    @Cacheable(value = "weekStarCache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "'getWeeklyGiftList'")
    public List<Integer> getWeeklyGiftList() {
        return configDao.getList(SysConfigDao.NEW_GIFT_KEY, SysConfigDao.NEW_GIFT_LIST_KEY, false);
    }

    @Cacheable(value = "weekStarCache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "'getWeekCount'")
    public int getWeekCount() {
        return configDao.getIntValue(SysConfigDao.WEEK_COUNT, SysConfigDao.WEEK_COUNT, false);
    }

    @Cacheable(value = "weekStarCache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "'getWeeklyGiftList'")
    public void delWeeklyGiftList() {

    }

    @CacheEvict(value = "weekStarCache", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE,
            key = "'getWeekCount'")
    public void delWeekCount() {
    }

    //    @Cacheable(value = "getWeekGiftList", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
//            cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public List<GiftRedis.RedisGiftData> getWeekGiftList(String key, String listKey) {
        List<GiftRedis.RedisGiftData> weeklyGiftList = new ArrayList<>();
        List<Integer> giftIdList = configDao.getList(key, listKey, false);
        for (Integer giftId : giftIdList) {
            GiftData giftData = giftDao.getGiftFromCache(giftId);

//            GiftRedis.RedisGiftData giftFromRedis = giftRedis.getGiftFromRedis(giftId);
            if (null != giftData) {
                GiftRedis.RedisGiftData giftFromRedis = new GiftRedis.RedisGiftData();
                giftFromRedis.setGiftId(giftData.getRid());
                giftFromRedis.setGiftIcon(giftData.getGicon());
//                giftFromRedis.setGiftName(giftData.getGname());
                giftFromRedis.setGiftName(giftData.getGnamear());
                giftFromRedis.setGiftPrice(giftData.getPrice());
//                giftFromRedis.setGiftTime(giftData.getGtime());
                giftFromRedis.setGatype(giftData.getGatype());
                giftFromRedis.setGiftType(giftData.getGtype());
//                giftFromRedis.setgPlatform(giftData.getGplatform());
//                giftFromRedis.setZipInfo(giftData.getZipInfo());
                weeklyGiftList.add(giftFromRedis);
            }
        }
        return weeklyGiftList;
    }
}
