package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.EventCenterVO;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.EventCenterDao;
import com.quhong.mongo.data.EventCenterData;
import com.quhong.mysql.dao.ActorPayExternalDao;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Service
public class EventCenterService {

    private static final Logger logger = LoggerFactory.getLogger(EventCenterService.class);

    @Resource
    private EventCenterDao eventCenterDao;
    @Resource
    private HomeBannerService homeBannerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ActorPayExternalDao actorPayExternalDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;

    public EventCenterVO eventCenterList(String uid, int eventType) {

        List<EventCenterData> dataList = eventCenterDao.selectListByType(eventType);;
        int currentTime = DateHelper.getNowSeconds();
        List<EventCenterData> totalList = new ArrayList<>();   // 置顶列表
        List<EventCenterData> stickyList = new ArrayList<>();   // 置顶列表
        List<EventCenterData> noStartList = new ArrayList<>();   // 未开始列表
        List<EventCenterData> runList = new ArrayList<>();   // 进行中列表
        List<EventCenterData> endList = new ArrayList<>();   // 已结束列表
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        int bcGameSwitch = homeBannerService.getBCGameSwitch(actorData);
        for (EventCenterData item : dataList) {
            // 过滤条件判断
            if(!checkFilterValid(actorData, item, bcGameSwitch)){
                continue;
            }
            if (item.getCycle() == 1) {
                // 周期活动
                int cycleTime = item.getEndTime() - item.getStartTime();
                int nowTime = DateHelper.getNowSeconds();
                int cycleNum = (nowTime - item.getStartTime()) / cycleTime;
                item.setStartTime(item.getStartTime() + cycleNum * cycleTime);
                item.setEndTime(item.getEndTime() + cycleNum * cycleTime);
            }
            int startTime = item.getStartTime();
            int endTime = item.getEndTime();

            // 置顶
            if(item.getSticky() > 0){
                stickyList.add(item);
                continue;
            }

            // 未开始
            if(currentTime < startTime){
                noStartList.add(item);
                continue;
            }

            // 进行中
            if(currentTime > startTime && currentTime < endTime){
                runList.add(item);
                continue;
            }

            if (currentTime > endTime){
                endList.add(item);
            }
        }
        stickyList.sort(Comparator.comparing(EventCenterData::getMtime).reversed());
        totalList.addAll(stickyList);
        totalList.addAll(runList);
        totalList.addAll(noStartList);
        totalList.addAll(endList);
        EventCenterVO vo = new EventCenterVO();
        vo.setList(totalList);
        return vo;
    }


    private boolean checkFilterValid(ActorData actorData, EventCenterData data, int bcGameSwitch){

        try {
            switch (data.getFilterType()){
                case HomeBannerService.FILTER_TYPE_SEX:
                    if(!StringUtils.isEmpty(data.getFilterItem()) && !data.getFilterItem().equals(String.valueOf(actorData.getFb_gender()))){
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_PAY:
                    double userRecharge = actorPayExternalDao.getUserRechargeMoney(actorData.getUid()).doubleValue();
                    String[] split = data.getFilterItem().split("-");
                    double minMoney = Double.parseDouble(split[0]);
                    double maxMoney = Double.parseDouble(split[1]);
                    if(userRecharge < minMoney || userRecharge > maxMoney){
                        return false;
                    }
                    break;

                case HomeBannerService.FILTER_TYPE_REGISTER:
                    String[] splitDay = data.getFilterItem().split("-");
                    int startDay = Integer.parseInt(splitDay[0]);
                    int endDay =Integer.parseInt(splitDay[1]);
                    int regDay = ActorUtils.getRegDays(actorData.getUid());
                    if(regDay < startDay || regDay > endDay){
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_USER:
                    String[] uidArray = data.getFilterItem().split(",");
                    List<String> uidList = Arrays.asList(uidArray);
                    if(!uidList.contains(actorData.getUid())){
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_LOSS:
//                    int backDay = Integer.parseInt(data.getFilterItem().trim());
                    if (backUserStateRedis.isReg(actorData.getUid())||
                            backUserStateRedis.isBackUser(actorData, true)==0){
                        //  不是回归用户
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_BC_GAME:
                    int filterStatus = Integer.parseInt(data.getFilterItem().trim());
                    if ((filterStatus == 0 && bcGameSwitch > 0) || (filterStatus == 1 && bcGameSwitch <= 0)) {
                        return false;
                    }
                    break;
                case HomeBannerService.FILTER_TYPE_COUNTRY:
                    if (!StringUtils.isEmpty(data.getFilterItem()) && !StringUtils.isEmpty(actorData.getCountry())) {
                        String[] countryArray = data.getFilterItem().split(",");
                        List<String> countryList = Arrays.asList(countryArray);
                        String countryCode = ActorUtils.getUpperCaseCountryCode(actorData.getCountry());
                        if (!StringUtils.isEmpty(countryCode) && !countryList.contains(countryCode)) {
                            return false;
                        }
                    }
                    break;
            }
        }catch (Exception e){
            logger.error("checkBannerValid IndexBannerData:{}, error:{}, ", JSONObject.toJSONString(data), e.getMessage(), e);
        }
        return true;
    }



}
