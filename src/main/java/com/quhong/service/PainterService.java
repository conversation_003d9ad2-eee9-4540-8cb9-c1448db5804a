package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.data.vo.PainterConfigVO;
import com.quhong.data.vo.PainterHallVO;
import com.quhong.data.vo.PainterRankVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.redis.PainterRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class PainterService {
    /**
     * 斋月书法家
     */

    private static final Logger logger = LoggerFactory.getLogger(PainterService.class);
    private static final String MOMENT_PAINTER_ORIGIN = "painterPicture";
    private static final String MOMENT_AUTOR_ORIGIN = "painterWritten";
    private static final String ACTIVITY_ID = "6417c55b49490641c0f8159c";
    private static final int START_TIME = 1680469200;
    private static final int FIRST_DAY_END_TIME = 1680556260;
    private static final int END_TIME = 1684814400;
    private static final List<Integer> PICTURE_RANKING = ServerConfig.isProduct() ? Arrays.asList(2067, 2068, 2069) : Arrays.asList(1667, 1668, 1669);
    private static final int WRITTEN_BADGE_ID = ServerConfig.isProduct() ? 2070 : 1670;
    private static final int BUBBLE_ID = ServerConfig.isProduct() ? 117 : 118;
    private static final String CDN_DOMAIN = "https://cloudcdn.qmovies.tv/";
    private static final String FIRST_DAY_LOGO = "https://cdn3.qmovies.tv/youstar/op_sys_1680165709_logo.png";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? "https://static.youstar.live/Calligrapher/?activityId=6417c55b49490641c0f8159c&shareId=2" : "https://test2.qmovies.tv/Calligrapher/?activityId=6417c55b49490641c0f8159c&shareId=2";
    private static final String ACTIVITY_ICON = "https://cdn3.qmovies.tv/common/op_1679985665_head.7f672abe.jpg";
    private static final List<String> PAINTER_SHARE_EN = Arrays.asList(
            "#Ramadan Calligrapher#\n" + "Welcome to YouStar Calligraphy Art Exhibition\n" + "looking forward to seeing your creation",
            "#Ramadan Calligrapher#\n" + "\"You\" can be both a \"connoisseur\" and an \"artist\"",
            "#Ramadan Calligrapher#\n" + "Come to experience a \"calligraphy art journey\" during Ramadan"
    );
    private static final List<String> PAINTER_SHARE_AR = Arrays.asList(
            "# خطاط رمضان #\n" + "مرحبًا بكم في معرض يوستار لفن الخط\n" + "نتطلع إلى رؤية إبداعك",
            "# خطاط رمضان #\n" + "يمكن أن تكون \"أنت\" \"خبيرًا\" و \"فنانًا\"",
            "# خطاط رمضان #\n" + "تعال لتجربة \"رحلة فن الخط\" خلال شهر رمضان"
    );

    private static final String FIRST_DAY_CONTENT_EN = "Ramadan Mubarak";
    private static final String FIRST_DAY_CONTENT_AR = "رمضان مبارك";

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private IMomentService iMomentService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private IDetectService idetectService;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private PainterRedis painterRedis;

    public PainterConfigVO painterConfig(String uid, int slang) {

        ActorData actor = actorDao.getActorDataFromCache(uid);
        if(actor == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        PainterConfigVO vo = new PainterConfigVO();
        vo.setStartTime(START_TIME);
        vo.setEndTime(END_TIME);
        vo.setRid(actor.getRid());
        vo.setName(actor.getName());

        int startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int endTime = startTime + 86400;
        int lastStartTime = startTime - 86400;

        // 设置top1文案
        String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date());
        String momentIdUid = painterRedis.getMomentWritten(yesterday);
        MomentActivityData writtenTop = null;
        ActorData actorData = null;
        int currenTime = DateHelper.getNowSeconds();

        if(currenTime < FIRST_DAY_END_TIME){
            PainterConfigVO.WrittenInfo topWrittenInfo = new PainterConfigVO.WrittenInfo();
            topWrittenInfo.setHead(FIRST_DAY_LOGO);
            topWrittenInfo.setMessage(slang == SLangType.ARABIC ? FIRST_DAY_CONTENT_AR : FIRST_DAY_CONTENT_EN);
            vo.setWrittenInfo(topWrittenInfo);

        }else if (!StringUtils.isEmpty(momentIdUid)){
            String[] ids = momentIdUid.split("_");
            String momentId = ids[0];
            String rankUid = ids[1];
            PainterConfigVO.WrittenInfo topWrittenInfo = new PainterConfigVO.WrittenInfo();

            actorData = actorDao.getActorDataFromCache(rankUid);
            writtenTop = momentActivityDao.findMomentOne(momentId, MOMENT_AUTOR_ORIGIN);
            topWrittenInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            topWrittenInfo.setMessage(painterRedis.getMomentMessage(momentId));
            if(writtenTop != null){
                topWrittenInfo.setLikes(writtenTop.getLikes() != null ? writtenTop.getLikes().size() : 0);
                topWrittenInfo.setComments(writtenTop.getComments());
                topWrittenInfo.setRepost(writtenTop.getRepost());
            }
            vo.setWrittenInfo(topWrittenInfo);

        }else {
            List<MomentActivityData> topWrittenList = momentActivityDao.momentRanking(MOMENT_AUTOR_ORIGIN, lastStartTime, startTime, 1);
            if(topWrittenList != null && topWrittenList.size() > 0){
                PainterConfigVO.WrittenInfo topWrittenInfo = new PainterConfigVO.WrittenInfo();

                writtenTop =  topWrittenList.get(0);
                actorData = actorDao.getActorDataFromCache(writtenTop.getUid());
                topWrittenInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                topWrittenInfo.setMessage(painterRedis.getMomentMessage(writtenTop.get_id().toString()));
                topWrittenInfo.setLikes(writtenTop.getLikes() != null ? writtenTop.getLikes().size() : 0);
                topWrittenInfo.setComments(writtenTop.getComments());
                topWrittenInfo.setRepost(writtenTop.getRepost());
                vo.setWrittenInfo(topWrittenInfo);
            }
        }




        // 设置个人发布图展
        List<MomentActivityData> momentList = momentActivityDao.findMomentList(uid, MOMENT_PAINTER_ORIGIN);
        List<PainterConfigVO.MomentInfo> momentInfoList = new ArrayList<>();

        for (MomentActivityData data : momentList) {
            PainterConfigVO.MomentInfo momentInfo = new PainterConfigVO.MomentInfo();
            String momentId = data.get_id().toString();
            int ctime = data.getC_time();

            momentInfo.setMomentId(momentId);
            momentInfo.setTitle(data.getText());
            momentInfo.setPicture(data.getImgs().get(0).getThumbnail());
            momentInfo.setCtime(ctime);
            momentInfo.setRepost(data.getRepost());
            momentInfo.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
            momentInfo.setComments(data.getComments());
            if(ctime >= startTime && ctime <= endTime){
                vo.setPainter(1);
            }

            if(data.getLikes() != null && data.getLikes().contains(uid)){
                momentInfo.setLikeStatus(1);
            }

            momentInfoList.add(momentInfo);

        }
        vo.setMomentInfoList(momentInfoList);


        // 设置个人发布文案
        List<MomentActivityData> writtenList = momentActivityDao.findMomentList(uid, MOMENT_AUTOR_ORIGIN);
        List<PainterConfigVO.WrittenInfo> writtenInfoList = new ArrayList<>();

        for (MomentActivityData data : writtenList) {
            PainterConfigVO.WrittenInfo writtenInfo = new PainterConfigVO.WrittenInfo();
            String momentId = data.get_id().toString();
            int ctime = data.getC_time();

            writtenInfo.setMomentId(momentId);
            writtenInfo.setMessage(painterRedis.getMomentMessage(data.get_id().toString()));
            writtenInfo.setCtime(ctime);
            writtenInfo.setRepost(data.getRepost());
            writtenInfo.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
            writtenInfo.setComments(data.getComments());
            if(ctime >= startTime && ctime <= endTime){
                vo.setWritten(1);
            }
            if(data.getLikes() != null && data.getLikes().contains(uid)){
                writtenInfo.setLikeStatus(1);
            }

            writtenInfoList.add(writtenInfo);

        }
        vo.setWrittenInfoList(writtenInfoList);
        return vo;

    }

    private void commonParamCheck(){
        int currentTime = DateHelper.getNowSeconds();
        if(currentTime < START_TIME || currentTime > END_TIME){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }


    public void painterPicturePush(PainterPictureDTO dto) {
        commonParamCheck();
        String uid = dto.getUid();
        String picture = dto.getPicture();
        int slang = dto.getSlang();

        if(StringUtils.isEmpty(picture)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        int startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int endTime = startTime + 86400;
        MomentActivityData momentActivityData = momentActivityDao.findMomentOneByTime(uid, MOMENT_PAINTER_ORIGIN, startTime, endTime);
        if(momentActivityData != null){
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }

        if (idetectService.detectImage(new ImageDTO(CDN_DOMAIN + picture, "activity")).getData().getIsSafe() == 0){
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_IMAGE);
        }


        synchronized (stringPool.intern(uid)) {

            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();

            publishMomentDTO.setUid(uid);

            List<String> momentText = slang == SLangType.ARABIC ? PAINTER_SHARE_AR : PAINTER_SHARE_EN;
            Collections.shuffle(momentText);
            publishMomentDTO.setText(momentText.get(0));
            publishMomentDTO.setShow(3);
            publishMomentDTO.setActiveId(ACTIVITY_ID);
            publishMomentDTO.setLocation(MOMENT_PAINTER_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("2000");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));

            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setType(6);
            quote.setIcon(ACTIVITY_ICON);
            quote.setContent("书法活动");
            quote.setAction(ACTIVITY_URL);

            publishMomentDTO.setQuote(quote);

            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if(result.getCode() == 20){
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
        }
    }


    public void painterWrittenPush(String uid, String message, String picture, int slang) {
        commonParamCheck();
        if(StringUtils.isEmpty(message) || StringUtils.isEmpty(picture)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        int startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        int endTime = startTime + 86400;
        MomentActivityData momentActivityData = momentActivityDao.findMomentOneByTime(uid, MOMENT_AUTOR_ORIGIN, startTime, endTime);
        if(momentActivityData != null){
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }

        synchronized (stringPool.intern(uid)) {

            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();

            publishMomentDTO.setUid(uid);
            List<String> momentText = slang == SLangType.ARABIC ? PAINTER_SHARE_AR : PAINTER_SHARE_EN;
            Collections.shuffle(momentText);
            publishMomentDTO.setText(momentText.get(0));
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(ACTIVITY_ID);
            publishMomentDTO.setLocation(MOMENT_AUTOR_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("3500");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));
            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if(result.getCode() == 20){
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if(result.getCode() == 41){
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
        }

        momentActivityData = momentActivityDao.findMomentOneByTime(uid, MOMENT_AUTOR_ORIGIN, startTime, endTime);
        if(momentActivityData != null){
            painterRedis.setMomentMessage(momentActivityData.get_id().toString(), message);
        }else {
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }


    }

    private int getSelectDateStartTime(String selectDate){
        try {
            Calendar cld = Calendar.getInstance(TimeZone.getTimeZone("GMT+3:00"));
            cld.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(selectDate));
            cld.set(Calendar.HOUR_OF_DAY, 0);
            cld.set(Calendar.MINUTE, 0);
            cld.set(Calendar.SECOND, 0);
            cld.set(Calendar.MILLISECOND, 0);
            return (int) (cld.getTimeInMillis() / 1000);
        }catch (Exception e){
            logger.error("getSelectDateStartTime error. selectDate={}", selectDate);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


    }


    public PainterRankVO painterRanking(String uid, String selectDate) {

        int startTime = getSelectDateStartTime(selectDate);
        int endTime = startTime + 86400;
        List<MomentActivityData> writtenList = momentActivityDao.momentRanking(MOMENT_AUTOR_ORIGIN, startTime, endTime, 1);
        PainterRankVO vo = new PainterRankVO();

        // 设置top1文案
        String momentIdUid = painterRedis.getMomentWritten(selectDate);
        MomentActivityData writtenTop = null;
        ActorData actorData = null;
        if(!StringUtils.isEmpty(momentIdUid)){
            String[] ids = momentIdUid.split("_");
            String momentId = ids[0];
            String rankUid = ids[1];

            actorData = actorDao.getActorDataFromCache(rankUid);
            writtenTop = momentActivityDao.findMomentOne(momentId, MOMENT_AUTOR_ORIGIN);
            PainterRankVO.MomentInfo topWrittenInfo = new PainterRankVO.MomentInfo();
            topWrittenInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            topWrittenInfo.setMessage(painterRedis.getMomentMessage(momentId));
            if(writtenTop != null){
                topWrittenInfo.setLikes(writtenTop.getLikes() != null ? writtenTop.getLikes().size() : 0);
                topWrittenInfo.setComments(writtenTop.getComments());
                topWrittenInfo.setRepost(writtenTop.getRepost());
                if(writtenTop.getLikes() != null && writtenTop.getLikes().contains(uid)){
                    topWrittenInfo.setLikeStatus(1);
                }

            }
            vo.setMomentInfo(topWrittenInfo);

        }else {
            List<MomentActivityData> topWrittenList = momentActivityDao.momentRanking(MOMENT_AUTOR_ORIGIN, startTime, endTime, 1);
            if(topWrittenList != null && topWrittenList.size() > 0){
                writtenTop =  topWrittenList.get(0);
                actorData = actorDao.getActorDataFromCache(writtenTop.getUid());
                PainterRankVO.MomentInfo topWrittenInfo = new PainterRankVO.MomentInfo();
                topWrittenInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                topWrittenInfo.setMessage(painterRedis.getMomentMessage(writtenTop.get_id().toString()));
                topWrittenInfo.setLikes(writtenTop.getLikes() != null ? writtenTop.getLikes().size() : 0);
                topWrittenInfo.setComments(writtenTop.getComments());
                topWrittenInfo.setRepost(writtenTop.getRepost());
                if(writtenTop.getLikes() != null && writtenTop.getLikes().contains(uid)){
                    topWrittenInfo.setLikeStatus(1);
                }

                vo.setMomentInfo(topWrittenInfo);
            }
        }

        // 设置排行榜
        List<String> pictureList = painterRedis.getMomentRankingList(selectDate, 0);
        List<PainterRankVO.MomentInfo> momentIds = new ArrayList<>();
        if(pictureList != null && pictureList.size() > 0){
            for (String rankUid : pictureList) {
                PainterRankVO.MomentInfo momentInfo = new PainterRankVO.MomentInfo();
                actorData = actorDao.getActorDataFromCache(rankUid);
                momentInfo.setName(actorData.getName());
                momentInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                momentIds.add(momentInfo);
            }

        }else {
            // 设置排行榜
            List<MomentActivityData> pictureMomentList = momentActivityDao.momentRanking(MOMENT_PAINTER_ORIGIN, startTime, endTime, 10);
            if(pictureList != null){
                for (MomentActivityData data : pictureMomentList) {
                    PainterRankVO.MomentInfo momentInfo = new PainterRankVO.MomentInfo();
                    actorData = actorDao.getActorDataFromCache(data.getUid());
                    momentInfo.setName(actorData.getName());
                    momentInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                    momentInfo.setRepost(data.getRepost());
                    momentInfo.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
                    momentInfo.setComments(data.getComments());
                    momentIds.add(momentInfo);
                }
            }
        }
        vo.setMomentList(momentIds);
        return vo;

    }

    public PainterHallVO painterHall(String uid, String selectDate, int page) {

        int size = 10;
        int start = (page - 1) * size;
        int startTime = getSelectDateStartTime(selectDate);
        int endTime = startTime + 86400;

        PainterHallVO vo = new PainterHallVO();
        List<MomentActivityData> pictureList = momentActivityDao.findMsgPageListByTime("", MOMENT_PAINTER_ORIGIN, start, size, startTime, endTime);
        List<PainterHallVO.MomentInfo> momentIds = new ArrayList<>();

        if(pictureList != null){
            for (MomentActivityData data : pictureList) {
                PainterHallVO.MomentInfo momentInfo = new PainterHallVO.MomentInfo();

                ActorData actorData = actorDao.getActorDataFromCache(data.getUid());

                momentInfo.setName(actorData.getName());
                momentInfo.setMomentId(data.get_id().toString());
                momentInfo.setTitle(data.getText());
                momentInfo.setPicture(data.getImgs().get(0).getThumbnail());
                momentInfo.setRepost(data.getRepost());
                momentInfo.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
                momentInfo.setComments(data.getComments());
                momentInfo.setLikeStatus(0);
                if(data.getLikes() != null && data.getLikes().contains(uid)){
                    momentInfo.setLikeStatus(1);
                }

                momentIds.add(momentInfo);
            }
            vo.setMomentList(momentIds);
            vo.setNextUrl(momentIds.size() < size ? -1 : page + 1);
        }
        return vo;
    }


    public void painterLike(String uid, String momentId) {
        commonParamCheck();
        if(StringUtils.isEmpty(momentId)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(uid)) {
            ApiResult<HttpCode> result = iMomentService.likeMoment(uid, momentId);
            if(result.getCode().getCode() == 20){
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            logger.info("motherMomentPush error. uid={} code={}", uid, result.getCode().getCode());
        }
    }


    public void painterRankingTopReward(){

        try{
            int currentTime = DateHelper.getNowSeconds();
            if(currentTime < START_TIME || currentTime > 1680948000){
                logger.info("painterRankingTopReward not in time. currentTime: {}", currentTime);
                return;
            }

            int endTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
            int startTime = endTime - 86400;

            List<MomentActivityData> pictureList = momentActivityDao.momentRanking(MOMENT_PAINTER_ORIGIN, startTime, endTime, 10);
            int baseNum = 100;
            String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date());

            if(pictureList != null){
                for (int i = 0; i < pictureList.size(); i++) {
                    String rankUid  = pictureList.get(i).getUid();

                    if(i < 3){
                        int badgeId = PICTURE_RANKING.get(i);
                        distributionService.sendRewardResource(rankUid, badgeId,
                                ActivityRewardTypeEnum.getEnumByName("badge"), 30, 0, MOMENT_PAINTER_ORIGIN, MOMENT_PAINTER_ORIGIN, 0);

                        distributionService.sendRewardResource(rankUid, BUBBLE_ID,
                                ActivityRewardTypeEnum.getEnumByName("buddle"), 15, 0, MOMENT_PAINTER_ORIGIN, MOMENT_PAINTER_ORIGIN, 0);
                    }
                    int rankNum = baseNum - i;
                    painterRedis.incrMomentRankingScore(rankUid, rankNum, yesterday);
                }
            }

            List<MomentActivityData> writtenList = momentActivityDao.momentRanking(MOMENT_AUTOR_ORIGIN, startTime, endTime, 1);
            if(writtenList != null && writtenList.size() > 0){
                MomentActivityData topWritten  = writtenList.get(0);
                String rankUid  = topWritten.getUid();
                distributionService.sendRewardResource(rankUid, WRITTEN_BADGE_ID,
                        ActivityRewardTypeEnum.getEnumByName("badge"), 30, 0, MOMENT_PAINTER_ORIGIN, MOMENT_PAINTER_ORIGIN, 0);

                distributionService.sendRewardResource(rankUid, BUBBLE_ID,
                        ActivityRewardTypeEnum.getEnumByName("buddle"), 15, 0, MOMENT_PAINTER_ORIGIN, MOMENT_PAINTER_ORIGIN, 0);

                String momentIdUid = topWritten.get_id().toString() + "_" + rankUid;
                painterRedis.setMomentWritten(yesterday, momentIdUid);

            }

        } catch (Exception e) {
            logger.error("painterRankingTopReward error. {}", e.getMessage(), e);
        }
    }

}
