package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.NationalDayHelpEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.NationalDayV2DrawVO;
import com.quhong.data.vo.NationalDayV2VO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;

import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NationalDayV2Dao;
import com.quhong.mongo.data.NationalDayV2Data;
import com.quhong.mq.MqSenderService;
import com.quhong.mq.ResourceDeliveryService;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.NationalDayV2RecordDao;
import com.quhong.mysql.data.NationalDayV2RecordData;
import com.quhong.redis.NationalDayV2Redis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class NationalDayV2Service{

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV2Service.class);
    private static final int LIMIT_INIT_POOL = 10;
    private static final int ZERO_INIT_POOL = 0;
    private static final String RECORD_TITLE = "National Day";

    @Resource
    private NationalDayV2Dao nationalDayV2Dao;
    @Resource
    private NationalDayV2Service nationalDayV2Service;
    @Resource
    private NationalDayV2Redis nationalDayV2Redis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private NationalDayV2RecordDao nationalDayV2RecordDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private ResourceDeliveryService resourceDeliveryService;
    @Resource
    private ResourceDistributionService distributionService;
    @Autowired(required = false)
    private EventReport eventReport;

    /**
     * 获取正在运行的冲榜活动
     */
    public List<NationalDayV2Data> getNationalDayV2Activities() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<NationalDayV2Data> activityList = nationalDayV2Service.getNationalDayV2ActivityFromCache();
        return activityList.stream().filter(a -> a.getEndTime() > nowSeconds && a.getStartTime() <= nowSeconds).collect(Collectors.toList());
    }

    @Cacheable(value = "getNationalDayV2ActivityFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<NationalDayV2Data> getNationalDayV2ActivityFromCache() {
        List<NationalDayV2Data> activityList = nationalDayV2Dao.getRankingActivities();
        logger.info("getNationalDayV2ActivityFromCache size={}", activityList.size());
        return activityList;
    }


    @Cacheable(value = "nationalDay", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public NationalDayV2Data getNationalDayActivity(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        NationalDayV2Data data = nationalDayV2Dao.findData(activityId);
        if (null == data) {
            logger.error("cannot find ranking activity activityId={}", activityId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }


    public NationalDayV2VO nationalDayV2Config(String uid, String activityId) {
        NationalDayV2VO vo = new NationalDayV2VO();
        NationalDayV2Data activity = nationalDayV2Service.getNationalDayActivity(activityId);
        BeanUtils.copyProperties(activity, vo);
        vo.setTotalJoin(nationalDayV2Redis.getJoinUserNums(activityId));
        vo.setJoined(nationalDayV2Redis.isJoinUser(activityId, uid));
        ActorData actor = actorDao.getActorDataFromCache(uid);
        vo.setRid(actor.getRid());

        // 发送数量
        vo.setSendNum(nationalDayV2Redis.getNationalDayV2RankingScore(activityId, uid));

        // 设置助力用户
        vo.setFlagProcess(nationalDayV2Redis.getHelpUserNums(activityId, uid));
        List<NationalDayV2VO.HelpUser> helpUserList = new ArrayList<>();
        Set<String> helpMembers = nationalDayV2Redis.getHelpUser(activityId, uid);
        for (String aid : helpMembers) {
            ActorData user = actorDao.getActorDataFromCache(aid);
            NationalDayV2VO.HelpUser item = new NationalDayV2VO.HelpUser();
            item.setName(user.getName());
            item.setHead(ImageUrlGenerator.generateRoomUserUrl(user.getHead()));
            item.setUid(aid);
            helpUserList.add(item);
        }
        vo.setHelpUserList(helpUserList);

        // 是否点赞
        vo.setLike(nationalDayV2Redis.isLikeUser(activityId, uid));
        vo.setLikeNum(nationalDayV2Redis.getLikeUserNums(activityId));

        // 当日是否抽奖
        NationalDayV2RecordData myRecord = nationalDayV2RecordDao.selectRecordOne(uid, activityId);
        if(myRecord != null){
            vo.setDrew(true);
            vo.setAwardIcon(myRecord.getRewardIcon());
            vo.setRewardType(myRecord.getRewardType());
            vo.setRewardNameEn(myRecord.getRewardNameEn());
            vo.setRewardNameAr(myRecord.getRewardNameAr());
            vo.setRewardTime(myRecord.getRewardTime());
            vo.setRewardNum(myRecord.getRewardNum());
        }

        // 滚屏记录
        List<NationalDayV2RecordData> recordDataList = nationalDayV2RecordDao.scrollList(activityId);
        List<NationalDayV2VO.RecordData> recordList = new ArrayList<>();

        for (NationalDayV2RecordData record : recordDataList) {
            ActorData user = actorDao.getActorDataFromCache(record.getUid());
            NationalDayV2VO.RecordData item = new NationalDayV2VO.RecordData();
            item.setName(user.getName());
            item.setRewardNameEn(record.getRewardNameEn());
            item.setRewardNameAr(record.getRewardNameAr());
            recordList.add(item);
        }
        vo.setRecordList(recordList);
        return vo;
    }

    private NationalDayV2Data commonParamCheck(String activityId){
        NationalDayV2Data activity = nationalDayV2Service.getNationalDayActivity(activityId);  // 参数检验
        int startTime = activity.getStartTime();
        int endTime = activity.getEndTime();
        int currentTime = DateHelper.getNowSeconds();

        if(currentTime < startTime || currentTime > endTime){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        return activity;

    }


    public void nationalDayV2Join(String uid, String activityId) {
        // commonParamCheck(activityId);
        if(!nationalDayV2Redis.isJoinUser(activityId, uid)){
            nationalDayV2Redis.setJoinUserNums(activityId, uid);
        }else {
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_GIVE);
        }

    }

    public void nationalDayV2Like(String uid, String activityId) {
        commonParamCheck(activityId);
        if(!nationalDayV2Redis.isLikeUser(activityId, uid)){
            nationalDayV2Redis.setLikeUserNums(activityId, uid);
        }else {
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_GIVE);
        }
    }

    /**
     * 助力
     */

    public void nationalDayV2Help(String uid, String activityId, String rid) {
        NationalDayV2Data activity = commonParamCheck(activityId);
        if(rid.startsWith("0")){
            logger.info("rid startsWith return  uid:{}, rid: {}", uid, rid);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        int helpRid;
        try{
            helpRid = Integer.parseInt(rid);
        }catch (Exception e){
            logger.error("nationalDayV2Help PARAM_ERROR return uid:{}, rid: {}, error:{}", uid, rid, e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        ActorData actor = actorDao.getActorByRid(helpRid);  // 被帮助者
        if(actor == null){
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }
        String aid = actor.getUid();

        // 自己不能给自己助力
        if(uid.equals(aid)){
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_SELF);
        }


        ActorData helpActor = actorDao.getActorDataFromCache(uid); // 帮助者
        String helpTnId = helpActor.getTn_id();
        if(StringUtils.isEmpty(helpTnId)){
            logger.info("nationalDayV2Help not helpTnId return uid:{}, rid: {}, helpTnId:{}", uid, rid, helpTnId);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        boolean helpDailyUser =  nationalDayV2Redis.isHelpTotalUser(activityId, uid);
        if(helpDailyUser){
            logger.info("nationalDayV2Help helpDailyUser return uid:{}, rid: {}, helpTnId:{}", uid, rid, helpTnId);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_DAILY_LIMIT);
        }

        boolean tnIdStatus =  nationalDayV2Redis.isHelpTnUser(activityId, helpTnId);
        if(tnIdStatus){
            logger.info("nationalDayV2Help isHelpTnUser return uid:{}, rid: {}, helpTnId:{}", uid, rid, helpTnId);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_SAME_DEVICE);
        }

        int helpUserNum = nationalDayV2Redis.getHelpUserNums(activityId, aid);
        if(helpUserNum >= 5){
            logger.info("nationalDayV2Help helpUserNum last return uid:{}, rid: {}, helpUserNum:{}", uid, rid, helpUserNum);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_SUCCESS);
        }

        if(nationalDayV2Redis.isHelpUser(activityId, aid, uid)){
            logger.info("nationalDayV2Help isHelpUser return uid:{}, rid: {}, aid:{}", uid, rid, aid);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_ALREADY);
        }

        nationalDayV2Redis.setHelpTotalUser(activityId, uid);
        nationalDayV2Redis.setHelpTnUser(activityId, helpTnId);
        nationalDayV2Redis.setHelpUser(activityId, aid, uid);

        NationalDayHelpEvent helpEvent = new NationalDayHelpEvent();
        helpEvent.setUid(aid);
        helpEvent.setActivity_name(activity.getAcNameEn());
        helpEvent.setGet_number(nationalDayV2Redis.getHelpUserNums(activityId, aid));
        helpEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(helpEvent));


    }

    /**
     * 清除每日助力次数
     */

    public void clearNationalDayV2(String rid, String activityId) {

        int clearRid;
        try{
            clearRid = Integer.parseInt(rid);
        }catch (Exception e){
            logger.error("nationalDayV2Help PARAM_ERROR return uid:{}, rid: {}, error:{}", rid, e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        ActorData actor = actorDao.getActorByRid(clearRid);  // 被帮助者
        if(actor == null){
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        String uid = actor.getUid();
        nationalDayV2Redis.removeHelpTotalUser(activityId, uid);
        String helpTnId = actor.getTn_id();
        nationalDayV2Redis.removeHelpTnUser(activityId, helpTnId);

    }



    // 抽奖相关逻辑

    // 初始化奖池
    private void needDrawPool(String activityId, int poolConfigSize,
                             Map<String, NationalDayV2Data.DrawRewardConfig> rewardConfigMap){
        List<String> poolList = new ArrayList<>();
        for(String rewardKey: rewardConfigMap.keySet()){
            double rewardPercent = rewardConfigMap.get(rewardKey).getRewardPercent() * 0.01;
            int rewardSize = (int) (poolConfigSize * rewardPercent);
            for (int i=0; i < rewardSize; i++){
                poolList.add(rewardKey);
            }
        }
        Collections.shuffle(poolList);
        nationalDayV2Redis.initPoolSize(activityId, poolList);
    }

    private void initDrawPool(String activityId, int poolConfigSize,
                             Map<String, NationalDayV2Data.DrawRewardConfig> rewardConfigMap){
        int poolSize = nationalDayV2Redis.getPoolSize(activityId);
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    needDrawPool(activityId, poolConfigSize, rewardConfigMap);
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            needDrawPool(activityId, poolConfigSize, rewardConfigMap);
        }
    }


    /**
     * 异步打钻
     */
    private void nationalDayDrawChargeDiamonds(String uid, int changed, String title, String detailDesc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(905);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        // moneyDetailReq.setTitle("LuckyDraw Award");
        // moneyDetailReq.setDesc(String.format("%d LuckyDraw Award", changed));
        moneyDetailReq.setDesc(detailDesc);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }


    public void rewardDistribute(String activityId, String uid,
                                 NationalDayV2Data.DrawRewardConfig rewardConfig, String acNameEn){
        String rewardType = rewardConfig.getRewardType();
        int rewardNum = rewardConfig.getRewardNum() != null?rewardConfig.getRewardNum():0;
        int rewardTime = rewardConfig.getRewardTime() != null?rewardConfig.getRewardTime():0;
        switch (rewardType){
            case ResourceConstant.THANKS:
                break;
            case ResourceConstant.HEART:
                heartRecordDao.changeHeart(uid, rewardNum, RECORD_TITLE, RECORD_TITLE + " reward");
                break;
            case ResourceConstant.DIAMOND:
                nationalDayDrawChargeDiamonds(uid, rewardNum, RECORD_TITLE, RECORD_TITLE + " reward");
                break;
            default:
                distributionService.sendRewardResource(uid, rewardConfig.getSourceId(),
                        ActivityRewardTypeEnum.getEnumByName(rewardConfig.getRewardType()), rewardTime, rewardNum, RECORD_TITLE, RECORD_TITLE, 0);
        }

        int curTime = DateHelper.getNowSeconds();
        NationalDayV2RecordData recordData = new NationalDayV2RecordData();
        recordData.setActivityId(activityId);
        recordData.setUid(uid);
        recordData.setRewardType(rewardConfig.getRewardType()!=null?rewardConfig.getRewardType():"");
        recordData.setRewardIcon(rewardConfig.getRewardIcon()!=null?rewardConfig.getRewardIcon():"");
        recordData.setRewardNameEn(rewardConfig.getRewardNameEn()!=null?rewardConfig.getRewardNameEn():"");
        recordData.setRewardNameAr(rewardConfig.getRewardNameAr()!=null?rewardConfig.getRewardNameAr():"");

        recordData.setRewardNum(rewardConfig.getRewardNum()!=null?rewardConfig.getRewardNum():0);
        recordData.setRewardTime(rewardConfig.getRewardTime()!=null?rewardConfig.getRewardTime():0);
        recordData.setSourceId(rewardConfig.getSourceId()!=null?rewardConfig.getSourceId():0);
        recordData.setCtime(curTime);
        nationalDayV2RecordDao.insertOne(recordData);


    }


    public NationalDayV2DrawVO nationalDayV2Draw(String uid, String activityId) {

        NationalDayV2Data activity = commonParamCheck(activityId);
        // 次数校验
        int helpUserNum = nationalDayV2Redis.getHelpUserNums(activityId, uid);
        if(helpUserNum < 5){
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_DRAW);
        }

        NationalDayV2RecordData myRecord = nationalDayV2RecordDao.selectRecordOne(uid, activityId);

        if(myRecord != null){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_GIVE);
        }

        // 抽奖
        Map<String, NationalDayV2Data.DrawRewardConfig> rewardConfigMap =
                activity.getDrawRewardConfigList().stream().collect(Collectors.toMap(k -> k.getRewardType() + k.getRewardIndex(), Function.identity()));
        int poolConfigSize = activity.getPoolSize();
        String acNameEn = activity.getAcNameEn();
        initDrawPool(activityId, poolConfigSize, rewardConfigMap);

        String drawLuckyKey = nationalDayV2Redis.drawLuckyKey(activityId);
        NationalDayV2Data.DrawRewardConfig rewardConfig = rewardConfigMap.get(drawLuckyKey);
        logger.info("drawLuckyKey activityId={}, uid={}, key={}, rewardConfig={}", activityId, uid, drawLuckyKey, rewardConfig);

        rewardDistribute(activityId, uid, rewardConfig, acNameEn);

        NationalDayV2DrawVO vo = new NationalDayV2DrawVO();
        BeanUtils.copyProperties(rewardConfig, vo);
        vo.setAwardIcon(rewardConfig.getRewardIcon());
        vo.setLike(nationalDayV2Redis.getLikeUserNums(activityId));
        return vo;
    }

}
