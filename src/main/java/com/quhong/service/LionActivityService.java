package com.quhong.service;


import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.FightConfigVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.FightRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class LionActivityService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(LionActivityService.class);
    private static final Integer ATTACK_GIFT = ServerConfig.isProduct() ? 591 : 732;
    private static final Integer HELP_GIFT = ServerConfig.isProduct() ? 592 : 733;
    private static final Integer PROTECT_TIME = ServerConfig.isProduct() ? 7200 : 600;
    private static final Integer DEFENSE_NUM = 594;
    private static final String ACTIVITY_ID = "646586a55bc27b2bdba43636";
    private static final List<Integer> ATTACK_AWARD_LIST = ServerConfig.isProduct() ? Arrays.asList(2107, 2108, 2109) : Arrays.asList(1702, 1703, 1704);
    private static final List<Integer> ATTACK_AWARD_DAY = Arrays.asList(15, 10, 5);
    private static final Integer ATTACK_AWARD_BUBBLE = 126;
    private static final String ACTIVITY_NAME = "Lion King";

    @Resource
    private FightRedis fightRedis;
    @Resource
    private ResourceDistributionService distributionService;

    public FightConfigVO lionConfig(String uid, String activityId, int slang) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if(activityData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        FightConfigVO vo = new FightConfigVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        ActorData actorData =actorDao.getActorDataFromCache(uid);
        vo.setUserRid(actorData.getRid());

        // 设置攻击排行榜
        FightConfigVO.AttackVO myAttackVO = new FightConfigVO.AttackVO();

        List<FightConfigVO.AttackVO> attackVOList = new ArrayList<>();
        Map<String, Integer> rankingMap = fightRedis.getTotalAttackRankingMap(10);
        ActorData rankActor = null;
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            FightConfigVO.AttackVO attackVO = new FightConfigVO.AttackVO();
            String aid = entry.getKey();
            rankActor = actorDao.getActorDataFromCache(aid);

            attackVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            attackVO.setName(rankActor.getName());
            attackVO.setAttack(entry.getValue());
            attackVO.setReceiveAttack(fightRedis.getAttackReceiveScore(aid));
            attackVO.setSendAttack(fightRedis.getAttackSendScore(aid));
            attackVO.setReceiveProtect(fightRedis.getHelpReceiveScore(aid));
            attackVO.setProtect(fightRedis.getHelpDefenseScore(aid));
            attackVO.setProtectTime(fightRedis.getProtectTimeScore(aid));


            attackVO.setRank(rank);
            if(uid.equals(entry.getKey())){
                BeanUtils.copyProperties(attackVO, myAttackVO);
            }
            attackVOList.add(attackVO);
            rank += 1;
        }

        vo.setAttackRankList(attackVOList);

        if(myAttackVO.getRank() == 0){
            myAttackVO.setName(actorData.getName());
            myAttackVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myAttackVO.setAttack(fightRedis.getTotalAttackRankingScore(uid));
            myAttackVO.setReceiveAttack(fightRedis.getAttackReceiveScore(uid));
            myAttackVO.setSendAttack(fightRedis.getAttackSendScore(uid));
            myAttackVO.setReceiveProtect(fightRedis.getHelpReceiveScore(uid));
            myAttackVO.setProtect(fightRedis.getHelpDefenseScore(uid));
            myAttackVO.setProtectTime(fightRedis.getProtectTimeScore(uid));
            myAttackVO.setRank(-1);
        }

        vo.setMyAttackRank(myAttackVO);


        // 设置help排行榜

        int roundNum = activityData.getRoundNum();
        FightConfigVO.HelpRankVO myHelpVO = new FightConfigVO.HelpRankVO();

        List<FightConfigVO.HelpRankVO> helpVOList = new ArrayList<>();
        Map<String, Integer> helpRankingMap = activityOtherRedis.getOtherRankingMap(activityId, ActivityConstant.SEND_RANK, 10, roundNum);
        int helpRank = 1;
        for (Map.Entry<String, Integer> entry : helpRankingMap.entrySet()) {
            FightConfigVO.HelpRankVO helpRankVO = new FightConfigVO.HelpRankVO();
            String aid = entry.getKey();
            rankActor = actorDao.getActorDataFromCache(aid);
            helpRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            helpRankVO.setName(rankActor.getName());
            helpRankVO.setScore(entry.getValue());
            helpRankVO.setRank(helpRank);
            if(uid.equals(entry.getKey())){
                BeanUtils.copyProperties(helpRankVO, myHelpVO);
            }
            helpVOList.add(helpRankVO);
            helpRank += 1;
        }

        vo.setHelpRankList(helpVOList);

        if(myHelpVO.getRank() == 0){
            myHelpVO.setName(actorData.getName());
            myHelpVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myHelpVO.setScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RANK, roundNum));
            myHelpVO.setRank(-1);
        }

        vo.setMyHelpRank(myHelpVO);

        return vo;
    }

    public void lionProtectTimeHandle(){
        int curTime = DateHelper.getNowSeconds();
        Map<String, Integer> rankingMap = fightRedis.getProtectTimeRankingMapByScore(0, curTime);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String rankUid = entry.getKey();
            fightRedis.removeProtectTime(rankUid);
            fightRedis.removeHelpDefense(rankUid);
        }

    }

    public void distributionLionRankingAward(){
        try{
            Map<String, Integer> rankingMap = fightRedis.getTotalAttackRankingMap(3);
            ActorData rankActor = null;
            int rank = 0;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String rankUid = entry.getKey();

                Integer badgeId = ATTACK_AWARD_LIST.get(rank);
                Integer day = ATTACK_AWARD_DAY.get(rank);
                distributionService.sendRewardResource(rankUid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                distributionService.sendRewardResource(rankUid, ATTACK_AWARD_BUBBLE, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distributionLionRankingAward error: {}", e.getMessage(), e);
        }
    }





    public void lionGiftHandle(SendGiftData giftData){
        int giftId = giftData.getGid();
        String fromUid = giftData.getFrom_uid();
        int totalPrice = giftData.getPrice() * giftData.getNumber();
        int allTotalPrice =  totalPrice * giftData.getAid_list().size();

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        if(activityData == null){
            return;
        }

        int curTime = DateHelper.getNowSeconds();
        int activityStartTime = activityData.getStartTime();
        int activityEndTime = activityData.getEndTime();
        if (curTime < activityStartTime || curTime > activityEndTime){
            return;
        }


        if(giftId == ATTACK_GIFT){

            // 1、发送攻击礼物, 增加攻击值
            // 2、接收者接收攻击礼物, 没在保护期, 减少攻击值
            fightRedis.incrAttackSendScore(fromUid, allTotalPrice);
            for (String aid: giftData.getAid_list()) {
                int protectTime = fightRedis.getProtectTimeScore(aid);
                if(protectTime <= 0){
                    int currentAttack = fightRedis.getTotalAttackRankingScore(aid);
                    if(currentAttack > 0){
                        int reduceNum = currentAttack - totalPrice > 0 ? totalPrice : currentAttack;
                        fightRedis.incrAttackReceiveScore(aid, reduceNum);
                    }
                }
            }
        } else if (giftId == HELP_GIFT) {

            // 1、 发送帮助礼物, 通用排行榜统计
            // 2、 接收者接收帮助礼物, 增加攻击值, 生成防御网
            for (String aid: giftData.getAid_list()) {
                fightRedis.incrHelpReceiveScore(aid, totalPrice);

                int protectTime = fightRedis.getProtectTimeScore(aid);
                if(protectTime <= 0){
                    int currentDefense =  fightRedis.getHelpDefenseScore(aid);
                    if(currentDefense < DEFENSE_NUM){
                        int incNum = currentDefense + totalPrice > DEFENSE_NUM ? DEFENSE_NUM - currentDefense : totalPrice;
                        int newCurrentDefense = fightRedis.incrHelpDefenseScore(aid, incNum);

                        if(newCurrentDefense >= DEFENSE_NUM){
                            int endTime = DateHelper.getNowSeconds() + PROTECT_TIME;
                            fightRedis.setProtectTimeScore(aid, endTime);
                        }

                    }
                }
            }
        }

    }





}
