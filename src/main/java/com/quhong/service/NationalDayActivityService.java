package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.data.vo.NationalDayVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.data.NationalDayActivities;
import com.quhong.redis.NationalDayRedis;
import com.quhong.service.mysql.NationalDayService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class NationalDayActivityService {

    @Resource
    private NationalDayRedis nationalDayRedis;
    @Resource
    private NationalDayService nationalDayService;
    @Resource
    private NationalDayActivityService nationalDayActivityService;

    public NationalDayVO nationalDay(String uid, int activityId) {
        NationalDayVO vo = new NationalDayVO();
        NationalDayActivities nationalDay = nationalDayActivityService.getNationalDayData(activityId);
        if (null == nationalDay) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        BeanUtils.copyProperties(nationalDay, vo);
        vo.setGiftList(nationalDay.getGiftListObjList());
        vo.setSpecialEffectInfo(nationalDay.getSpecialEffectInfoObjList());
        vo.setRewardAttributes(nationalDay.getRewardAttributesObjList());
        // 处理个人数据
        int rewardEffectNum = nationalDay.getSpecialEffect() == 1 ? 666 : 999;
        vo.setSpecialEffect(nationalDayRedis.getScore(nationalDay.getId(), uid, rewardEffectNum));
        for (NationalDayActivities.SpecialEffectInfoObj effectInfoObj : vo.getSpecialEffectInfo()) {
            int effectNum = effectInfoObj.getDisplay_effect() == 1 ? 666 : 999;
            effectInfoObj.setTriggerNumber(nationalDayRedis.getScore(nationalDay.getId(), uid, effectNum));
        }
        // 转换时间戳
        if (ServerConfig.isProduct()) {
            vo.setStartTime(DateSupport.ARABIAN.getTimeSeconds(DateSupport.ARABIAN.parse_yyyyMMdd(nationalDay.getStartTime() + "")));
            vo.setEndTime(DateSupport.ARABIAN.getTimeSeconds(DateSupport.ARABIAN.parse_yyyyMMdd(nationalDay.getEndTime() + "")));
        } else {
            vo.setStartTime(DayTimeSupport.ARABIAN.getTimeSeconds(DayTimeSupport.ARABIAN.parse_yyyyMMddHH(nationalDay.getStartTime() + "")));
            vo.setEndTime(DayTimeSupport.ARABIAN.getTimeSeconds(DayTimeSupport.ARABIAN.parse_yyyyMMddHH(nationalDay.getEndTime() + "")));
        }
        return vo;
    }

    @Cacheable(value = "getNationalDayData", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public NationalDayActivities getNationalDayData(int id) {
        NationalDayActivities nationalDay = nationalDayService.getNationalDayById(id);
        nationalDay.stringDataToObj();
        return nationalDay;
    }
}
