package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.PlantTreeVO;
import com.quhong.data.vo.PlantTreeWaterVO;
import com.quhong.data.vo.PopularListVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.config.DBMysqlBean;
import com.quhong.mysql.dao.PlantTreeDao;
import com.quhong.mysql.dao.PlantTreeSignDao;
import com.quhong.mysql.data.PlantTreeData;
import com.quhong.mysql.data.PlantTreeSignData;
import com.quhong.redis.PlantTreeRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 2023植树活动
 */
@Component
public class PlantTreeService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(PlantTreeService.class);

    @Resource
    private IMomentService iMomentService;
    @Resource
    private PlantTreeDao plantTreeDao;
    @Resource
    private PlantTreeSignDao plantTreeSignDao;
    @Resource
    private PlantTreeRedis plantTreeRedis;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private ResourceDistributionService distributionService;

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final String SHARE_EN = "I have started the journey of planting date palm, join with me~";
    private static final String SHARE_AR = "لقد بدأت رحلة زراعة النخيل ، انضم إليّ ~";
    private static final String ACTIVITY_ORIGIN = "activity_plant_tree";
    private static final String ACTIVITY_NAME = "Plant Tree";
    private static final List<String> SIGN_TABLE_LIST = Arrays.asList("2023-05-01", "2023-05-02", "2023-05-03", "2023-05-04",
            "2023-05-05", "2023-05-06", "2023-05-07", "2023-05-08", "2023-05-09", "2023-05-10");
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? "https://static.youstar.live/plant_date_palm/?activityId=6446682cb9607fdbcb11256f" : "https://test2.qmovies.tv/plant_date_palm/?activityId=6446682cb9607fdbcb11256f";
    private static final String ACTIVITY_ID = "6446682cb9607fdbcb11256f";
    private static final Integer INC_WATER_COMMON = 10;
    private static final Integer STOP_WATER_POINT = 4;
    private static final Integer INC_GIFT_ID = ServerConfig.isProduct() ? 590 : 731;
    private static final Integer SIGN_BADGE_ID = ServerConfig.isProduct() ? 2097 : 1698;

    // 推送
    public static final String ACTION_EN = "View";
    public static final String ACTION_AR = "شاهد";
    public static final String SIGN_TITLE_EN = "Sign-in reminder";
    public static final String SIGN_TITLE_AR = "تذكير تسجيل الدخول";
    public static final String SIGN_BODY_EN = "Sign in for 10 consecutive days to get the badge";
    public static final String SIGN_BODY_AR = "سجّل الدخول لمدة 10 أيام متتالية للحصول على الوسام";
    public static final String SIGN_BANNER_EN = "https://cdn3.qmovies.tv/youstar/op_sys_1682507508_banner_en.png";
    public static final String SIGN_BANNER_AR = "https://cdn3.qmovies.tv/youstar/op_sys_1682507508_banner_ar.png";


    private static final Map<Integer, Integer> TREE_WATER_MAP = new HashMap<>();
    private static final Map<Integer, String> TREE_USER_PAGE_MAP = new HashMap<>();
    static {
        TREE_WATER_MAP.put(0, 30);
        TREE_WATER_MAP.put(1, 60);
        TREE_WATER_MAP.put(2, 120);
        TREE_WATER_MAP.put(3, 360);
        TREE_WATER_MAP.put(4, 0);
        TREE_USER_PAGE_MAP.put(0, "https://cdn3.qmovies.tv/youstar/op_sys_1682566259_0.png");
        TREE_USER_PAGE_MAP.put(1, "https://cdn3.qmovies.tv/youstar/op_sys_1682508226_1.png");
        TREE_USER_PAGE_MAP.put(2, "https://cdn3.qmovies.tv/youstar/op_sys_1682508226_2.png");
        TREE_USER_PAGE_MAP.put(3, "https://cdn3.qmovies.tv/youstar/op_sys_1682508226_3.png");
        TREE_USER_PAGE_MAP.put(4, "https://cdn3.qmovies.tv/youstar/op_sys_1682508226_4.png");
    }


    private void isActivityTime(String activityId){
        int curTime = DateHelper.getNowSeconds();

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if(activityData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        if (curTime < startTime || curTime > endTime){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }


    public PlantTreeVO plantTreeConfig(String uid, String activityId, int slang) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if(activityData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        PlantTreeVO vo = new PlantTreeVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        // 设置个人树状态
        PlantTreeData plantTreeData = plantTreeDao.selectOne(uid);
        if(plantTreeData == null){
            return vo;
        }

        ActorData actorData =actorDao.getActorDataFromCache(uid);
        vo.setUserRid(actorData.getRid());
        vo.setOpenTree(true);
        vo.setWaterNum(plantTreeData.getWaterNum());
        vo.setTreeStatus(plantTreeData.getTreeStatus());
        vo.setWaterProcess(plantTreeData.getWaterProcess());

        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        // 设置是否分享朋友圈
        vo.setShareMoment(plantTreeRedis.getShareMoment(uid, currentDate) > 0);


        // 设置签到状态
        List<PlantTreeSignData> treeSignDataList = plantTreeSignDao.selectList(uid);
        Map<String, PlantTreeSignData> signDataMap = treeSignDataList.stream().collect(Collectors.toMap(PlantTreeSignData::getSignDate, Function.identity()));
        List<PlantTreeVO.SignTable> signTableList = new ArrayList<>();

        for (String signDate : SIGN_TABLE_LIST) {
            PlantTreeVO.SignTable signTable = new PlantTreeVO.SignTable();
            signTable.setSignDate(signDate);
            signTable.setSignStatus(signDataMap.get(signDate) != null);
            signTableList.add(signTable);
        }
        vo.setSignTableList(signTableList);
        vo.setSignStatus(signDataMap.get(currentDate) != null);  // 当天是否已签到
        vo.setSignPush(plantTreeData.getSignPush());


        // 设置房间id
        List<PopularListVO> popularList = plantTreeRedis.getPopularList();
        vo.setJoinRoom(popularList != null && popularList.size() > 0 ? popularList.get(0).getRoomId() : "");

        // 设置排行榜
        PlantTreeVO.HardWordVO myHardWordVO = new PlantTreeVO.HardWordVO();

        List<PlantTreeVO.HardWordVO> hardWordVOList = new ArrayList<>();
        Map<String, Integer> rankingMap = plantTreeRedis.getWaterRankingMap(activityId, 10);
        ActorData rankActor = null;
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            PlantTreeVO.HardWordVO hardWordVO = new PlantTreeVO.HardWordVO();
            rankActor = actorDao.getActorDataFromCache(entry.getKey());

            hardWordVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            hardWordVO.setName(rankActor.getName());
            hardWordVO.setScore(entry.getValue());
            hardWordVO.setRank(rank);
            if(uid.equals(entry.getKey())){
                BeanUtils.copyProperties(hardWordVO, myHardWordVO);
            }
            hardWordVOList.add(hardWordVO);
            rank += 1;
        }

        vo.setRankingList(hardWordVOList);

        if(myHardWordVO.getRank() == 0){
            myHardWordVO.setName(actorData.getName());
            myHardWordVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myHardWordVO.setScore(plantTreeRedis.getWaterRankingScore(activityId, uid));
            myHardWordVO.setRank(-1);
        }

        vo.setMyRank(myHardWordVO);
        return vo;
    }

    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void plantTreeOpen(String uid, String activityId) {
        isActivityTime(activityId);
        synchronized (stringPool.intern(uid)) {
            // 检查状态
            PlantTreeData plantTreeData = plantTreeDao.selectOne(uid);
            if(plantTreeData != null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }


            plantTreeData = new PlantTreeData();
            plantTreeData.setUid(uid);
            plantTreeData.setCtime(DateHelper.getNowSeconds());
            int insertNum = plantTreeDao.insert(plantTreeData);
            if(insertNum <= 0){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }else {
                String pictureUrl = TREE_USER_PAGE_MAP.getOrDefault(0, "");
                plantTreeRedis.setUserDecoration(uid, pictureUrl);
            }
        }
    }

    private void distributionSignAward(String uid){

        List<PlantTreeSignData> treeSignDataList = plantTreeSignDao.selectList(uid);
        Map<String, PlantTreeSignData> signDataMap = treeSignDataList.stream().collect(Collectors.toMap(PlantTreeSignData::getSignDate, Function.identity()));
        int totalSign = 0;
        for (String signDate : SIGN_TABLE_LIST) {
            if(signDataMap.get(signDate) != null){
                totalSign += 1;
            }
        }

        if(totalSign == 10){
            distributionService.sendRewardResource(uid, SIGN_BADGE_ID,
                    ActivityRewardTypeEnum.getEnumByName("badge"), 0, 1, ACTIVITY_NAME, ACTIVITY_NAME, 0);
        }

    }


    public void distributionRankingAward(){
        try{
            Map<String, Integer> rankingMap = plantTreeRedis.getWaterRankingMap(ACTIVITY_ID, 3);
            int rank = 1;
            Map<String, List<ActivityCommonConfig.PlantRankConfig>> plantTreeRankConfig = activityCommonConfig.getPlantTreeRankConfig();

            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String rankUid = entry.getKey();

                String rankKey = "rank" + rank;
                List<ActivityCommonConfig.PlantRankConfig> awardRankList = plantTreeRankConfig.get(rankKey);
                if(awardRankList != null && awardRankList.size() > 0){

                    for (ActivityCommonConfig.PlantRankConfig plantRankConfig: awardRankList) {
                        distributionService.sendRewardResource(rankUid, plantRankConfig.getSourceId(),
                                ActivityRewardTypeEnum.getEnumByName(plantRankConfig.getRewardType()), plantRankConfig.getRewardTime(), plantRankConfig.getRewardNum(), ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    }
                }
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distributionRankingAward error: {}", e.getMessage(), e);
        }
    }


    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void plantTreeSign(String uid, String activityId) {
        isActivityTime(activityId);
        synchronized (stringPool.intern(uid)) {
            // 检查状态
            PlantTreeData plantTreeData = plantTreeDao.selectOne(uid);
            if(plantTreeData == null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }


            String currentDate = DateHelper.ARABIAN.formatDateInDay();
            PlantTreeSignData signData = plantTreeSignDao.selectOne(uid, currentDate);
            if(signData != null){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            signData = new PlantTreeSignData();
            signData.setUid(uid);
            signData.setSignDate(currentDate);
            signData.setCtime(DateHelper.getNowSeconds());
            int insertNum = plantTreeSignDao.insert(signData);
            if(insertNum > 0){
                plantTreeData.setWaterNum(plantTreeData.getWaterNum() + INC_WATER_COMMON);
                plantTreeDao.updateOne(plantTreeData);
                if(currentDate.equals("2023-05-10")){
                    distributionSignAward(uid);
                }

            }
        }
    }

    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public void plantTreeSignTest(String uid, String activityId, String signDate) {
        isActivityTime(activityId);
        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
            df.parse(signDate);
        }catch (Exception e){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        synchronized (stringPool.intern(uid)) {
            // 检查状态
            PlantTreeData plantTreeData = plantTreeDao.selectOne(uid);
            if(plantTreeData == null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            PlantTreeSignData signData = plantTreeSignDao.selectOne(uid, signDate);
            if(signData != null){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            signData = new PlantTreeSignData();
            signData.setUid(uid);
            signData.setSignDate(signDate);
            signData.setCtime(DateHelper.getNowSeconds());
            int insertNum = plantTreeSignDao.insert(signData);
            if(insertNum > 0){
                plantTreeData.setWaterNum(plantTreeData.getWaterNum() + INC_WATER_COMMON);
                plantTreeDao.updateOne(plantTreeData);
                if(signDate.equals("2023-05-10")){
                    distributionSignAward(uid);
                }
            }
        }
    }

    public void plantTreeSignAlarm(String uid, String activityId, int signPush) {
        isActivityTime(activityId);
        PlantTreeData plantTreeData = plantTreeDao.selectOne(uid);
        if(plantTreeData == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        plantTreeData.setSignPush(signPush);
        plantTreeDao.updateOne(plantTreeData);
    }


    @Transactional(value = DBMysqlBean.USTAR_TRANSACTION)
    public PlantTreeWaterVO plantTreeWater(String uid, String activityId, int slang) {
        isActivityTime(activityId);
        synchronized (stringPool.intern(uid)) {
            PlantTreeData plantTreeData = plantTreeDao.selectOne(uid);
            if(plantTreeData == null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            int treeStatus = plantTreeData.getTreeStatus();
            int waterNum = plantTreeData.getWaterNum();
            int waterProcess = plantTreeData.getWaterProcess();

            if(waterNum <= 0){
                throw new CommonH5Exception(ActivityHttpCode.PLANT_TREE_NOT_ENOUGH_WATER);
            }

            if(treeStatus == STOP_WATER_POINT){
                throw new CommonH5Exception(ActivityHttpCode.PLANT_TREE_FULL_TREE);
            }

            int needLevelWater = TREE_WATER_MAP.getOrDefault(treeStatus, -1);
            if(needLevelWater == -1){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            // 升级所需水滴数 - 已浇水滴数 = 还需浇水滴数
            int needWater = needLevelWater - waterProcess;
            if(needWater <= 0){
                throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
            }

            PlantTreeWaterVO vo = new PlantTreeWaterVO();

            // 自己本身水滴数量 大于等于 所需浇水数量
            int leftWater = waterNum - needWater;
            int realWaterNum = leftWater >= 0 ? needWater : waterNum;
            if(leftWater >= 0){
                int nextStatus = treeStatus + 1;
                plantTreeData.setTreeStatus(nextStatus);
                plantTreeData.setWaterNum(leftWater);
                plantTreeData.setWaterProcess(0);

                // 奖励下发
                Map<Integer, ActivityCommonConfig.PlantTreeConfig> plantTreeConfig = activityCommonConfig.getPlantTreeConfigList().stream().collect(Collectors.toMap(ActivityCommonConfig.PlantTreeConfig::getAwardType, Function.identity()));

                ActivityCommonConfig.PlantTreeConfig awardConfig = plantTreeConfig.getOrDefault(nextStatus, null);
                if(awardConfig != null){
                    PlantTreeWaterVO.AwardInfo awardInfo = new PlantTreeWaterVO.AwardInfo();
                    awardInfo.setIcon(awardConfig.getIcon());
                    awardInfo.setName(slang == SLangType.ARABIC ? awardConfig.getNameAr() : awardConfig.getNameEn());
                    vo.setAwardInfo(awardInfo);
                    distributionService.sendRewardResource(uid, awardConfig.getSourceId(),
                            ActivityRewardTypeEnum.getEnumByName(awardConfig.getRewardType()), awardConfig.getRewardTime(), awardConfig.getRewardNum(), ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }

                // 设置背景图
                String pictureUrl = TREE_USER_PAGE_MAP.getOrDefault(nextStatus, "");
                if(!StringUtils.isEmpty(pictureUrl)){
                    plantTreeRedis.setUserDecoration(uid, pictureUrl);
                }

                vo.setWaterNum(leftWater);
            }else {
                plantTreeData.setWaterNum(0);
                plantTreeData.setWaterProcess(waterProcess + waterNum);
                vo.setWaterNum(0);
            }
            plantTreeDao.updateOne(plantTreeData);
            plantTreeRedis.incrWaterRankingScore(activityId, uid, realWaterNum);
            return vo;
        }
    }


    public void plantTreePicturePush(String uid, String activityId, String picture, int slang) {
        isActivityTime(activityId);

        if(StringUtils.isEmpty(picture)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        if(plantTreeRedis.getShareMoment(uid, currentDate) == 1){
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }

        synchronized (stringPool.intern(uid)) {


            PlantTreeData plantTreeData = plantTreeDao.selectOne(uid);
            if(plantTreeData == null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText(slang == SLangType.ARABIC ? SHARE_AR : SHARE_EN);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(ACTIVITY_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("4000");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));
            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if(result.getCode() == 20){
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }


            plantTreeRedis.setShareMoment(uid, currentDate, 1);

            plantTreeData.setWaterNum(plantTreeData.getWaterNum() + INC_WATER_COMMON);
            plantTreeDao.updateOne(plantTreeData);

        }
    }


    public void plantTreeGiftHandle(SendGiftData giftData){
        int giftId = giftData.getGid();
        if (INC_GIFT_ID == giftId){
            int totalNum = giftData.getNumber() * giftData.getAid_list().size();
            String fromUid = giftData.getFrom_uid();
            PlantTreeData plantTreeData = plantTreeDao.selectOne(fromUid);
            if(plantTreeData == null){
                return;
            }

            plantTreeData.setWaterNum(plantTreeData.getWaterNum() + totalNum);
            plantTreeDao.updateOne(plantTreeData);
        }
    }


    public void plantTreeSignPushHandle(){
        try {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
            if(activityData == null){
                return;
            }

            int curTime = DateHelper.getNowSeconds();
            int startTime = activityData.getStartTime();
            int endTime = activityData.getEndTime();
            if (curTime < startTime || curTime > endTime){
                return;
            }

            List<PlantTreeData> signPushList = plantTreeDao.selectSignPushList();
            for (PlantTreeData treeData : signPushList) {
                String pushUid = treeData.getUid();
                ActorData actorData = actorDao.getActorDataFromCache(pushUid);
                int slang = actorData.getSlang();
                String actText = slang == SLangType.ARABIC ? ACTION_AR : ACTION_EN;
                String title = slang == SLangType.ARABIC ? SIGN_TITLE_AR : SIGN_TITLE_EN;
                String body = slang == SLangType.ARABIC ? SIGN_BODY_AR : SIGN_BODY_EN;
                String picture = slang == SLangType.ARABIC ? SIGN_BANNER_AR : SIGN_BANNER_EN;
                commonOfficialMsg(pushUid, picture, 0, 0, actText, title, body, ACTIVITY_URL);
            }
        }catch (Exception e){
            logger.error("plantTreeSignPushHandle error e={}", e.getMessage(), e);
        }





    }


}
