package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.*;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.NationalDayV2VO;
import com.quhong.data.vo.NationalDayV3VO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IFriendService;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NationalDayV2Dao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.NationalDayV2Data;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.NationalDayV2Redis;
import com.quhong.service.redis.RechargeRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@Component
public class NationalDayV3Service {

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV3Service.class);
    private static final int LIMIT_INIT_POOL = 10;
    private static final int ZERO_INIT_POOL = 0;
    private static final String RECORD_TITLE = "NationalDay";
    public static final String NATIONAL_DAY_TITLE = "NationalDay";
    private static final int BASE_OPEN_SHOW = 50000000;

    public static final Map<Integer, String> TYPE_EVENT_TITLE_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(1, "NationalDay-draw");
            put(2, "NationalDay-assist");
            put(3, "NationalDay-invite assist task");
            put(4, "NationalDay-send gift task");
            put(5, "NationalDay-invite assist task all");
        }
    };

    @Resource
    private NationalDayV2Dao nationalDayV2Dao;
    @Resource
    private NationalDayV3Service nationalDayV3Service;
    @Resource
    private NationalDayV2Redis nationalDayV2Redis;
    @Resource
    private ActorDao actorDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private IFriendService ifriendService;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private RechargeRedis rechargeRedis;

    @Cacheable(value = "nationalDay", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public NationalDayV2Data getNationalDayActivity(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        NationalDayV2Data data = nationalDayV2Dao.findData(activityId);
        if (null == data) {
            logger.error("cannot find ranking activity activityId={}", activityId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }


    public NationalDayV2VO nationalDayV3Config(String uid, String activityId) {
        NationalDayV2VO vo = new NationalDayV2VO();
        NationalDayV2Data activity = nationalDayV3Service.getNationalDayActivity(activityId);
        BeanUtils.copyProperties(activity, vo);
        ActorData actor = actorDao.getActorDataFromCache(uid);
        vo.setRid(actor.getRid());
        vo.setName(actor.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        // 发送999个国旗礼物的人次
        vo.setSendNum(nationalDayV2Redis.getNationalDayV2RankingScore(activityId, uid));

        // 设置助力用户
        List<Integer> baseLevelList = activity.getHelpLevelList();
        List<Integer> scoreLevelList = new ArrayList<>(baseLevelList);
        scoreLevelList.sort(Integer::compare);
        scoreLevelList.add(0, 0);
        int helpNums = nationalDayV2Redis.getHelpUserNums(activityId, uid);
        int oldIndex = activityCommonRedis.getCommonZSetRankingScore(getZetNationalLevelKey(activityId), uid);
        int afterIndex = getIndexLevel(helpNums, scoreLevelList);
        logger.info("oldIndex:{} afterIndex:{} helpNums:{} scoreLevelList:{}", oldIndex, afterIndex, helpNums, scoreLevelList);
        vo.setDrew(afterIndex > oldIndex);
        vo.setFlagProcess(helpNums);

        List<NationalDayV2VO.HelpUser> helpUserList = new ArrayList<>();
        Set<String> helpMembers = nationalDayV2Redis.getHelpUser(activityId, uid);
        for (String aid : helpMembers) {
            ActorData user = actorDao.getActorDataFromCache(aid);
            NationalDayV2VO.HelpUser item = new NationalDayV2VO.HelpUser();
            item.setName(user.getName());
            item.setHead(ImageUrlGenerator.generateRoomUserUrl(user.getHead()));
            item.setUid(aid);
            helpUserList.add(item);
        }
        vo.setHelpUserList(helpUserList);

        List<String> keyList = new ArrayList<>();
        keyList.add(activity.getLevelDrawResKey1());
        keyList.add(activity.getLevelDrawResKey2());
        keyList.add(activity.getLevelDrawResKey3());
        keyList.add(activity.getLevelDrawResKey4());
        keyList.add(activity.getLevelDrawResKey5());
        int maxSize = keyList.size();

        List<String> showLevelKeyList = new ArrayList<>();
        if (oldIndex > 0) {
            for (int i = 0; i <= Math.min(oldIndex - 1, maxSize - 1); i++) {
                showLevelKeyList.add(keyList.get(i));
            }
        }
        vo.setAllLevelKeyList(keyList);
        vo.setShowLevelKeyList(showLevelKeyList);

        return vo;
    }

    private NationalDayV2Data commonParamCheck(String activityId) {
        NationalDayV2Data activity = nationalDayV3Service.getNationalDayActivity(activityId);  // 参数检验
        int startTime = activity.getStartTime();
        int endTime = activity.getEndTime();
        int currentTime = DateHelper.getNowSeconds();

        if (currentTime < startTime || currentTime > endTime) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        return activity;

    }


    /**
     * 邀请好友助力-发私信
     */
    public void nationalDayV3InviteHelp(String uid, String activityId, String aid) {
        NationalDayV2Data activity = commonParamCheck(activityId);
        if (!StringUtils.isEmpty(nationalDayV2Redis.getNationalDayV3InviteHelpValue(activityId, uid, aid))) {
            logger.info("nationalDayV2Help isHelpUser 24h send msg return uid:{}, aid:{}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL3_DAY_24H_LIMIT);
        }
        if (nationalDayV2Redis.isHelpUser(activityId, uid, aid)) {
            logger.info("nationalDayV2Help isHelpUser return uid:{}, aid:{}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL3_DAY_HELP_ALREADY);
        }
        sendPrivateMsg(uid, aid, activity.getShareId());
        nationalDayV2Redis.setNationalDayV3InviteHelp(activityId, uid, aid);
    }

    /**
     * 点击find friend list
     */
    public NationalDayV3VO nationalDayV3FindFriendList(String activityId, String uid, int page) {
        NationalDayV2Data activity = commonParamCheck(activityId);
        int now = DateHelper.getNowSeconds();
        String findListKey = getZetNationalFindKey(activityId);
        int oldTime = activityCommonRedis.getCommonZSetRankingScore(findListKey, uid);
        if (oldTime == 0) {
            long nowNum = activityCommonRedis.incCommonStrScore(getStrNationalIncKey(activityId), 1);
            int targetNum = rechargeRedis.isRechargeUserByCache(uid) ? (int) nowNum : (int) (BASE_OPEN_SHOW + nowNum);
            activityCommonRedis.addCommonZSetRankingScore(findListKey, uid, targetNum);
            oldTime = targetNum;
        }

        NationalDayV3VO vo = new NationalDayV3VO();
        List<OtherRankingListVO> findUserList = new ArrayList<>();
        OtherRankingListVO myFindUser = new OtherRankingListVO();
        page = page <= 0 ? 1 : page;
        int pageSize = 20;
        int start = (page - 1) * pageSize;
        int end = page * pageSize;
//        List<String> uidLists = activityCommonRedis.getCommonRankingList(findListKey, start, end);
        Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
        Map<String, Integer> rankingMap = activityCommonRedis.getOtherRankingMapByScoreAPage(findListKey, BASE_OPEN_SHOW, BASE_OPEN_SHOW * 2, start, pageSize);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String findUid = entry.getKey();
            if (uid.equals(findUid)) {
                continue;
            }
            ActorData actorData = actorDao.getActorDataFromCache(findUid);
            if (actorData == null) {
                continue;
            }
            String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
            OtherRankingListVO itemVO = new OtherRankingListVO();
            itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            itemVO.setName(actorData.getName());
            itemVO.setUid(findUid);
            itemVO.setRidData(actorData.getRidData());
            itemVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
            findUserList.add(itemVO);
        }

        ActorData myActorData = actorDao.getActorDataFromCache(uid);
        myFindUser.setHead(ImageUrlGenerator.generateRoomUserUrl(myActorData.getHead()));
        myFindUser.setName(myActorData.getName());
        myFindUser.setUid(uid);
        myFindUser.setRidData(myActorData.getRidData());
        String countryCode = ActorUtils.getCountryCode(myActorData.getCountry());
        myFindUser.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));

        vo.setFindUserList(findUserList);
        vo.setMyFindUser(myFindUser);
        vo.setIsOpenFind(oldTime > BASE_OPEN_SHOW ? 1 : 2);
        return vo;
    }

    /**
     * @param activityId
     * @param uid
     * @param state
     */
    public void nationalDayV3State(String activityId, String uid, int state) {
        NationalDayV2Data activity = commonParamCheck(activityId);
        String findListKey = getZetNationalFindKey(activityId);
        int oldTime = activityCommonRedis.getCommonZSetRankingScore(findListKey, uid);
        if (oldTime != 0) {
            int targetNum = 0;
            if (state == 1 && oldTime < BASE_OPEN_SHOW) {
                targetNum = BASE_OPEN_SHOW + oldTime;
            } else if (state == 2 && oldTime > BASE_OPEN_SHOW) {
                targetNum = oldTime - BASE_OPEN_SHOW;
            }
            if (targetNum != 0) {
                activityCommonRedis.addCommonZSetRankingScore(findListKey, uid, targetNum);
            }
        } else {
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_FIND_EXIST);
        }
    }


    public void sendPrivateMsg(String uid, String aid, Integer shareId) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            SendChatMsgDTO msgDto = new SendChatMsgDTO();
            msgDto.setUid(uid);
            msgDto.setAid(aid);
            msgDto.setMsgType(MsgType.SHARE_ACTIVITY);
            msgDto.setOs(actorData.getIntOs());
            msgDto.setMsgBody("");
            msgDto.setSlang(actorData.getSlang());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("shareId", shareId);
            jsonObject.put("helpType", 1);
            msgDto.setMsgInfo(jsonObject);
            msgDto.setVersioncode(actorData.getVersion_code());
            msgDto.setNew_versioncode(5);
            HttpResult<Object> ret = iMsgService.sendMsg(msgDto);
            logger.info("inner msg success msgDto={} return--> code={} msg={}", msgDto, ret.getCode(), ret.getMsg());
        } catch (Exception e) {
            logger.error("sendPrivateMsg error:{}", e.getMessage(), e);
        }
    }

    /**
     * 助力
     */
    public NationalDayV3VO nationalDayV3Help(String uid, String activityId, String aid) {
        NationalDayV2Data activity = commonParamCheck(activityId);
        if (StringUtils.isEmpty(aid)) {
            logger.info("rid startsWith return  uid:{}, rid: {}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        // 自己不能给自己助力
        if (uid.equals(aid)) {
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_SELF);
        }

        ActorData helpActor = actorDao.getActorDataFromCache(uid); // 帮助者
        String helpTnId = helpActor.getTn_id();
        if (StringUtils.isEmpty(helpTnId)) {
            logger.info("nationalDayV2Help not helpTnId return uid:{}, aid:{}, helpTnId:{}", uid, aid, helpTnId);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }


        boolean tnIdStatus = nationalDayV2Redis.isHelpTnUser(activityId, helpTnId);
        if (tnIdStatus) {
            logger.info("nationalDayV2Help isHelpTnUser return uid:{}, aid:{}, helpTnId:{}", uid, aid, helpTnId);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_SAME_DEVICE);
        }

        int maxNum = activity.getHelpLevelList().get(activity.getHelpLevelList().size() - 1);
        int helpUserNum = nationalDayV2Redis.getHelpUserNums(activityId, aid);
        if (helpUserNum >= maxNum) {
            logger.info("nationalDayV2Help helpUserNum last return uid:{}, aid:{}, helpUserNum:{} maxNum:{}", uid, aid, helpUserNum, maxNum);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL3_DAY_HELP_MAX_LIMIT);
        }

        if (nationalDayV2Redis.isHelpUser(activityId, aid, uid)) {
            logger.info("nationalDayV2Help isHelpUser return uid:{}, aid:{}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL3_DAY_HELP_ALREADY);
        }

        nationalDayV2Redis.setHelpTnUser(activityId, helpTnId);
        nationalDayV2Redis.setHelpUser(activityId, aid, uid);

        String helpUserResKey = activity.getHelpUserResKey();
        ResourceKeyConfigData.ResourceMeta resourceMeta = drawOne(uid, helpUserResKey);
        NationalDayV3VO vo = new NationalDayV3VO();
        vo.setDrawResourceMetaList(Collections.singletonList(resourceMeta));
        Set<String> keySet = new HashSet<>(Arrays.asList(helpUserResKey));
        vo.setResourceKeyDataList(resourceKeyConfigDao.findListByKeys(new HashSet<>(keySet)));

        doFriendsAssistanceRecordEvent(aid, uid);
        return vo;
    }

    /**
     * 获取助力奖励
     *
     * @param uid
     * @param activityId
     */
    public NationalDayV3VO userClickReward(String activityId, String uid) {
        NationalDayV2Data activity = commonParamCheck(activityId);
        List<Integer> baseLevelList = activity.getHelpLevelList();
        List<Integer> scoreLevelList = new ArrayList<>(baseLevelList);
        scoreLevelList.sort(Integer::compare);
        scoreLevelList.add(0, 0);
        int helpNums = nationalDayV2Redis.getHelpUserNums(activityId, uid);
        int oldIndex = activityCommonRedis.getCommonZSetRankingScore(getZetNationalLevelKey(activityId), uid);
        int afterIndex = getIndexLevel(helpNums, scoreLevelList); // 如果有6个等级，最大值为6
        int maxLevel = baseLevelList.size();
        List<ResourceKeyConfigData.ResourceMeta> drawResourceMetaList = new ArrayList<>();

        List<String> keyList = new ArrayList<>();
        keyList.add(activity.getLevelDrawResKey1());
        keyList.add(activity.getLevelDrawResKey2());
        keyList.add(activity.getLevelDrawResKey3());
        keyList.add(activity.getLevelDrawResKey4());
        keyList.add(activity.getLevelDrawResKey5());
        int maxSize = keyList.size();
        if (afterIndex > oldIndex) {
            for (int i = oldIndex + 1; i <= afterIndex; i++) {
//                String resourceKey = i <= 2 ? activity.getLevelDrawResKey1() : activity.getLevelDrawResKey3();
//                ResourceKeyConfigData.ResourceMeta resourceMeta = drawOne(uid, resourceKey);
                String resourceKey = keyList.get(i <= maxSize ? i - 1 : maxSize - 1);
                String eventTitle = getEventTitle(i);
                resourceKeyHandlerService.sendResourceData(uid, resourceKey,
                        eventTitle,
                        eventTitle,
                        eventTitle,
                        activity.getAcUrl(), "");
                doActivityParticipationEvent(uid, activityId, i);
                ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
                drawResourceMetaList.addAll(resourceKeyConfigData.getResourceMetaList());
            }
            activityCommonRedis.addCommonZSetRankingScore(getZetNationalLevelKey(activityId), uid, afterIndex);
            if (afterIndex == maxLevel) {
                // 下发完成全部等级奖励
                String eventTitle = getEventTitle(60);
                resourceKeyHandlerService.sendResourceData(uid, activity.getAllLevelDrawResKey(),
                        eventTitle,
                        eventTitle,
                        eventTitle,
                        activity.getAcUrl(), "");
            }
        }
        NationalDayV3VO vo = new NationalDayV3VO();
        vo.setDrawResourceMetaList(drawResourceMetaList);
        return vo;
    }

    public void addFriendApply(String activityId, String uid, String aid, String msg) {
        NationalDayV2Data activity = commonParamCheck(activityId);
        ApiResult<List<String>> msgFriendList = ifriendService.addFriendApply(uid, aid, msg);
        if (msgFriendList.isOk()) {
            List<String> msgListData = msgFriendList.getData();
            if (!CollectionUtils.isEmpty(msgListData)) {
                throw new CommonH5Exception(new HttpCode(72, msgListData.get(0),
                        StringUtils.isEmpty(msgListData.get(1)) ? msgListData.get(0) : msgListData.get(1)));
            }
        } else {
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }

    }


    private ResourceKeyConfigData.ResourceMeta drawOne(String uid, String resKey) {
        ResourceKeyConfigData keyConfigData = resourceKeyConfigDao.findByKey(resKey);
        if (keyConfigData == null || keyConfigData.getKeyType() != 1) {
            logger.info("资源key必须为概率类型");
            return null;
        }
        Map<String, ResourceKeyConfigData.ResourceMeta> allPrizeConfigMap = new HashMap<>();
        keyConfigData.getResourceMetaList().forEach(subItem -> {
            allPrizeConfigMap.put(subItem.getMetaId(), subItem);
        });
        Map<String, Integer> calcMap = getCalcMap(allPrizeConfigMap);
        String prizeId = getAwardIdByProbability(calcMap);
        ResourceKeyConfigData.ResourceMeta resourceMeta = allPrizeConfigMap.get(prizeId);
        ResourceKeyConfigData.ResourceMeta toResourceMeta = null;

        if (resourceMeta != null) {
            toResourceMeta = new ResourceKeyConfigData.ResourceMeta();
            BeanUtils.copyProperties(resourceMeta, toResourceMeta);
            String eventTitle = getEventTitle(70);
            resourceKeyHandlerService.sendOneResourceData(uid, toResourceMeta, 905,
                    eventTitle,
                    eventTitle,
                    eventTitle,
                    "", "", 1);
            doDrawPrizeRecordEvent(uid, Collections.singletonList(toResourceMeta));
        }
        return toResourceMeta;
    }

    private Map<String, Integer> getCalcMap(Map<String, ResourceKeyConfigData.ResourceMeta> srcMap) {
        Map<String, Integer> calcMap = new HashMap<>();
        srcMap.forEach((k, v) -> {
            try {
                int rate = (int) (Float.parseFloat(v.getRateNumber()) * 1000);
                if (rate >= 0) {
                    calcMap.put(k, rate);
                }
            } catch (NumberFormatException e) {
                logger.info("key:{} rateNumber:{} msg:{} ", k, v.getRateNumber(), e.getMessage(), e);
                throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
            }
        });
        return calcMap;
    }

    public String getAwardIdByProbability(Map<String, Integer> sourceMap) {
        try {
            if (CollectionUtils.isEmpty(sourceMap)) {
                logger.info("getAwardIdByProbability sourceMap is empty");
                return null;
            }
            int total = 0;
            Map<Integer, String> mapRatio = new HashMap<>();
            List<Integer> ratioList = new ArrayList<>(); // 这样大于等于0，小于1000取第一个（key为1000） 、大于等于1000，小于2000取第二个 （key为2000）
            for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
                String awardId = entry.getKey();
                int value = entry.getValue();
                if (value <= 0) {
                    continue;
                }
                total += value;
                mapRatio.put(total, awardId);
                ratioList.add(total);
            }
            if (total == 0) {
                logger.info("getAwardIdByProbability total is zero sourceMap={} ", sourceMap);
                return null;
            }
            int ratio = ThreadLocalRandom.current().nextInt(0, total);//0-(total-1)
            if (!ratioList.contains(ratio)) {
                ratioList.add(ratio);
                Collections.sort(ratioList);
            }

            int index = ratioList.indexOf(ratio);
            int destNum = ratioList.get(index + 1);
            String hitType = mapRatio.get(destNum);
//            logger.info("getAwardIdByProbability-->ratioList:{} mapRatio:{} total:{} ratio:{} index:{} destNum:{} hitType:{}"
//                    , ratioList, mapRatio, total, ratio, index, destNum, hitType);
            return hitType;
        } catch (Exception e) {
            logger.error("getAwardIdByProbability error sourceMap:{} msg:{}", sourceMap, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清除助力次数
     */

    public void clearNationalDayV3(String rid, String activityId, String uid) {
        int clearRid;
        try {
            clearRid = Integer.parseInt(rid);
        } catch (Exception e) {
            logger.error("nationalDayV2Help PARAM_ERROR return uid:{}, rid: {}, error:{}", rid, e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        ActorData actor = actorDao.getActorByRid(clearRid);  // 被帮助者
        if (actor == null) {
            throw new CommonH5Exception(ActivityHttpCode.NATIONAL_DAY_HELP_NOT_USER);
        }

        String aid = actor.getUid();
//        nationalDayV2Redis.removeHelpTotalUser(activityId, aid); // 一个用户每日只能帮助一次
        if (!StringUtils.isEmpty(uid)) {
            nationalDayV2Redis.removerOneHelpUser(activityId, uid, aid);
        }
        String helpTnId = actor.getTn_id();
        nationalDayV2Redis.removeHelpTnUser(activityId, helpTnId);

    }

    private int getIndexLevel(int score, List<Integer> scoreLevelList) {
        List<Integer> tempLevelNumList = new ArrayList<>(scoreLevelList);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

    private String getZetNationalLevelKey(String activityId) {
        return activityId + ":national:v3:level";
    }

    private String getZetNationalFindKey(String activityId) {
        return activityId + ":national:v3:find";
    }

    private String getStrNationalIncKey(String activityId) {
        return activityId + ":national:v3:inc";
    }


    private void doActivityParticipationEvent(String uid, String acId, int level) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(RECORD_TITLE);
        event.setActive_id(acId);
        event.setActivity_stage(level);
        eventReport.track(new EventDTO(event));
    }


    private void doFriendsAssistanceRecordEvent(String uid, String aid) {
        FriendsAssistanceRecordEvent event = new FriendsAssistanceRecordEvent();
        event.setUid(uid);
        event.setAid(aid);
        event.setSence(RECORD_TITLE);
        event.setCtime(DateHelper.getNowSeconds());
        event.setAssist_uid(uid);
        eventReport.track(new EventDTO(event));
    }


    private void doDrawPrizeRecordEvent(String uid, List<ResourceKeyConfigData.ResourceMeta> resourceMetaList) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(RECORD_TITLE);
        event.setCost_ticket(1);
        event.setDraw_nums(1);
        event.setDraw_success_nums(1);
        event.setTicket_type(0);
        event.setSence_detail(0);
        event.setDraw_detail("");
        event.setDraw_result(JSONObject.toJSONString(resourceMetaList));
        eventReport.track(new EventDTO(event));
    }

    public String getEventTitle(int type) {
        if (type <= 5) {
            return String.format("%s-levelDrawResKey%s", RECORD_TITLE, type);
        } else if (type == 60) {
            return String.format("%s-allLevelDrawResKey", RECORD_TITLE);
        } else if (type == 70) {
            return String.format("%s-SupporterResKey", RECORD_TITLE);
        } else if (type == 80) {
            return String.format("%s-advanceResKey", RECORD_TITLE);
        }
        return RECORD_TITLE;
    }
}
