package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.InviteFissionData;
import com.quhong.mysql.data.QuestionData;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class MatchQuizService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(MatchQuizService.class);
    private static final String ACTIVITY_TITLE_EN = "test Match Quiz";
    private static final String ACTIVITY_TITLE_AR = "test Match Quiz";
    public static String ACTIVITY_ID = "689ee1add1a79b68dc072408";//
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/match_quiz/?shareId=65&activityId=%s", ACTIVITY_ID);

    private static String GENERATE_QUIZ_REWARD_KEY = "generateQuizRewardKey"; // 生成问卷奖励-奖励邀请者
    private static String DONE_30_USER_REWARD_KEY = "done30UserRewardKey"; // 完成30名用户的答题奖励-奖励邀请者
    private static String NEW_DEVICE_REWARD_KEY = "newDeviceRewardKey"; // 新设备答题奖励-奖励邀请者


    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final int HISTORY_PAGE_SIZE = 10;
    private static final int HISTORY_MAX_SIZE = 3000;

    private static final String QUIZ_RESULT_FLIED = "quiz_result_flied"; // 我的问卷答题结果
    private static final String QUIZ_TOTAL_NUM_FLIED = "quiz_total_num_flied"; // 问卷答题总人数
    private static final String QUIZ_NEW_NUM_FLIED = "quiz_new_num_flied"; // 问卷答题新用户人数
    private static final String QUIZ_MATCH_NUM_FLIED = "quiz_match_num_flied"; // 问卷答题匹配心有灵犀人数
    private static final String QUIZ_MATCH_DAY_LIMIT_NUM_FLIED = "quiz_match_day_num_flied_%s"; // 每日问卷答题新设备人数
    private static final String QUIZ_MATCH_NEW_DEVICE_FLIED = "quiz_match_new_device_flied"; // 问卷答题新设备人数集合

    private static final int QUIZ_MATCH_LEVEL_1 = 1; // 0～2题 萍水相逢
    private static final int QUIZ_MATCH_LEVEL_2 = 2; // 3～5题 泛泛之交
    private static final int QUIZ_MATCH_LEVEL_3 = 3; // 6～7题 志同道合
    private static final int QUIZ_MATCH_LEVEL_4 = 4; // 8～10题 心有灵犀

    private static final int TYPE_MATCH_ALL_RECORD = 1; // 所有匹配结果记录
    private static final int TYPE_MATCH_PERFECT_RECORD = 2; // 心有灵犀匹配结果记录

    private static final int QUIZ_QUESTION_NUM = 10;

    private static String GID = "124"; // 题库id

    private static final String TACIT_UNDERSTANDING_TEST_CREATE = "TacitUnderstandingTest-create";
    private static final String TACIT_UNDERSTANDING_TEST_30_USER = "TacitUnderstandingTest-30 user answer questions";
    private static final String TACIT_UNDERSTANDING_TEST_NEW_DEVICE = "TacitUnderstandingTest-new user answer questions";

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.INVITE_BIND_USER);

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private RoomEventSubDao roomEventSubDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private QuestionDao questionDao;
    @Resource
    private InviteFissionDao inviteFissionDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            GID = "0121";
            ACTIVITY_ID = "6889931a36710475b95fb63d";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/match_quiz/?activityId=%s&shareId=26", ACTIVITY_ID);

        }
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:game_event:user:" + uid;
    }


    /**
     * 获取生成问卷奖励key
     */
    private String getGenerateRewardKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return String.format("match_quiz:generate_reward:%s", aId);
    }

    /**
     * 获取30人完成奖励key
     */
    private String getDone30RewardKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return String.format("match_quiz:done30_reward:%s", aId);
    }

    /**
     * 获取新设备奖励key
     */
    // private String getNewDeviceRewardKey(String activityId, String uid) {
    //     String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
    //     return String.format("match_quiz:new_device_reward:%s:%s", aId, uid);
    // }
    private String getUserHashDetailKey(String activityId, String uid) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return String.format(":match_quiz:user:detail:%s:%s", aId, uid);
    }

    /**
     * @param activityId
     * @param uid
     * @param type       1 所有匹配结果记录 2 心有灵犀匹配结果记录
     * @return
     */
    private String getHistoryListKey(String activityId, String uid, int type) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return String.format("match_quiz:history:%s:%s:%s", aId, uid, type);
    }

    // private String getAllSetKey(String activityId) {
    //     String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
    //     return String.format("match_quiz:all_set:%s", aId);
    // }

    private String getAllHashKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return String.format("match_quiz:all_hash:%s", aId);
    }

    /**
     * @param uid 邀请者-出题者
     * @param aid 被邀请者-回答者
     * @return
     */
    private String getIndexKey(String uid, String aid) {
        return String.format("%s-%s", uid, aid);
    }


    /**
     * 获取题目列表
     */
    public QuizQuestionVO getQuestionList(String activityId, String uid, String aid) {
        OtherRankingActivityData activityData = checkActivityTime(activityId);
        QuizQuestionVO vo = new QuizQuestionVO();
        List<QuizQuestionVO.QuestionVO> list = new ArrayList<>();
        List<QuestionData> questionList = questionDao.selectListPage(GID, 1, 100);
        if (!CollectionUtils.isEmpty(questionList)) {
            if (StringUtils.isEmpty(aid)) {
                // 生成问卷
                for (QuestionData item : questionList) {
                    QuizQuestionVO.QuestionVO questionVO = new QuizQuestionVO.QuestionVO();
                    questionVO.setId(item.getId());
                    questionVO.setPictureUrl(item.getPictureUrl());
                    questionVO.setContent(item.getContent());
                    Map<String, String> optionContentMap;
                    String optionContent = item.getOptionContent();
                    if (!StringUtils.isEmpty(optionContent)) {
                        optionContentMap = JSON.parseObject(optionContent, new TypeReference<HashMap<String, String>>() {
                        });
                    } else {
                        optionContentMap = new HashMap<>(4);
                    }
                    questionVO.setOptionContent(optionContentMap);
                    questionVO.setCorrectOption(item.getCorrectOption());
                    list.add(questionVO);
                }
            } else {
                Map<Integer, QuestionData> questionDataMap = questionList.stream().collect(Collectors.toMap(QuestionData::getId, Function.identity()));
                // 回答问卷列表
                String detailUserKey = getUserHashDetailKey(activityId, aid);
                String aidQuizAnswerJson = activityCommonRedis.getCommonHashStrValue(detailUserKey, QUIZ_RESULT_FLIED);
                if (StringUtils.isEmpty(aidQuizAnswerJson)) {
                    logger.error("target user has not generated quiz. aid={}", aid);
                    throw new CommonH5Exception(ActivityHttpCode.MATCH_QUIZ_NOT_GENERATE);
                }
                Map<Integer, String> aidQuizAnswer = JSON.parseObject(aidQuizAnswerJson,
                        new TypeReference<HashMap<Integer, String>>() {
                        });
                for (Map.Entry<Integer, String> entry : aidQuizAnswer.entrySet()) {
                    Integer questionId = entry.getKey();
                    QuizQuestionVO.QuestionVO questionVO = new QuizQuestionVO.QuestionVO();
                    QuestionData questionData = questionDataMap.get(questionId);
                    if (questionData == null) {
                        logger.error("can not find question data. questionId={}", questionId);
                        continue;
                    }
                    questionVO.setId(questionId);
                    questionVO.setPictureUrl(questionData.getPictureUrl());
                    questionVO.setContent(questionData.getContent());
                    Map<String, String> optionContentMap;
                    String optionContent = questionData.getOptionContent();
                    if (!StringUtils.isEmpty(optionContent)) {
                        optionContentMap = JSON.parseObject(optionContent, new TypeReference<HashMap<String, String>>() {
                        });
                    } else {
                        optionContentMap = new HashMap<>(4);
                    }
                    questionVO.setOptionContent(optionContentMap);
                    questionVO.setCorrectOption(questionData.getCorrectOption());
                    list.add(questionVO);
                }
            }

        }
        vo.setList(list);
        return vo;
    }

    /**
     * 提交答题回答
     */
    public MatchQuizVO submitAnswer(String activityId, String uid, String aid, QuizActivityDTO dto) {
        checkActivityTime(activityId);
        Map<Integer, String> quizAnswer = dto.getQuizAnswer();
        if (CollectionUtils.isEmpty(quizAnswer) || quizAnswer.size() != QUIZ_QUESTION_NUM) {
            logger.error("submit answer param error. quizAnswer={}", quizAnswer);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        MatchQuizVO vo = new MatchQuizVO();
        MatchQuizVO.QuestionResultVO questionResultVO = new MatchQuizVO.QuestionResultVO();

        if (StringUtils.isEmpty(aid)) {
            // 生成问卷
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            vo.setUid(uid);
            vo.setName(actorData.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            return handleGenerateQuiz(activityId, uid, quizAnswer, vo, questionResultVO);
        } else {
            // 回答问卷
            return handleAnswerQuiz(activityId, uid, aid, quizAnswer, vo, questionResultVO);
        }
    }

    /**
     * 处理生成问卷
     */
    private MatchQuizVO handleGenerateQuiz(String activityId, String uid, Map<Integer, String> quizAnswer,
                                           MatchQuizVO vo, MatchQuizVO.QuestionResultVO questionResultVO) {
        String detailUserKey = getUserHashDetailKey(activityId, uid);

        // 检查是否已经生成过问卷
        String existingQuiz = activityCommonRedis.getCommonHashStrValue(detailUserKey, QUIZ_RESULT_FLIED);
        if (!StringUtils.isEmpty(existingQuiz)) {
            logger.error("user already generated quiz. uid={}", uid);
            throw new CommonH5Exception(ActivityHttpCode.MATCH_QUIZ_ALREADY_GENERATE);
        }

        // 保存问卷数据到Redis hash
        String quizAnswerJson = JSON.toJSONString(quizAnswer);
        activityCommonRedis.setCommonHashData(detailUserKey, QUIZ_RESULT_FLIED, quizAnswerJson);

        // 发放生成问卷奖励（活动期间只能领取一次）
        String generateRewardKey = getGenerateRewardKey(activityId);
        if (activityCommonRedis.isCommonSetData(generateRewardKey, uid) == 0) {
            resourceKeyHandlerService.sendResourceData(uid, GENERATE_QUIZ_REWARD_KEY,
                    TACIT_UNDERSTANDING_TEST_CREATE, TACIT_UNDERSTANDING_TEST_CREATE, TACIT_UNDERSTANDING_TEST_CREATE, "", "");
            activityCommonRedis.addCommonSetData(generateRewardKey, uid);
            logger.info("send generate quiz reward. uid={}", uid);
        }
        doAnswerOrQuestionEvent(activityId, uid, 1, "");
        return vo;
    }

    /**
     * 处理回答问卷
     */
    private MatchQuizVO handleAnswerQuiz(String activityId, String uid, String aid, Map<Integer, String> quizAnswer,
                                         MatchQuizVO vo, MatchQuizVO.QuestionResultVO questionResultVO) {
        // 检查是否自己回答自己的问卷
        if (uid.equals(aid)) {
            logger.error("user cannot answer own quiz. uid={} aid={}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.MATCH_QUIZ_CANNOT_ANSWER_SELF);
        }

        // 检查被回答者是否生成了问卷
        String detailUserKey = getUserHashDetailKey(activityId, aid);
        String aidQuizAnswerJson = activityCommonRedis.getCommonHashStrValue(detailUserKey, QUIZ_RESULT_FLIED);
        if (StringUtils.isEmpty(aidQuizAnswerJson)) {
            logger.error("target user has not generated quiz. aid={}", aid);
            throw new CommonH5Exception(ActivityHttpCode.MATCH_QUIZ_NOT_GENERATE);
        }

        // 检查是否已经回答过这个用户的问卷
        // String allSetKey = getAllSetKey(activityId);
        String indexKey = getIndexKey(aid, uid);

        if (getQuestionResultFromRedis(activityId, indexKey) != null) {
            logger.error("user already answered this quiz. uid={} aid={}", uid, aid);
            throw new CommonH5Exception(ActivityHttpCode.MATCH_QUIZ_ALREADY_ANSWER);
        }

        // 解析被回答者的问卷答案
        Map<Integer, String> aidQuizAnswer = JSON.parseObject(aidQuizAnswerJson,
                new TypeReference<HashMap<Integer, String>>() {
                });

        // 计算得分
        int score = calculateScore(quizAnswer, aidQuizAnswer);
        int level = getScoreLevel(score);

        // 记录回答关系
        // activityCommonRedis.addCommonSetData(allSetKey, indexKey);

        // 保存回答结果到历史记录（记录到被回答者的历史中，记录回答者的信息）
        leftPushAllHistoryList(activityId, aid, TYPE_MATCH_ALL_RECORD, score, uid);
        if (level == QUIZ_MATCH_LEVEL_4) { // 心有灵犀
            leftPushAllHistoryList(activityId, aid, TYPE_MATCH_PERFECT_RECORD, score, uid);
        }

        // 更新统计数据
        updateQuizStatistics(activityId, aid, uid, level);

        // 发放奖励
        handleQuizRewards(activityId, aid, uid);


        // 答题人
        questionResultVO.setAid(uid);
        questionResultVO.setScore(score);
        questionResultVO.setLevel(level);
        vo.setMyQuestionResult(questionResultVO);

        // 保存回答结果到Redis
        saveQuestionResultToRedis(activityId, indexKey, questionResultVO);

        // 出题人
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        vo.setUid(aid);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

        doAnswerOrQuestionEvent(activityId, uid, 2, actorData.getStrRid());
        return vo;
    }

    /**
     * 计算得分
     */
    private int calculateScore(Map<Integer, String> userAnswer, Map<Integer, String> targetAnswer) {
        int score = 0;
        for (Map.Entry<Integer, String> entry : userAnswer.entrySet()) {
            Integer questionId = entry.getKey();
            String userOption = entry.getValue();
            String targetOption = targetAnswer.get(questionId);
            if (userOption != null && userOption.equals(targetOption)) {
                score++;
            }
        }
        return score;
    }

    /**
     * 获取分数评级
     */
    private int getScoreLevel(int score) {
        if (score >= 0 && score <= 2) {
            return QUIZ_MATCH_LEVEL_1; // 萍水相逢
        } else if (score >= 3 && score <= 5) {
            return QUIZ_MATCH_LEVEL_2; // 泛泛之交
        } else if (score >= 6 && score <= 7) {
            return QUIZ_MATCH_LEVEL_3; // 志同道合
        } else if (score >= 8 && score <= 10) {
            return QUIZ_MATCH_LEVEL_4; // 心有灵犀
        }
        return QUIZ_MATCH_LEVEL_1;
    }

    /**
     * 更新问卷统计数据
     */
    private void updateQuizStatistics(String activityId, String aid, String uid, int level) {
        String detailUserKey = getUserHashDetailKey(activityId, aid);

        // 更新总答题人数
        activityCommonRedis.incCommonHashNum(detailUserKey, QUIZ_TOTAL_NUM_FLIED, 1);

        // 如果是心有灵犀等级，更新匹配心有灵犀人数
        if (level == QUIZ_MATCH_LEVEL_4) {
            activityCommonRedis.incCommonHashNum(detailUserKey, QUIZ_MATCH_NUM_FLIED, 1);
        }
    }

    /**
     * 处理问卷奖励
     *
     * @param activityId
     * @param aid        出题人
     * @param uid        答题人
     */
    private void handleQuizRewards(String activityId, String aid, String uid) {
        // 检查是否达到30名用户答题，给被回答者发奖励
        String detailUserKey = getUserHashDetailKey(activityId, aid);
        int totalNum = activityCommonRedis.getCommonHashValue(detailUserKey, QUIZ_TOTAL_NUM_FLIED);
        if (totalNum == 30) {
            String done30RewardKey = getDone30RewardKey(activityId);
            if (activityCommonRedis.isCommonSetData(done30RewardKey, aid) == 0) {
                resourceKeyHandlerService.sendResourceData(aid, DONE_30_USER_REWARD_KEY,
                        TACIT_UNDERSTANDING_TEST_30_USER, TACIT_UNDERSTANDING_TEST_30_USER, TACIT_UNDERSTANDING_TEST_30_USER, "", "");
                activityCommonRedis.addCommonSetData(done30RewardKey, aid);
                logger.info("send 30 users completed reward. aid={}", aid);
            }
        }
        handleQuizNewDeviceRewards(activityId, aid, uid);
    }

    private void handleQuizNewDeviceRewards(String activityId, String aid, String uid) {
        // 新设备答题奖励，每天上限5次
        String detailUserKey = getUserHashDetailKey(activityId, aid);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData != null && ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId())) {
            InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(uid); // 邀请者,答题者
            if (inviteFissionData == null) {
                logger.info("return. invite fission data not found. uid={}", uid);
                return;
            }
            String yaoQingUid = inviteFissionData.getUid(); // 邀请者,出题者
            if (!aid.equals(yaoQingUid)) {
                logger.info("return. not invite user. uid={} aid={}", uid, aid);
                return;
            }

            // Map<String, String> allDataMap = activityCommonRedis.getCommonHashAllMapStr(detailUserKey);
            // allDataMap.getOrDefault(QUIZ_TOTAL_NUM_FLIED, "0");

            String newDeviceJson = activityCommonRedis.getCommonHashStrValue(detailUserKey, QUIZ_MATCH_NEW_DEVICE_FLIED);
            Set<String> newUidSet;
            if (StringUtils.isEmpty(newDeviceJson)) {
                newUidSet = new HashSet<>();
            } else {
                newUidSet = JSON.parseObject(newDeviceJson, new TypeReference<Set<String>>() {
                });
            }
            if (newUidSet.contains(uid)) {
                logger.info("return. already answered. uid={}", uid);
                return;
            }

            String todayKey = String.format(QUIZ_MATCH_DAY_LIMIT_NUM_FLIED, getDayByBase(activityId, aid));
            int todayCount = activityCommonRedis.getCommonHashValue(detailUserKey, todayKey);
            if (todayCount < 5) {
                resourceKeyHandlerService.sendResourceData(aid, NEW_DEVICE_REWARD_KEY,
                        TACIT_UNDERSTANDING_TEST_NEW_DEVICE, TACIT_UNDERSTANDING_TEST_NEW_DEVICE, TACIT_UNDERSTANDING_TEST_NEW_DEVICE, "", "");
                activityCommonRedis.incCommonHashNum(detailUserKey, todayKey, 1);
                logger.info("send new device answer reward. uid={} todayCount={}", aid, todayCount + 1);
            }


            newUidSet.add(uid);
            // 保存问卷数据到Redis hash
            newDeviceJson = JSON.toJSONString(newUidSet);
            activityCommonRedis.setCommonHashData(detailUserKey, QUIZ_MATCH_NEW_DEVICE_FLIED, newDeviceJson);
            // 更新新用户答题人数
            activityCommonRedis.incCommonHashNum(detailUserKey, QUIZ_NEW_NUM_FLIED, 1);
            logger.info("send new device answer reward. aid={} todayCount={} newUidSet={}", aid, todayCount + 1, newUidSet);

        }
    }


    public MatchQuizVO matchQuizConfig(String activityId, String uid, String aid) {
        MatchQuizVO vo = new MatchQuizVO();
        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivity(activityId);
        vo.setStartTime(otherRankingActivityData.getStartTime());
        vo.setEndTime(otherRankingActivityData.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setUid(uid);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

        // 填充统计数据
        String detailUserKey = getUserHashDetailKey(activityId, uid);
        Map<String, String> dataAllMap = activityCommonRedis.getCommonHashAllMapStr(detailUserKey);

        String totalNum = dataAllMap.getOrDefault(QUIZ_TOTAL_NUM_FLIED, "0");
        String newNum = dataAllMap.getOrDefault(QUIZ_NEW_NUM_FLIED, "0");
        String matchNum = dataAllMap.getOrDefault(QUIZ_MATCH_NUM_FLIED, "0");
        String resultQuizJson = dataAllMap.getOrDefault(QUIZ_RESULT_FLIED, "");
        String newDeviceJson = dataAllMap.getOrDefault(QUIZ_MATCH_NEW_DEVICE_FLIED, "");

        Set<String> newUidSet;
        if (StringUtils.isEmpty(newDeviceJson)) {
            newUidSet = new HashSet<>();
        } else {
            newUidSet = JSON.parseObject(newDeviceJson, new TypeReference<Set<String>>() {
            });
        }

        List<MatchQuizVO.QuestionResultVO> matchAllVOList = getHistoryListPageRecord(activityId, uid, 1, TYPE_MATCH_ALL_RECORD, newUidSet);
        List<MatchQuizVO.QuestionResultVO> matchPerfectVOList = getHistoryListPageRecord(activityId, uid, 1, TYPE_MATCH_PERFECT_RECORD, newUidSet);

        vo.setTotalNum(Integer.valueOf(totalNum));
        vo.setNewNum(Integer.valueOf(newNum));
        vo.setMatchNum(Integer.valueOf(matchNum));
        vo.setIsGeneratedQuiz(StringUtils.isEmpty(resultQuizJson) ? 0 : 1);
        vo.setQuestionRecordList(matchAllVOList);
        vo.setQuestionMatchList(matchPerfectVOList);

        if (!StringUtils.isEmpty(aid)) {
            // aid 是出题人 uid 是答题人
            String indexKey = getIndexKey(aid, uid);
            MatchQuizVO.QuestionResultVO questionResult = getQuestionResultFromRedis(activityId, indexKey);
            if (questionResult != null) {
                // 回写结果
                ActorData actorAidData = actorDao.getActorDataFromCache(aid);
                MatchQuizVO.QuestionResultVO questionResultVO = new MatchQuizVO.QuestionResultVO();
                questionResultVO.setAid(aid); // 出题人
                questionResultVO.setAidName(actorAidData.getName());
                questionResultVO.setAidHead(ImageUrlGenerator.generateRoomUserUrl(actorAidData.getHead()));
                questionResultVO.setScore(questionResult.getScore());
                questionResultVO.setLevel(questionResult.getLevel());
                vo.setMyQuestionResult(questionResultVO);
            }
        }

        return vo;
    }

    private String checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return null;
            }
        }
        return "ok";
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        if (checkAc(uid) == null) {
            return;
        }
        //  uid 是出题者 aid 是答题者
        // 检查是否已经回答过这个用户的问卷
        // String allSetKey = getAllSetKey(ACTIVITY_ID);
        String indexKey = getIndexKey(uid, aid);
        if (getQuestionResultFromRedis(ACTIVITY_ID, indexKey) == null) {
            logger.info("user not answered this quiz. uid={} aid={}", uid, aid);
            return;
        }
        handleQuizNewDeviceRewards(ACTIVITY_ID, uid, aid);
    }


    public void leftPushAllHistoryList(String activityId, String targetUid, int type, int score, String answererUid) {
        String key = getHistoryListKey(activityId, targetUid, type);
        MatchQuizVO.HistoryRedisData historyRedisData = new MatchQuizVO.HistoryRedisData();
        historyRedisData.setAid(answererUid); // 记录回答者的uid
        historyRedisData.setScore(score);
        historyRedisData.setLevel(getScoreLevel(score));
        historyRedisData.setCtime(DateHelper.getNowSeconds());
        List<String> strList = new ArrayList<>();
        strList.add(JSONObject.toJSONString(historyRedisData));
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(key, strList, HISTORY_MAX_SIZE);
    }


    public List<MatchQuizVO.QuestionResultVO> getHistoryListPageRecord(String activityId, String uid, int page, int type, Set<String> newUidSet) {
        try {
            int start = (page - 1) * HISTORY_PAGE_SIZE;
            int end = page * HISTORY_PAGE_SIZE;
            String key = getHistoryListKey(activityId, uid, type);
            List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
            List<MatchQuizVO.QuestionResultVO> resultList = new ArrayList<>();

            for (String json : jsonList) {
                MatchQuizVO.HistoryRedisData rewardData = JSON.parseObject(json, MatchQuizVO.HistoryRedisData.class);
                MatchQuizVO.QuestionResultVO questionResultVO = new MatchQuizVO.QuestionResultVO();
                BeanUtils.copyProperties(rewardData, questionResultVO);
                ActorData actorData = actorDao.getActorDataFromCache(rewardData.getAid());
                questionResultVO.setAidName(actorData.getName());
                questionResultVO.setAidHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                questionResultVO.setIsNew(newUidSet.contains(rewardData.getAid()) ? 1 : 0);
                resultList.add(questionResultVO);
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getHistoryListPageRecord error", e);
            return Collections.emptyList();
        }
    }

    /**
     * 将QuestionResult测试结果保存到Redis
     */
    private void saveQuestionResultToRedis(String activityId, String indexKey,  MatchQuizVO.QuestionResultVO resultData) {
        try {
            String jsonData = JSON.toJSONString(resultData);
            activityCommonRedis.setCommonHashData(getAllHashKey(activityId), indexKey, jsonData);
            logger.info("保存QuestionResult结果到Redis成功: activityId={}, uid={}, resultData={}", activityId, indexKey, jsonData);
        } catch (Exception e) {
            logger.error("保存QuestionResult结果到Redis失败: activityId={}, uid={}, resultData={}", activityId, indexKey, JSON.toJSONString(resultData), e);
        }
    }

    /**
     * 从Redis获取QuestionResultVO测试结果
     */
    private MatchQuizVO.QuestionResultVO getQuestionResultFromRedis(String activityId, String indexKey) {
        try {
            String jsonData = activityCommonRedis.getCommonHashStrValue(getAllHashKey(activityId), indexKey);
            if (StringUtils.hasLength(jsonData)) {
                return JSON.parseObject(jsonData, MatchQuizVO.QuestionResultVO.class);
            }
        } catch (Exception e) {
            logger.error("从Redis获取QuestionResult结果失败: activityId={}, indexKey={}", activityId, indexKey, e);
        }
        return null;
    }

    /**
     * 书法家活动
     *
     * @param activityStage 1 1 创建题目  2  参与答题(完成答题)
     */
    private void doAnswerOrQuestionEvent(String activityId, String uid, int activityStage, String questionerRid) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name("Tacit Understanding Test");
        event.setActive_id(activityId);
        event.setActivity_stage(activityStage);
        event.setActivity_stage_desc(questionerRid);
        eventReport.track(new EventDTO(event));
    }
}
