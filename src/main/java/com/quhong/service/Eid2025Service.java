package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RechargeInfo;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.Eid2025VO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.ActivityHandler;
import com.quhong.handler.ActivityRechargeHandler;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 2025宰牲节
 */
@Service
public class Eid2025Service extends OtherActivityService implements ActivityRechargeHandler {


    private static final Logger logger = LoggerFactory.getLogger(Eid2025Service.class);
    private static final String ACTIVITY_TITLE_EN = "Eid al Adha";
    private static final String ACTIVITY_TITLE_AR = "تألق بعيد الأضحى";
    private static final String ACTIVITY_PRIZE_ICON = "https://cdn3.qmovies.tv/youstar/op_1717596913_FJRK.png";
    private static final String ACTIVITY_DESC = "Eid al Adha";
    public static final String ACTIVITY_ID = "683ef0c473e9615b4b04167e";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/catching_sheep2025/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/catching_sheep2025/?activityId=%s", ACTIVITY_ID);


    private static final String SNAPCHAT_SHARE_KEY = "HappyAdhaSnapchat";
    private static final String CHARGE_TASK_KEY = "HappyAdhaRecharge";
    private static final String NORMAL_SHEEP_CHARGE_KEY = "HappyAdhaDraw1";   // 普通羊-充值用户
    private static final String LUCKY_SHEEP_CHARGE_KEY = "HappyAdhaDraw3";       // 吉祥羊-充值用户
    private static final String NORMAL_SHEEP_COMMON_KEY = "HappyAdhaDraw2";   // 普通羊-非充值用户
    private static final String LUCKY_SHEEP_COMMON_KEY = "HappyAdhaDraw4";       // 吉祥羊-非充值用户
    private static final Map<String, String> DRAW_COLLECT_MAP = new HashMap<>();


    // 抓羊任务
    private static final List<Integer> CATCH_SHEEP_LEVEL_LIST = Arrays.asList(0, 5, 15, 35, 75);
    private static final List<String> CATCH_SHEEP_KEY_LIST = Arrays.asList("", "HappyAdhaSheep5", "HappyAdhaSheep15", "HappyAdhaSheep35", "HappyAdhaSheep75");

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final Map<String, String> RES_EVENT_MAP = new HashMap<>();
    private static final String CATCH_SHEEP_EVENT_TITLE = "Eid al Adha-Taskreward";

    static {
        DRAW_COLLECT_MAP.put("0-1", NORMAL_SHEEP_CHARGE_KEY);
        DRAW_COLLECT_MAP.put("1-1", LUCKY_SHEEP_CHARGE_KEY);
        DRAW_COLLECT_MAP.put("0-0", NORMAL_SHEEP_COMMON_KEY);
        DRAW_COLLECT_MAP.put("1-0", LUCKY_SHEEP_COMMON_KEY);

        RES_EVENT_MAP.put(NORMAL_SHEEP_CHARGE_KEY, "Eid al Adha-reward22");
        RES_EVENT_MAP.put(LUCKY_SHEEP_CHARGE_KEY, "Eid al Adha-reward100");
        RES_EVENT_MAP.put(NORMAL_SHEEP_COMMON_KEY, "Eid al Adha-reward11");
        RES_EVENT_MAP.put(LUCKY_SHEEP_COMMON_KEY, "Eid al Adha-reward99");
        RES_EVENT_MAP.put(CHARGE_TASK_KEY, "Eid al Adha-rechargereward");
        RES_EVENT_MAP.put(SNAPCHAT_SHARE_KEY, "Eid al Adha-Snapchatreward");
    }

    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int HISTORY_PAGE_SIZE = 20;
    private static final int GIFT_DRAW_BEANS = 100;

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;


    private String getLocalLockKey(String activityId, String uid) {
        return String.format("getLocalLockKey:%s:%s", activityId, uid);
    }

    private String getSheepChargeKey(int sheepType, int chargeType) {
        return String.format("%s-%s", sheepType, chargeType);
    }

    // 下面是redis的key

    // 活动期间的分享任务数据
    private String getHashAllTaskKey(String activityId) {
        return String.format("hash:catch:all:%s", activityId);
    }

    // 活动期间的每日充值任务数据
    private String getHashDayTaskKey(String activityId, String dateStr) {
        return String.format("hash:catch:day:%s:%s", activityId, dateStr);
    }

    // 抓羊机会key
    private String getCatchChanceKey(String activityId) {
        return String.format("zset:catch:chance:%s", activityId);
    }

    // 抓羊机会modkey
    private String getCatchChanceModKey(String activityId) {
        return String.format("zset:catch:chance:mod:%s", activityId);
    }

    // 抓羊数量key
    private String getCatchNumKey(String activityId) {
        return String.format("zset:catch:num:%s", activityId);
    }


    private String getListHistoryKey(String activityId, String uid) {
        return activityId + ":catch:draw:history:" + uid;
    }

    public Eid2025VO eid2025Config(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        String currentDate = getDayByBase(activityId, uid);
        String rechargeKey = getHashDayTaskKey(ACTIVITY_ID, currentDate);
        int shareState = activityCommonRedis.getCommonHashValue(getHashAllTaskKey(activityId), uid);
        int rechargeState = activityCommonRedis.getCommonHashValue(rechargeKey, uid);
        Eid2025VO vo = new Eid2025VO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setSnapchatState(shareState);
        vo.setChargeState(rechargeState);
        vo.setCatchSleepNum(activityCommonRedis.getCommonZSetRankingScore(getCatchNumKey(activityId), uid));
        vo.setCatchSleepChance(activityCommonRedis.getCommonZSetRankingScore(getCatchChanceKey(activityId), uid));
        return vo;
    }

    public Eid2025VO.Eid2025HistoryVO getHistoryListPageRecord(String activityId, String uid, int page) {
        Eid2025VO.Eid2025HistoryVO vo = new Eid2025VO.Eid2025HistoryVO();
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        String key = getListHistoryKey(activityId, uid);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        List<Eid2025VO.ResourceMetaTmp> resultList = new ArrayList<>();
        for (String json : jsonList) {
            Eid2025VO.ResourceMetaTmp rewardData = JSON.parseObject(json, Eid2025VO.ResourceMetaTmp.class);
            resultList.add(rewardData);
        }
        vo.setMyHistoryList(resultList);
        vo.setNextPage(resultList.size() < HISTORY_PAGE_SIZE ? -1 : page + 1);
        return vo;

    }

    public void setShareSnapchat(String activityId, String uid) {
        checkActivityTime(activityId);
        String key = getHashAllTaskKey(activityId);
        int state = activityCommonRedis.getCommonHashValue(key, uid);
        if (state == 1) {
            return;
        }
        activityCommonRedis.setCommonHashNum(key, uid, 1);
        String title = RES_EVENT_MAP.getOrDefault(SNAPCHAT_SHARE_KEY, ACTIVITY_TITLE_EN);
        resourceKeyHandlerService.sendResourceData(uid, SNAPCHAT_SHARE_KEY,
                title, title, title, "", "");
    }

    public Eid2025VO.Eid2025DrawVO catchSheep(String activityId, String uid, int zone, int sheepType, int amount) {
        checkActivityTime(activityId);
        if (amount != 1 && amount != 10 && amount != 50 && sheepType != 0 && sheepType != 1) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        Eid2025VO.Eid2025DrawVO drawVO = new Eid2025VO.Eid2025DrawVO();

        String catchChanceKey = getCatchChanceKey(activityId);
        int chargeType = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30) > 0 ? 1 : 0;
        List<Eid2025VO.ResourceMetaTmp> popList = new ArrayList<>();
        synchronized (stringPool.intern(getLocalLockKey(ACTIVITY_ID, uid))) {
            int totalIntegral = activityCommonRedis.getCommonZSetRankingScore(catchChanceKey, uid);
            if (totalIntegral <= 0 || totalIntegral < amount) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_SHEEP_CHANCE);
            }
            activityCommonRedis.incrCommonZSetRankingScoreSimple(catchChanceKey, uid, -amount);
            doReportSpecialItemsEvent(activityId, uid, 2, amount);
            if (zone > 0) {
                String sheepChargeKey = getSheepChargeKey(sheepType, chargeType);
                String resKey = DRAW_COLLECT_MAP.getOrDefault(sheepChargeKey, null);


                Map<String, Eid2025VO.ResourceMetaTmp> countMap = new HashMap<>();

                List<Eid2025VO.ResourceMetaTmp> srcList = new ArrayList<>();
                logger.info("catchSheep sheepType:{} chargeType:{} resKey:{} amount:{}", sheepType, chargeType, resKey, amount);
                for (int i = 0; i < amount; i++) {
                    ResourceKeyConfigData.ResourceMeta resourceMeta = drawOne(uid, resKey, RES_EVENT_MAP.getOrDefault(resKey, ACTIVITY_TITLE_EN));
                    Eid2025VO.ResourceMetaTmp historyMeta = new Eid2025VO.ResourceMetaTmp();
                    BeanUtils.copyProperties(resourceMeta, historyMeta);
                    historyMeta.setSheepType(sheepType);
                    historyMeta.setCtime(DateHelper.getNowSeconds());
                    srcList.add(historyMeta);

                    if (!countMap.containsKey(resourceMeta.getMetaId())) {
                        historyMeta.setAwardNum(1);
                        countMap.put(resourceMeta.getMetaId(), historyMeta);
                    } else {
                        countMap.get(resourceMeta.getMetaId())
                                .setAwardNum(countMap.get(resourceMeta.getMetaId()).getAwardNum() + 1);
                    }
                }
                //历史记录
                leftPushAllHistoryList(uid, srcList);
                //弹窗列表
                popList.addAll(countMap.values());

                // 下发等级任务
                String catchNumKey = getCatchNumKey(activityId);
                int oldNum = activityCommonRedis.getCommonZSetRankingScore(catchNumKey, uid);
                int nowNum = activityCommonRedis.incrCommonZSetRankingScoreSimple(catchNumKey, uid, amount);
                int oldIndex = getBaseIndexLevel(oldNum, CATCH_SHEEP_LEVEL_LIST);
                int afterIndex = getBaseIndexLevel(nowNum, CATCH_SHEEP_LEVEL_LIST);
                if (afterIndex > oldIndex) {
                    for (int i = oldIndex + 1; i <= afterIndex; i++) {
                        String levelResKey = CATCH_SHEEP_KEY_LIST.get(i);
                        if (!StringUtils.isEmpty(levelResKey)) {
                            resourceKeyHandlerService.sendResourceData(uid, levelResKey, CATCH_SHEEP_EVENT_TITLE, CATCH_SHEEP_EVENT_TITLE, CATCH_SHEEP_EVENT_TITLE, ACTIVITY_URL, "");
                        }
                    }
                }
                doDrawPrizeRecordEvent(uid, sheepType, chargeType, amount, amount, srcList);
            } else {
                doDrawPrizeRecordEvent(uid, sheepType, chargeType, amount, 0, null);
            }
        }
        drawVO.setDrawNum(amount);
        drawVO.setMyDrawyList(popList);
        return drawVO;
    }


    public void leftPushAllHistoryList(String uid, List<Eid2025VO.ResourceMetaTmp> srcList) {
        String key = getListHistoryKey(ACTIVITY_ID, uid);
        List<String> strList = new ArrayList<>();
        for (Eid2025VO.ResourceMetaTmp resourceMeta : srcList) {
            strList.add(JSONObject.toJSONString(resourceMeta));
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(key, strList, HISTORY_USER_MAX_SIZE);
    }


    public void sendGiftHandle(SendGiftData data, String activityId) {
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }

        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }
        // 礼物
        if (data == null || !RoomUtils.isVoiceRoom(data.getRoomId()) || StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }
        String uid = data.getFrom_uid();
        if (!checkAc(uid)) {
            return;
        }
        GiftData giftData = giftDao.getGiftFromCache(data.getGid());
        if (giftData != null && giftData.getGtype() == 2) {
            // 金币礼物不加
            return;
        }


        int sendBeans = data.getNumber() * data.getPrice() * data.getAid_list().size();
        String modKey = getCatchChanceModKey(ACTIVITY_ID);
        int extraPoints = activityCommonRedis.getCommonZSetRankingScore(modKey, uid);
        long totalBeans = extraPoints + sendBeans;
        int incNum = (int) (totalBeans / GIFT_DRAW_BEANS);
        int modNum = (int) (totalBeans % GIFT_DRAW_BEANS);
        if (incNum != 0) {
            String catchChanceKey = getCatchChanceKey(ACTIVITY_ID);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(catchChanceKey, uid, incNum);
            doReportSpecialItemsEvent(activityId, uid, 1, incNum);
        }
        activityCommonRedis.addCommonZSetRankingScore(modKey, uid, modNum);
    }

    @Override
    public void process(RechargeInfo rechargeInfo) {
        //充值
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }

        if (rechargeInfo == null || !StringUtils.hasLength(rechargeInfo.getUid()) || rechargeInfo.getRechargeMoney() == null) {
            logger.info("eid2025 process rechargeInfo is invalid");
            return;
        }
        int rechargeType = rechargeInfo.getRechargeType();
        if (rechargeType != 1) {
            logger.info("not honor recharge return rechargeInfo:{}", rechargeInfo);
            return;
        }
        if (!checkAc(rechargeInfo.getUid())) {
            return;
        }

        String currentDate = getDayByBase(ACTIVITY_ID, rechargeInfo.getUid());
        String rechargeKey = getHashDayTaskKey(ACTIVITY_ID, currentDate);
        int state = activityCommonRedis.getCommonHashValue(rechargeKey, rechargeInfo.getUid());
        if (state == 0) {
            activityCommonRedis.setCommonHashNum(rechargeKey, rechargeInfo.getUid(), 1);
            String title = RES_EVENT_MAP.getOrDefault(CHARGE_TASK_KEY, ACTIVITY_TITLE_EN);
            resourceKeyHandlerService.sendResourceData
                    (rechargeInfo.getUid(), CHARGE_TASK_KEY, title, title
                            , title, "", "");
        }
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }


    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(action);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }


    /**
     * 上报抽奖记录
     */
    private void doDrawPrizeRecordEvent(String uid, int sheepType, int chargeType, int amount, int successAmount, List<Eid2025VO.ResourceMetaTmp> resourceMetaList) {
        int senceDetail = sheepType == 0 ? 1 : 2;
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE_EN);
        event.setSence_detail(senceDetail);
        event.setTicket_type(0);
        event.setCost_ticket(amount);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(successAmount);
        event.setDraw_detail(chargeType > 0 ? "1" : "2");
        if (!CollectionUtils.isEmpty(resourceMetaList)) {
            event.setDraw_result(JSONObject.toJSONString(resourceMetaList));
        }
        eventReport.track(new EventDTO(event));
    }

    private void doEventCatchReport(String activityId, String uid, int zone, int amount) {
        ActiveDataEvent activeDataEvent = new ActiveDataEvent();
        activeDataEvent.setActive_id(activityId);
        activeDataEvent.setDate(DateHelper.ARABIAN.formatDateInDay());
        activeDataEvent.setUid(uid);
        activeDataEvent.setActive_data_desc(zone > 0 ? "sheep_catch_success_number" : "sheep_catch_fail_number");
        activeDataEvent.setNumber(amount);
        activeDataEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(activeDataEvent));
    }
}
