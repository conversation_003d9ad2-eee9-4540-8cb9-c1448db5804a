package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.data.vo.ArabicPainterVO;
import com.quhong.data.vo.OtherRankConfigVO;
import com.quhong.datas.DayTimeData;
import com.quhong.datas.HttpResult;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.PainterRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class SummerPhotoContestService extends OtherActivityService implements TaskMsgHandler {
    /**
     * 夏日摄影大赛
     */

    private static final Logger logger = LoggerFactory.getLogger(SummerPhotoContestService.class);
    private static final String MOMENT_PAINTER_ORIGIN = "summerPhotoContest";
    public static final String ACTIVITY_ID = "6879b992f882160d9431fbfc";
    private static final String CDN_DOMAIN = "https://cloudcdn.qmovies.tv/";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/summer_photo/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/summer_photo/?activityId=%s", ACTIVITY_ID);
    //    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? "https://static.youstar.live/Calligrapher/?activityId=6417c55b49490641c0f8159c&shareId=2" : "https://test2.qmovies.tv/Calligrapher/?activityId=6417c55b49490641c0f8159c&shareId=2";
    private static final String ACTIVITY_ICON = "https://cdn3.qmovies.tv/youstar/op_1742805228_albsfj_.jpg";
    private static final String ACTIVITY_TITLE_AR = "مسابقة التصوير الصيفية";
    private static final String ACTIVITY_TITLE_EN = "Summer Photo Contest";
    private static final List<String> TITLE_TEXT_AR = Arrays.asList(
            "البحر، الشمس، واستراحة قصيرة من زحمة الحياة.\n" +
                    "التقطت هذه اللحظة لتبقى…",
            "أحيانًا، صورة واحدة تكفي لتذكّرك بأن الحياة جميلة.\n" +
                    "شاركنا لقطة من صيفك. \uD83D\uDCF7☀\uFE0F",
            "مغامرة جديدة، مدينة جديدة، صيف لا يُنسى.\n" +
                    "التقطها وعدّها على الذكريات. \uD83C\uDF0D\uD83D\uDCF8",
            "الصيف لا يُحكى، بل يُعاش ويُصوَّر.",
            "كل صورة تُخلّد لحظة، وكل لحظة تُحكى.",
            "إن كانت الذكريات تلمع، فلا شك أنها تضيء بلون الصيف.",
            "في هذه اللحظة، نقرة على زر التصوير تُخلّد الأبد.",
            "لا شيء يُضاهي غروب شمس صيفي على شاطئ هادئ."
    );
    private static final List<String> COMMENT_TEXT_AR = Arrays.asList(
            "الصيف معاكم أحلى!",
            "والله جو!",
            "ما شاء الله! \uD83C\uDF1F",
            "رائع والله! \uD83D\uDC4C",
            "جمال! \uD83D\uDE0D",
            "كل التوفيق! \uD83D\uDE4F",
            "هالحياة! \uD83D\uDE0D",
            "ما شاء الله تبارك الله! \uD83C\uDF38",
            "هالجو يخليني أنسى الحر! \uD83C\uDF34",
            "دام الونس والعافية! \uD83D\uDE4F"
    );
    private static final List<String> COMMENT_TEXT_MD5_LIST = Arrays.asList(
            "c664fc61f7fc466aa2ca500cc6f639cc",
            "a3f3b63c0d0700b0420ae00477614301",
            "6856fc068a518b1e9d2b836006b31e44",
            "5688522fcf7634d6813bf5131e628aaa",
            "b265608f4f0790b85d1b0d58d81955b8",
            "fc8f43420a2ce14fb151b70840e56d70",
            "0203b13b221c74a486d04d4756353d5a",
            "b60f9d0e6af526c2bdfa024bfb0c030e",
            "afb75aef0317f3929f27fdab65c4ecec",
            "a75a3dd3c054961764503375f5f33feb"
    );

    /**
     * 榜单前3名奖励
     */
    public static final List<String> RANK_KEY_LIST = Arrays.asList(
            "ArabicPainterTop1", "ArabicPainterTop2", "ArabicPainterTop3");

    private static final String COMMENT_REWARD_KEY = "ArabicPainterCommentRewardKey";

    private static final int MAX_COMMENT_LIMIT = 5;

    private static int TOPIC_RID = 13493;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.COMMENT_MOMENT, CommonMqTaskConstant.LIKE_MOMENT);

    private static final List<Integer> LIKE_RECEIVE_LEVEL_LIST = Arrays.asList(0, 50, 100, 500, 1000, 2000);
    private static final List<String> RES_RECEIVE_LEVEL_LIST = Arrays.asList("", "50", "100", "500", "1000", "2000");

    private static final List<String> EVENT_LEVEL_LIST = Arrays.asList("", "50-event", "100-event", "500-event", "1000-event", "2000-event");
    /**
     * 埋点事件
     */
    public static final Map<Integer, String> EVENT_TITLE_MAP = new HashMap<Integer, String>() {
        {
            put(1, "sumer photo contest-task reward");
            put(2, "sumer photo contest-rank reward");
        }
    };
    @Resource
    private IMomentService iMomentService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private IDetectService idetectService;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private PainterRedis painterRedis;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private WhiteTestDao whiteTestDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            TOPIC_RID = 10132;
            for (String text : COMMENT_TEXT_AR) {
                String textMd5 = DigestUtils.md5DigestAsHex(text.getBytes(StandardCharsets.UTF_8));
                logger.info("textMd5:{}", textMd5);
            }
        }
    }

    public ArabicPainterVO painterConfig(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        ArabicPainterVO vo = new ArabicPainterVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        int enterType = 4;
        int nowTime = DateHelper.getNowSeconds();
        String now = getDay(uid);
        String totalInfoKey = getHashTotalKey(activityId);
        ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
        Map<String, Integer> dayMapLikeState = painterInfo.getDayMapLikeState();
        if (nowTime < activityData.getEndTime()) {
            if (painterInfo.getFirstEntryTime() == 0) {
                synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                    painterInfo.setFirstEntryTime(nowTime);
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
                }
                enterType = 1;
            } else {
                enterType = painterInfo.getFirstWriteTime() > 0 ? 2 : 3;
            }
        }
        vo.setEnterType(enterType);

        int pos = getPositionByDay(activityData.getStartTime(), Math.min(DateHelper.getNowSeconds(), activityData.getEndTime() - 10));
        int maxPos = TITLE_TEXT_AR.size() - 1;
        pos = Math.min(pos, maxPos);
        vo.setRecommendText(TITLE_TEXT_AR.get(pos));
        vo.setRecommendTextState(dayMapLikeState.getOrDefault(now, 0));

        List<ArabicPainterVO.ArabicPainterMomentInfoVO> myMomentListVO = new ArrayList<>();
        List<ArabicPainterVO.ArabicPainterMomentInfoVO> rankMomentListVO = new ArrayList<>();
        ArabicPainterVO.ArabicPainterMomentInfoVO myRankVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
        int uploadCount = 0;
        int myRank = -1;
        if (nowTime < activityData.getStartTime()) {
            String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
            ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
            itemVO.setDayStr(acStart);
            myMomentListVO.add(itemVO);
        } else if (nowTime >= activityData.getStartTime()) {
            List<MomentActivityData> myMomentActivities = momentActivityDao.findMomentList(uid, MOMENT_PAINTER_ORIGIN);
            if (!CollectionUtils.isEmpty(myMomentActivities)) {
                Map<String, MomentActivityData> midMap = myMomentActivities.stream()
                        .collect(Collectors.toMap(k -> k.get_id().toString(), Function.identity()));
                int endTime = Math.min(activityData.getEndTime() - 10, DateHelper.getNowSeconds()); // 偏移10秒为活动的最后一天
                endTime = Math.max(endTime, DateHelper.ARABIAN.stringDateToStampSecond(now)); // 这里可能按测试偏移天数转换，用于测试环境
                String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
                String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
                List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
                Map<String, String> dayMapMomentId = painterInfo.getDayMapMomentId();
                uploadCount = dayMapMomentId.size();
                for (DayTimeData item : dayTimeDataList) {
                    String day = item.getDate();
                    String mid = dayMapMomentId.get(day);
                    if (!StringUtils.isEmpty(mid)) {
                        MomentActivityData data = midMap.get(mid);
                        if (data != null) {
                            ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                            itemVO.setDayStr(day);
                            itemVO.setMomentId(mid);
                            itemVO.setPicture(data.getImgs().get(0).getThumbnail());
                            itemVO.setTitle(data.getText());
                            itemVO.setGiftTotalPrice(data.getGiftTotalPrice());
                            itemVO.setComments(data.getComments());
                            itemVO.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
                            if (data.getLikes() != null && data.getLikes().contains(uid)) {
                                itemVO.setLikeStatus(1);
                            } else {
                                itemVO.setLikeStatus(0);
                            }
                            myMomentListVO.add(itemVO);
                        }
                    } else if (day.equals(now)) {
                        // 之前有上传作品，当天没有上传的作品，构造一个空数据
                        ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                        itemVO.setDayStr(day);
                        myMomentListVO.add(itemVO);
                    }
                }
            } else {
                // 如果没有上传任何作品，只构造一个当天的数据
                ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                itemVO.setDayStr(now);
                myMomentListVO.add(itemVO);
            }
        }

        List<MomentActivityData> mMomentList = momentActivityDao.momentRankingByGiftTotalPrice(MOMENT_PAINTER_ORIGIN, activityData.getStartTime(), activityData.getEndTime(), 20);
        if (!CollectionUtils.isEmpty(mMomentList)) {
            List<String> uidList = mMomentList.stream().map(MomentActivityData::getUid).collect(Collectors.toList());
            myRank = uidList.indexOf(uid);
            myRank = myRank == -1 ? -1 : myRank + 1;
            int rank = 0;
            for (MomentActivityData item : mMomentList) {
                rank++;
                if (rank > 10) {
                    break;
                }
                ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                itemVO.setRank(rank);
                itemVO.setMomentId(item.get_id().toString());
                itemVO.setPicture(item.getImgs().get(0).getThumbnail());
                itemVO.setTitle(item.getText());
                itemVO.setGiftTotalPrice(item.getGiftTotalPrice());
                itemVO.setComments(item.getComments());
                itemVO.setLikes(item.getLikes() != null ? item.getLikes().size() : 0);
                if (item.getLikes() != null && item.getLikes().contains(uid)) {
                    itemVO.setLikeStatus(1);
                } else {
                    itemVO.setLikeStatus(0);
                }

                ActorData actorData = actorDao.getActorDataFromCache(item.getUid());
                itemVO.setName(actorData.getName());
                itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                itemVO.setAid(item.getUid());
                rankMomentListVO.add(itemVO);
            }

        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        myRankVO.setUploadCount(uploadCount);
        myRankVO.setName(actorData.getName());
        myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        myRankVO.setRank(myRank);

        vo.setMyMomentListVO(myMomentListVO);
        vo.setMyRankVO(myRankVO);
        vo.setRankMomentListVO(rankMomentListVO);

        return vo;

    }

    /**
     * 开始鉴赏
     *
     * @param activityId
     * @param uid
     * @return
     */
    public ArabicPainterVO recommendDayCreateList(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ArabicPainterVO vo = new ArabicPainterVO();
        List<ArabicPainterVO.ArabicPainterDayVO> arabicPainterDayVOList = new ArrayList<>();
        int nowTime = DateHelper.getNowSeconds();

        if (nowTime >= activityData.getStartTime()) {
            int endTime = Math.min(activityData.getEndTime() - 10, DateHelper.getNowSeconds()); // 偏移10秒为活动的最后一天
            String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
            String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
            List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
            for (DayTimeData item : dayTimeDataList) {
                String day = item.getDate();
                int s = DateHelper.ARABIAN.stringDateToStampSecond(day);
                int e = (int) (s + TimeUnit.DAYS.toSeconds(1));
                List<MomentActivityData> rankList = momentActivityDao.momentRankingCache(MOMENT_PAINTER_ORIGIN, s, e, 20, 0);
                if (!CollectionUtils.isEmpty(rankList)) {
                    List<ArabicPainterVO.ArabicPainterMomentInfoVO> recommendMomentList = new ArrayList<>();
                    for (MomentActivityData top1Data : rankList) {
                        ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                        itemVO.setDayStr(day);
                        itemVO.setMomentId(top1Data.get_id().toString());
                        itemVO.setPicture(top1Data.getImgs().get(0).getThumbnail());
                        itemVO.setTitle(top1Data.getText());
                        itemVO.setGiftTotalPrice(top1Data.getGiftTotalPrice());
                        itemVO.setComments(top1Data.getComments());
                        itemVO.setLikes(top1Data.getLikes() != null ? top1Data.getLikes().size() : 0);
                        if (top1Data.getLikes() != null && top1Data.getLikes().contains(uid)) {
                            itemVO.setLikeStatus(1);
                        } else {
                            itemVO.setLikeStatus(0);
                        }
                        ActorData actorData = actorDao.getActorDataFromCache(top1Data.getUid());
                        itemVO.setName(actorData.getName());
                        itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                        itemVO.setAid(top1Data.getUid());
                        recommendMomentList.add(itemVO);
                    }
                    ArabicPainterVO.ArabicPainterDayVO dayVO = new ArabicPainterVO.ArabicPainterDayVO();
                    dayVO.setDayStr(day);
                    dayVO.setRecommendMomentListVOList(recommendMomentList);
                    arabicPainterDayVOList.add(dayVO);
                }
            }
        }

        if (CollectionUtils.isEmpty(arabicPainterDayVOList)) {
            ArabicPainterVO.ArabicPainterDayVO dayVO = new ArabicPainterVO.ArabicPainterDayVO();
            String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
            dayVO.setDayStr(acStart);
            dayVO.setRecommendMomentListVOList(Collections.emptyList());
            arabicPainterDayVOList.add(dayVO);
        }

        vo.setArabicPainterDayVOList(arabicPainterDayVOList);
        vo.setRecommendCommentList(COMMENT_TEXT_AR);
        vo.setTopicRid(TOPIC_RID);

        String nowDay = getDay(uid);
        String totalInfoKey = getHashTotalKey(activityId);
        ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
        Map<String, Set<String>> dayMapCommentAidList = painterInfo.getDayMapCommentAidList();
        Set<String> dayAidList = dayMapCommentAidList.getOrDefault(nowDay, new HashSet<>());
        vo.setCommentCount(dayAidList.size());
        return vo;
    }

    public void painterPicturePush(String activityId, String uid, PainterPictureDTO dto) {
        checkActivityTime(activityId);
        String picture = dto.getPicture();
        String momentText = dto.getMomentText();
        int slang = dto.getSlang();

        if (StringUtils.isEmpty(picture)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (StringUtils.isEmpty(momentText)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (momentText.length() > 50) {
            logger.info("length too long momentText length:{}", momentText.length());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        String nowDay = getDay(uid);
        int startTime = DateHelper.ARABIAN.stringDateToStampSecond(nowDay);
        int endTime = (int) (startTime + TimeUnit.DAYS.toSeconds(1));
        MomentActivityData momentActivityData = momentActivityDao.findMomentOneByTime(uid, MOMENT_PAINTER_ORIGIN, startTime, endTime);
        if (momentActivityData != null) {
            throw new CommonH5Exception(ActivityHttpCode.ARABIC_PAINTER_ALREADY_JOIN);
        }
        if (idetectService.detectImage(new ImageDTO(CDN_DOMAIN + picture, "activity")).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_IMAGE);
        }
        if (idetectService.detectText(new TextDTO(momentText, "activity")).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }

        synchronized (stringPool.intern(getLocalPublishUserKey(uid))) {
            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText(momentText);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(ACTIVITY_ID);
            publishMomentDTO.setLocation(MOMENT_PAINTER_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("2000");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));

            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setType(6);
            quote.setIcon(ACTIVITY_ICON);
            quote.setContent(ACTIVITY_TITLE_AR);
            quote.setAction(ACTIVITY_URL);

            publishMomentDTO.setQuote(quote);

            publishMomentDTO.setTopicRid(TOPIC_RID);

            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if (result.getCode() == 20) {
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            String mid = result.getData();
            if (!StringUtils.isEmpty(mid)) {
                String now = getDay(uid);
                String totalInfoKey = getHashTotalKey(activityId);
                ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
                synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                    Map<String, String> dayMapMomentId = painterInfo.getDayMapMomentId();
                    dayMapMomentId.put(now, mid);
                    if (painterInfo.getFirstWriteTime() == 0) {
                        painterInfo.setFirstWriteTime(DateHelper.getNowSeconds());
                    }
                    doCalligrapherEvent(ACTIVITY_ID, uid, 1);
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
                    activityCommonRedis.setCommonHashData(getHashMidAllKey(activityId), mid, uid);
                }
                cacheDataService.delSetCache(getHashMidAllKey(activityId), 1);
            }
        }
    }


    /**
     * 给今日推荐文案点赞
     *
     * @param activityId
     * @param uid
     */
    public void painterLikeMomentText(String activityId, String uid) {
        checkActivityTime(activityId);
        String now = getDay(uid);
        String totalInfoKey = getHashTotalKey(activityId);
        ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
        Map<String, Integer> dayMapLikeState = painterInfo.getDayMapLikeState();
        if (dayMapLikeState.getOrDefault(now, 0) == 0) {
            synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                dayMapLikeState.put(now, 1);
                activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
            }
        }
    }


    public void painterLike(String activityId, String uid, String momentId) {
        checkActivityTime(activityId);
        if (StringUtils.isEmpty(momentId)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ApiResult<HttpCode> result = iMomentService.likeMoment(uid, momentId);
        if (result.getCode().getCode() == 20) {
            logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        if (item.equals(CommonMqTaskConstant.COMMENT_MOMENT)) {
            syncHandle(uid, data);
        }

    }

    private void syncHandle(String uid, CommonMqTopicData mqData) {
        String eventId = checkAc(uid, mqData);
        if (eventId == null) {
            return;
        }
//        MomentActivityData momentLikeActivityData = null;
//        if (mqData.getItem().equals(CommonMqTaskConstant.LIKE_MOMENT)) {
//            momentLikeActivityData = momentActivityDao.findMomentOne(mqData.getHandleId(), MOMENT_PAINTER_ORIGIN);
//            if (momentLikeActivityData == null) {
//                return;
//            }
//            uid = momentLikeActivityData.getUid();
//        }
        String now = getDay(uid);
        String totalInfoKey = getHashTotalKey(null);

        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            if (mqData.getItem().equals(CommonMqTaskConstant.COMMENT_MOMENT)) {
                ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
                String aid = mqData.getAid();
//                String mid = mqData.getHandleId();
                Map<String, Set<String>> dayMapCommentAidList = painterInfo.getDayMapCommentAidList();
                Set<String> levelAidList = dayMapCommentAidList.getOrDefault(now, new HashSet<>());
//                MomentActivityData momentActivityData = momentActivityDao.findMomentById(mid);
//                if (momentActivityData != null && MOMENT_PAINTER_ORIGIN.equals(momentActivityData.getLocation())
//                        && levelAidList.size() < MAX_COMMENT_LIMIT && !levelAidList.contains(aid)) {
                if (levelAidList.size() < MAX_COMMENT_LIMIT && !levelAidList.contains(aid)) {
                    levelAidList.add(aid);
                    dayMapCommentAidList.put(now, levelAidList);
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
                    if (levelAidList.size() == MAX_COMMENT_LIMIT) {
                        handleRes(uid, COMMENT_REWARD_KEY, 1);
                        doCalligrapherEvent(ACTIVITY_ID, uid, 2);
                    }
                }
            } else if (mqData.getItem().equals(CommonMqTaskConstant.LIKE_MOMENT)) {
                MomentActivityData momentLikeActivityData = momentActivityDao.findMomentOne(mqData.getHandleId(), MOMENT_PAINTER_ORIGIN);
                if (momentLikeActivityData == null) {
                    return;
                }
                uid = momentLikeActivityData.getUid();
                ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
                int oldCount = painterInfo.getReceivedLikeCount();
                int newCount = CollectionUtils.isEmpty(momentLikeActivityData.getLikes()) ? 0 : momentLikeActivityData.getLikes().size();
                if (newCount > oldCount) {
                    painterInfo.setReceivedLikeCount(newCount);
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));

                    int oldLevel = getNewBaseIndexLevel(oldCount, LIKE_RECEIVE_LEVEL_LIST);
                    int newLevel = getNewBaseIndexLevel(newCount, LIKE_RECEIVE_LEVEL_LIST);
                    if (newLevel > oldLevel) {
                        // 跨级的，需要把之前等级的key都下发
                        for (int level = oldLevel + 1; level <= newLevel; level++) {
                            if (level < RES_RECEIVE_LEVEL_LIST.size() && StringUtils.hasLength(RES_RECEIVE_LEVEL_LIST.get(level))) {
                                String resKey = RES_RECEIVE_LEVEL_LIST.get(level);
                                String eventTitle = EVENT_LEVEL_LIST.get(level);
                                resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
                                logger.info("sendGiftHandle reward sent to uid: {}, level: {}, resKey: {}", uid, level, resKey);
                            }
                        }
                    }
                }
            }
        }
    }

    private String checkAc(String uid, CommonMqTopicData mqData) {
        String eventId = null;
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return null;
            }
        }
        if (StringUtils.isEmpty(mqData.getHandleId())) {
            return null;
        }
        String mid = mqData.getHandleId();
        Set<String> allMidSet = cacheDataService.getAllSetCache(getHashMidAllKey(ACTIVITY_ID), 1);
        if (!allMidSet.contains(mid)) {
            logger.info("mid:{} not in allMidSet", mid);
            return null;
        }

        if (mqData.getItem().equals(CommonMqTaskConstant.COMMENT_MOMENT)) {
            logger.info("uid:{} aid:{} mid:{} textMD5:{}", uid, mqData.getAid(), mqData.getHandleId(), mqData.getJsonData());
            if (StringUtils.isEmpty(mqData.getJsonData()) || !COMMENT_TEXT_MD5_LIST.contains(mqData.getJsonData())) {
                return null;
            }
        }

        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return null;
        }
        return "ok";
    }


    // 下发榜单奖励
    public void distributionRanking() {
        try {
            OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
            int length = 3;
            List<MomentActivityData> mMomentList = momentActivityDao.momentRankingByGiftTotalPrice(MOMENT_PAINTER_ORIGIN,
                    activityData.getStartTime(), activityData.getEndTime(), length);
            int rank = 1;
            for (MomentActivityData item : mMomentList) {
                if (rank > length) {
                    continue;
                }
                String aid = item.getUid();
                String resKey = RANK_KEY_LIST.get(rank - 1);
                handleRes(aid, resKey, 2);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, int atype) {
        String eventTitle = EVENT_TITLE_MAP.getOrDefault(atype, "");
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    public OtherRankConfigVO testUidDay(String activityId, int cmd, String uid, int addDays) {
        if(ServerConfig.isProduct()){
            throw new CommonH5Exception(ActivityHttpCode.AUTH_ERROR);
        }
        OtherRankConfigVO otherRankConfigVO = new OtherRankConfigVO();
        if (cmd == 1) {
            // 设置偏移天数
            activityCommonRedis.setCommonHashData(getHashTestDayKey(activityId), uid, String.valueOf(addDays));
            otherRankConfigVO.setScore(addDays);
        } else if (cmd == 2) {
            // 查询偏移天数
            int add = activityCommonRedis.getCommonHashValue(getHashTestDayKey(activityId), uid);
            otherRankConfigVO.setScore(add);
        }
        return otherRankConfigVO;
    }

    private String getDay(String uid) {
        return getDay(uid, true);
    }

    private String getDay(String uid, boolean baseIsToday) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isNotProduct() || activityData.getAcNameEn().startsWith("test")) {
            return getTestDays(uid, baseIsToday);
        }
        return baseIsToday ? DateHelper.ARABIAN.formatDateInDay()
                : DateHelper.ARABIAN.getYesterdayStr(new Date());
    }


    private String getTestDays(String uid, boolean baseIsToday) {
        int addDays = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
        logger.info("test add uid:{} days:{}", uid, addDays);
        addDays = baseIsToday ? addDays : addDays - 1;
        return todayMinusDays(addDays);
    }

    /**
     * 书法家活动
     *
     * @param activityStage 1 上传作品  2 完成评论任务
     */
    private void doCalligrapherEvent(String activityId, String uid, int activityStage) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_stage(activityStage);
        eventReport.track(new EventDTO(event));
    }


    /**
     * @param days
     * @return
     */
    private String todayMinusDays(int days) {
        days = -days; // -1为明天  1为昨天
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }

    private int getPositionByDay(int startTime, int endTime) {
        if (startTime >= endTime) {
            return 0;
        }
        String before = DateHelper.ARABIAN.formatDateInDay
                (new Date(startTime * 1000L));
        String after = DateHelper.ARABIAN.formatDateInDay
                (new Date(endTime * 1000L));
        int d = (int) (DateSupport.calculateDaysBetween(before, after));
        return d;
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:photo_painter:uid:" + uid;
    }

    private String getLocalPublishUserKey(String uid) {
        return "lock:photo_painter:publish:uid:" + uid;
    }

    private String getHashTotalKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":photo_painter:total";
    }

    private String getHashMidAllKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":photo_mid_painter:all";
    }

    private String getHashTestDayKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":test:uid:day";
    }
}
