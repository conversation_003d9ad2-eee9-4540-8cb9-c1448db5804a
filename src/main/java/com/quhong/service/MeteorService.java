package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.MeteorVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.msg.obj.LuckyGiftRewardObject;
import com.quhong.msg.room.RoomLuckGiftRewardMsg;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class MeteorService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(MeteorService.class);
    private static final String ACTIVITY_TITLE = "Meteor Activity";
    private static final String BAG_KEY = "bagStatus";
    public static final String ACTIVITY_ID = "659cfd32095cc2d7bed9b2ff";
    private static final Map<String, Integer> METEOR_RATE_MAP = new HashMap<>();
    private static final Map<String, String> METEOR_IMAGE_MAP = new HashMap<>();
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<String> CARD_LIST = Arrays.asList("family", "happy", "health", "love", "peace", "rich");

    static {
        METEOR_RATE_MAP.put("health", 20);
        METEOR_RATE_MAP.put("love", 5);
        METEOR_RATE_MAP.put("happy", 15);
        METEOR_RATE_MAP.put("rich", 10);
        METEOR_RATE_MAP.put("peace", 30);
        METEOR_RATE_MAP.put("family", 20);

        METEOR_IMAGE_MAP.put("family", "https://cdn3.qmovies.tv/youstar/op_1704786271_Family.png");
        METEOR_IMAGE_MAP.put("happy", "https://cdn3.qmovies.tv/youstar/op_1704786271_Happy.png");
        METEOR_IMAGE_MAP.put("health", "https://cdn3.qmovies.tv/youstar/op_1704786271_Health.png");
        METEOR_IMAGE_MAP.put("love", "https://cdn3.qmovies.tv/youstar/op_1704786271_Love.png");
        METEOR_IMAGE_MAP.put("peace", "https://cdn3.qmovies.tv/youstar/op_1704786271_Peace.png");
        METEOR_IMAGE_MAP.put("rich", "https://cdn3.qmovies.tv/youstar/op_1704786272_Rich.png");
    }

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;

    public String getHashActivityId(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }

    public String getSetTnKey(String activityId){
        return String.format("SetTn:%s", activityId);
    }

    public MeteorVO meteorConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        Map<String, Integer> taskNumMap =  activityCommonRedis.getCommonHashAll(getHashActivityId(activityId, uid));
        MeteorVO vo = JSON.parseObject(JSON.toJSONString(taskNumMap), MeteorVO.class);
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        String setTnKey = getSetTnKey(activityId);
        vo.setDeviceGet(activityCommonRedis.isCommonSetData(setTnKey, tnId));

        return vo;
    }

    // 初始化奖池
    public void commonInitPoolSize(String activityId){
        List<String> poolList = new ArrayList<>();
        for (String meteor : METEOR_RATE_MAP.keySet()) {
            int keySize = METEOR_RATE_MAP.get(meteor);
            for (int i = 0; i < keySize; i++) {
                poolList.add(meteor);
            }
        }
        Collections.shuffle(poolList);
        activityCommonRedis.rightPushAllCommonList(activityId, poolList);
    }

    private void initPoolSize(String activityId){
        int poolSize = activityCommonRedis.getCommonListSize(activityId);
        if(poolSize <= 20){
            commonInitPoolSize(activityId);
        }
    }

    public void distributionMeteorAward(String uid, String activityId){
        try{

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> taskNumMap =  activityCommonRedis.getCommonHashAll(hashActivityId);

            for (String card : CARD_LIST) {
                int cardStatus = taskNumMap.getOrDefault(card, 0);
                if(cardStatus <= 0){
                    return;
                }
            }

            int bagStatus = taskNumMap.getOrDefault(BAG_KEY, 0);
            if(bagStatus > 0){
                return;
            }

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            String tnId = actorData.getTn_id();
            if(StringUtils.isEmpty(tnId)){
                logger.error("distributionMeteorAward no find tnId uid:{}", uid);
                return;
            }

            String setTnKey = getSetTnKey(activityId);
            int isTnFlag = activityCommonRedis.isCommonSetData(setTnKey, tnId);
            if(isTnFlag > 0){
                logger.error("distributionMeteorAward tnId check uid:{}", uid);
                return;
            }
            synchronized (stringPool.intern(ACTIVITY_TITLE + tnId)) {
                distributionService.sendRewardResource(uid, 315, ActivityRewardTypeEnum.getEnumByName("background"), 7, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                distributionService.sendRewardResource(uid, 22, ActivityRewardTypeEnum.getEnumByName("ripple"), 7, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 1000, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);

                activityCommonRedis.addCommonSetData(setTnKey, tnId);
                activityCommonRedis.setCommonHashNum(hashActivityId, BAG_KEY, 1);

            }
        }catch (Exception e){
            logger.error("distributionLionRankingAward error: {}", e.getMessage(), e);
        }
    }



    public void handleSendGiftData(SendGiftData giftData, String activityId) {
        int totalNum = giftData.getNumber() * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();

        String hashActivityId = getHashActivityId(activityId, fromUid);
        Map<String, Integer> taskNumMap =  activityCommonRedis.getCommonHashAll(hashActivityId);

        // 抽奖及合并奖励
        Map<String, Integer> rewardMap = new HashMap<>();
        for (int i=0; i < totalNum; i++){
            initPoolSize(activityId);
            String cardKey = activityCommonRedis.leftPopCommonListKey(activityId);

            int taskNum = taskNumMap.getOrDefault(cardKey, 0);
            if(taskNum <= 0){
                activityCommonRedis.setCommonHashNum(hashActivityId, cardKey, 1);
            }

            rewardMap.compute(cardKey, (k, v) -> {
                if (null == v) {
                    v = 1;
                }else {
                    v += 1;
                }
                return v;
            });
        }

        // 奖励下发
        distributionMeteorAward(fromUid, activityId);

        if (!rewardMap.isEmpty()){
            RoomLuckGiftRewardMsg rewardMsg = new RoomLuckGiftRewardMsg();
            List<LuckyGiftRewardObject> luckyGiftRewardList = new ArrayList<>();
            for (String key : rewardMap.keySet()) {
                LuckyGiftRewardObject rewardObject = new LuckyGiftRewardObject();
                rewardObject.setIcon(METEOR_IMAGE_MAP.get(key));
                rewardObject.setName("");
                rewardObject.setType(2);
                rewardObject.setValue(rewardMap.get(key));
                luckyGiftRewardList.add(rewardObject);
            }

            rewardMsg.setAid(fromUid);
            rewardMsg.setA_type(0);
            rewardMsg.setType(2);
            rewardMsg.setLucky_gift_reward(luckyGiftRewardList);
            marsMsgActivityService.asyncSendPlayerMsg(roomId, fromUid, fromUid, rewardMsg, false);
        }
    }

}
