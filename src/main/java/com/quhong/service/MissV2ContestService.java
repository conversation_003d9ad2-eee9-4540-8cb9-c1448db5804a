package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.CompetitionVO;
import com.quhong.data.vo.MissV2ContestVO;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MissV2ContestService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(MissV2ContestService.class);
    public static final String ACTIVITY_ID = "66eeaa2b5835362f3818ddd4";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/rose_youstar_2024/?activityId=%s", ACTIVITY_ID): String.format("https://test2.qmovies.tv/rose_youstar_2024/?activityId=%s", ACTIVITY_ID);
    public static final String BASE_BEAN_NAME = "baseBean";
    public static final List<String> QUEEN_ACTIVITY_ID_LIST = Arrays.asList("66ed402aa15760d6955a6ac1", "66ed402aa15760d6955a6ac2", "66ed402aa15760d6955a6ac3", "66ed402aa15760d6955a6ac4", "66ed402aa15760d6955a6ac5", "66ed402aa15760d6955a6ac6");
    public static final List<String> KING_ACTIVITY_ID_LIST = Arrays.asList("66ed402aa15760d6955a6ab1", "66ed402aa15760d6955a6ab2", "66ed402aa15760d6955a6ab3", "66ed402aa15760d6955a6ab4", "66ed402aa15760d6955a6ab5", "66ed402aa15760d6955a6ab6");
    private static final List<String> KING_ROUND_1_KEY_LIST = Arrays.asList("anniversaryV7Round1KingTop1", "anniversaryV7Round1KingTop2", "anniversaryV7Round1KingTop3",
            "anniversaryV7Round1KingTop4-10", "anniversaryV7Round1KingTop11-16", "anniversaryV7Round1KingTop17-24", "anniversaryV7Round1KingTop25-32", "anniversaryV7Round1KingTop33-64");
    private static final List<String> KING_ROUND_2_4_KEY_LIST = Arrays.asList("anniversaryV7Round2-4KingTop1", "anniversaryV7Round2-4KingTop2-8", "anniversaryV7Round2-4KingTop9-16", "anniversaryV7Round2-4KingLose");
    private static final List<String> KING_ROUND_5_KEY_LIST = Arrays.asList("anniversaryV7Round5KingTop1", "anniversaryV7Round5KingTop2-4", "anniversaryV7Round5KingTop5-8");
    private static final List<String> KING_ROUND_6_KEY_LIST = Arrays.asList("anniversaryV7Round6KingTop1", "anniversaryV7Round6KingTop2", "anniversaryV7Round6KingTop3", "anniversaryV7Round6KingTop4");

    private static final List<String> QUEEN_ROUND_1_KEY_LIST = Arrays.asList("anniversaryV7Round1QueenTop1", "anniversaryV7Round1QueenTop2", "anniversaryV7Round1QueenTop3",
            "anniversaryV7Round1QueenTop4-10", "anniversaryV7Round1QueenTop11-16", "anniversaryV7Round1QueenTop17-24", "anniversaryV7Round1QueenTop25-32", "anniversaryV7Round1QueenTop33-64");
    private static final List<String> QUEEN_ROUND_2_4_KEY_LIST = Arrays.asList("anniversaryV7Round2-4QueenTop1", "anniversaryV7Round2-4QueenTop2-8", "anniversaryV7Round2-4QueenTop9-16", "anniversaryV7Round2-4QueenLose");
    private static final List<String> QUEEN_ROUND_5_KEY_LIST = Arrays.asList("anniversaryV7Round5QueenTop1", "anniversaryV7Round5QueenTop2-4", "anniversaryV7Round5QueenTop5-8");
    private static final List<String> QUEEN_ROUND_6_KEY_LIST = Arrays.asList("anniversaryV7Round6QueenTop1", "anniversaryV7Round6QueenTop2", "anniversaryV7Round6QueenTop3", "anniversaryV7Round6QueenTop4");

    private static final TreeMap<Integer, Integer> KING_ROUND_1_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_2_4_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_5_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_6_MAP = new TreeMap<>();

    private static final TreeMap<Integer, Integer> QUEEN_ROUND_1_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_2_4_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_5_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_6_MAP = new TreeMap<>();
    static {
        KING_ROUND_1_MAP.put(1, 0);
        KING_ROUND_1_MAP.put(2, 1);
        KING_ROUND_1_MAP.put(3, 2);
        KING_ROUND_1_MAP.put(10, 3); // 4-10 映射到同一个索引
        KING_ROUND_1_MAP.put(16, 4); // 11-16 映射到同一个索引
        KING_ROUND_1_MAP.put(24, 5); // 17-24 映射到同一个索引
        KING_ROUND_1_MAP.put(32, 6); // 25-32 映射到同一个索引
        KING_ROUND_1_MAP.put(64, 7); // 33-64 映射到同一个索引
        KING_ROUND_2_4_MAP.put(1, 0);
        KING_ROUND_2_4_MAP.put(8, 1);
        KING_ROUND_2_4_MAP.put(16, 2);
        KING_ROUND_2_4_MAP.put(64, 3);
        KING_ROUND_5_MAP.put(1, 0);
        KING_ROUND_5_MAP.put(4, 1);
        KING_ROUND_5_MAP.put(8, 2);
        KING_ROUND_6_MAP.put(1, 0);
        KING_ROUND_6_MAP.put(2, 1);
        KING_ROUND_6_MAP.put(3, 2);
        KING_ROUND_6_MAP.put(4, 3);

        QUEEN_ROUND_1_MAP.put(1, 0);
        QUEEN_ROUND_1_MAP.put(2, 1);
        QUEEN_ROUND_1_MAP.put(3, 2);
        QUEEN_ROUND_1_MAP.put(10, 3); // 4-10 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(16, 4); // 11-16 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(24, 5); // 17-24 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(32, 6); // 25-32 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(64, 7); // 33-64 映射到同一个索引
        QUEEN_ROUND_2_4_MAP.put(1, 0);
        QUEEN_ROUND_2_4_MAP.put(8, 1);
        QUEEN_ROUND_2_4_MAP.put(16, 2);
        QUEEN_ROUND_2_4_MAP.put(64, 3);
        QUEEN_ROUND_5_MAP.put(1, 0);
        QUEEN_ROUND_5_MAP.put(4, 1);
        QUEEN_ROUND_5_MAP.put(8, 2);
        QUEEN_ROUND_6_MAP.put(1, 0);
        QUEEN_ROUND_6_MAP.put(2, 1);
        QUEEN_ROUND_6_MAP.put(3, 2);

    }

    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;


    private String getHashActivityId(String activityId){
        return String.format("missContestConfig:%s", activityId);
    }

    public MissV2ContestVO missV2ContestConfig(String activityId, String uid, int missType) {

        List<String> activityIdList = missType == 0 ? QUEEN_ACTIVITY_ID_LIST : KING_ACTIVITY_ID_LIST;
        List<OtherRankingActivityData> activityDataList = otherActivityService.getOtherRankingActivityList(activityIdList);
        Map<String, OtherRankingActivityData> activityDataMap = activityDataList.stream().collect(Collectors.toMap(k -> k.get_id().toString(), Function.identity()));
        MissV2ContestVO vo = new MissV2ContestVO();
        Map<String, String> hashAllMap = activityCommonRedis.getCommonHashAllMapStr(this.getHashActivityId(activityId));

        List<MissV2ContestVO.MissPkVO> allPkRankList = new ArrayList<>();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (String activityIdItem : activityIdList) {
            OtherRankingActivityData activityData= activityDataMap.get(activityIdItem);
            if(activityData == null){
                continue;
            }

            MissV2ContestVO.MissPkVO missPkVO = new MissV2ContestVO.MissPkVO();
            missPkVO.setActivityId(activityIdItem);
            missPkVO.setStartTime(activityData.getStartTime());
            missPkVO.setEndTime(activityData.getEndTime());
            int activityType = activityData.getActivityType();  // 0: 默认榜单 1: pk榜
            if(activityType == 0){
                int rankType = missType == 0 ? ActivityConstant.RECEIVE_RANK : ActivityConstant.SEND_RANK;
                missPkVO.setTopRankList(getOtherRankingListVO(activityIdItem, rankType, 64, 0, 0));
                missPkVO.setMyRank(getOtherMyRank(activityIdItem, uid, rankType, 0, 0));
            }else{
                String missPkConfig = hashAllMap.get(activityIdItem);  // 获取pk对抗配置
                List<CompetitionVO> missPkRankList = JSONObject.parseObject(missPkConfig, List.class);
                OtherMyRankVO myPkRank = new OtherMyRankVO();
                for (CompetitionVO cpVO: missPkRankList) {
                    String pk1Uid = cpVO.getPlayer1Uid();
                    ActorData pk1User = actorDao.getActorDataFromCache(pk1Uid);
                    cpVO.setPlayer1Name(pk1User.getName());
                    cpVO.setPlayer1Head(ImageUrlGenerator.generateRoomUserUrl(pk1User.getHead()));
                    cpVO.setPlayer1Bean(activityCommonRedis.getCommonZSetRankingScore(activityIdItem, cpVO.getPlayer1Uid()));

                    String pk2Uid = cpVO.getPlayer2Uid();
                    ActorData pk2User = actorDao.getActorDataFromCache(pk2Uid);
                    cpVO.setPlayer2Name(pk2User.getName());
                    cpVO.setPlayer2Head(ImageUrlGenerator.generateRoomUserUrl(pk2User.getHead()));
                    cpVO.setPlayer2Bean(activityCommonRedis.getCommonZSetRankingScore(activityIdItem, cpVO.getPlayer2Uid()));

                    if (uid.equals(cpVO.getPlayer1Uid())) {
                        myPkRank.setName(pk1User.getName());
                        myPkRank.setHead(ImageUrlGenerator.generateRoomUserUrl(pk1User.getHead()));
                        myPkRank.setScore(cpVO.getPlayer1Bean());
                    } else if (uid.equals(pk2Uid)) {
                        myPkRank.setName(pk2User.getName());
                        myPkRank.setHead(ImageUrlGenerator.generateRoomUserUrl(pk2User.getHead()));
                        myPkRank.setScore(cpVO.getPlayer2Bean());
                    }
                }
                missPkVO.setPkRankList(missPkRankList);
                missPkVO.setMyRank(myPkRank);

            }
            allPkRankList.add(missPkVO);

        }
        vo.setAllPkRankList(allPkRankList);
        return vo;
    }

    /**
     * 处理从榜单生成PK对-第一轮
     */
    public void handleMissRound1TopRankData(OtherRankingActivityData otherActivity) {
        try {
            String activityId = otherActivity.get_id().toString();
            int rankType;
            TreeMap<Integer, Integer> ROUND_1_MAP;
            List<String> ROUND_1_KEY_LIST;
            if(QUEEN_ACTIVITY_ID_LIST.contains(activityId)){
                rankType = ActivityConstant.RECEIVE_RANK;
                ROUND_1_MAP = QUEEN_ROUND_1_MAP;
                ROUND_1_KEY_LIST = QUEEN_ROUND_1_KEY_LIST;
            }else {
                rankType = ActivityConstant.SEND_RANK;
                ROUND_1_MAP = KING_ROUND_1_MAP;
                ROUND_1_KEY_LIST = KING_ROUND_1_KEY_LIST;
            }
            List<String> top64RankList = activityOtherRedis.getOtherRankingList(activityId, rankType, 64, 0);

            String round2ActivityId = otherActivity.getNextActivityId();
            Map<String, Integer> uidRankMap = new HashMap<>();
            int rank = 1;
            for (String aid : top64RankList) {
                Integer higherKey = ROUND_1_MAP.higherKey(rank);
                int resKeyIndex =  higherKey != null ? ROUND_1_MAP.get(higherKey) : ROUND_1_MAP.lastEntry().getValue();
                String resourceKey = ROUND_1_KEY_LIST.get(resKeyIndex);

                ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
                for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceKeyConfigData.getResourceMetaList()) {
                    if(BASE_BEAN_NAME.equals(resourceMeta.getResourceNameEn())){
                        activityCommonRedis.incrCommonZSetRankingScore(round2ActivityId, aid, resourceMeta.getResourceNumber());
                    }
                }
                resourceKeyHandlerService.sendResourceData(aid, resourceKey, otherActivity.getAcNameEn(), otherActivity.getAcNameAr(), otherActivity.getAcNameEn(), ACTIVITY_URL, "");
                if(rank <= 32){
                    uidRankMap.put(aid, rank);
                }
                rank += 1;
            }

            List<String> top32RankList = top64RankList.size() > 32 ? top64RankList.subList(0, 32) : top64RankList;
            int listSize = top32RankList.size();
            if (listSize < 2) {
                return;
            }

            int endListIndex = listSize - 1;
            int halfLength = listSize / 2;
            List<CompetitionVO> missPkRankList = new ArrayList<>();
            List<String> missPkUidList = new ArrayList<>();
            for (int i = 0; i < halfLength; i++) {
                CompetitionVO cpVO = new CompetitionVO();
                String player1 = top32RankList.get(i);
                String player2 = top32RankList.get(endListIndex - i);
                int player1Rank = uidRankMap.getOrDefault(player1, 0);
                int player2Rank = uidRankMap.getOrDefault(player2, 0);
                cpVO.setPlayer1Uid(player1);
                cpVO.setPlayer1Rank(player1Rank);
                cpVO.setPlayer2Uid(player2);
                cpVO.setPlayer2Rank(player2Rank);

                missPkUidList.add(player1);
                missPkUidList.add(player2);
                missPkRankList.add(cpVO);
            }
            activityCommonRedis.setCommonHashData(getHashActivityId(ACTIVITY_ID), round2ActivityId, JSONObject.toJSONString(missPkRankList));
            OtherRankingActivityData nextActivityData = otherRankingActivityDao.findData(round2ActivityId);
            Update update = new Update();
            update.set("rankUidList", missPkUidList);
            otherRankingActivityDao.updateData(nextActivityData, update);

        } catch (Exception e) {
            logger.error("handleWriteTop16Data error={}", e.getMessage(), e);
        }
    }

    /**
     * 处理从pk榜单奖励
     */
    public void handlerPKRoundData(String activityId) {
        try{
            // Map<String, String> hashAllMap = activityCommonRedis.getCommonHashAllMapStr(this.getHashActivityId(ACTIVITY_ID));
            // String missPkConfig = hashAllMap.get(activityId);  // 获取pk对抗配置
            // List<CompetitionVO> missPkRankList = JSONObject.parseObject(missPkConfig, List.class);
            // for (CompetitionVO cpVO: missPkRankList) {
            //     String pk1Uid = cpVO.getPlayer1Uid();
            //     ActorData pk1User = actorDao.getActorDataFromCache(pk1Uid);
            //     cpVO.setPlayer1Name(pk1User.getName());
            //     cpVO.setPlayer1Head(ImageUrlGenerator.generateRoomUserUrl(pk1User.getHead()));
            //     cpVO.setPlayer1Bean(activityCommonRedis.getCommonZSetRankingScore(activityIdItem, cpVO.getPlayer1Uid()));
            //
            //     String pk2Uid = cpVO.getPlayer2Uid();
            //     ActorData pk2User = actorDao.getActorDataFromCache(pk2Uid);
            //     cpVO.setPlayer2Name(pk2User.getName());
            //     cpVO.setPlayer2Head(ImageUrlGenerator.generateRoomUserUrl(pk2User.getHead()));
            //     cpVO.setPlayer2Bean(activityCommonRedis.getCommonZSetRankingScore(activityIdItem, cpVO.getPlayer2Uid()));
            //
            // }
            //
            // String missStrPK16 = hashAllMap.get(activityId);
            // OtherRankingActivityData otherActivity = getOtherRankingActivity(activityId);
            //
            // if (!StringUtils.isEmpty(missStrPK16)) {
            //     List<String> missPK16List = JSONObject.parseObject(missStrPK16, List.class);
            //     int top16ListSize = missPK16List.size();
            //     if (top16ListSize < 2) {
            //         return;
            //     }
            //
            //     List<Integer> rankWinList = new ArrayList<>();
            //     List<String> rankLoseList = new ArrayList<>();
            //     Map<Integer, String> rankWinMap = new HashMap<>();
            //     int defaultIndex = 1;
            //     for (String pkUser : missPK16List) {
            //
            //         String[] pkUserList = pkUser.split("-");
            //         String[] pk1UidRank = pkUserList[0].split("_");
            //         String[] pk2UidRank = pkUserList[1].split("_");
            //         String pk1Uid = pk1UidRank[0];
            //         String pk2Uid = pk2UidRank[0];
            //         int rank1 = activityCommonRedis.getCommonZSetRank(activityId, pk1Uid);
            //         rank1 = rank1 == 0 ? 99 : rank1;
            //         int rank2 = activityCommonRedis.getCommonZSetRank(activityId, pk2Uid);
            //         rank2 = rank2 == 0 ? 99 : rank2;
            //
            //         String winUid = rank1 <= rank2 ? pk1Uid : pk2Uid;
            //         rankLoseList.add(winUid.equals(pk1Uid) ? pk2Uid : pk1Uid);
            //
            //         int winRank = Math.min(rank1, rank2);
            //         winRank = winRank == 99 ? winRank + defaultIndex : winRank;
            //         defaultIndex += 1;
            //
            //         rankWinList.add(winRank);
            //         rankWinMap.put(winRank, winUid);
            //     }
            //     Collections.sort(rankWinList);
            //
            //     // 发放奖励
            //     int rank = 1;
            //     Map<String, Integer> uidRankMap = new HashMap<>();
            //     for (Integer winRank : rankWinList){
            //         String resourceKey = null;
            //         if(rank >= 1 && rank <= 8){
            //             resourceKey = MISS_ROUND_2_KEY.get(0);
            //         } else {
            //             resourceKey = MISS_ROUND_2_KEY.get(1);
            //         }
            //         String winUid = rankWinMap.get(winRank);
            //         if(winUid != null){
            //             uidRankMap.put(winUid, rank);
            //             resourceKeyHandlerService.sendResourceData(winUid, resourceKey, otherActivity.getAcNameEn(), otherActivity.getAcNameAr(), otherActivity.getAcNameEn(), ACTIVITY_URL, "");
            //         }
            //         rank += 1;
            //     }
            //
            //     for (String loseUid : rankLoseList) {
            //         resourceKeyHandlerService.sendResourceData(loseUid, MISS_ROUND_2_KEY.get(2), otherActivity.getAcNameEn(), otherActivity.getAcNameAr(), otherActivity.getAcNameEn(), ACTIVITY_URL, "");
            //     }
            //
            //     String pk16Select8ActivityId = ACTIVITY_ID_LIST.get(2);
            //     List<String> top8RankStrList = new ArrayList<>();
            //     int listSize = rankWinList.size();
            //     int endListIndex = listSize - 1;
            //     int halfLength = listSize / 2;
            //     for (int i = 0; i < halfLength; i++) {
            //         int winRank1 = rankWinList.get(i);
            //         int winRank2 = rankWinList.get(endListIndex - i);
            //         String win1Uid = rankWinMap.get(winRank1);
            //         String win2Uid = rankWinMap.get(winRank2);
            //         int player1Rank = uidRankMap.getOrDefault(win1Uid, 0);
            //         int player2Rank = uidRankMap.getOrDefault(win2Uid, 0);
            //         String playUser = String.format("%s_%s-%s_%s", win1Uid, player1Rank, win2Uid, player2Rank);
            //         top8RankStrList.add(playUser);
            //
            //         int player1BeseBean = getRound3BeseBean(player1Rank);
            //         int player2BeseBean = getRound3BeseBean(player2Rank);
            //         activityCommonRedis.incrCommonZSetRankingScore(pk16Select8ActivityId, win1Uid, player1BeseBean);
            //         activityCommonRedis.incrCommonZSetRankingScore(pk16Select8ActivityId, win2Uid, player2BeseBean);
            //         activityCommonRedis.addCommonSetData(pk16Select8ActivityId, win1Uid);
            //         activityCommonRedis.addCommonSetData(pk16Select8ActivityId, win2Uid);
            //     }
            //     activityCommonRedis.setCommonHashData(PK_MISS_CONTEST_KEY, pk16Select8ActivityId, JSONObject.toJSONString(top8RankStrList));
            // }

        } catch (Exception e) {
            logger.error("handleWritePK16Select8Data error={}", e.getMessage(), e);
        }
    }

    public void handleSendGift(SendGiftData sendGiftData, OtherRankingActivityData activityData) {
        try {
            int sendBeans = sendGiftData.getNumber() * sendGiftData.getPrice();
            Set<String> aidSet = sendGiftData.getAid_list();
            int sendTotalBeans = sendBeans * aidSet.size();
            List<String> pkUidList = activityData.getRankUidList();
            if(pkUidList == null){
                return;
            }

            String activityId = activityData.get_id().toString();
            String fromUid = sendGiftData.getFrom_uid();

            if(KING_ACTIVITY_ID_LIST.contains(activityId) && pkUidList.contains(fromUid)){
                activityCommonRedis.incrCommonZSetRankingScore(activityId, fromUid, sendTotalBeans);
            } else if (QUEEN_ACTIVITY_ID_LIST.contains(activityId)) {
                for (String aid : aidSet) {
                    if (!pkUidList.contains(aid)) {
                        continue;
                    }
                    activityCommonRedis.incrCommonZSetRankingScore(activityId, aid, sendBeans);
                }
            }
        } catch (Exception e) {
            logger.error("handleSendGift error={}", e.getMessage(), e);
        }
    }

}
