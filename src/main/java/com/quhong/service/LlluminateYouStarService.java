package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GetActivitySpecialItemsRecordEvent;
import com.quhong.analysis.StarBeatGameLogEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.RoomEventDTO;
import com.quhong.data.dto.TempToAidDTO;
import com.quhong.data.vo.LlluminateYouStarVO;
import com.quhong.data.vo.UnreadMsgVO;
import com.quhong.datas.DayTimeData;
import com.quhong.datas.HttpResult;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.feign.IRoomService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.dao.RoomEventSubDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.data.RoomEventSubData;
import com.quhong.room.RoomTags;
import com.quhong.user.CustomerServiceUser;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 闪耀youstar活动
 */
@Service
public class LlluminateYouStarService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(LlluminateYouStarService.class);
    private static final String ACTIVITY_TITLE_EN = "Sparkling YouStar";
    private static final String ACTIVITY_TITLE_AR = "Sparkling YouStar"; // تألق يوستار
    private static final String ACTIVITY_ID = "66b040ab669acd275d60436d";

    private static final List<Integer> SCORE_LEVEL_LIST = Arrays.asList(0, 150, 450, 900, 1500, 2400, 4500, 9000, 15000);

    private static final List<Integer> TIMES_LEVEL_LIST = Arrays.asList(0, 1, 2, 3, 4, 6, 14, 30, 40);

    private static final String ACTIVITY_DRAW_RES_KEY = "shiningActivityDraw"; // 活动首抽资源key
    private static final String DAILY_DRAW_RES_KEY = "shiningDailyDraw"; // 每日首抽资源key
    private static final String NORMAL_DRAW_RES_KEY = "shiningNormalDraw";// 正常抽资源key
    private static Set<String> allDrawTypeKey = new HashSet<>();

    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/LlluminateYouStar/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/LlluminateYouStar/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/op_1721374572_shanyao_FJRK_a.png";

    public static final Map<Integer, List<String>> DAY_RES_KEY_MAP = new HashMap<Integer, List<String>>() {
        {
            put(1, Arrays.asList("shiningDayTop1Host", "shiningDayTop1Manger"));
            put(2, Arrays.asList("shiningDayTop2Host", "shiningDayTop2Manger"));
            put(3, Arrays.asList("shiningDayTop3Host", "shiningDayTop3Manger"));
            put(4, Arrays.asList("shiningDayTop4Host", "shiningDayTop4Manger"));
            put(5, Arrays.asList("shiningDayTop4Host", "shiningDayTop4Manger"));
            put(6, Arrays.asList("shiningDayTop4Host", "shiningDayTop4Manger"));
            put(7, Arrays.asList("shiningDayTop4Host", "shiningDayTop4Manger"));
            put(8, Arrays.asList("shiningDayTop4Host", "shiningDayTop4Manger"));
            put(9, Arrays.asList("shiningDayTop4Host", "shiningDayTop4Manger"));
            put(10, Arrays.asList("shiningDayTop4Host", "shiningDayTop4Manger"));
        }
    };

    public static final Map<Integer, List<String>> TOTAL_RES_KEY_MAP = new HashMap<Integer, List<String>>() {
        {
            put(1, Arrays.asList("shiningTotalTop1Host", "shiningTotalTop1Manger"));
            put(2, Arrays.asList("shiningTotalTop2Host", "shiningTotalTop2Manger"));
            put(3, Arrays.asList("shiningTotalTop3Host", "shiningTotalTop3Manger"));
            put(4, Arrays.asList("shiningTotalTop4Host", "shiningTotalTop4Manger"));
            put(5, Arrays.asList("shiningTotalTop5Host", "shiningTotalTop5Manger"));
            put(6, Arrays.asList("shiningTotalTop6Host", "shiningTotalTop6Manger"));
            put(7, Arrays.asList("shiningTotalTop7Host", "shiningTotalTop7Manger"));
            put(8, Arrays.asList("shiningTotalTop8Host", "shiningTotalTop8Manger"));
            put(9, Arrays.asList("shiningTotalTop9Host", "shiningTotalTop9Manger"));
            put(10, Arrays.asList("shiningTotalTop10Host", "shiningTotalTop10Manger"));
        }
    };

    public static final Map<Integer, List<String>> EVENT_TYPE_MAP = new HashMap<Integer, List<String>>() {
        {
            put(1, Arrays.asList("Party", "الحفلة", "https://cdn3.qmovies.tv/youstar/op_sys_1675064676_2x.png"));
            put(2, Arrays.asList("Chat", "الدردشة", "https://cdn3.qmovies.tv/youstar/op_sys_1675064639_22x.png"));
            put(3, Arrays.asList("Game", "اللعب", "https://cdn3.qmovies.tv/youstar/op_sys_1673512837_82x.png"));
            put(4, Arrays.asList("Singing", "الغناء", "https://cdn3.qmovies.tv/youstar/op_sys_1675064724_142x.png"));
            put(5, Arrays.asList("Competition", "المسابقة", "https://cdn3.qmovies.tv/youstar/op_sys_1673512788_142x.png"));
            put(6, Arrays.asList("Poetry", "الشعر", "https://cdn3.qmovies.tv/youstar/op_sys_1673512729_332x.png"));
            put(7, Arrays.asList("Other", "الآخر", "https://cdn3.qmovies.tv/youstar/op_sys_1673512623_152x.png"));
        }
    };

    private static final List<String> ROOM_LEVEL_ICON = Arrays.asList
            ("",
                    "https://cdn3.qmovies.tv/youstar/op_1695721534_1.png",
                    "https://cdn3.qmovies.tv/youstar/op_1695721534_2.png",
                    "https://cdn3.qmovies.tv/youstar/op_1695721534_3.png",
                    "https://cdn3.qmovies.tv/youstar/op_1695721534_4.png",
                    "https://cdn3.qmovies.tv/youstar/op_1695721534_5.png",
                    "https://cdn3.qmovies.tv/youstar/op_1695721534_6.png",
                    "https://cdn3.qmovies.tv/youstar/op_1697099399_notice_7.webp",
                    "https://cdn3.qmovies.tv/youstar/op_1697099399_notice_8.webp"
            );

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private final Comparator<LlluminateYouStarVO.RoomItemVO> pointsDesc = Comparator.comparing(LlluminateYouStarVO.RoomItemVO::getPoints).reversed();

    private final Comparator<LlluminateYouStarVO.RoomItemVO> roomOnlineDesc = Comparator.comparing(LlluminateYouStarVO.RoomItemVO::getRoomOnline).reversed();

    private final Comparator<LlluminateYouStarVO.RoomItemVO> eventStartTimeAsc = Comparator.comparing(LlluminateYouStarVO.RoomItemVO::getRoomEventStartTime);

    private final Comparator<LlluminateYouStarVO.RoomItemVO> eventSubCountDesc = Comparator.comparing(LlluminateYouStarVO.RoomItemVO::getRoomEventSubCount).reversed();

    private static final int HISTORY_PAGE_SIZE = 30;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private RoomTags roomTags;
    @Resource
    private RoomLevelService roomLevelService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private RoomEventSubDao roomEventSubDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private IRoomService iRoomService;
    @Resource
    private RecreationTagService recreationTagService;
    @Resource
    private EventReport eventReport;

    @PostConstruct
    public void init() {
        allDrawTypeKey.add(ACTIVITY_DRAW_RES_KEY);
        allDrawTypeKey.add(DAILY_DRAW_RES_KEY);
        allDrawTypeKey.add(NORMAL_DRAW_RES_KEY);
    }

    public LlluminateYouStarVO joinLlluminateActivity(String uid, String rid) {
        checkActivityTime(ACTIVITY_ID);
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        String roomId = RoomUtils.formatRoomId(uid);
        String aid = "";
        if (StringUtils.hasLength(rid)) {
            if (rid.length() > 9) {
                logger.info("rid is invalid");
                throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
            }
            ActorData aidActorData = actorDao.getActorByStrRid(rid);
            if (aidActorData != null) {
                aid = aidActorData.getUid();
            } else {
                logger.info("not find rid:{}", rid);
                throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
            }
            if (RoomMemberDao.isRoomHost(roomId, aid)) {
                logger.info("manger not allow add myself ");
                throw new CommonH5Exception(ActivityHttpCode.SHINING_NOT_ADD_MYSELF);
            }
            RoomRoleData roleData = roomMemberDao.getRoleData(roomId, aid);
            if (!roleData.isAdmin()) {
                logger.info("aid:{} is not room:{} manger", aid, roomId);
                throw new CommonH5Exception(ActivityHttpCode.SHINING_NOT_REAL_ADMIN);
            }
        } else {
            logger.info("rid is empty");
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(), roomId);
        if (StringUtils.hasLength(bindAid)) {
            logger.info("set manger error already set other aid:{} room:{} bindAid:{}", aid, roomId, bindAid);
            throw new CommonH5Exception(ActivityHttpCode.SHINING_ADMIN_EXIST);
        }
        activityCommonRedis.setCommonHashData(getHashBindKey(), roomId, aid);
        cacheDataService.delSetCache(getHashBindKey(),1);
        cacheDataService.delCommonHashStrValue(getHashBindKey(), roomId);

        LlluminateYouStarVO vo = new LlluminateYouStarVO();
        ActorData host = actorDao.getActorDataFromCache(uid);
        vo.setHostName(host.getName());
        vo.setHostHead(ImageUrlGenerator.generateRoomUserUrl(host.getHead()));
        if (StringUtils.hasLength(aid)) {
            ActorData manger = actorDao.getActorDataFromCache(aid);
            vo.setMangerName(manger.getName());
            vo.setMangerHead(ImageUrlGenerator.generateRoomUserUrl(manger.getHead()));
        }
        recreationTagService.addCommonZSetScore(roomId, activityData.getEndTime());
        return vo;
    }

    public LlluminateYouStarVO mainTab(String uid, Integer slang) {
        LlluminateYouStarVO vo = new LlluminateYouStarVO();
        OtherRankingActivityData data = getOtherRankingActivity(ACTIVITY_ID);
        vo.setStartTime(data.getStartTime());
        vo.setEndTime(data.getEndTime());
        vo.setDailyShiningList(getRoomItemVORankList(uid, 1, slang));
        vo.setHotEventList(getRoomItemVORankList(uid, 2, slang));

//        TempToAidDTO dto = new TempToAidDTO();
//        dto.setUid(uid);
//        dto.setAid(CustomerServiceUser.getUid());
//        HttpResult<UnreadMsgVO> ret = iMsgService.getMsgAccountUnread(dto);
//        if (ret.isError()) {
//            logger.info("uid:{} getMsgAccountUnread is error", uid);
//            vo.setUnRead(0);
//        } else {
//            vo.setUnRead(ret.getData().getUnreadNum());
//        }
        vo.setUnRead(0);
        return vo;
    }

    public LlluminateYouStarVO getDetailVO(String uid) {
        LlluminateYouStarVO vo = new LlluminateYouStarVO();
        String roomId = RoomUtils.formatRoomId(uid);
        String bindAid = cacheDataService.getCommonHashStrValue(getHashBindKey(), roomId);
        if (bindAid == null) {
            vo.setJoinState(0);
            vo.setFirstOpen(activityCommonRedis.hasUid(getHashFirstOpenKey(), roomId) ? 0 : 1);
        } else {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
            int startTime = activityData.getStartTime();
            int endTime = activityData.getEndTime() - 10; // 偏移10秒为活动的最后一天
            int now = DateHelper.getNowSeconds();
            String end;
            String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
            if (now < endTime) {
                end = DateHelper.ARABIAN.formatDateInDay();
            } else {
                end = acEnd;
            }
            String start = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
            logger.info("start:{} end:{} acEnd:{}", start, end, acEnd);
            RoomEventData roomEventData = roomEventDao.getOneRoomEvent(roomId, startTime);
            List<LlluminateYouStarVO.DetailVO> detailVOList = new ArrayList<>();
            if (roomEventData != null) {
                List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(start, end);
                for (DayTimeData item : dayTimeDataList) {
                    String day = item.getDate();
                    String detailKey = getHashDetailKey(day);
                    String dayKey = getZetDayKey(day);
                    LlluminateYouStarVO.DetailVO sourceDetailVO = cacheDataService.getLlluminateDetailVO(detailKey, roomId);
                    LlluminateYouStarVO.DetailVO detailVO = new LlluminateYouStarVO.DetailVO();
                    BeanUtils.copyProperties(sourceDetailVO, detailVO);
                    int rank = activityCommonRedis.getCommonZSetRank(dayKey, roomId);
                    detailVO.setDayStr(day);
                    detailVO.setRank(rank == 0 ? -1 : rank > 10 ? -1 : rank);
                    detailVO.setTotalUserSubNum(detailVO.getNewUserSubNum() + detailVO.getOldUserSubNum());
                    detailVO.setMicUsers(null);
                    detailVO.setSubUsers(null);
                    detailVOList.add(detailVO);
                }
            }
            vo.setJoinState(1);
            vo.setFirstOpen(0);
            vo.setCoverUrl(roomEventData != null ? roomEventData.getEventCoverUrl() : "");
            vo.setDetailVOList(detailVOList);
        }
        // 总榜
        fillVORankList(1, uid, bindAid, vo);
        // 日榜
        fillVORankList(2, uid, bindAid, vo);
        return vo;
    }


    private void fillVORankList(int rankType, String myUid, String myBindAid, LlluminateYouStarVO vo) {
        Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
        String redisKey;
        if (rankType == 1) {
            redisKey = getZetTotalKey();
        } else {
            String now = DateHelper.ARABIAN.formatDateInDay();
            redisKey = getZetDayKey(now);
        }

        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(redisKey, 10);
        List<LlluminateYouStarVO.RankVO> rankList = new ArrayList<>();
        LlluminateYouStarVO.RankVO myRank = new LlluminateYouStarVO.RankVO();
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            LlluminateYouStarVO.RankVO itemRank = new LlluminateYouStarVO.RankVO();
            String roomId = entry.getKey();
            String uid = RoomUtils.getRoomHostId(roomId);
            ActorData host = actorDao.getActorDataFromCache(uid);
            itemRank.setRoomId(roomId);
            itemRank.setHostName(host.getName());
            itemRank.setHostHead(ImageUrlGenerator.generateRoomUserUrl(host.getHead()));
            String countryCode = ActorUtils.getCountryCode(host.getCountry());
            itemRank.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
            String bindAid = cacheDataService.getCommonHashStrValue(getHashBindKey(), roomId);
            if (StringUtils.hasLength(bindAid)) {
                ActorData manger = actorDao.getActorDataFromCache(bindAid);
                itemRank.setMangerName(manger.getName());
                itemRank.setMangerHead(ImageUrlGenerator.generateRoomUserUrl(manger.getHead()));
            }
            itemRank.setPoints(entry.getValue());
            itemRank.setRank(rank);

            if (myUid.equals(uid)) {
                BeanUtils.copyProperties(itemRank, myRank);
            }
            rankList.add(itemRank);
            rank += 1;
        }

        if (myRank.getRank() == null || myRank.getRank() == 0) {
            ActorData host = actorDao.getActorDataFromCache(myUid);
            myRank.setRoomId(RoomUtils.formatRoomId(myUid));
            myRank.setHostName(host.getName());
            myRank.setHostHead(ImageUrlGenerator.generateRoomUserUrl(host.getHead()));
            String countryCode = ActorUtils.getCountryCode(host.getCountry());
            myRank.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
            if (StringUtils.hasLength(myBindAid)) {
                ActorData manger = actorDao.getActorDataFromCache(myBindAid);
                myRank.setMangerName(manger.getName());
                myRank.setMangerHead(ImageUrlGenerator.generateRoomUserUrl(manger.getHead()));
            }
            myRank.setPoints(activityCommonRedis.getCommonZSetRankingScore(redisKey, RoomUtils.formatRoomId(myUid)));
            myRank.setRank(-1);
        }
        if (rankType == 1) {
            vo.setTotalRankList(rankList);
            vo.setMyTotalRank(myRank);
        } else {
            vo.setDayRankList(rankList);
            vo.setMyDayRank(myRank);
        }
    }


    private List<LlluminateYouStarVO.RoomItemVO> getRoomItemVORankList(String uid, int rankType, Integer slang) {
        List<LlluminateYouStarVO.RoomItemVO> tabList = new ArrayList<>();
        Set<String> allAcRoom = cacheDataService.getAllSetCache(getHashBindKey(),1);
        Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        if (!inActivityTime(ACTIVITY_ID)) {
            return Collections.emptyList();
        }
        int startAcTime = activityData.getStartTime();
        if (rankType == 1) {
            // 左tab dailyShiningList
            String nowDay = DateHelper.ARABIAN.formatDateInDay();
            Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(getZetDayKey(nowDay), 30);
            int now = DateHelper.getNowSeconds();
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String roomId = entry.getKey();
                String hostUid = RoomUtils.getRoomHostId(roomId);
                if (!allAcRoom.contains(roomId)) {
                    continue;
                }
                LlluminateYouStarVO.RoomItemVO roomItemVO = new LlluminateYouStarVO.RoomItemVO();
                roomItemVO.setRoomId(roomId);
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
                if (null == roomData) {
                    continue;
                }
                roomItemVO.setRoomName(roomData.getName());
                roomItemVO.setRoomCover(ImageUrlGenerator.generateRoomUrl(roomData.getHead()));
                roomItemVO.setRoomOnline(getFixOnline(roomData.getOnline()));

                RoomEventData roomEventData = roomEventDao.getOneRoomEvent(roomId, startAcTime);
                List<String> stringList = EVENT_TYPE_MAP.get(roomEventData.getType());
                if (stringList != null) {
                    roomItemVO.setRoomTypeIcon(stringList.get(2));
                    roomItemVO.setRoomTypeName(slang == SLangType.ARABIC ? stringList.get(1) : stringList.get(0));
                }

                ActorData host = actorDao.getActorDataFromCache(hostUid);
                roomItemVO.setHostHead(ImageUrlGenerator.generateRoomUserUrl(host.getHead()));
                String countryCode = ActorUtils.getCountryCode(host.getCountry());
                roomItemVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
                String bindAid = cacheDataService.getCommonHashStrValue(getHashBindKey(), roomId);
                if (StringUtils.hasLength(bindAid)) {
                    ActorData manger = actorDao.getActorDataFromCache(bindAid);
                    roomItemVO.setMangerHead(ImageUrlGenerator.generateRoomUserUrl(manger.getHead()));
                }
                roomItemVO.setPoints(entry.getValue());
                roomItemVO.setRoomLevel(roomLevelService.getRoomLevel(roomId));
                int sLevel = roomLevelService.getStepLevel(roomItemVO.getRoomLevel());
                roomItemVO.setRoomLevelIcon(ROOM_LEVEL_ICON.get(sLevel));

                tabList.add(roomItemVO);
            }
            List<LlluminateYouStarVO.RoomItemVO> retList = tabList.stream()
                    .sorted(pointsDesc.thenComparing(roomOnlineDesc))
                    .limit(20).collect(Collectors.toList());
            return retList;
        } else {
            // 右tab hotEventList
            int now = DateHelper.getNowSeconds();
            List<RoomEventData> roomEventDataList = roomEventDao.getRoomEventList(now);
            for (RoomEventData item : roomEventDataList) {
                String roomId = item.getRoomId();
                String hostUid = RoomUtils.getRoomHostId(roomId);
                if (!allAcRoom.contains(roomId)) {
                    continue;
                }
                LlluminateYouStarVO.RoomItemVO roomItemVO = new LlluminateYouStarVO.RoomItemVO();
                int startTime = item.getStartTime();
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
                if (null == roomData) {
                    continue;
                }
                roomItemVO.setRoomId(roomId);
                roomItemVO.setRoomEventCover(item.getEventCoverUrl());
                roomItemVO.setRoomEventState(now > startTime ? 1 : 0);
                roomItemVO.setRoomEventStartTime(startTime);
                roomItemVO.setRoomEventName(item.getName());
                roomItemVO.setRoomEventSubCount(item.getSubNum());
                String countryCode = ActorUtils.getCountryCode(roomData.getCountry());
                roomItemVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
                List<String> stringList = EVENT_TYPE_MAP.get(item.getType());
                if (stringList != null) {
                    roomItemVO.setRoomTypeIcon(stringList.get(2));
                    roomItemVO.setRoomTypeName(slang == SLangType.ARABIC ? stringList.get(1) : stringList.get(0));
                }
                if (!hostUid.equals(uid)) {
                    RoomEventSubData subData = roomEventSubDao.selectOne(item.getId(), uid);
                    roomItemVO.setRoomEventSubState(subData == null ? 0 : 1);
                } else {
                    roomItemVO.setRoomEventSubState(1);
                }
                roomItemVO.setRoomEventId(item.getId());
                tabList.add(roomItemVO);
            }
            List<LlluminateYouStarVO.RoomItemVO> retList = tabList.stream()
                    .sorted(eventStartTimeAsc.thenComparing(eventSubCountDesc))
                    .limit(20).collect(Collectors.toList());

            return retList;
        }
    }

    public void handleUserScore(SendGiftData data, CommonMqTopicData mqData) {
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        String roomId;
        if (data != null) {
            roomId = data.getRoomId();
        } else {
            roomId = mqData.getRoomId();
        }
        if (ServerConfig.isProduct()) {
            boolean isWhiteTestRoom = whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID);
//            if (!isWhiteTestRoom) {
//                // 灰度测试,只统计测试房间的
//                return;
//            }
            if (isWhiteTestRoom) {
                // 正式开始，排除测试房间
                logger.info("return testRoom:{}", roomId);
                return;
            }
        }

        Set<String> allAcRoom = cacheDataService.getAllSetCache(getHashBindKey(),1);
        if (!allAcRoom.contains(roomId)) {
            // 没报名参加活动
            return;
        }

        if (mqData != null && CommonMqTaskConstant.SUB_ROOM_EVENT.equals(mqData.getItem())) {
            // 订阅不需要再房间活动期间
        } else {
            if (cacheDataService.isRunRoomEvent(roomId)==null) {
                // 房间没有进行中的房间活动
                return;
            }
        }

        String hostUid = RoomUtils.getRoomHostId(roomId); // 房主uid
        String aid; // 上麦用户或者发礼物用户
        boolean newUserFlag = false;
        int score;
        long totalPrice = 0;
        int actionType = 0; // 1 发礼物  2有效上麦 3活动订阅
        if (data != null) {
            actionType = 1;
            aid = data.getFrom_uid();
            newUserFlag = ActorUtils.isNewRegisterActor(aid, 7);
            totalPrice = ((long) data.getPrice() * data.getNumber() * data.getAid_list().size());
            score = newUserFlag ? (int) totalPrice / 3 * 2 : (int) totalPrice / 3;
        } else {
            actionType = CommonMqTaskConstant.ON_MIC_TIME.equals(mqData.getItem()) ? 2 : 3;
            aid = mqData.getUid();
            newUserFlag = ActorUtils.isNewRegisterActor(aid, 7);
            if (actionType == 2) {
                score = newUserFlag ? 3 : 1;
            } else {
                score = newUserFlag ? 3 : 2;
            }
        }
        if (score <= 0) {
            logger.info("score:{} lte 0", score);
            return;
        }
        String now = DateHelper.ARABIAN.formatDateInDay();
        String detailKey = getHashDetailKey(now);
        String dayKey = getZetDayKey(now);
        String totalKey = getZetTotalKey();
        synchronized (stringPool.intern("lock:shang_yao:" + roomId)) {
            LlluminateYouStarVO.DetailVO detailVO = cacheDataService.getLlluminateDetailVO(detailKey, roomId);
            if (actionType == 2 || actionType == 3) {
                Set<String> disUsers;
                int maxLimit = 0;
                int nowNum = 0;
                if (actionType == 2) {
                    disUsers = detailVO.getMicUsers();
                    if (newUserFlag) {
                        maxLimit = 5;
                        nowNum = detailVO.getNewUserMic();
                    } else {
                        maxLimit = 30;
                        nowNum = detailVO.getOldUserMic();
                    }
                } else {
                    disUsers = detailVO.getSubUsers();
                    if (newUserFlag) {
                        maxLimit = 5;
                        nowNum = detailVO.getNewUserSubNum();
                    } else {
                        maxLimit = 15;
                        nowNum = detailVO.getOldUserSubNum();
                    }
                }
                if (disUsers.contains(aid) || nowNum >= maxLimit) {
//                    logger.info("return actionType:{} newUserFlag:{} roomId:{} dis user list contains aid:{} or nowNum:{} > maxLimit:{}",
//                            actionType, newUserFlag, roomId, aid, nowNum, maxLimit);
                    return;
                }
                if (actionType == 2) {
                    if (newUserFlag) {
                        detailVO.setNewUserMic(nowNum + 1);
                    } else {
                        detailVO.setOldUserMic(nowNum + 1);
                    }
                } else {
                    if (newUserFlag) {
                        detailVO.setNewUserSubNum(nowNum + 1);
                    } else {
                        detailVO.setOldUserSubNum(nowNum + 1);
                    }
                }
                disUsers.add(aid);
            } else {
                detailVO.setSendGift(detailVO.getSendGift() + totalPrice);
            }

            detailVO.setDayPoint(detailVO.getDayPoint() + score);
            activityCommonRedis.setCommonHashData(detailKey, roomId, JSONObject.toJSONString(detailVO));
            activityCommonRedis.incrCommonZSetRankingScoreSimple(totalKey, roomId, score);
            int dayOld = activityCommonRedis.getCommonZSetRankingScore(dayKey, roomId);
            int dayNew = activityCommonRedis.incrCommonZSetRankingScoreSimple(dayKey, roomId, score);
            int oldIndex = getIndexLevel(dayOld);
            int afterIndex = getIndexLevel(dayNew);
            int times = 0; // 抽奖的次数
            if (afterIndex > oldIndex) {
                for (int i = oldIndex + 1; i <= afterIndex; i++) {
                    times += TIMES_LEVEL_LIST.get(i);
                }
                activityCommonRedis.incrCommonZSetRankingScoreSimple(getZetDayDrawKey(now), hostUid, times);
            }
            doAddScoreEvent(roomId, aid, score, actionType);
            logger.info("success add hostUid:{} score:{} old:{} after:{} oldIndex:{} afterIndex:{} times:{}",
                    hostUid, score, dayOld, dayNew, oldIndex, afterIndex, times);
        }
    }

    public LlluminateYouStarVO llluminateDrawInfo(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.info("not find uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        String tnId = actorData.getTn_id();
        String nowDay = DateHelper.ARABIAN.formatDateInDay();
        String roomId = RoomUtils.formatRoomId(uid);
        LlluminateYouStarVO vo = new LlluminateYouStarVO();
        int dayScore = activityCommonRedis.getCommonZSetRankingScore(getZetDayKey(nowDay), roomId);
        int index = getIndexLevel(dayScore);
        LlluminateYouStarVO.DetailVO detailVO = cacheDataService.getLlluminateDetailVO(getHashDetailKey(nowDay), roomId);
        LlluminateYouStarVO.DetailVO myDetailVO = new LlluminateYouStarVO.DetailVO();
        myDetailVO.setSendGift(detailVO.getSendGift());
        myDetailVO.setTotalUserMic(detailVO.getNewUserMic() + detailVO.getOldUserMic());
        myDetailVO.setTotalUserSubNum(detailVO.getNewUserSubNum() + detailVO.getOldUserSubNum());
        List<ResourceKeyConfigData> keyConfigDataList = resourceKeyConfigDao.findListByKeys(allDrawTypeKey);
        Map<String, ResourceKeyConfigData.ResourceMeta> configMap = getConfigMap(keyConfigDataList);

        vo.setFirstDrawReward(0);

        String bindAid = cacheDataService.getCommonHashStrValue(getHashBindKey(), roomId);
        boolean joinState = bindAid != null;

        if (StringUtils.hasLength(tnId)) {
            if (activityCommonRedis.getCommonHashStrValue(getHashDeviceFirstKey(), tnId) == null) {

                vo.setFirstDrawReward(1);
            }
        }
        if (0 == vo.getFirstDrawReward() && joinState &&
                activityCommonRedis.getCommonHashStrValue(getHashDayFirstKey(nowDay), roomId) == null) {
            vo.setFirstDrawReward(1);
        }

        vo.setLeftDrawTimes(activityCommonRedis.getCommonZSetRankingScore(getZetDayDrawKey(nowDay), uid));
        vo.setCurrentPoints(dayScore);
        vo.setLevelPoints(SCORE_LEVEL_LIST.get(index));
        vo.setMyDetailVO(myDetailVO);
        vo.setRollList(getResourceDataVOList(uid, 1, configMap));
        vo.setMyHistoryList(getResourceDataVOList(uid, 2, configMap));

        return vo;
    }

    public LlluminateYouStarVO llluminateDraw(String uid, int num, String fromRoomId) {
        checkActivityTime(ACTIVITY_ID);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.info("not find uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        if (num != 1 && num != 10) {
            logger.info("num error uid:{} num:{}", uid, num);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String tnId = actorData.getTn_id();
        List<ResourceKeyConfigData> keyConfigDataList = resourceKeyConfigDao.findListByKeys(allDrawTypeKey);
        Map<String, Map<String, ResourceKeyConfigData.ResourceMeta>> configMap = checkConfig(keyConfigDataList);
        String roomId = RoomUtils.formatRoomId(uid);
        String nowDay = DateHelper.ARABIAN.formatDateInDay();
        LlluminateYouStarVO vo = new LlluminateYouStarVO();
        List<String> resultList = new ArrayList<>();
        vo.setFirstDraw(0);
        synchronized (stringPool.intern("lock:shang_yao_draw:" + uid)) {
            String bindAid = cacheDataService.getCommonHashStrValue(getHashBindKey(), roomId);
            boolean joinState = bindAid != null;
            String drawType = NORMAL_DRAW_RES_KEY;
            if (num == 1) {
                // 判断是否为设备首抽
                int totalScore = activityCommonRedis.getCommonZSetRankingScore(getZetTotalKey(), roomId);
                if (totalScore > 0) {
                    logger.info("roomId:{} totalScore gt 0", roomId);
                } else {
                    if (StringUtils.hasLength(tnId)) {
                        if (!activityCommonRedis.hasUid(getHashDeviceFirstKey(), tnId)) {
                            // 设备首抽
                            drawType = ACTIVITY_DRAW_RES_KEY;
                            vo.setFirstDraw(!joinState ? 1 : 0);
                            logger.info("device first draw roomId:{} bindAid:{}", roomId, bindAid);
                        }
                    } else {
                        logger.info("roomId:{} tnId is empty", roomId);
                    }
                }
                if (NORMAL_DRAW_RES_KEY.equals(drawType)) {
                    if (!joinState) {
                        logger.info("not join activity roomId:{} bindAid:{}", roomId, bindAid);
                        throw new CommonH5Exception(ActivityHttpCode.SHINING_NOT_JOIN);
                    }
                    // 判断是否为每日首抽
                    if (!activityCommonRedis.hasUid(getHashDayFirstKey(nowDay), roomId)) {
                        // 每日首抽
                        drawType = DAILY_DRAW_RES_KEY;
                        logger.info("day first draw roomId:{} nowDay:{}", roomId, nowDay);
                    }
                }
            }

            if (NORMAL_DRAW_RES_KEY.equals(drawType)) {
                if (!joinState) {
                    logger.info("not join activity roomId:{} bindAid:{}", roomId, bindAid);
                    throw new CommonH5Exception(ActivityHttpCode.SHINING_NOT_JOIN);
                }
                int nowNum = activityCommonRedis.getCommonZSetRankingScore(getZetDayDrawKey(nowDay), uid);
                if (nowNum < num) {
                    logger.info("not enough num roomId:{} nowNum:{} num:{}", roomId, nowNum, num);
                    throw new CommonH5Exception(ActivityHttpCode.SHINING_NOT_ENOUGH_NUM);
                }

            }
            Map<String, ResourceKeyConfigData.ResourceMeta> srcMap = configMap.get(drawType);
            Map<String, Integer> calcMap = getCalcMap(srcMap);
            for (int i = 0; i < num; i++) {
                String ret = drawOne(drawType, calcMap);
                if (!StringUtils.hasLength(ret)) {
                    logger.error("server config error drawType:{} calcMap:{} ret:{}", drawType, calcMap, ret);
                    throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
                }
                resultList.add(ret);
            }
            if (!CollectionUtils.isEmpty(resultList)) {
                List<LlluminateYouStarVO.ResourceDataVO> resultVOList = new ArrayList<>();
                resultList.forEach(item -> {
                    LlluminateYouStarVO.ResourceDataVO itemVO = new LlluminateYouStarVO.ResourceDataVO();
                    ResourceKeyConfigData.ResourceMeta resourceMeta = srcMap.get(item);
                    BeanUtils.copyProperties(resourceMeta, itemVO);
                    resultVOList.add(itemVO);
                });
                vo.setResultList(resultVOList);
                if (NORMAL_DRAW_RES_KEY.equals(drawType)) {
                    int left = activityCommonRedis.incrCommonZSetRankingScoreSimple(getZetDayDrawKey(nowDay), uid, -num);
                    vo.setLeftDrawTimes(left);
                } else {
                    vo.setLeftDrawTimes(activityCommonRedis.getCommonZSetRankingScore(getZetDayDrawKey(nowDay), uid));
                }
                asyncPushAndRecord(actorData, resultList, srcMap, num, drawType, fromRoomId);
            }
            if (StringUtils.hasLength(tnId)) {
                activityCommonRedis.hasUid(getHashDeviceFirstKey(), tnId);
            }
            activityCommonRedis.hasUid(getHashDayFirstKey(nowDay), roomId);
            logger.info("draw success drawType:{} uid:{} num:{}", drawType, uid, num);
        }

        return vo;
    }

    public void llluminateSub(String uid, Integer eventId) {
        RoomEventDTO dto = new RoomEventDTO();
        dto.setUid(uid);
        dto.setEventId(eventId);
        dto.setSubOpt(0);
        ApiResult<Object> ret = iRoomService.subEvent(dto);
        if (ret.isError()) {
            logger.info("sub fail uid:{} eventId:{} ", uid, eventId);
            throw new CommonH5Exception(ActivityHttpCode.SHINING_SUB_FAILED);
        }
    }


    private void asyncPushAndRecord(ActorData actorData, List<String> resultList,
                                    Map<String, ResourceKeyConfigData.ResourceMeta> srcMap
            , int num, String drawType, String roomId) {
        BaseTaskFactory.getFactory().add(new Task() {
            @Override
            protected void execute() {
                List<String> resultList2 = new ArrayList<>();
                String uid = actorData.getUid();
                for (String prizeId : resultList) {
                    resultList2.add(uid + "-" + prizeId);
                    resourceKeyHandlerService.sendOneResourceData(actorData, srcMap.get(prizeId), 905, ACTIVITY_TITLE_EN,
                            ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
                }
                //历史记录
                activityCommonRedis.leftPushAllCommonList(getListHistoryKey(uid), resultList, 100);
                // 滚动消息
                activityCommonRedis.leftPushAllCommonList(getListRollKey(), resultList2, HISTORY_PAGE_SIZE);

                doReportDrawEvent(uid, num, drawType, roomId, resultList, srcMap);

            }
        });

    }

    private Map<String, Integer> getCalcMap(Map<String, ResourceKeyConfigData.ResourceMeta> srcMap) {
        Map<String, Integer> calcMap = new HashMap<>();
        srcMap.forEach((k, v) -> {
            try {
                int rate = (int) (Float.parseFloat(v.getRateNumber()) * 1000);
                if (rate >= 0) {
                    calcMap.put(k, rate);
                }
            } catch (NumberFormatException e) {
                logger.info("key:{} rateNumber:{} msg:{} ", k, v.getRateNumber(), e.getMessage(), e);
                throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
            }
        });
        return calcMap;
    }

    private String drawOne(String drawType, Map<String, Integer> calcMap) {
        // 1 正常抽取 2 设备首抽 3 每日首抽
        String prizeId = null;
        if (ACTIVITY_DRAW_RES_KEY.equals(drawType)) {
            Set<String> keySet = calcMap.keySet();
            for (String metaId : keySet) {
                return metaId;
            }
        } else {
            prizeId = getAwardIdByProbability(calcMap);
        }
        return prizeId;
    }

    private Map<String, Map<String, ResourceKeyConfigData.ResourceMeta>> checkConfig(List<ResourceKeyConfigData> keyConfigDataList) {
        if (CollectionUtils.isEmpty(keyConfigDataList)) {
            logger.info(" keyConfigDataList:{}", keyConfigDataList);
            throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
        }
        Map<String, Map<String, ResourceKeyConfigData.ResourceMeta>> allPrizeConfigMap = new HashMap<>();
        keyConfigDataList.forEach(item -> {
            if (CollectionUtils.isEmpty(item.getResourceMetaList())) {
                logger.info("key:{} resourceMetaList:{}", item.getKey(), item.getResourceMetaList());
                throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
            }
            if (item.getKeyType() != 1) {
                logger.info("key:{} keyType not 1:{}", item.getKey(), item.getKeyType());
                throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
            }
            Map<String, ResourceKeyConfigData.ResourceMeta> oneMap = new HashMap<>();
            item.getResourceMetaList().forEach(subItem -> {
                oneMap.put(subItem.getMetaId(), subItem);
            });
            allPrizeConfigMap.put(item.getKey(), oneMap);
        });
        return allPrizeConfigMap;
    }

    private List<LlluminateYouStarVO.ResourceDataVO> getResourceDataVOList(String uid, int type, Map<String, ResourceKeyConfigData.ResourceMeta> configMap) {
        List<LlluminateYouStarVO.ResourceDataVO> resList = new ArrayList<>();

//        page = page > 0 ? page : 1;
        int page = 1;
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        String key;
        if (type == 1) {
            key = getListRollKey();
        } else {
            key = getListHistoryKey(uid);
        }
        List<String> retList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        if (!CollectionUtils.isEmpty(retList)) {
            for (String item : retList) {
                String prizeId;
                String aid = null;
                if (type == 1) {
                    String[] split = item.split("-");
                    if (split.length == 2) {
                        aid = split[0];
                        prizeId = split[1];
                    } else {
                        continue;
                    }
                } else {
                    prizeId = item;
                }
                ResourceKeyConfigData.ResourceMeta resourceMeta = configMap.get(prizeId);
                if (resourceMeta != null) {
                    LlluminateYouStarVO.ResourceDataVO itemVO = new LlluminateYouStarVO.ResourceDataVO();
                    BeanUtils.copyProperties(resourceMeta, itemVO);
                    if (StringUtils.hasLength(aid)) {
                        ActorData aidData = actorDao.getActorDataFromCache(aid);
                        itemVO.setName(aidData.getName());
                    }
                    resList.add(itemVO);
                }
            }
        }
        return resList;
    }

    private Map<String, ResourceKeyConfigData.ResourceMeta> getConfigMap(List<ResourceKeyConfigData> keyConfigDataList) {
        if (CollectionUtils.isEmpty(keyConfigDataList)) {
            logger.info(" keyConfigDataList:{}", keyConfigDataList);
            throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
        }
        Map<String, ResourceKeyConfigData.ResourceMeta> allPrizeConfigMap = new HashMap<>();
        keyConfigDataList.forEach(item -> {
            if (CollectionUtils.isEmpty(item.getResourceMetaList())) {
                logger.info("key:{} resourceMetaList:{}", item.getKey(), item.getResourceMetaList());
                throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
            }
            if (item.getKeyType() != 1) {
                logger.info("key:{} keyType not 1:{}", item.getKey(), item.getKeyType());
                throw new CommonH5Exception(ActivityHttpCode.SHINING_CONFIG_ERROR);
            }
            item.getResourceMetaList().forEach(subItem -> {
                allPrizeConfigMap.put(subItem.getMetaId(), subItem);
            });
        });
        return allPrizeConfigMap;
    }

    private int getIndexLevel(int score) {
        List<Integer> tempLevelNumList = new ArrayList<>(SCORE_LEVEL_LIST);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

    private int getFixOnline(int actorsCount) {
        if (actorsCount < 5) {
            return actorsCount;
        } else if (actorsCount < 7) {
            return actorsCount + 1;
        } else if (actorsCount < 10) {
            return actorsCount + 2;
        } else if (actorsCount < 15) {
            return actorsCount + 3;
        } else if (actorsCount < 25) {
            return actorsCount + 4;
        } else if (actorsCount < 50) {
            return actorsCount + 5;
        } else {
            return actorsCount + 6 + (actorsCount / 50);
        }
    }

    public String getAwardIdByProbability(Map<String, Integer> sourceMap) {
        try {
            if (CollectionUtils.isEmpty(sourceMap)) {
                logger.info("getAwardIdByProbability sourceMap is empty");
                return null;
            }
            int total = 0;
            Map<Integer, String> mapRatio = new HashMap<>();
            List<Integer> ratioList = new ArrayList<>(); // 这样大于等于0，小于1000取第一个（key为1000） 、大于等于1000，小于2000取第二个 （key为2000）
            for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
                String awardId = entry.getKey();
                int value = entry.getValue();
                if (value <= 0) {
                    continue;
                }
                total += value;
                mapRatio.put(total, awardId);
                ratioList.add(total);
            }
            if (total == 0) {
                logger.info("getAwardIdByProbability total is zero sourceMap={} ", sourceMap);
                return null;
            }
            int ratio = ThreadLocalRandom.current().nextInt(0, total);//0-(total-1)
            if (!ratioList.contains(ratio)) {
                ratioList.add(ratio);
                Collections.sort(ratioList);
            }

            int index = ratioList.indexOf(ratio);
            int destNum = ratioList.get(index + 1);
            String hitType = mapRatio.get(destNum);
//            logger.info("getAwardIdByProbability-->ratioList:{} mapRatio:{} total:{} ratio:{} index:{} destNum:{} hitType:{}"
//                    , ratioList, mapRatio, total, ratio, index, destNum, hitType);
            return hitType;
        } catch (Exception e) {
            logger.error("getAwardIdByProbability error sourceMap:{} msg:{}", sourceMap, e.getMessage(), e);
            return null;
        }
    }

    public void distributionRanking() {
        // 日榜
        distributionRanking(2);
        // 总榜
        distributionRanking(1);
    }

    public String testTotal() {
        distributionRanking(1);
        return "";
    }

    // 日榜排行榜奖励或者总榜奖励
    private void distributionRanking(int rankType) {
        try {
            String currentDate = DateHelper.ARABIAN.getYesterdayStr(new Date());
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
            int endTime = activityData.getEndTime();
            int now = DateHelper.getNowSeconds();
            int status = activityData.getStatus();
            if (now - 3600 > endTime) {
                logger.info("time over return currentDate:{} status:{}", currentDate, status);
                return;
            }
            String rankKey;
            Map<Integer, List<String>> resKeyMap;
            if (rankType == 1) {
                String end = DateHelper.ARABIAN.formatDateInDay(new Date((endTime - 10) * 1000L));
                if (currentDate.equals(end)) {
                    rankKey = getZetTotalKey();
                    resKeyMap = TOTAL_RES_KEY_MAP;
                } else {
                    logger.info("not endDay return currentDate:{} end:{}", currentDate, end);
                    return;
                }
            } else {
                rankKey = getZetDayKey(currentDate);
                resKeyMap = DAY_RES_KEY_MAP;
            }

            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(rankKey, 10);
            int rank = 1;
            List<String> resList = null;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                String rankRoomId = entry.getKey();
                switch (rank) {
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                    case 7:
                    case 8:
                    case 9:
                    case 10:
                        resList = resKeyMap.get(rank);
                        break;
                }
                handleRes(rankRoomId, resList.get(0), resList.get(1));
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String rankRoomId, String resKeyHost, String resKeyManger) {
        String hostUid = RoomUtils.getRoomHostId(rankRoomId);
        logger.info("hostUid:{} resKeyHost:{}", hostUid, resKeyHost);
        resourceKeyHandlerService.sendResourceData(hostUid, resKeyHost,
                ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);

        String mangerUid = cacheDataService.getCommonHashStrValue(getHashBindKey(), rankRoomId);
        if (StringUtils.hasLength(mangerUid)) {
            logger.info("mangerUid:{} resKeyManger:{}", mangerUid, resKeyManger);
            resourceKeyHandlerService.sendResourceData(mangerUid, resKeyManger,
                    ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
        }

    }

    private void doReportDrawEvent(String uid, int num, String drawType, String roomId, List<String> resultList,
                                   Map<String, ResourceKeyConfigData.ResourceMeta> srcMap) {
        StarBeatGameLogEvent event = new StarBeatGameLogEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE_EN);
        event.setSence_detail(0);
        event.setRoom_id(roomId);
        event.setCost_diamonds(0);
        int costTicket = NORMAL_DRAW_RES_KEY.equals(drawType) ? num : 0;
        event.setCost_ticket(costTicket);
        event.setDraw_nums(num);
        Map<String, JSONObject> prizeConfigMap = new HashMap<>();
        resultList.forEach(item -> {
            JSONObject jsonObject = new JSONObject();
            ResourceKeyConfigData.ResourceMeta resourceMeta = srcMap.get(item);
            jsonObject.put("resourceName", resourceMeta.getResourceNameEn());
            jsonObject.put("resourceNum", resourceMeta.getResourceNumber());
            jsonObject.put("resourceTime", resourceMeta.getResourceTime());
            prizeConfigMap.put(item, jsonObject);
        });
        event.setDraw_result(JSON.toJSONString(prizeConfigMap));
        event.setDesc(drawType);
        eventReport.track(new EventDTO(event));
    }

    private void doAddScoreEvent(String roomId, String aid, int score, int actionType) {
        String hostUid = RoomUtils.getRoomHostId(roomId);
        GetActivitySpecialItemsRecordEvent event = new GetActivitySpecialItemsRecordEvent();
        event.setUid(hostUid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setRoomid(roomId);
        event.setActivity_special_items_resource(String.valueOf(actionType));
        event.setGet_activity_special_items_nums(score);
        event.setFrom_uid(aid);
        eventReport.track(new EventDTO(event));
    }

    private String getHashBindKey() {
        return ACTIVITY_ID + ":bind";
    }

    private String getHashDetailKey(String dayStr) {
        return ACTIVITY_ID + ":" + dayStr;
    }

    private String getZetDayKey(String dayStr) {
        return ACTIVITY_ID + ":day:" + dayStr;
    }

    private String getZetTotalKey() {
        return ACTIVITY_ID + ":total";
    }

    private String getHashFirstOpenKey() {
        return ACTIVITY_ID + ":firstOpen";
    }


    /**
     * 每日剩余抽奖次数
     *
     * @param dayStr
     * @return
     */
    private String getZetDayDrawKey(String dayStr) {
        return ACTIVITY_ID + ":draw:day:" + dayStr;
    }

    /**
     * 每日首抽，uid排重
     *
     * @param dayStr
     * @return
     */
    private String getHashDayFirstKey(String dayStr) {
        return ACTIVITY_ID + ":draw:first:day:" + dayStr;
    }

    /**
     * 活动首抽，设备排重
     *
     * @return
     */
    private String getHashDeviceFirstKey() {
        return ACTIVITY_ID + ":draw:first:device";
    }

    private String getListRollKey() {
        return ACTIVITY_ID + ":draw:roll";
    }

    private String getListHistoryKey(String uid) {
        return ACTIVITY_ID + ":draw:history:" + uid;
    }

}
