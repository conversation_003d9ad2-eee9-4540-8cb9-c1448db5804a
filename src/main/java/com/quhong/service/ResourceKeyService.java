package com.quhong.service;

import com.quhong.data.dto.ResourceKeyDTO;
import com.quhong.data.vo.ResourceKeyConfigVO;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

@Service
public class ResourceKeyService {

    private static final Logger logger = LoggerFactory.getLogger(ResourceKeyService.class);

    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;

    public ResourceKeyConfigVO getResourceKeyList(String uid, ResourceKeyDTO dto) {

        ResourceKeyConfigVO vo = new ResourceKeyConfigVO();
        List<String> keyList = dto.getKeyList();
        if(CollectionUtils.isEmpty(keyList)){
            vo.setResourceKeyDataList(Collections.emptyList());
            return vo;
        }
        vo.setResourceKeyDataList(resourceKeyConfigDao.findListByKeyList(keyList));
        return vo;
    }

}
