package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.SudGameInfo;
import com.quhong.data.vo.*;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.enums.SudGameConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.CollectionCommonRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

/**
 * 房间数据页活动
 */
@Service
public class RoomDataCenterService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(RoomDataCenterService.class);
    private static final String ACTIVITY_TITLE_EN = "Room data Center";
    public static final String ACTIVITY_ID = "6892d271cfc6214c49bb476a";

    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/room_data_center/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/room_data_center/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";


    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.ON_MIC_TIME, CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL,
            CommonMqTaskConstant.JOIN_ROOM_MEMBER);

    private static final List<Integer> TOTAL_REWARD_LIST = Arrays.asList(
            2, 4, 6);

    static {
    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private EventReport eventReport;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private CollectionCommonRedis collectionCommonRedis;
    @Resource
    private ActorDao actorDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
        }
    }

    private String getHashOnlineReportKey(String dateTimeH) {
        return String.format("onlineReport:%s", dateTimeH);
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:room_data_center:user:" + uid;
    }

    private String getUserHashDetailKey(String activityId, String dayStr) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":room_data_center:user:detail:" + dayStr;
    }


    public RoomDataCenterVO roomDataCenterInfo(String activityId, String uid, String dayStr) {
        RoomDataCenterVO vo = new RoomDataCenterVO();
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());


        if(StringUtils.isEmpty(dayStr)){
            dayStr = getDayByBase(activityId,uid);
        }
        // 获取房间数据
        String detailUserKey = getUserHashDetailKey(activityId, dayStr);
        RoomDataCenterVO.DetailInfo detailInfo = cacheDataService.getRoomDataCenterVODetailInfo(detailUserKey, dayStr, RoomUtils.formatRoomId(uid));

        // 填充DetailInfoVO数据
        RoomDataCenterVO.DetailInfoVO detailInfoVO = new RoomDataCenterVO.DetailInfoVO();
        detailInfoVO.setDayStr(dayStr);

        // 房间活跃数据
        detailInfoVO.setRoomOwnerTime(detailInfo.getRoomOwnerTime());
        detailInfoVO.setRoomMaxOnlineCount(detailInfo.getRoomMaxOnlineCount());
        detailInfoVO.setInviteUserCount(detailInfo.getInviteUserSet() != null ? detailInfo.getInviteUserSet().size() : 0);
        detailInfoVO.setInviteNewUserCount(detailInfo.getInviteNewUserSet() != null ? detailInfo.getInviteNewUserSet().size() : 0);
        detailInfoVO.setAllUserMicCount(detailInfo.getAllUserMicSet() != null ? detailInfo.getAllUserMicSet().size() : 0);
        detailInfoVO.setNewUserMicCount(detailInfo.getNewUserMicSet() != null ? detailInfo.getNewUserMicSet().size() : 0);
        detailInfoVO.setAllUserJoinRoomCount(detailInfo.getAllUserJoinRoomCount());
        detailInfoVO.setNewUserJoinRoomCount(detailInfo.getNewUserJoinRoomCount());

        // 房间消耗数据
        detailInfoVO.setSendGiftCount(detailInfo.getSendGiftSet() != null ? detailInfo.getSendGiftSet().size() : 0);
        detailInfoVO.setAllSendGiftDiamondCount(detailInfo.getAllSendGiftDiamondCount());
        detailInfoVO.setNewSendGiftDiamondCount(detailInfo.getNewSendGiftDiamondCount());

        // 房间活动数据
        detailInfoVO.setSendGiftEventCount(detailInfo.getSendGiftEventSet() != null ? detailInfo.getSendGiftEventSet().size() : 0);
        detailInfoVO.setAllSendGiftDiamondEventCount(detailInfo.getAllSendGiftDiamondEventCount());
        detailInfoVO.setAllUserMicEventCount(detailInfo.getAllUserMicEventSet() != null ? detailInfo.getAllUserMicEventSet().size() : 0);
        detailInfoVO.setNewUserMicEventCount(detailInfo.getNewUserMicEventSet() != null ? detailInfo.getNewUserMicEventSet().size() : 0);

        vo.setDetailInfoVO(detailInfoVO);
        return vo;
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        syncAddHandle(roomId, data, null, null);
    }


    private boolean checkAc(String roomId) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return false;
        }
        return true;
    }

    private void syncAddHandle(String roomId, CommonMqTopicData mqData, SendGiftData giftData, Integer maxOnlineCount) {
        if (!RoomUtils.isVoiceRoom(roomId)) {
            return;
        }
        if (!checkAc(roomId)) {
            return;
        }
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        String now = getDayByBase(ACTIVITY_ID, RoomUtils.getRoomHostId(roomId), true, activityData.getAcNameEn().startsWith("test"));
        String detailUserKey = getUserHashDetailKey(null, now);
        boolean isRoomEvent = cacheDataService.isRunRoomEvent(roomId) != null;
        synchronized (stringPool.intern(getLocalEventUserKey(roomId))) {
            RoomDataCenterVO.DetailInfo detailInfo = cacheDataService.getRoomDataCenterVODetailInfo(detailUserKey, now, roomId);
            int addScore = 0; // 1 更新detailInfo
            if (mqData != null) {
                String item = mqData.getItem();
                String uid = mqData.getUid();
                String aid = mqData.getAid();

                if (CommonMqTaskConstant.ON_MIC_TIME.equals(item)) {
                    // 用户上麦1分钟处理
                    if (StringUtils.hasLength(uid)) {
                        ActorData actorData = actorDao.getActorDataFromCache(uid);
                        if (actorData != null) {
                            boolean isNewUser = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
                            boolean hasChange = false;

                            // 检查并添加到所有用户上麦集合
                            if (!detailInfo.getAllUserMicSet().contains(uid)) {
                                detailInfo.getAllUserMicSet().add(uid);
                                hasChange = true;
                            }

                            // 检查并添加到新用户上麦集合
                            if (isNewUser && !detailInfo.getNewUserMicSet().contains(uid)) {
                                detailInfo.getNewUserMicSet().add(uid);
                                hasChange = true;
                            }

                            // 房间活动期间数据
                            if (isRoomEvent) {
                                if (!detailInfo.getAllUserMicEventSet().contains(uid)) {
                                    detailInfo.getAllUserMicEventSet().add(uid);
                                    hasChange = true;
                                }
                                if (isNewUser && !detailInfo.getNewUserMicEventSet().contains(uid)) {
                                    detailInfo.getNewUserMicEventSet().add(uid);
                                    hasChange = true;
                                }
                            }

                            // 房主上麦时长处理
                            if (RoomUtils.isHomeowner(uid, roomId)) {
                                detailInfo.setRoomOwnerTime(detailInfo.getRoomOwnerTime() + 1);
                                hasChange = true;
                            }

                            if (hasChange) {
                                addScore = 1;
                            }
                        }
                    }
                } else if (CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL.equals(item)) {
                    // 邀请用户上麦处理
                    if (StringUtils.hasLength(aid)) {
                        ActorData actorData = actorDao.getActorDataFromCache(aid);
                        if (actorData != null) {
                            boolean isNewUser = ActorUtils.isNewDeviceAccount(aid, actorData.getFirstTnId());
                            boolean hasChange = false;

                            // 检查并添加到邀请用户集合
                            if (!detailInfo.getInviteUserSet().contains(aid)) {
                                detailInfo.getInviteUserSet().add(aid);
                                hasChange = true;
                            }

                            // 检查并添加到邀请新用户集合
                            if (isNewUser && !detailInfo.getInviteNewUserSet().contains(aid)) {
                                detailInfo.getInviteNewUserSet().add(aid);
                                hasChange = true;
                            }

                            if (hasChange) {
                                addScore = 1;
                            }
                        }
                    }
                } else if (CommonMqTaskConstant.JOIN_ROOM_MEMBER.equals(item)) {
                    // 用户加入房间处理
                    if (StringUtils.hasLength(uid)) {
                        ActorData actorData = actorDao.getActorDataFromCache(uid);
                        if (actorData != null) {
                            boolean isNewUser = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
                            detailInfo.setAllUserJoinRoomCount(detailInfo.getAllUserJoinRoomCount() + 1);
                            if (isNewUser) {
                                detailInfo.setNewUserJoinRoomCount(detailInfo.getNewUserJoinRoomCount() + 1);
                            }
                            addScore = 1;
                        }
                    }
                }
            } else if (giftData != null) {
                // 送礼处理
                String fromUid = giftData.getFrom_uid();
                if (StringUtils.hasLength(fromUid)) {
                    ActorData actorData = actorDao.getActorDataFromCache(fromUid);
                    if (actorData != null) {
                        boolean isNewUser = ActorUtils.isNewDeviceAccount(fromUid, actorData.getFirstTnId());
                        int totalPrice = giftData.getNumber() * giftData.getPrice();
                        if (giftData.getAid_list() != null && !giftData.getAid_list().isEmpty()) {
                            totalPrice *= giftData.getAid_list().size();
                        }

                        boolean hasChange = false;

                        // 检查并添加到送礼用户集合
                        if (!detailInfo.getSendGiftSet().contains(fromUid)) {
                            detailInfo.getSendGiftSet().add(fromUid);
                            hasChange = true;
                        }

                        // 钻石消耗总是累加的，所以总是有变化
                        detailInfo.setAllSendGiftDiamondCount(detailInfo.getAllSendGiftDiamondCount() + totalPrice);
                        hasChange = true;

                        if (isNewUser) {
                            detailInfo.setNewSendGiftDiamondCount(detailInfo.getNewSendGiftDiamondCount() + totalPrice);
                        }

                        // 房间活动期间数据
                        if (isRoomEvent) {
                            if (!detailInfo.getSendGiftEventSet().contains(fromUid)) {
                                detailInfo.getSendGiftEventSet().add(fromUid);
                            }
                            detailInfo.setAllSendGiftDiamondEventCount(detailInfo.getAllSendGiftDiamondEventCount() + totalPrice);
                        }

                        if (hasChange) {
                            addScore = 1;
                        }
                    }
                }
            } else if (maxOnlineCount != null) {
                // 房间最大在线人数处理
                if (maxOnlineCount > detailInfo.getRoomMaxOnlineCount()) {
                    detailInfo.setRoomMaxOnlineCount(maxOnlineCount);
                    addScore = 1;
                }
            }
            if (addScore > 0) {
                String jsonStr = JSONObject.toJSONString(detailInfo);
                activityCommonRedis.setCommonHashData(detailUserKey, roomId, jsonStr);
                if(ServerConfig.isNotProduct()) {
                    logger.info("success add roomId:{} detailInfo:{} mqData:{} giftData:{} maxOnlineCount:{}",
                            roomId, jsonStr, JSON.toJSONString(mqData), JSON.toJSONString(giftData), maxOnlineCount);
                }
                            }
        }
    }

    public void fillMaxOnlineCount() {
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }

        long nowTime = System.currentTimeMillis();
        String beforeDateTimeH = DateHelper.DEFAULT.formatDateInHour(new Date(nowTime - 3600 * 1000));
        String beforeDateTimeKey = getHashOnlineReportKey(beforeDateTimeH);
        Map<String, Integer> beforeAllRoomCount = collectionCommonRedis.getCommonHashAll(beforeDateTimeKey);
        if (CollectionUtils.isEmpty(beforeAllRoomCount)) {
            return;
        }
        for (Map.Entry<String, Integer> entry : beforeAllRoomCount.entrySet()) {
            String roomId = entry.getKey();
            int maxOnlineCount = entry.getValue();
            syncAddHandle(roomId, null, null, maxOnlineCount);
        }
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        try {
            String roomId = giftData.getRoomId();
            if (StringUtils.isEmpty(roomId)) {
                return;
            }
            syncAddHandle(roomId, null, giftData, null);
        } catch (Exception e) {
            logger.error("sendGiftHandle error: {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }

}
