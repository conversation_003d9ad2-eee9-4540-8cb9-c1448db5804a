package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.GreedyElephantVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 贪婪的大象
 */
@Service
public class GreedyElephantService extends OtherActivityService implements TaskMsgHandler {

    private static final Logger logger = LoggerFactory.getLogger(GreedyElephantService.class);
    private static final String ACTIVITY_TITLE_EN = "Greedy Elephant";
    private static final String ACTIVITY_TITLE_AR = "الفيل الجشع";
    private static final String ACTIVITY_DESC = "Greedy Elephant reward";
    public static final String ACTIVITY_ID = "68514244a1f9e577905eb60e";
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/greedy_elephant_icon.png";
    
    // 等级任务：统计下注钻石数的奖励等级
    private static final List<Integer> BET_REWARD_LEVEL_LIST = Arrays.asList(40, 140, 300, 800, 2000, 5000, 15000, 50000, 160000, 400000);
    private static final List<String> BET_REWARD_LEVEL_KEY_LIST = Arrays.asList("GreedyTask1", "GreedyTask2", "GreedyTask3", "GreedyTask4", "GreedyTask5", "GreedyTask6", "GreedyTask7", "GreedyTask8", "GreedyTask9", "GreedyTask10");
    
    // 赢取钻石榜单奖励
    private static final List<String> WIN_RANK_KEY_LIST = Arrays.asList("GreedyGameTop1", "GreedyGameTop2", "GreedyGameTop3", "GreedyGameTop4", "GreedyGameTop5", "GreedyGameTop6-10");
    
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/greedy_elephant/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/greedy_elephant/?activityId=%s", ACTIVITY_ID);
    private static final String GAME_URL = ServerConfig.isProduct() ? "https://h5.bigluckygame.com/game_greedy/index.html?appId=888888&gameName=greedy" : "https://api.springluckygame.com/game_greedy/?appId=880088&gameName=greedy";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private WhiteTestDao whiteTestDao;

    /**
     * 获取下注总任务排行榜Key
     */
    private String getGreedyTotalRankKey(String activityId) {
        return String.format("greedyBetTotalRank:%s", activityId);
    }

    /**
     * 获取贪婪大象活动配置
     */
    public GreedyElephantVO greedyElephantConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        GreedyElephantVO vo = new GreedyElephantVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        // 设置下注总任务排行榜
        List<OtherRankingListVO> betRankingList = new ArrayList<>();
        OtherRankingListVO myBetRank = new OtherRankingListVO();
        makeOtherRankingData(betRankingList, myBetRank, getGreedyTotalRankKey(activityId), uid, 10, true);
        vo.setBetRankingList(betRankingList);
        vo.setMyBetRank(myBetRank);
        vo.setGameUrl(GAME_URL);
        return vo;
    }

    /**
     * 分发赢取钻石排行榜奖励
     */
    public void distributionWinRanking() {
        try {
            String winRankKey = getGreedyTotalRankKey(ACTIVITY_ID);
            Map<String, Integer> winRankingMap = activityCommonRedis.getCommonRankingMap(winRankKey, 10);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : winRankingMap.entrySet()) {
                String rankUid = entry.getKey();
                String resourceKey = getBetRankResourceKey(rank);
                if (resourceKey != null) {
                    resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                }
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionWinRanking error: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据排名获取赢取榜单奖励资源Key
     */
    private String getBetRankResourceKey(int rank) {
        if (rank == 1) return WIN_RANK_KEY_LIST.get(0);
        if (rank == 2) return WIN_RANK_KEY_LIST.get(1);
        if (rank == 3) return WIN_RANK_KEY_LIST.get(2);
        if (rank == 4) return WIN_RANK_KEY_LIST.get(3);
        if (rank == 5) return WIN_RANK_KEY_LIST.get(4);
        if (rank >= 6 && rank <= 10) return WIN_RANK_KEY_LIST.get(5);
        return null;
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {

        String item = data.getItem();
        if (!CommonMqTaskConstant.PLAY_GREEDY_GAME.equals(item)) {
            return;
        }

        CommonMqTopicData.PlayGameInfo playGameInfo = JSONObject.parseObject(data.getJsonData(), CommonMqTopicData.PlayGameInfo.class);
        if (playGameInfo == null){
            return;
        }

        String fromUid = data.getUid();
        // if (ServerConfig.isProduct() && !whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
        //     // 灰度测试
        //     return;
        // }

        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        synchronized (stringPool.intern("betGame" + fromUid)) {
            int betValue = playGameInfo.getCurrencyValue();
            String betTotalRankKey = getGreedyTotalRankKey(ACTIVITY_ID);
            int currentBetNum = activityCommonRedis.getCommonZSetRankingScore(betTotalRankKey, fromUid);

            // 处理下注等级任务奖励
            while (betValue > 0) {
                List<Integer> tempLevelNumList = new ArrayList<>(BET_REWARD_LEVEL_LIST);
                int currentLevelIndex = 0;

                if (tempLevelNumList.contains(currentBetNum)) {
                    currentLevelIndex = tempLevelNumList.indexOf(currentBetNum);
                } else {
                    tempLevelNumList.add(currentBetNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentBetNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if (upLevelIndex >= BET_REWARD_LEVEL_LIST.size()) {
                    // 已达到最高等级，直接增加积分
                    activityCommonRedis.incrCommonZSetRankingScore(betTotalRankKey, fromUid, betValue);
                    betValue = 0;
                } else {
                    int upLevelNum = BET_REWARD_LEVEL_LIST.get(upLevelIndex);
                    int needUpNum = upLevelNum - currentBetNum;

                    if (betValue >= needUpNum) {
                        // 可以升级
                        currentBetNum = currentBetNum + needUpNum;
                        betValue = betValue - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(betTotalRankKey, fromUid, needUpNum);

                        // 发放等级奖励
                        String resKey = BET_REWARD_LEVEL_KEY_LIST.get(upLevelIndex);
                        resourceKeyHandlerService.sendResourceData(fromUid, resKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                    } else {
                        // 不能升级，直接增加积分
                        activityCommonRedis.incrCommonZSetRankingScore(betTotalRankKey, fromUid, betValue);
                        betValue = 0;
                    }
                }
            }
        }
    }
}
