package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.HappyTripVO;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class HappyTripService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(HappyTripService.class);

    private static final String MILEAGE_AVAILABLE_KEY = "mileageAvailableNum"; // 剩余可用里程数
    private static final String CONSUME_MILEAGE_KEY = "consumeMileageNum";// 消耗里程数
    private static final String TOTAL_MILEAGE_KEY = "totalMileageNum"; // 总里程数
    private static final String BID_TOTAL_NUM_KEY = "bidTotalNum"; // 拍的藏品总次数
    private static final String LAST_ROUND_WIN_INFO_RETURNED_KEY = "lastRoundWinInfoReturned"; // 上轮中奖是否已经返回

    private static final String TEAM_ID_KEY = "teamId";                    // 队伍id-Key
    public static final String ACTIVITY_ID = "kkk";
    public static final String ACTIVITY_TITLE_EN = "Happy Trip";
    private static final String HAPPY_TRIP_BID_KEY = "HappyTripBidKey";    // 拍品资源key
    private static final Integer LIMIT_INIT_POOL = 30;
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final Integer MAX_TEAM_SIZE = 3;
    private static final List<Integer> TEAM_SCORE_LEVEL_LIST = Arrays.asList(100, 10000, 60000, 140000, 300000, 600000);
    private static final List<String> TEAM_LEVEL_KEY_LIST = Arrays.asList("HappyAnniversaryTripReward1", "HappyAnniversaryTripReward2", "HappyAnniversaryTripReward3"
            , "HappyAnniversaryTripReward4", "HappyAnniversaryTripReward5", "HappyAnniversaryTripReward6");
    private static final List<String> TRIP_TOP_KEY_LIST = Arrays.asList("HappyAnniversaryTripTop1", "HappyAnniversaryTripTop2", "HappyAnniversaryTripTop3"
            , "HappyAnniversaryTripTop4", "HappyAnniversaryTripTop5", "HappyAnniversaryTripTop6-10", "HappyAnniversaryTripTop6-10"
            , "HappyAnniversaryTripTop6-10", "HappyAnniversaryTripTop6-10", "HappyAnniversaryTripTop6-10");
    private static final Interner<String> stringPool = Interners.newWeakInterner();


    @Resource
    private EventReport eventReport;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;


    // hash 每一轮的开奖结果,filed为roundNum
    private String getHashRoundActivityId(String activityId) {
        return String.format("happy_trip:all:activityId:%s", activityId);
    }

    private String getStrNowBidInfo(String activityId) {
        return String.format("happy_trip:bidInfo:activityId:%s", activityId);
    }

    // 个人数据信息key
    private String getHashActivityId(String activityId, String uid) {
        return String.format("happy_trip:my:%s:%s", activityId, uid);
    }

    // 个人消耗里程数key
    private String getZSetUserConsumeRankKey(String activityId) {
        return String.format("happy_trip:consumeRank:%s", activityId);
    }

    // 个人竞拍数据key
    private String getZSetUserBidRankKey(String activityId, int roundNum) {
        return String.format("happy_trip:bidRank:%s:%s", activityId, roundNum);
    }


    // 团队成员key
    private String getListTeamMemberKey(String activityId, String teamId) {
        return String.format("happy_trip:teamMember:%s:%s", activityId, teamId);
    }

    // 团队榜单key
    private String getZSetTeamRankKey(String activityId) {
        return String.format("happy_trip:teamRank:%s", activityId);
    }


    // 个人投注历史记录Key,结算后再记录
    private String getListHistoryBidKey(String activityId, String uid) {
        return String.format("happy_trip:history:user:%s:%s", activityId, uid);
    }

    // 开奖记录Key,结算后再记录
    private String getListHistoryBidKey(String activityId) {
        return String.format("happy_trip:history:roll:%s:%s", activityId);
    }


    // 限制每日分享次数
    private String getShareLimitKey(String activityId, String uid, String aid) {
        return String.format("shareLimit:%s:%s:%s", activityId, uid, aid);
    }

    private String getLocalUserKey(String uid) {
        return "lock:happy_trip:user:" + uid;
    }


    private int getCurrLevelScore(int score) {
        List<Integer> tempLevelNumList = new ArrayList<>(TEAM_SCORE_LEVEL_LIST);
        tempLevelNumList.add(0, 0);
        return getBaseIndexLevel(score, tempLevelNumList);
    }

    public HappyTripVO happyTripConfig(String activityId, String uid) {
        HappyTripVO vo = new HappyTripVO();
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        vo.setMileageTotalNum(Integer.valueOf(userDataMap.getOrDefault(MILEAGE_AVAILABLE_KEY, "0")));


        String teamId = userDataMap.get(TEAM_ID_KEY);
        HappyTripVO.TripTeamConfigVO teamInfoVO = new HappyTripVO.TripTeamConfigVO();
        if (ObjectUtils.isEmpty(teamId)) {
            // 未组队成功则显示只有自己在队伍中
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            teamInfoVO.setTeamName(actorData.getName());
            teamInfoVO.setTeamLeadUid(uid);
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
            teamInfo.setUid(uid);
            teamInfo.setName(actorData.getName());
            teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            teamMemberList.add(teamInfo);
            teamInfoVO.setTeamMemberList(teamMemberList);
        } else {
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), MAX_TEAM_SIZE);
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            teamInfoVO.setTeamId(teamId);
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0) {
                    teamInfoVO.setTeamName(actorData.getName());
                    teamInfoVO.setTeamLeadUid(memberUid);
                }
                HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamMemberList.add(teamInfo);
            }
            teamInfoVO.setTeamMemberList(teamMemberList);
        }
        vo.setTeamInfo(teamInfoVO);

        // 设置竞拍信息

        return vo;
    }

    public HappyTripVO happyTripTeamRank(String activityId, String uid) {
        HappyTripVO vo = new HappyTripVO();
        // 设置榜单数据
        List<HappyTripVO.TripTeamConfigVO> teamRankList = new ArrayList<>();
        Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId), 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
            String rankTeamId = entry.getKey();
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, rankTeamId), MAX_TEAM_SIZE);
            HappyTripVO.TripTeamConfigVO dragonTeamConfig = new HappyTripVO.TripTeamConfigVO();
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0) {
                    dragonTeamConfig.setTeamName(actorData.getName());
                    dragonTeamConfig.setTeamLeadUid(memberUid);
                }
                HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamMemberList.add(teamInfo);
            }
            dragonTeamConfig.setTeamId(rankTeamId);
            dragonTeamConfig.setTeamScore(entry.getValue());
            dragonTeamConfig.setTeamMemberList(teamMemberList);
            dragonTeamConfig.setRank(rank);
            dragonTeamConfig.setTripLevel(getCurrLevelScore(entry.getValue()));
            teamRankList.add(dragonTeamConfig);
            rank += 1;
        }
        vo.setTeamRankList(teamRankList);

        // 设置我的队伍数据
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        String myTeamId = userDataMap.get(TEAM_ID_KEY);
        if (!ObjectUtils.isEmpty(myTeamId)) {
            HappyTripVO.TripTeamConfigVO myTeamConfig = new HappyTripVO.TripTeamConfigVO();
            List<String> myTeamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, myTeamId), MAX_TEAM_SIZE);
            List<HappyTripVO.TeamInfoVO> teamMemberList = new ArrayList<>();
            for (int i = 0; i < myTeamMemberUidList.size(); i++) {
                String memberUid = myTeamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                String memberActivityId = getHashActivityId(activityId, memberUid);
                Map<String, String> memberDataMap = memberUid.equals(uid) ? userDataMap : activityCommonRedis.getCommonHashAllMapStr(memberActivityId);
                int trainNum = Integer.parseInt(memberDataMap.getOrDefault(TOTAL_MILEAGE_KEY, "0"));
                if (i == 0) {
                    myTeamConfig.setTeamName(actorData.getName());
                    myTeamConfig.setTeamLeadUid(memberUid);
                }
                HappyTripVO.TeamInfoVO teamInfo = new HappyTripVO.TeamInfoVO();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamInfo.setScore(trainNum);
                teamMemberList.add(teamInfo);
            }
            myTeamConfig.setTeamId(myTeamId);
            int myTeamScore = activityCommonRedis.getCommonZSetRankingScore(getZSetTeamRankKey(activityId), myTeamId);
            myTeamConfig.setTeamScore(myTeamScore);
            myTeamConfig.setTeamMemberList(teamMemberList);

            int myRank = activityCommonRedis.getCommonZSetRank(getZSetTeamRankKey(activityId), myTeamId);
            myTeamConfig.setRank(myRank);
            myTeamConfig.setTripLevel(getCurrLevelScore(myTeamScore));

            // 处理距离上一名分差
            int muchScore = 0;
            int lastRank = myRank - 1;
            if (lastRank > 0) {
                // 只获取上一名的数据
                Map<String, Integer> lastRankMap = activityCommonRedis.getCommonRankingMapByPage(getZSetTeamRankKey(activityId), lastRank - 1, lastRank);
                if (!CollectionUtils.isEmpty(lastRankMap)) {
                    // 只取第一个元素，即上一名的分数
                    for (Map.Entry<String, Integer> entry : lastRankMap.entrySet()) {
                        int lastScore = entry.getValue();
                        logger.info("happyTripTeamRank lastRankMap rankTeamId:{} lastScore:{} myTeamScore:{}", entry.getKey(), lastScore, myTeamScore);
                        // 计算分差：上一名分数 - 我的分数
                        muchScore = lastScore - myTeamScore;
                        break; // 只处理第一个元素
                    }
                }
            }
            myTeamConfig.setMuchScore(muchScore);
            vo.setTeamInfo(myTeamConfig);
        }
        return vo;
    }

    /**
     * 驯龙礼物发送处理
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        synchronized (stringPool.intern(getLocalUserKey(fromUid))) {
            int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            String hashActivityId = getHashActivityId(activityId, fromUid);
            if (totalBeans > 0) {
                activityCommonRedis.incCommonHashNum(hashActivityId, MILEAGE_AVAILABLE_KEY, totalBeans);
                activityCommonRedis.incCommonHashNum(hashActivityId, TOTAL_MILEAGE_KEY, totalBeans);
                doReportSpecialItemsEvent(activityId, fromUid, 1, totalBeans, 1);
            }
        }
    }

    /**
     * 拍卖下注-竞价
     *
     * @param activityId: 活动id
     * @param uid:        用户uid
     * @param amount:     下注的里程数
     * @return vo
     */

    public HappyTripVO.BidRewardDataVO happyTripBid(String activityId, String uid, int amount) {
        checkActivityTime(activityId);

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        HappyTripVO.BidRewardDataVO vo = new HappyTripVO.BidRewardDataVO();

        synchronized (stringPool.intern(getLocalUserKey(uid))) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            int stoneNum = Integer.parseInt(userDataMap.getOrDefault(MILEAGE_AVAILABLE_KEY, "0"));
            if (stoneNum <= 0 || stoneNum < amount) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }


        }
        return vo;
    }

    /**
     * 竞拍相关的信息
     *
     * @param activityId
     * @param uid
     * @return
     */
    public HappyTripVO happyTripBidInfo(String activityId, String uid) {
        HappyTripVO.BidRewardDataVO bidRewardDataVO = new HappyTripVO.BidRewardDataVO();
        List<HappyTripVO.BidRankingListVO> bidRankingList = new ArrayList<>();   // 竞拍榜单
        HappyTripVO.BidRankingListVO myBidRank = new HappyTripVO.BidRankingListVO();   // 我的竞拍排名


        HappyTripVO vo = new HappyTripVO();
        vo.setBidRewardDataVO(bidRewardDataVO);
        vo.setBidRankingList(bidRankingList);
        vo.setMyBidRank(myBidRank);
        return vo;
    }

    /**
     * 结算竞拍-每小时一次
     */
    public void bidSettleAuction() {
        String activityId = ACTIVITY_ID;
        if(getOtherRankingActivityNull(activityId) == null){
            return;
        }
        if (!inActivityTime(activityId)) {

        }
        ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig();
        if (resourceKeyConfigData == null) {
            return;
        }
        String resKey = resourceKeyConfigData.getKey();
        Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(meteId);
        if (resourceMeta == null) {
            return;
        }

        String hashActivityId = getHashActivityId(activityId, uid);

        // 扣除可用里程数
        activityCommonRedis.incCommonHashNum(hashActivityId, MILEAGE_AVAILABLE_KEY, -amount);
        doReportSpecialItemsEvent(activityId, uid, 2, amount, 2);

        // 增加消耗里程数
        activityCommonRedis.incCommonHashNum(hashActivityId, CONSUME_MILEAGE_KEY, costMileage);
        activityCommonRedis.incrCommonZSetRankingScoreSimple(getZSetUserConsumeRankKey(activityId), uid, costMileage);

        // 增加竞拍拍中次数
        activityCommonRedis.incCommonHashNum(hashActivityId, BID_TOTAL_NUM_KEY, 1);

        int currentTime = DateHelper.getNowSeconds();
        resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, "", "", 0);
        HappyTripVO.RecordDataVO drawRecord = new HappyTripVO.RecordDataVO();
        BeanUtils.copyProperties(resourceMeta, drawRecord);
        drawRecord.setCtime(currentTime);

        String jsonRecord = JSON.toJSONString(drawRecord);
        activityCommonRedis.addCommonListData(getListHistoryBidKey(activityId, uid), jsonRecord);
        doDrawPrizeRecordEvent(uid, 1, 1, resourceMeta);
    }


    /**
     * 增加团队分数
     */
    private void incTeamTrainScore(String activityId, String teamId, int value) {
        synchronized (stringPool.intern("incTeamTrainScore" + teamId)) {
            String teamRankKey = getZSetTeamRankKey(activityId);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(teamRankKey, teamId);
            while (value > 0) {
                List<Integer> tempLevelNumList = new ArrayList<>(TEAM_SCORE_LEVEL_LIST);
                int currentLevelIndex = 0;
                if (tempLevelNumList.contains(currentNum)) {
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                } else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if (upLevelIndex >= TEAM_SCORE_LEVEL_LIST.size()) {
                    activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, value);
                    value = 0;
                } else {
                    int upLevelNum = TEAM_SCORE_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if (value >= needUpNum) {                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        value = value - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, needUpNum);
                        String resKey = TEAM_LEVEL_KEY_LIST.get(upLevelIndex);
                        List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), MAX_TEAM_SIZE);
                        for (String memberUid : teamMemberUidList) {
                            resourceKeyHandlerService.sendResourceData(memberUid, resKey, "Dragon Training-TeamTaskreward", "Dragon Training-TeamTaskreward");
                        }
                    } else {
                        activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, value);
                        value = 0;
                    }
                }
            }
        }
    }


    /**
     * 获取抽奖配置
     */
    private ResourceKeyConfigData getResourceKeyConfig() {
        return resourceKeyHandlerService.getConfigData(HAPPY_TRIP_BID_KEY);

    }


    /**
     * 历史记录
     */
    public HappyTripVO happyTripRecord(String activityId, String uid, int page) {
        HappyTripVO vo = new HappyTripVO();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        List<String> pageRecordList = activityCommonRedis.getCommonListPageRecord(getListHistoryBidKey(activityId, uid), start, end);
        List<HappyTripVO.RecordDataVO> recordList = new ArrayList<>();
        for (String record : pageRecordList) {
            HappyTripVO.RecordDataVO rollRecordData = JSONObject.parseObject(record, HappyTripVO.RecordDataVO.class);
            recordList.add(rollRecordData);
        }
        vo.setRecordList(recordList);
        if (pageRecordList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(0);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    /**
     * 加入团队
     */
    public void happyTripJoinTeam(String activityId, String uid, String captainUid) {
        checkActivityTime(activityId);
        synchronized (stringPool.intern("happyTripJoinTeam" + captainUid)) {
            if (ObjectUtils.isEmpty(captainUid)) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            if (captainUid.equals(uid)) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE.getCode(), "لا تستطيع التعاون مع نفسك");
            }

            ActorData captainActorData = actorDao.getActorDataFromCache(captainUid);
            // 队长不存在
            if (captainActorData == null) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_NOT_DIAMONDS.getCode(), "المستخدم الكابتن الذي تقدمت بطلب للانضمام إليه غير موجود");
            }

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            String teamId = userDataMap.get(TEAM_ID_KEY);
            int trainNum = Integer.parseInt(userDataMap.getOrDefault(TOTAL_MILEAGE_KEY, "0"));
            // 判断该用户是否已加入队伍
            if (!ObjectUtils.isEmpty(teamId)) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لقد انضممت إلى فريق آخر");
            }

            String captainHashActivityId = getHashActivityId(activityId, captainUid);
            Map<String, String> captainDataMap = activityCommonRedis.getCommonHashAllMapStr(captainHashActivityId);
            String captainTeamId = captainDataMap.get(TEAM_ID_KEY);
            // 队长没有团队id, 则加入
            if (ObjectUtils.isEmpty(captainTeamId)) {
                captainTeamId = new ObjectId().toString();
                List<String> teamMemberList = new ArrayList<>();
                teamMemberList.add(captainUid);
                teamMemberList.add(uid);

                activityCommonRedis.setCommonHashData(captainHashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.rightPushAllCommonList(getListTeamMemberKey(activityId, captainTeamId), teamMemberList);
                // 处理团队分数及奖励
                int captainTrainNum = Integer.parseInt(captainDataMap.getOrDefault(TOTAL_MILEAGE_KEY, "0"));
                int totalScore = trainNum + captainTrainNum;
                this.incTeamTrainScore(activityId, captainTeamId, totalScore);
                doTeamRecordEvent(activityId, uid, captainUid, captainActorData.getName());
                doTeamRecordEvent(activityId, captainUid, captainUid, captainActorData.getName());
                logger.info("happyTripJoinTeam1 captainTeamId:{}, captainUid:{}, uid:{}, totalScore:{}", captainTeamId, captainUid, uid, totalScore);
            } else {
                String teamMemberKey = getListTeamMemberKey(activityId, captainTeamId);
                List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(teamMemberKey, MAX_TEAM_SIZE);
                if (CollectionUtils.isEmpty(teamMemberUidList)) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي طلبت الانضمام إليه غير موجود");
                }
                if (teamMemberUidList.size() >= MAX_TEAM_SIZE) {
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي تتقدم بطلب الانضمام إليه ممتلئ");
                }
                String originCaptainUid = teamMemberUidList.get(0);
                teamMemberUidList.add(uid);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.addRightCommonListData(teamMemberKey, uid);
                this.incTeamTrainScore(activityId, captainTeamId, trainNum);
                doTeamRecordEvent(activityId, uid, originCaptainUid, captainActorData.getName());
                logger.info("happyTripJoinTeam2 captainTeamId:{}, captainUid:{}, uid:{}", captainTeamId, originCaptainUid, uid);
                if (teamMemberUidList.size() == MAX_TEAM_SIZE) {
                    for (String memberUid : teamMemberUidList) {
                        resourceKeyHandlerService.sendResourceData(memberUid, "HappyAnniversaryTripTeam", "Dragon Training-Teamreward", "Dragon Training-Teamreward");
                    }
                }
            }
        }
    }

    /**
     * 下发团队top1奖励
     */
    public void distributionTotalRanking(String activityId) {
        try {
            Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId), 1);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
                String teamId = entry.getKey();
                List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 10);
                String resourceKey = TRIP_TOP_KEY_LIST.get(rank - 1);
                for (String rankUid : teamMemberUidList) {
                    resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, "Dragon Training-TeamTopreward", "Dragon Training-TeamTopreward");
                }
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    /**
     * 分享配置信息给好友
     */
    public void happyTripShare(ShareActivityDTO dto) {
        if (StringUtils.isEmpty(dto.getActivity_id()) || StringUtils.isEmpty(dto.getAid())) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        checkActivityTime(dto.getActivity_id());
        String uid = dto.getUid();
        String activityId = dto.getActivity_id();
        String aid = dto.getAid();
        if (uid.equals(aid)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        String shareLimitKey = getShareLimitKey(activityId, uid, aid);
        String shareLimit = activityCommonRedis.getCommonStrValue(shareLimitKey);
        if (!StringUtils.isEmpty(shareLimit)) {
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE.getCode(), "لقد شاركت");
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("activity_id", dto.getActivity_id());
        jsonObject.put("activity_name", dto.getActivity_name());
        jsonObject.put("activity_desc", dto.getActivity_desc());
        jsonObject.put("activity_icon", dto.getActivity_icon());
        jsonObject.put("activity_banner", dto.getActivity_banner());
        jsonObject.put("activity_url", dto.getActivity_url());
        sendActivityShareMsg(dto.getUid(), dto.getAid(), jsonObject);
        activityCommonRedis.setCommonStrScore(shareLimitKey, 1);

    }


    /**
     * 上报石头数量
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int num, int source) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name("Dragon Training");
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 上报抽奖记录
     */
    private void doDrawPrizeRecordEvent(String uid, int senceDetail, int amount, ResourceKeyConfigData.ResourceMeta resourceMeta) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence("Dragon Training");
        event.setSence_detail(senceDetail);
        event.setTicket_type(0);
        event.setCost_ticket(amount);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(amount);
        event.setDraw_result(JSONObject.toJSONString(resourceMeta));
        eventReport.track(new EventDTO(event));
    }


    /**
     * 上报组队情况
     */
    private void doTeamRecordEvent(String activityId, String uid, String captainUid, String teamName) {
        TeamRecordEvent event = new TeamRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name("Dragon Training");
        event.setActive_id(activityId);
        event.setTeam_captain_uid(captainUid);
        event.setTeam_name(teamName);
        eventReport.track(new EventDTO(event));
    }


}
