package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.MiningGridVO;
import com.quhong.data.vo.MiningInfoVO;
import com.quhong.data.vo.MiningRecordVO;
import com.quhong.data.vo.MiningVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.redis.MiningRedis;
import com.quhong.utils.CacheUtils;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/14
 */
@Service
public class MiningService extends OtherActivityService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final String EXCHANGE_KEY = "miningExchangeReward";
    private static final int RECORD_PAGE_SIZE = 15;
    public static final String ACTIVITY_ID = ServerConfig.isProduct() ? "6878c142f62e91adaa827e55" : "673ae9e11b040000ca0075f9";

    private final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<Integer> ALL_POSITION_LIST = new ArrayList<>();

    private static final String GOLD_NUM = "gold_num_%s"; // 金子数
    private static final String HOE_NUM = "hoe_num"; // 锄头数量
    private static final String EXTRA_POINTS = "extra_points"; // 多余积分

    private static final List<Integer> BOX_POSITION_1 = Arrays.asList(63, 64, 73, 74); // 宝箱1占用4个格子
    private static final List<Integer> BOX_POSITION_2 = Arrays.asList(80, 81, 90, 91); // 宝箱2占用4个格子

    private static final List<Integer> GEM_POSITION_1 = Arrays.asList(20, 40, 44); // 是宝石1的格子
    private static final List<Integer> GEM_POSITION_2 = Arrays.asList(32, 50, 84, 94); // 是宝石2的格子

    private static final String GEM_ICON_1 = "https://cdn3.qmovies.tv/youstar/op_1731923909_gem1.png";
    private static final String GEM_ICON_2 = "https://cdn3.qmovies.tv/youstar/op_1731923909_gem2.png";
    private static final String BOX_ICON_1 = "https://cdn3.qmovies.tv/youstar/op_1731923961_box1.png";
    private static final String BOX_ICON_2 = "https://cdn3.qmovies.tv/youstar/op_1731923909_box2.png";
    private static final String GOLD_ICON = "https://cdn3.qmovies.tv/youstar/op_1731922905_gold.png";

    private static final int LIMIT_INIT_POOL = 100;
    private static final Integer INIT_POOL_SIZE = 1000;
    private static final int RESET_HOE_NUM = 5;

    private final List<MiningConfig> CONFIG_LIST = Arrays.asList(
            new MiningConfig(1, 1, Arrays.asList(1, 2), Arrays.asList(500, 500)),
            new MiningConfig(2, 5, Arrays.asList(5, 10, 15), Arrays.asList(330, 340, 330)),
            new MiningConfig(3, 25, Arrays.asList(40, 75, 110), Arrays.asList(330, 340, 330)),
            new MiningConfig(6, 50, Arrays.asList(80, 200, 320), Arrays.asList(330, 340, 330))
    );

    public static final String ACTION_EN = "View";
    public static final String ACTION_AR = "شاهد";
    public static final String REMINDER_MSG_TITLE_EN = "You get a free hoe";
    public static final String REMINDER_MSG_TITLE_AR = "تحصل على مجرفة مجانية";
    public static final String REMINDER_MSG_BODY_EN = "You can get gold by mining, come and mine with your free hoe~";
    public static final String REMINDER_MSG_BODY_AR = "يمكنك الحصول على الذهب عن طريق التعدين، تعال واستخرجه باستخدام معزقتك المجانية~";
    public static final String REMINDER_MSG_BANNER = "https://cdn3.qmovies.tv/youstar/op_1731923744_QH_banner.jpg";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/dig_mine/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/dig_mine/?activityId=%s", ACTIVITY_ID);

    @PostConstruct
    public void init() {
        // 挖矿的格子是5(列) * 10（行）的
        ALL_POSITION_LIST.addAll(Arrays.asList(0, 1, 2, 3, 4));
        ALL_POSITION_LIST.addAll(Arrays.asList(10, 11, 12, 13, 14));
        ALL_POSITION_LIST.addAll(Arrays.asList(20, 21, 22, 23, 24));
        ALL_POSITION_LIST.addAll(Arrays.asList(30, 31, 32, 33, 34));
        ALL_POSITION_LIST.addAll(Arrays.asList(40, 41, 42, 43, 44));
        ALL_POSITION_LIST.addAll(Arrays.asList(50, 51, 52, 53, 54));
        ALL_POSITION_LIST.addAll(Arrays.asList(60, 61, 62, 63, 64));
        ALL_POSITION_LIST.addAll(Arrays.asList(70, 71, 72, 73, 74));
        ALL_POSITION_LIST.addAll(Arrays.asList(80, 81, 82, 83, 84));
        ALL_POSITION_LIST.addAll(Arrays.asList(90, 91, 92, 93, 94));
    }

    @Resource
    private MiningRedis miningRedis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private EventReport eventReport;

    /**
     * 活动页信息
     */
    public MiningInfoVO getInfo(String activityId, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = getActivityData(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        boolean completedSign = false;
        int curTime = DateHelper.getNowSeconds();
        if (curTime >= activityData.getStartTime() && curTime < activityData.getEndTime()) {
            completedSign = checkSign(activityId, uid, actorData.getTn_id());
        }
        MiningInfoVO vo = new MiningInfoVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        Map<String, Long> userDataMap = miningRedis.getUserData(activityId, uid);
        vo.setHoeNum(userDataMap.getOrDefault(HOE_NUM, 0L).intValue());
        vo.setExtraPoint(userDataMap.getOrDefault(EXTRA_POINTS, 0L).intValue());
        vo.setGoldNum(userDataMap.getOrDefault(String.format(GOLD_NUM, DateHelper.ARABIAN.formatDateInDay()), 0L).intValue());
        vo.setGridList(getMiningGridVOList(activityId, uid));
        vo.setGetLoginReward(completedSign ? 1 : 0);
        vo.setRewardNotifyList(getRewardNotifyList(activityId));
        vo.setReminderStatus(miningRedis.isReminderUser(activityId, uid) ? 1 : 0);
        return vo;
    }

    /**
     * 挖矿
     */
    public MiningVO mining(String activityId, String uid, int position) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        if (!ALL_POSITION_LIST.contains(position)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        MiningVO vo;
        synchronized (stringPool.intern(uid)) {
            Set<Integer> allMinedPosition = miningRedis.getAllMinedPosition(activityId, uid);
            if (allMinedPosition.contains(position)) {
                // 已挖矿的位置不能再挖
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            if (!getCanMinePositionSet(allMinedPosition).contains(position)) {
                // 不在可以挖矿的位置内
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            MiningConfig config = getConfig(position);
            if (config == null) {
                logger.error("can not find config. activityId={} uid={} position={}", activityId, uid, position);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            long curHoeNum = miningRedis.getUserData(activityId, uid, HOE_NUM);
            int costHoeNum = config.getHoeNum();
            if (curHoeNum - costHoeNum < 0) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_HOE_NUMBER);
            }
            // 扣减用户锄头的数量
            miningRedis.incUserData(activityId, uid, HOE_NUM, -costHoeNum);
            doReportItemsChangeEvent(activityId, uid, 1, 2, costHoeNum, 3, getMiningType(position) + "");
            // 获取挖宝奖励
            vo = getMiningReward(activityId, uid, actorData, position, config);
            if (miningRedis.getMinedPositionNum(activityId, uid) >= 50) {
                // 全部格子都挖完了，清空开始新一轮
                miningRedis.clearMinedPosition(activityId, uid);
            }
        }
        Map<String, Long> userDataMap = miningRedis.getUserData(activityId, uid);
        vo.setHoeNum(userDataMap.getOrDefault(HOE_NUM, 0L).intValue());
        vo.setGoldNum(userDataMap.getOrDefault(String.format(GOLD_NUM, DateHelper.ARABIAN.formatDateInDay()), 0L).intValue());
        vo.setGridList(getMiningGridVOList(activityId, uid));
        return vo;
    }

    /**
     * 金子兑换奖品
     */
    public MiningVO exchange(String activityId, String uid, String drawType) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        String goldKey = String.format(GOLD_NUM, DateHelper.ARABIAN.formatDateInDay());
        synchronized (stringPool.intern(uid)) {
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(EXCHANGE_KEY);
            if (resourceKeyConfigData == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "Not Find Exchange Item");
            }
            Map<String, ResourceKeyConfigData.ResourceMeta> metaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            ResourceKeyConfigData.ResourceMeta resourceMeta = metaMap.get(drawType);
            if (resourceMeta == null) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            long curGoldNum = miningRedis.getUserData(activityId, uid, goldKey);
            int costGoldNum = Integer.parseInt(resourceMeta.getRateNumber());
            if (curGoldNum - costGoldNum < 0) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            // 扣减用户金子的数量
            miningRedis.incUserData(activityId, uid, goldKey, -costGoldNum);
            doReportItemsChangeEvent(activityId, uid, 2, 2, costGoldNum, 2, "");
            String title = "Goldmine-gold exchange";
            // 下发兑换商品
            resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, title, title, title, "", "", 1);
        }
        Map<String, Long> userDataMap = miningRedis.getUserData(activityId, uid);
        MiningVO vo = new MiningVO();
        vo.setHoeNum(userDataMap.getOrDefault(HOE_NUM, 0L).intValue());
        vo.setGoldNum(userDataMap.getOrDefault(goldKey, 0L).intValue());
        return vo;
    }

    /**
     * 重置刷新
     */
    public MiningVO reset(String activityId, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        synchronized (stringPool.intern(uid)) {
            long curHoeNum = miningRedis.getUserData(activityId, uid, HOE_NUM);
            if (curHoeNum - RESET_HOE_NUM < 0) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_HOE_NUMBER);
            }
            // 扣减用户锄头的数量
            miningRedis.incUserData(activityId, uid, HOE_NUM, -RESET_HOE_NUM);
            doReportItemsChangeEvent(activityId, uid, 1, 2, RESET_HOE_NUM, 4, "");
            // 清空开始新一轮
            miningRedis.clearMinedPosition(activityId, uid);
        }
        MiningVO vo = new MiningVO();
        Map<String, Long> userDataMap = miningRedis.getUserData(activityId, uid);
        vo.setHoeNum(userDataMap.getOrDefault(HOE_NUM, 0L).intValue());
        vo.setGoldNum(userDataMap.getOrDefault(String.format(GOLD_NUM, DateHelper.ARABIAN.formatDateInDay()), 0L).intValue());
        vo.setGridList(getMiningGridVOList(activityId, uid));
        return vo;
    }

    /**
     * 挖矿记录
     */
    public PageVO<MiningRecordVO> miningRecord(String activityId, String uid, Integer page) {
        List<MiningRecordVO> list = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        List<String> drawRecordList = miningRedis.getMiningRecordList(activityId, uid, start, end);
        if (!CollectionUtils.isEmpty(drawRecordList)) {
            for (String strDrawRecord : drawRecordList) {
                list.add(JSONObject.parseObject(strDrawRecord, MiningRecordVO.class));
            }
        }
        return new PageVO<>(list, list.size() >= RECORD_PAGE_SIZE ? page + 1 + "" : "");
    }

    /**
     * 设置活动提醒
     */
    public void setReminder(String activityId, String uid, int status) {
        checkActivityTime(activityId);
        if (status == 0) {
            miningRedis.removeReminderUser(activityId, uid);
        } else {
            miningRedis.addReminderUser(activityId, uid);
        }
    }

    // @Override
    public void dailyTaskRun(String dateStr) {
        sendReminderMsg();
    }

    /**
     * 发送活动提醒消息
     */
    public void sendReminderMsg() {
        try {
            Set<String> uidSet = miningRedis.getAllReminderUser(ACTIVITY_ID);
            for (String uid : uidSet) {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                int slang = actorData.getSlang();
                String actText = slang == SLangType.ARABIC ? ACTION_AR : ACTION_EN;
                String title = slang == SLangType.ARABIC ? REMINDER_MSG_TITLE_AR : REMINDER_MSG_TITLE_EN;
                String body = slang == SLangType.ARABIC ? REMINDER_MSG_BODY_AR : REMINDER_MSG_BODY_EN;
                otherActivityService.commonOfficialMsg(uid, REMINDER_MSG_BANNER, 0, 0, actText, title, body, ACTIVITY_URL);
            }
        } catch (Exception e) {
            logger.error("sendReminderMsg error e={}", e.getMessage(), e);
        }
    }

    private List<MiningGridVO> getMiningGridVOList(String activityId, String uid) {
        Set<Integer> allMinedPosition = miningRedis.getAllMinedPosition(activityId, uid);
        Set<Integer> canMinePositionSet = getCanMinePositionSet(allMinedPosition);
        List<MiningGridVO> list = new ArrayList<>(ALL_POSITION_LIST.size());
        for (Integer position : ALL_POSITION_LIST) {
            int costHoeNum = Objects.requireNonNull(getConfig(position)).getHoeNum();
            int status = allMinedPosition.contains(position) ? 2 : canMinePositionSet.contains(position) ? 1 : 0;
            list.add(new MiningGridVO(position, costHoeNum, status));
        }
        return list;
    }

    private MiningVO getMiningReward(String activityId, String uid, ActorData actorData, int position, MiningConfig config) {
        Set<Integer> minedPositionSet = new HashSet<>();
        minedPositionSet.add(position);
        MiningVO vo;
        if (GEM_POSITION_1.contains(position)) {
            ResourceKeyConfigData.ResourceMeta meta = draw(activityId, actorData, "miningGem1", "Goldmine-dig gem");
            vo = new MiningVO(1, meta.getResourceType(), GEM_ICON_1, meta.getResourceIcon(), meta.getResourceNumber(), meta.getResourcePrice(), "x" + meta.getResourceNumber());
        } else if (GEM_POSITION_2.contains(position)) {
            ResourceKeyConfigData.ResourceMeta meta = draw(activityId, actorData, "miningGem2", "Goldmine-dig gem");
            vo = new MiningVO(1, meta.getResourceType(), GEM_ICON_2, meta.getResourceIcon(), meta.getResourceNumber(), meta.getResourcePrice(), "x" + meta.getResourceNumber());
        } else if (BOX_POSITION_1.contains(position)) {
            ResourceKeyConfigData.ResourceMeta meta = draw(activityId, actorData, "miningBox1", "Goldmine-dig box");
            // 一个宝箱占多个格子
            minedPositionSet.addAll(BOX_POSITION_1);
            vo = new MiningVO(2, meta.getResourceType(), BOX_ICON_1, meta.getResourceIcon(), meta.getResourceNumber(), meta.getResourcePrice(), "x" + meta.getResourceNumber());
        } else if (BOX_POSITION_2.contains(position)) {
            ResourceKeyConfigData.ResourceMeta meta = draw(activityId, actorData, "miningBox2", "Goldmine-dig box");
            // 一个宝箱占多个格子
            minedPositionSet.addAll(BOX_POSITION_2);
            vo = new MiningVO(2, meta.getResourceType(), BOX_ICON_2, meta.getResourceIcon(), meta.getResourceNumber(), meta.getResourcePrice(), "x" + meta.getResourceNumber());
        } else {
            // 挖出金子
            int goldNum = calcuMiningGoldNum(activityId, config);
            // 給用户加金子数
            miningRedis.incUserData(activityId, uid, String.format(GOLD_NUM, DateHelper.ARABIAN.formatDateInDay()), goldNum);
            doReportItemsChangeEvent(activityId, uid, 2, 1, goldNum, 1, "");
            vo = new MiningVO(0, 0, "", GOLD_ICON, goldNum, 0, "x" + goldNum);
            if (!CacheUtils.hasKey("firstGold_" + uid)) {
                vo.setFirstMiningGold(1);
                CacheUtils.put("firstGold_" + uid, 1);
            }
        }
        // 记录已挖掉的位置
        miningRedis.addMinedPosition(activityId, uid, minedPositionSet);
        MiningRecordVO record = new MiningRecordVO(
                (position / 10) + 1,
                config.getHoeNum(),
                vo.getType(),
                vo.getRewardType(),
                vo.getSourceIcon(),
                vo.getRewardIcon(),
                vo.getRewardNum(),
                vo.getRewardPrice(),
                DateHelper.getNowSeconds()
        );
        // 挖矿记录
        miningRedis.saveMiningRecord(activityId, uid, JSONObject.toJSONString(record));
        return vo;
    }

    private int calcuMiningGoldNum(String activityId, MiningConfig config) {
        initDrawPrizePool(activityId, config);
        return Integer.parseInt(miningRedis.popRewardFromPool(activityId, config.getLevel() + ""));
    }

    private void initDrawPrizePool(String activityId, MiningConfig config) {
        int poolSize = miningRedis.getPoolSize(activityId, config.getLevel() + "");
        if (poolSize <= 0) {
            expandPrizePool(activityId, config);
        } else if (poolSize <= LIMIT_INIT_POOL) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    expandPrizePool(activityId, config);
                }
            });
        }
    }

    private void expandPrizePool(String activityId, MiningConfig config) {
        List<String> prizePoolList = new ArrayList<>(INIT_POOL_SIZE);
        for (int i = 0; i < config.getRateList().size(); i++) {
            // 添加元素到奖池
            prizePoolList.addAll(Collections.nCopies(config.getRateList().get(i), config.getGoldNumList().get(i) + ""));
        }
        // 打乱奖池顺序
        Collections.shuffle(prizePoolList);
        miningRedis.pushRewardInPool(activityId, config.getLevel() + "", prizePoolList);
    }

    private MiningConfig getConfig(int position) {
        int level = position / 10 + 1;
        for (int i = CONFIG_LIST.size() - 1; i >= 0; i--) {
            if (level >= CONFIG_LIST.get(i).level) {
                return CONFIG_LIST.get(i);
            }
        }
        return null;
    }

    /**
     * 可挖矿的位置
     */
    private Set<Integer> getCanMinePositionSet(Set<Integer> allMinedPosition) {
        // 第一行的格子默认可以挖矿
        Set<Integer> canMinePositionSet = new HashSet<>(Arrays.asList(0, 1, 2, 3, 4));
        allMinedPosition.forEach(k -> {
            // 已挖矿的位置相邻的上下左右四个位置都可以挖
            canMinePositionSet.add(k + 1);
            canMinePositionSet.add(k - 1);
            canMinePositionSet.add(k + 10);
            canMinePositionSet.add(k - 10);
        });
        // 过滤掉超出范围的和已被挖矿的格子
        return canMinePositionSet.stream().filter(k -> ALL_POSITION_LIST.contains(k) && !allMinedPosition.contains(k)).collect(Collectors.toSet());
    }

    private boolean checkSign(String activityId, String uid, String tnId) {
        if (!StringUtils.hasLength(tnId)) {
            logger.error("sign failure, tnId is empty. uid={}", uid);
            return false;
        }
        String dayStr = DateHelper.ARABIAN.formatDateInDay();
        synchronized (stringPool.intern(tnId)) {
            if (miningRedis.hasSignRecord(activityId, uid, dayStr) || miningRedis.hasSignRecord(activityId, tnId, dayStr)) {
                return false;
            }
            miningRedis.addSignRecord(activityId, uid, dayStr);
            miningRedis.addSignRecord(activityId, tnId, dayStr);
            miningRedis.incUserData(activityId, uid, HOE_NUM, 1);
            doReportItemsChangeEvent(activityId, uid, 1, 1, 1, 2, "");
            return true;
        }
    }

    /**
     * 处理礼物消息
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        // 获取礼物锤子
        String uid = giftData.getFrom_uid();
        synchronized (stringPool.intern(uid)) {
//            int sendBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
//            Map<String, Long> userMapData = miningRedis.getUserData(activityId, uid);
//            long extraPoints = userMapData.getOrDefault(EXTRA_POINTS, 0L);
//            long totalBeans = extraPoints + sendBeans;
//            int incNum = (int)(totalBeans / 100);
//            int modNum = (int)(totalBeans % 100);

            int incNum = giftData.getNumber() * giftData.getAid_list().size();
            if (incNum != 0) {
                miningRedis.incUserData(activityId, uid, HOE_NUM, incNum);
                doReportItemsChangeEvent(activityId, uid, 1, 1, incNum, 1, "");
            }
//            miningRedis.setUserData(activityId, uid, EXTRA_POINTS, modNum);
        }
    }

    private ResourceKeyConfigData.ResourceMeta draw(String activityId, ActorData actorData, String poolName, String rewardTitle) {
        ResourceKeyConfigData.ResourceMeta resourceMeta = null;
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(poolName);
        if (resourceKeyConfigData != null) {
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            initDrawPrizePool(activityId, poolName, resourceMetaMap);
            String metaId = miningRedis.popRewardFromPool(activityId, poolName);
            resourceMeta = resourceMetaMap.get(metaId);
            if (resourceMeta != null) {
                sendRewardAndSaveRecord(activityId, actorData, rewardTitle, resourceMeta);
            }
        }
        if (resourceMeta == null) {
            logger.error("can not find resourceMeta. poolName={}", poolName);
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return resourceMeta;
    }

    private List<MiningInfoVO.RewardNotify> getRewardNotifyList(String activityId) {
        List<MiningInfoVO.RewardNotify> list = new ArrayList<>();
        List<String> strValueList = miningRedis.getRewardNotifyList(activityId);
        if (!CollectionUtils.isEmpty(strValueList)) {
            for (String strValue : strValueList) {
                MiningInfoVO.RewardNotify rewardNotify = JSONObject.parseObject(strValue, MiningInfoVO.RewardNotify.class);
                list.add(rewardNotify);
            }
        }
        return list;
    }

    private void sendRewardAndSaveRecord(String activityId, ActorData actorData, String rewardTitle, ResourceKeyConfigData.ResourceMeta resourceMeta) {
        resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, rewardTitle, rewardTitle, rewardTitle, rewardTitle, "", 1);
        if (resourceMeta.getResourceType() == -2) {
            miningRedis.addRewardNotify(activityId, JSON.toJSONString(new MiningInfoVO.RewardNotify(actorData.getName(), resourceMeta.getResourceIcon(), resourceMeta.getResourceNumber())));
        }
    }

    private void initDrawPrizePool(String activityId, String poolName, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap) {
        int poolSize = miningRedis.getPoolSize(activityId, poolName);
        if (poolSize <= 0) {
            expandPrizePool(activityId, poolName, resourceMetaMap.values());
        } else if (poolSize <= LIMIT_INIT_POOL) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    expandPrizePool(activityId, poolName, resourceMetaMap.values());
                }
            });
        }
    }

    private void expandPrizePool(String activityId, String poolName, Collection<ResourceKeyConfigData.ResourceMeta> resourceMetas) {
        List<String> prizePoolList = new ArrayList<>(INIT_POOL_SIZE);
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetas) {
            // 添加元素到奖池
            int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
            prizePoolList.addAll(Collections.nCopies(rateNumber, resourceMeta.getMetaId()));
        }
        // 打乱奖池顺序
        Collections.shuffle(prizePoolList);
        miningRedis.pushRewardInPool(activityId, poolName, prizePoolList);
    }

    /**
     * @param itemType 1锄头 2金子
     * @param action   1增加 2减少
     * @param source   锄头 1送活动礼物（获取） 2平台下发（获取） 3采矿消耗（减少） 4刷新消耗 (减少)
     *                 金子 1采矿（获取） 2兑换礼物（消耗）
     * @param desc     仅在采矿消耗锄头时有值 1金子 2绿宝石 3红宝石 4蓝宝箱 5红宝箱
     */
    private void doReportItemsChangeEvent(String activityId, String uid, int itemType, int action, int num, int source, String desc) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setActivity_name("Goldmine");
        event.setActive_id(activityId);
        event.setChange_action(action);
        event.setActivity_special_items_id(itemType + "");
        event.setActivity_special_items_resource(source);
        event.setResource_desc(desc);
        event.setChange_nums(num);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private int getMiningType(int position) {
        if (GEM_POSITION_1.contains(position)) {
            return 2;
        } else if (GEM_POSITION_2.contains(position)) {
            return 3;
        } else if (BOX_POSITION_1.contains(position)) {
            return 4;
        } else if (BOX_POSITION_2.contains(position)) {
            return 5;
        } else {
            return 1;
        }
    }

    private static class MiningConfig {
        private final int level; // 第几层
        private final int hoeNum; // 挖矿所需锄头数
        private final List<Integer> goldNumList; // 能挖出的金矿数量
        private final List<Integer> rateList; // 比例

        public MiningConfig(int level, int hoeNum, List<Integer> goldNumList, List<Integer> rateList) {
            this.level = level;
            this.hoeNum = hoeNum;
            this.goldNumList = goldNumList;
            this.rateList = rateList;
        }

        public int getLevel() {
            return level;
        }

        public int getHoeNum() {
            return hoeNum;
        }

        public List<Integer> getGoldNumList() {
            return goldNumList;
        }

        public List<Integer> getRateList() {
            return rateList;
        }
    }
}
