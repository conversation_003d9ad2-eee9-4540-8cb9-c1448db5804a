package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.mongodb.client.result.UpdateResult;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.GiftWishVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GiftWishService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(GiftWishService.class);
    private static final Integer MAX_TASK_NUM = 1;
    private static final Integer LUCKY_GIFT_ID = ServerConfig.isProduct() ? 232 : 230;
    private static final String ACTIVITY_NAME = "Gift Wish";
    private static final String DAILY_FINISH_SHOW_KEY = "dailyShow";
    private static final String WEEKLY_FINISH_SHOW_KEY = "weeklyShow";
    private static final String DAILY_AWARD_STATUS = "dailyAwardStatus";
    private static final String WEEKLY_AWARD_STATUS = "surprise";
    private static final List<String> DAILY_FINISH_KEY_LIST = Arrays.asList("1", "2", "3", "4", "5", "6", "7");
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private GiftDao giftDao;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ActorDao actorDao;

    private String getDailyTaskActivityId(String activityId, int roundNum, int dailyNum){
        return String.format("dailyTask:%s:%s:%s", activityId, roundNum, dailyNum);
    }

    private String getWeeklyTaskActivityId(String activityId, int roundNum){
        return String.format("weeklyTask:%s:%s", activityId, roundNum);
    }

    private String getHashActivityId(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }


    public GiftWishVO giftWishConfig(String activityId, String uid) {
        GiftWishVO vo = new GiftWishVO();
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);


        ActorData userActor = actorDao.getActorDataFromCache(uid);
        if(userActor == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        vo.setRid(userActor.getRid());

        int dailyNum = activityData.getDailyNum();
        Map<Integer, List<ActivityCommonConfig.GiftWishConfig>> giftWishConfig = activityCommonConfig.getGiftWishConfig();


        // 设置每日礼物任务
        String dailyTaskActivityId = getDailyTaskActivityId(activityId, activityData.getRoundNum(), dailyNum);
        vo.setFinishUserNum(activityCommonRedis.getCommonSetNum(dailyTaskActivityId) * 3);


        String hashDailyTaskActivityId = getHashActivityId(dailyTaskActivityId, uid);
        Map<String, Integer>  dailyTaskHashMap = activityCommonRedis.getCommonHashAll(hashDailyTaskActivityId);
        vo.setShowDailyStatus(dailyTaskHashMap.getOrDefault(DAILY_FINISH_SHOW_KEY, 0));

        List<ActivityCommonConfig.GiftWishConfig> giftWishConfigList = giftWishConfig.get(dailyNum);
        List<GiftWishVO.GiftConfig> giftConfigList = new ArrayList<>();
        if(giftWishConfigList != null){
            for (ActivityCommonConfig.GiftWishConfig wishConfig: giftWishConfigList){
                int giftId = wishConfig.getGiftId();

                GiftData giftData = giftDao.getGiftFromCache(giftId);
                if(giftData == null){
                    continue;
                }

                GiftWishVO.GiftConfig giftConfig = new GiftWishVO.GiftConfig();
                giftConfig.setGiftName(giftData.getGnamear());
                giftConfig.setGiftPrice(giftData.getPrice());
                giftConfig.setGiftIcon(giftData.getGicon());
                giftConfig.setGiftEffect(wishConfig.getGiftEffect());

                int currentProcess = dailyTaskHashMap.getOrDefault(String.valueOf(giftId), 0);
                giftConfig.setShow(currentProcess >= MAX_TASK_NUM ? 1 : 0);
                if(wishConfig.getCollect() == 0 && vo.getShowDailyStatus() > 0){
                    giftConfig.setShow(1);
                }

                giftConfigList.add(giftConfig);
            }

            vo.setGiftConfigList(giftConfigList);
        }




        // 设置滚屏记录
        List<String> rollRecordList = activityCommonRedis.getCommonListRecord(dailyTaskActivityId);
        List<GiftWishVO.RollRecord> rollRecords = new ArrayList<>();

        for (String record : rollRecordList) {
            try {
                String[]  splitArray =  record.split("_");
                ActorData actorData = actorDao.getActorDataFromCache(splitArray[0]);
                GiftData giftData = giftDao.getGiftFromCache(Integer.parseInt(splitArray[1]));
                if(giftData == null){
                    continue;
                }
                GiftWishVO.RollRecord rollRecord = new GiftWishVO.RollRecord();
                rollRecord.setName(actorData.getName());
                rollRecord.setGiftName(giftData.getGnamear());
                rollRecords.add(rollRecord);
            }catch (Exception e){
                logger.error("giftConfig error: {}", e.getMessage(), e);
            }
        }
        vo.setRollRecordList(rollRecords);


        // 设置每周任务完成情况
        List<ActivityCommonConfig.GiftWishWeeklyConfig> giftWishWeeklyConfigList = activityCommonConfig.getGiftWishWeeklyConfigList();
        String weeklyTaskActivityId = getWeeklyTaskActivityId(activityId, activityData.getRoundNum());
        String hashWeeklyTaskActivityId = getHashActivityId(weeklyTaskActivityId, uid);
        Map<String, Integer>  weeklyTaskHashMap = activityCommonRedis.getCommonHashAll(hashWeeklyTaskActivityId);
        List<GiftWishVO.WeeklyConfig> dailyFinishStatusList = new ArrayList<>();

        for (ActivityCommonConfig.GiftWishWeeklyConfig giftWishWeeklyConfig: giftWishWeeklyConfigList){
            GiftWishVO.WeeklyConfig weeklyConfig = new GiftWishVO.WeeklyConfig();
            GiftData giftData = giftDao.getGiftFromCache(giftWishWeeklyConfig.getGiftId());
            if(giftData == null){
                continue;
            }

            weeklyConfig.setNameEn(giftWishWeeklyConfig.getNameEn());
            weeklyConfig.setNameAr(giftWishWeeklyConfig.getNameAr());
            weeklyConfig.setGiftIcon(giftData.getGicon());
            weeklyConfig.setStatus(weeklyTaskHashMap.getOrDefault(giftWishWeeklyConfig.getDailyKey(), 0));
            dailyFinishStatusList.add(weeklyConfig);
        }
        vo.setDailyFinishStatusList(dailyFinishStatusList);
        vo.setShowWeeklyStatus(weeklyTaskHashMap.getOrDefault(WEEKLY_FINISH_SHOW_KEY, 0));

        return vo;
    }

    public void giftWishSee(String activityId, String uid, String showKey) {

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if(DAILY_FINISH_SHOW_KEY.equals(showKey)){
            int dailyNum = activityData.getDailyNum();
            // 每日礼物任务Id
            String dailyTaskActivityId = getDailyTaskActivityId(activityId, activityData.getRoundNum(), dailyNum);
            String hashDailyTaskActivityId = getHashActivityId(dailyTaskActivityId, uid);
            Map<String, Integer>  dailyTaskHashMap = activityCommonRedis.getCommonHashAll(hashDailyTaskActivityId);
            if(dailyTaskHashMap.getOrDefault(DAILY_AWARD_STATUS, 0) > 0){
                activityCommonRedis.setCommonHashNum(hashDailyTaskActivityId, DAILY_FINISH_SHOW_KEY, 2);
            }


        } else if (WEEKLY_FINISH_SHOW_KEY.equals(showKey)) {
            // 每周任务Id
            String weeklyTaskActivityId = getWeeklyTaskActivityId(activityId, activityData.getRoundNum());
            String hashWeeklyTaskActivityId = getHashActivityId(weeklyTaskActivityId, uid);
            Map<String, Integer>  weeklyTaskHashMap = activityCommonRedis.getCommonHashAll(hashWeeklyTaskActivityId);
            if(weeklyTaskHashMap.getOrDefault(WEEKLY_AWARD_STATUS, 0) > 0){
                activityCommonRedis.setCommonHashNum(hashWeeklyTaskActivityId, WEEKLY_FINISH_SHOW_KEY, 2);
            }
        }else {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
    }


    private void distributionGiftWish(String uid, String activityId, int roundNum, int dailyNum, List<ActivityCommonConfig.GiftWishConfig> giftWishConfigList){

        // 每日奖励下发
        String dailyTaskActivityId = getDailyTaskActivityId(activityId, roundNum, dailyNum);
        String hashDailyTaskActivityId = getHashActivityId(dailyTaskActivityId, uid);

        String weeklyTaskActivityId = getWeeklyTaskActivityId(activityId, roundNum);
        String hashWeeklyTaskActivityId = getHashActivityId(weeklyTaskActivityId, uid);
        Map<String, Integer>  dailyTaskHashMap = activityCommonRedis.getCommonHashAll(hashDailyTaskActivityId);
        int awardStatus = dailyTaskHashMap.getOrDefault(DAILY_AWARD_STATUS, 0);
        if(awardStatus > 0){
            return;
        }

        int finishProcess = 0;
        for (ActivityCommonConfig.GiftWishConfig giftWishConfig: giftWishConfigList){
            if(giftWishConfig.getCollect() <= 0){
                continue;
            }

            String giftIdStr = String.valueOf(giftWishConfig.getGiftId());
            int giftProcess = dailyTaskHashMap.getOrDefault(giftIdStr, 0);
            if(giftProcess >= MAX_TASK_NUM){
                finishProcess += 1;
            }
        }

        if(finishProcess >= 3){
            ActivityCommonConfig.GiftWishConfig giftWishConfig = giftWishConfigList.get(3);
            distributionService.sendRewardResource(uid, giftWishConfig.getGiftId(), ActivityRewardTypeEnum.getEnumByName("gift"), 2, 1, ACTIVITY_NAME, ACTIVITY_NAME, 0);

            // 设置当日奖励获得状态
            activityCommonRedis.setCommonHashNum(hashDailyTaskActivityId, DAILY_AWARD_STATUS, 1);

            // 设置当日奖励获得提醒
            activityCommonRedis.setCommonHashNum(hashDailyTaskActivityId, DAILY_FINISH_SHOW_KEY, 1);

            // 设置当日总完成人数
            activityCommonRedis.addCommonSetData(dailyTaskActivityId, uid);

            // 设置当日最近30条历史记录
            String rollData = uid + "_" + giftWishConfig.getGiftId();
            activityCommonRedis.addCommonListRecord(dailyTaskActivityId, rollData);

            // 设置周每日完成状态
            activityCommonRedis.setCommonHashNum(hashWeeklyTaskActivityId, String.valueOf(dailyNum), 1);

        }

        // 周奖励下发
        Map<String, Integer>  weeklyTaskHashMap = activityCommonRedis.getCommonHashAll(hashWeeklyTaskActivityId);
        int weeklyAwardStatus = weeklyTaskHashMap.getOrDefault(WEEKLY_AWARD_STATUS, 0);
        if(weeklyAwardStatus > 0){
            return;
        }

        int weeklyFinishProcess = 0;
        for (String dailyKey : DAILY_FINISH_KEY_LIST) {
            weeklyFinishProcess += weeklyTaskHashMap.getOrDefault(dailyKey, 0);
        }

        if(weeklyFinishProcess >= 5){
            distributionService.sendRewardResource(uid, LUCKY_GIFT_ID, ActivityRewardTypeEnum.getEnumByName("gift"), 2, 3, ACTIVITY_NAME, ACTIVITY_NAME, 0);
            activityCommonRedis.setCommonHashNum(hashWeeklyTaskActivityId, WEEKLY_FINISH_SHOW_KEY, 1);
            activityCommonRedis.setCommonHashNum(hashWeeklyTaskActivityId, WEEKLY_AWARD_STATUS, 1);
        }
    }



    public void handleGiftMqMsg(SendGiftData giftData, String activityId, int roundNum, int dailyNum) {
        try {
            int sendGiftId = giftData.getGid();
            int sendGiftNum = giftData.getNumber();

            Map<Integer, List<ActivityCommonConfig.GiftWishConfig>> giftWishConfigMap = activityCommonConfig.getGiftWishConfig();
            List<ActivityCommonConfig.GiftWishConfig> giftWishConfigList = giftWishConfigMap.get(dailyNum);
            if(giftWishConfigList == null){
                return;
            }

            List<Integer> giftIdList = giftWishConfigList.stream().filter(item -> item.getCollect() > 0).map(ActivityCommonConfig.GiftWishConfig::getGiftId).collect(Collectors.toList());
            if(!giftIdList.contains(sendGiftId)){
                return;
            }

            String dailyTaskActivityId = getDailyTaskActivityId(activityId, roundNum, dailyNum);
            String sendGiftIdStr = String.valueOf(sendGiftId);

            for (String aid : giftData.getAid_list()) {
                synchronized (stringPool.intern(ACTIVITY_NAME + aid)) {
                    String hashDailyTaskActivityId = getHashActivityId(dailyTaskActivityId, aid);
                    Map<String, Integer>  dailyTaskHashMap = activityCommonRedis.getCommonHashAll(hashDailyTaskActivityId);
                    int receiveNum = dailyTaskHashMap.getOrDefault(sendGiftIdStr, 0);
                    if(receiveNum < MAX_TASK_NUM){
                        activityCommonRedis.incCommonHashNum(hashDailyTaskActivityId, sendGiftIdStr, sendGiftNum);
                        distributionGiftWish(aid, activityId, roundNum, dailyNum, giftWishConfigList);

                    }
                }
            }
        }catch (Exception e){
            logger.error("handleGiftMqMsg error:{}", e.getMessage(), e);
        }

    }

    public void updateGiftWishDailyConfig(OtherRankingActivityData activity){
        try {
            int nowSeconds = DateHelper.getNowSeconds();
            if (activity.getDailyEndTime() < nowSeconds){

                int nextDailyStartTime = activity.getDailyStartTime() + 86400;
                int nextDailyEndTime = activity.getDailyEndTime() + 86400;
                int nextDailyNum = activity.getDailyNum() + 1;
                nextDailyNum = nextDailyNum > 7 ? 1 : nextDailyNum;
                Update update = new Update();
                update.set("dailyStartTime", nextDailyStartTime);
                update.set("dailyEndTime", nextDailyEndTime);
                update.set("dailyNum", nextDailyNum);
                UpdateResult updateResult = otherRankingActivityDao.updateData(activity, update);

                if (null == updateResult || updateResult.getModifiedCount() == 0) {
                    monitorSender.info("activity", "心愿礼物活动更新参数异常", "活动更新失败");
                }
            }
        }catch (Exception e){
            logger.error("updateGiftWishDailyConfig error:{}", e.getMessage(), e);
        }
    }



}
