package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.MatchConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.vo.RoomReturnBonusVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.mysql.dao.RoomAllowanceLogDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.RoomAllowanceLogData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.ArithmeticUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 房间返钻政策
 */
@Service
public class RoomReturnBonusService {


    private static final Logger logger = LoggerFactory.getLogger(RoomReturnBonusService.class);
    private static final List<Integer> SCORE_LEVEL_LIST = Arrays.asList(0, 100, 300, 1000, 3000, 5000, 10000, 20000, 33333);
    private static final List<Float> RATIO_LEVEL_LIST = Arrays.asList(0f, 0.12f, 0.135f, 0.15f, 0.165f, 0.18f, 0.195f, 0.21f, 0.225f);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final int HISTORY_PAGE_SIZE = 30;
    private static final String ACTIVITY_ID = "RoomReturnBonus";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? "https://static.youstar.live/RoomReturnBonus/" : "https://test2.qmovies.tv/RoomReturnBonus/";
    private static int NOTICE_DAY = 7; // 通知时间 距离数据统计为7天，距离发放日为6天
    private static int EXPIRE_DAY = 8; //过期时间 距离数据统计为8天，距离发放日为7天

    private static final int ROOM_ALLOWANCE_ATYPE = 202;
    private static final String ROOM_ALLOWANCE_DESC = "room allowance";
    private static final String ROOM_ALLOWANCE_TITLE = "room allowance";


    public static final String AWARD_EN_GET = "Room allowance award";
    public static final String AWARD_AR_GET = "جائزة بدل الغرفة";
    public static final String AWARD_DESC_GET_EN = "Today you will receive %s diamonds as room allowance reward, please claim it in time.";
    public static final String AWARD_DESC_GET_AR = "اليوم سوف تتلقى %s الماس كمكافأة بدل الغرفة، يرجى المطالبة بها في الوقت المناسب.";

    public static final String NOTICE_EN_EXPIRE = "Room allowance will expire soon.";
    public static final String NOTICE_AR_EXPIRE = "سينتهي بدل الغرفة قريبًا.";
    public static final String NOTICE_DESC_EXPIRE_EN = "You have an unclaimed room allowance that will expire soon. Please claim it now.";
    public static final String NOTICE_DESC_EXPIRE_AR = "لديك بدل غرفة غير مطالب به والذي سينتهي قريبًا. يرجى المطالبة به الآن.";


    private static int START_TIME = 1724014800;

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private RoomAllowanceLogDao roomAllowanceLogDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    protected OfficialDao officialDao;
    @Resource
    protected NoticeNewDao noticeNewDao;
    @Autowired
    protected RoomWebSender roomWebSender;
    @Resource
    protected MqSenderService mqSenderService;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            // 测试服
            START_TIME = 1724014800;
            NOTICE_DAY = 2; // 通知时间 距离数据统计为2天，距离发放日为1天
            EXPIRE_DAY = 3; //过期时间 距离数据统计为3天，距离发放日为2天
        }
    }


    public RoomReturnBonusVO getRoomReturnBonusInfos(String uid) {
        RoomReturnBonusVO vo = new RoomReturnBonusVO();
        String roomId = RoomUtils.formatRoomId(uid);
        String now = DateHelper.ARABIAN.formatDateInDay();
        String detailKey = getHashDetailKey(now);
        RoomReturnBonusVO.DetailVO sourceDetailVO = cacheDataService.getRoomReturnBonusDetailVO(detailKey, roomId);
        int dayPoint = sourceDetailVO.getDayPoint();
        int index = getIndexLevel(dayPoint);
        float ratio = RATIO_LEVEL_LIST.get(index);
        int reward = (int) (dayPoint * ratio);
        int startTime = DateHelper.getNowSeconds() - 30 * 86400;
        RoomReturnBonusVO.HistoryVO todayReward = new RoomReturnBonusVO.HistoryVO();
        todayReward.setPoints(dayPoint);
        todayReward.setRatio(ArithmeticUtils.round(String.valueOf(ratio * 100), 1));
        todayReward.setReward(reward);
        List<RoomReturnBonusVO.HistoryVO> historyList = new ArrayList<>();
        List<RoomAllowanceLogData> roomAllowanceLogDataList = roomAllowanceLogDao.selectList(roomId, startTime);
        if (!CollectionUtils.isEmpty(roomAllowanceLogDataList)) {
            roomAllowanceLogDataList.forEach(item -> {
                RoomReturnBonusVO.HistoryVO itemVO = new RoomReturnBonusVO.HistoryVO();
                itemVO.setDay(item.getDay());
                itemVO.setPoints(item.getPoints());
                itemVO.setRatio(String.valueOf(ArithmeticUtils.round((double) item.getRatio() / 10, 1)));
                itemVO.setReward(item.getAward());
                itemVO.setStatus(item.getStatus());
                itemVO.setCtime(item.getCtime());
                historyList.add(itemVO);
            });
        }
        vo.setTodayReward(todayReward);
        vo.setTotalReward(roomAllowanceLogDao.getSumRoomAllowance(roomId, 0));
        vo.setHistoryList(historyList);

        String noticeDay = getBeforeDay(NOTICE_DAY);
        String expireDay = getBeforeDay(EXPIRE_DAY);
        logger.info("getRoomReturnBonusInfos now:{} noticeDay:{} expireDay:{}", now, noticeDay, expireDay);
        return vo;
    }

    public RoomReturnBonusVO collectRoomReturnBonus(String uid) {
        if (!MatchConstant.IS_ROOM_ALLOWANCE) {
            logger.info("change beans fail isRoomAllowance={} uid={} ", MatchConstant.IS_ROOM_ALLOWANCE, uid);
            throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
        }
        RoomReturnBonusVO vo = new RoomReturnBonusVO();
        String roomId = RoomUtils.formatRoomId(uid);
        synchronized (stringPool.intern("lock:room_return_bonus:collect" + roomId)) {
            int sum = roomAllowanceLogDao.getSumRoomAllowance(roomId, 0);
            if (sum <= 0) {
                logger.info("collect fail uid:{} sum is zero", uid);
                throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
            }
//            increaseBeans(roomId, uid, sum);
            asyncIncreaseBeans(roomId, uid, sum);
            int collectDayCount = roomAllowanceLogDao.collectByRoomId(roomId);
            doReturnsDiamondsEvent(uid, 1, sum);
            logger.info("collect success roomId:{} sum:{} collectDayCount:{}", roomId, sum, collectDayCount);
        }
        return vo;
    }

    private void increaseBeans(String roomId, String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setAtype(ROOM_ALLOWANCE_ATYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(ROOM_ALLOWANCE_TITLE);
        moneyDetailReq.setDesc(ROOM_ALLOWANCE_DESC);
        ApiResult<String> addBeansResult = dataCenterService.chargeBeans(moneyDetailReq);
        if (!addBeansResult.isOk()) {
            logger.info("change beans fail add={} uid={} ", changed, uid);
            throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
        }
    }

    private void asyncIncreaseBeans(String roomId, String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setAtype(ROOM_ALLOWANCE_ATYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(ROOM_ALLOWANCE_TITLE);
        moneyDetailReq.setDesc(ROOM_ALLOWANCE_DESC);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }


    public void handleUserScore(SendGiftData data, CommonMqTopicData mqData) {
//        int nowTime = DateHelper.getNowSeconds();
//        if (nowTime < START_TIME) {
//            return;
//        }
        String roomId;
        if (data != null) {
            roomId = data.getRoomId();
        } else {
            roomId = mqData.getRoomId();
        }
        if (ServerConfig.isProduct()) {
//            boolean isWhiteTestRoom = whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID);
//            if (!isWhiteTestRoom) {
//                // 灰度测试,只统计测试房间的
//                return;
//            }
//            if (isWhiteTestRoom) {
//                // 正式开始，排除测试房间
//                logger.info("return testRoom:{}", roomId);
//                return;
//            }
        }

        String hostUid = RoomUtils.getRoomHostId(roomId); // 房主uid
        String aid; // 上麦用户或者发礼物用户
        boolean newUserFlag = false;
        int score;
        long totalPrice = 0;
        int actionType = 0; // 1 发礼物  2有效上麦 3加入房间会员
        if (data != null) {
            actionType = 1;
            aid = data.getFrom_uid();
//            newUserFlag = ActorUtils.isNewRegisterActor(aid, 7);
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            newUserFlag = ActorUtils.isNewDeviceAccount(aid, actorData.getFirstTnId());
            totalPrice = ((long) data.getPrice() * data.getNumber() * data.getAid_list().size());
            score = (int) totalPrice / 3;
//            score = newUserFlag ? (int) totalPrice / 3 * 2 : (int) totalPrice / 3;
        } else {
            actionType = CommonMqTaskConstant.ON_MIC_TIME.equals(mqData.getItem()) ? 2 : 3;
            aid = mqData.getUid();
//            newUserFlag = ActorUtils.isNewRegisterActor(aid, 7);
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            newUserFlag = ActorUtils.isNewDeviceAccount(aid, actorData.getFirstTnId());
            if (actionType == 2) {
                score = newUserFlag ? 10 : 3;
            } else {
                score = newUserFlag ? 5 : 2;
            }
        }
        if (score <= 0) {
            logger.info("score:{} lte 0", score);
            return;
        }
        String now = DateHelper.ARABIAN.formatDateInDay();
        String detailKey = getHashDetailKey(now);
        synchronized (stringPool.intern("lock:room_return_bonus:" + roomId)) {
            RoomReturnBonusVO.DetailVO detailVO = cacheDataService.getRoomReturnBonusDetailVO(detailKey, roomId);
            if (actionType == 2 || actionType == 3) {
                Set<String> disUsers = detailVO.getMicUsers();
                int maxLimit = 0;
                int nowNum = 0;
                if (actionType == 2) {
                    if (newUserFlag) {
                        maxLimit = 4; // 40/10
                        nowNum = detailVO.getNewUserMic();
                    } else {
                        maxLimit = 10;//  30/3
                        nowNum = detailVO.getOldUserMic();
                    }
                    if (disUsers.contains(aid)) {
//                    logger.info("return actionType:{} newUserFlag:{} roomId:{} dis user list contains aid:{} or nowNum:{} > maxLimit:{}",
//                            actionType, newUserFlag, roomId, aid, nowNum, maxLimit);
                        return;
                    }
                } else {
                    if (newUserFlag) {
                        maxLimit = 4; // 20/5
                        nowNum = detailVO.getNewMemberNum();
                    } else {
                        maxLimit = 10; // 20/2
                        nowNum = detailVO.getOldMemberNum();
                    }
                }
                if (nowNum >= maxLimit) {
//                    logger.info("return actionType:{} newUserFlag:{} roomId:{} dis user list contains aid:{} or nowNum:{} > maxLimit:{}",
//                            actionType, newUserFlag, roomId, aid, nowNum, maxLimit);
                    return;
                }
                if (actionType == 2) {
                    if (newUserFlag) {
                        detailVO.setNewUserMic(nowNum + 1);
                        doScoreReportEvent(hostUid, score, 3, "invite new user up mic");
                    } else {
                        detailVO.setOldUserMic(nowNum + 1);
                        doScoreReportEvent(hostUid, score, 4, "invite old user up mic");
                    }
                    detailVO.setUserMicPoint(detailVO.getUserMicPoint() + score);
                    disUsers.add(aid);
                } else {
                    if (newUserFlag) {
                        detailVO.setNewMemberNum(nowNum + 1);
                        doScoreReportEvent(hostUid, score, 5, "new user become member");
                    } else {
                        detailVO.setOldMemberNum(nowNum + 1);
                        doScoreReportEvent(hostUid, score, 6, "old user become member");
                    }
                    detailVO.setMemberPoint(detailVO.getMemberPoint() + score);
                }

            } else {
                detailVO.setSendGiftPoint(detailVO.getSendGiftPoint() + score);
                if (newUserFlag) {
                    doScoreReportEvent(hostUid, score, 1, "new user send gift");
                } else {
                    doScoreReportEvent(hostUid, score, 2, "old user send gift");
                }
            }
            detailVO.setDayPoint(detailVO.getDayPoint() + score);
            activityCommonRedis.setCommonHashData(detailKey, roomId, JSONObject.toJSONString(detailVO));
            logger.info("room return bonus success add roomId:{} score:{}", roomId, score);
        }
    }


    public void everyDayHandle() {
        int nowTime = DateHelper.getNowSeconds();
        if (!MatchConstant.IS_ROOM_ALLOWANCE) {
            int expireCtimeCount = roomAllowanceLogDao.expireByCtime(nowTime);
            logger.info("room return is close expireCtimeCount:{}", expireCtimeCount);
            return;
        }
        if (nowTime < MatchConstant.ROOM_ALLOWANCE_START + (int) TimeUnit.HOURS.toSeconds(20)) {
            logger.info("room return is close not start +80000 nowTime:{}", nowTime);
            return;
        }
        String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date());
        String detailKey = getHashDetailKey(yesterday);
        Map<String, String> srcMap = activityCommonRedis.getCommonHashAllMapStr(detailKey);
        List<RoomAllowanceLogData> dataList = new ArrayList<>();
        Map<String, Integer> rewardRoomMap = new HashMap<>();
        int reCount = 0;
        try {
            for (Map.Entry<String, String> entry : srcMap.entrySet()) {
                String roomId = entry.getKey();
                String jsonValue = entry.getValue();
//                if (ServerConfig.isProduct()) {
//                    boolean isWhiteTestRoom = whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID);
//                    if (!isWhiteTestRoom) {
//                        // 灰度测试,只统计测试房间的
//                        continue;
//                    }
//                }
                if (StringUtils.hasLength(jsonValue)) {
                    RoomReturnBonusVO.DetailVO detailVO = JSONObject.parseObject(jsonValue, RoomReturnBonusVO.DetailVO.class);
                    int dayPoint = detailVO.getDayPoint();
                    int index = getIndexLevel(dayPoint);
                    float ratio = RATIO_LEVEL_LIST.get(index);
                    int reward = (int) (dayPoint * ratio);
                    dataList.add(new RoomAllowanceLogData(yesterday, roomId, dayPoint,
                            (int) (ratio * 1000), reward, 0, nowTime));
                    if (reward > 0) {
                        rewardRoomMap.put(roomId, reward);
                    }
                }

                if (dataList.size() >= 1000) {
                    roomAllowanceLogDao.batchInsert(dataList);
                    dataList.clear();
                    reCount++;
                }
            }
            if (!CollectionUtils.isEmpty(dataList)) {
                roomAllowanceLogDao.batchInsert(dataList);
            }

            rewardRoomMap.forEach((k, v) -> {
                String uid = RoomUtils.getRoomHostId(k);
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                int slang = actorData.getSlang();
                String title = slang == SLangType.ARABIC ? AWARD_AR_GET : AWARD_EN_GET;
                String body = slang == SLangType.ARABIC ? AWARD_DESC_GET_AR : AWARD_DESC_GET_EN;
                body = String.format(body, v);
                commonOfficialMsg(uid, title, body);
                doReturnsDiamondsEvent(uid, 3, v);
            });

            String nowDay = DateHelper.ARABIAN.formatDateInDay();
            String noticeDay = getBeforeDay(NOTICE_DAY);
            String expireDay = getBeforeDay(EXPIRE_DAY);
            int noticeSize = 0;
            List<RoomAllowanceLogData> noticeLogList = roomAllowanceLogDao.selectListByDayStatus(noticeDay, 0);
            if (!CollectionUtils.isEmpty(noticeLogList)) {
                noticeSize = noticeLogList.size();
                noticeLogList.forEach(item -> {
                    String uid = RoomUtils.getRoomHostId(item.getRoomId());
                    ActorData actorData = actorDao.getActorDataFromCache(uid);
                    int slang = actorData.getSlang();
                    String title = slang == SLangType.ARABIC ? NOTICE_AR_EXPIRE : NOTICE_EN_EXPIRE;
                    String body = slang == SLangType.ARABIC ? NOTICE_DESC_EXPIRE_AR : NOTICE_DESC_EXPIRE_EN;
                    commonOfficialMsg(uid, title, body);
                });
            }

            List<RoomAllowanceLogData> expireLogList = roomAllowanceLogDao.selectListByDayStatus(expireDay, 0);
            if (!CollectionUtils.isEmpty(expireLogList)) {
                expireLogList.forEach(item -> {
                    String uid = RoomUtils.getRoomHostId(item.getRoomId());
                    doReturnsDiamondsEvent(uid, 2, item.getAward());
                });
            }

            int expireCount = roomAllowanceLogDao.expireByDay(expireDay);
            int expireDayCtime = DateHelper.ARABIAN.stringDateToStampSecond(expireDay);
            int expireCtimeCount = roomAllowanceLogDao.expireByCtime(expireDayCtime);
            logger.info("nowDay:{} noticeDay:{} noticeLogList.size:{} rewardRoomMap.size:{} expireDay:{} expireCount:{} " +
                            "expireCtimeCount:{}",
                    nowDay, noticeDay, noticeSize, rewardRoomMap.size(), expireDay, expireCount, expireCtimeCount);
        } catch (Exception e) {
            logger.error("everyDayHandle error msg:{}", e.getMessage(), e);
        }
    }

    private int getIndexLevel(int score) {
        List<Integer> tempLevelNumList = new ArrayList<>(SCORE_LEVEL_LIST);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

    protected String getBeforeDay(int days) {
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }

    public void commonOfficialMsg(String uid, String title, String body) {
        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setValid(1);
//        officialData.setPicture(picture);
        officialData.setTo_uid(uid);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setNews_type(0);
        officialData.setAtype(0);
//        officialData.setAct(actText);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialData.setUrl(ACTIVITY_URL);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }


    private String getHashDetailKey(String dayStr) {
        return ACTIVITY_ID + ":" + dayStr;
    }

    private void doScoreReportEvent(String uid, int changed, int queenType, String changedDesc) {
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(30);
        event.setScore_changed_detail(queenType);
        event.setScore_changed_desc(changedDesc);
        eventReport.track(new EventDTO(event));
    }

    private void doReturnsDiamondsEvent(String uid, int atype, int award) {
        RoomConsumptionReturnsDiamondsEvent event = new RoomConsumptionReturnsDiamondsEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setAtype(atype);
        event.setDiamonds_num(award);
        eventReport.track(new EventDTO(event));
    }

}
