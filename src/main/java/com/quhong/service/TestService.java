package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.RechargeInfo;
import com.quhong.data.vo.RechargeCanivalVO;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.RechargeDailyInfoData;
import com.quhong.utils.ArithmeticUtils;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 *
 */
@Service
public class TestService {

    private static final Logger logger = LoggerFactory.getLogger(TestService.class);
    @Resource
    private RechargeCanivalService rechargeCanivalService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private ActorDao actorDao;

    @PostConstruct
    public void postInit() {
    }

    public void tt() {
        int startTime = 1733346000;
        List<RechargeDailyInfoData> allList = rechargeDailyInfoDao.getAllRechargeInfoList(startTime);
        Map<String, BigDecimal> sMap = new HashMap<>();
        allList.forEach(item -> {
            String uid = item.getUid();
            BigDecimal decimal = item.getRechargeMoney();
            BigDecimal oldDecimal = sMap.get(uid);
            if (oldDecimal != null) {
                sMap.put(uid, oldDecimal.add(decimal));
            } else {
                sMap.put(uid, decimal);
            }
        });
        logger.info("sMap->{}", sMap);
        if (!CollectionUtils.isEmpty(sMap)) {
            sMap.forEach((k, v) -> {
                logger.info("sMap->uid:{} rid:{} d:{} ", k, actorDao.getActorDataFromCache(k).getStrRid(), v.doubleValue());
                RechargeInfo rechargeInfo = new RechargeInfo();
                rechargeInfo.setUid(k);
                rechargeInfo.setRechargeMoney(v.doubleValue());
//                rechargeCanivalService.handleUserRecharge(rechargeInfo);
            });
        }
    }

}
