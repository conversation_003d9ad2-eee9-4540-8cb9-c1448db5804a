package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.VoteLogEvent;
import com.quhong.analysis.VoteResultLogEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RoomRoleData;
import com.quhong.data.dto.VoteDTO;
import com.quhong.data.vo.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.TextDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.RoomRoleType;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IDetectService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.msg.obj.UNameObject;
import com.quhong.msg.obj.VoteOptionInfoObject;
import com.quhong.msg.room.RoomVotePushMsg;
import com.quhong.mysql.config.DBMysqlBean;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.VoteDao;
import com.quhong.mysql.dao.VoteOptionDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.VoteData;
import com.quhong.mysql.data.VoteOptionData;
import com.quhong.redis.VoteRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
@Service
public class VoteService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(VoteService.class);

    private static final int LOOP_TIME = 5 * 1000; // 每5秒扫描一下是否有要结束的投票
    private static final int MAX_DURATION_TIME_DEBUG = 24 * 60; // 最大持续时间24分钟（测试服）
    private static final int MAX_DURATION_TIME_PRO = 24 * 60 * 60; // 最大持续时间24小时（正式服）
    private static final int COIN = 0; // 费用类型：金币
    private static final int DIAMOND = 1; // 费用类型：钻石
    private static final Integer[] COST_RANGE = {0, 500}; // 费用可选择范围0-500
    private static final Integer[] TEST_COST_RANGE = {0, 300}; // 费用可选择范围0-300

    private static final int GIFT_VOTE = 0; // 礼物投票
    private static final int QUIZ_VOTE = 1; // 问答投票

    private static final int SINGLE_CHOICE = 0; // 单选

    private static final int MSG_STATUS_CREATE = 0; // 创建
    public static final int MSG_STATUS_RUNNING = 1; // 进行中
    private static final int MSG_STATUS_END = 2; // 手动结束
    private static final int MSG_STATUS_AUTO_END = 3; // 自动结束

    private static final int VOTE_STATUS_RUNNING = 0; // 进行中
    private static final int VOTE_STATUS_END = 1; // 手动结束
    private static final int VOTE_STATUS_AUTO_END = 2; // 自动结束

    private static final int DEFAULT_GIFT_ID = 110; // 礼物投票默认礼物为110玫瑰礼物

    private static final int A_TYPE = 934;
    private static final String TITLE = "Q and A Voting";
    private static final String QA_VOTING_REMARK = "Q and A Voting Fee";

    /**
     * 票数倒序排序
     */
    private static final Comparator<VoteOptionData> VOTES_NUM_DESC = Comparator.comparing(VoteOptionData::getVotesNum).reversed();
    /**
     * 投票列表顺序
     */
    private static final Comparator<VoteOptionData> ORDER_ASC = Comparator.comparing(VoteOptionData::getOptionOrder);

    @Resource
    private IDetectService detectService;
    @Resource
    private VoteDao voteDao;
    @Resource
    private VoteOptionDao voteOptionDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private VoteRedis voteRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private CommonTaskService commonTaskService;

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                onTick();
            }
        });
    }

    private void onTick() {
        int nowTime = DateHelper.getNowSeconds();
        Set<Integer> needCloseVotes = voteRedis.getNeedCloseVotes(nowTime);
        if (CollectionUtils.isEmpty(needCloseVotes)) {
            return;
        }
        for (Integer voteId : needCloseVotes) {
            voteRedis.removeVotingCloseTime(voteId);
            VoteData data = voteDao.findData(voteId);
            if (data == null || data.getStatus() != VOTE_STATUS_RUNNING) {
                continue;
            }
            closeVote(data.getEndTime(), VOTE_STATUS_AUTO_END, data, null);
        }
    }

    /**
     * 创建投票
     */
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class}, transactionManager = DBMysqlBean.USTAR_TRANSACTION)
    public CreateVoteVO createVote(VoteDTO req) {
        checkCreateParam(req);
        // 房间的管理员及以上用户可创建
        RoomActorDetailData detailData = roomActorCache.getData(req.getRoomId(), req.getUid(), true);
        if (detailData.getViceHost() != 1 && detailData.getRole() != RoomRoleType.HOST && detailData.getRole() != RoomRoleType.MANAGER) {
            logger.info("Only room owner and admins can create vote. roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(ActivityHttpCode.OWNER_AND_ADMINS_CAN_CREATE_VOTE);
        }
        // 校验是否已存在投票
        int roomVoteId = voteRedis.getRoomVote(req.getRoomId());
        if (roomVoteId != 0) {
            logger.info("The voting function has been turned on in the room and cannot be repeated. roomId={}", req.getRoomId());
            throw new CommonException(ActivityHttpCode.VOTING_FUNCTION_HAS_BEEN_TURNED_ON);
        }
        int nowTime = DateHelper.getNowSeconds();
        VoteData voteData = buildVoteData(req, nowTime);
        // 保存到数据库
        voteDao.insert(voteData);
        List<VoteOptionData> optionDataList = buildVoteOptionDataList(voteData.getId(), req.getOptionContentList());
        voteOptionDao.batchInsert(optionDataList);
        voteRedis.saveRoomVote(req.getRoomId(), voteData.getId());
        voteRedis.setVotingCloseTime(voteData.getId(), voteData.getEndTime());
        if (req.getType() == GIFT_VOTE) {
            voteRedis.saveVoteGiftId(voteData.getId(), voteData.getGiftId());
        }
        // 发送投票创建消息
        sendRoomVotePushMsg(MSG_STATUS_CREATE, voteData, optionDataList, detailData);
        CreateVoteVO vo = new CreateVoteVO();
        buildVoteInfoVO(vo, voteData, optionDataList);
        vo.setOptUser(buildOptUserInfo(detailData));
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(req.getUid(), req.getRoomId(), "",
                String.valueOf(voteData.getId()), CommonMqTaskConstant.CREATE_VOTE, 1));
        return vo;
    }

    private void buildVoteInfoVO(VoteInfoVO vo, VoteData data, List<VoteOptionData> list) {
        BeanUtils.copyProperties(data, vo);
        vo.setVoteId(data.getId());
        List<VoteOptionVO> optionList = new ArrayList<>();
        if (data.getType() == GIFT_VOTE && data.getStatus() == VOTE_STATUS_END) {
            list.sort(VOTES_NUM_DESC.thenComparing(ORDER_ASC));
        } else {
            list.sort(ORDER_ASC);
        }
        for (VoteOptionData optionData : list) {
            VoteOptionVO optionVO = new VoteOptionVO();
            if (data.getType() == GIFT_VOTE) {
                optionVO.setAid(optionData.getOptionContent());
                ActorData actorData = actorDao.getActorDataFromCache(optionData.getOptionContent());
                optionVO.setUserName(actorData != null ? actorData.getName() : "");
                optionVO.setUserHead(actorData != null ? ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()) : "");
                GiftData giftData = giftDao.getGiftFromCache(data.getGiftId());
                optionVO.setGiftId(data.getGiftId());
                optionVO.setGiftIcon(giftData != null ? giftData.getGicon() : "");
            } else {
                optionVO.setOptionContent(optionData.getOptionContent());
            }
            optionVO.setId(optionData.getId());
            optionVO.setVotesNum(optionData.getVotesNum());
            optionList.add(optionVO);
        }
        vo.setStatus(data.getStatus() == VOTE_STATUS_END ? MSG_STATUS_END : MSG_STATUS_RUNNING);
        vo.setOptionList(optionList);
    }

    /**
     * 构建OptUserInfo
     */
    private CreateVoteVO.OptUserInfo buildOptUserInfo(RoomActorDetailData actorData) {
        if (actorData == null) {
            return null;
        }
        String uid = actorData.getAid();
        CreateVoteVO.OptUserInfo userInfo = new CreateVoteVO.OptUserInfo();
        userInfo.setRid(actorData.getRid());
        userInfo.setName(actorData.getName());
        userInfo.setAge(actorData.getAge());
        userInfo.setGender(actorData.getGender());
        userInfo.setRole(actorData.getNewRole());
        userInfo.setHead(actorData.getHead());
        // 设置vip
        userInfo.setVip(actorData.getVipLevel());
        // 设置徽章
        userInfo.setBadgeList(actorData.getBadgeList());
        userInfo.setLevel(actorData.getLevel());
        // 设置气泡
        userInfo.setBid(actorData.getBubbleId());
        userInfo.setUid(uid);
        return userInfo;
    }

    /**
     * 构建UNameObject
     */
    private UNameObject buildUnameObject(RoomActorDetailData actorData) {
        if (actorData == null) {
            return null;
        }
        String uid = actorData.getAid();
        UNameObject userInfo = new UNameObject();
        userInfo.setRid(actorData.getRid());
        userInfo.setName(actorData.getName());
        userInfo.setRole(actorData.getNewRole());
        userInfo.setHead(actorData.getHead());
        // 设置vip
        userInfo.setVip(actorData.getVipLevel());
        // 设置徽章
        userInfo.setBadgeList(actorData.getBadgeList());
        userInfo.setLevel(actorData.getLevel());
        // 设置气泡
        userInfo.setBid(actorData.getBubbleId());
        userInfo.setIdentify(actorData.getIdentify());
        userInfo.setUid(uid);
        userInfo.setIsNewUser(actorData.getIsNewUser());
        return userInfo;
    }

    private List<VoteOptionData> buildVoteOptionDataList(int voteId, List<String> optionContentList) {
        List<VoteOptionData> optionDataList = new ArrayList<>();
        int order = 0;
        for (String optionContent : optionContentList) {
            order++;
            optionDataList.add(new VoteOptionData(voteId, order, optionContent, 0));
        }
        return optionDataList;
    }

    /**
     * 投票礼物列表
     */
    @Cacheable(value = "getVoteGiftList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public VoteGiftVO giftList() {
        List<GiftData> hotGiftList = giftDao.getHotGiftList();
        List<VoteGiftVO.Gift> giftList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(hotGiftList)) {
            for (GiftData giftData : hotGiftList) {
                giftList.add(new VoteGiftVO.Gift(
                        giftData.getRid(),
                        giftData.getGicon(),
                        giftData.getGname(),
                        giftData.getGnamear(),
                        giftData.getGtype(),
                        giftData.getPrice()));
            }
        }
        // 默认礼物排在最前面
        giftList.sort(Comparator.comparing(o -> o.getGid() == DEFAULT_GIFT_ID ? 0 : 1));
        return new VoteGiftVO(giftList);
    }

    /**
     * 加入房间时检查是否存在投票
     */
    public CheckVoteVO checkVote(VoteDTO req) {
        CheckVoteVO vo = new CheckVoteVO();
        GiftData defaultGift = giftDao.getGiftFromCache(DEFAULT_GIFT_ID);
        if (ServerConfig.isProduct()) {
            vo.setVoteConfig(new CheckVoteVO.VoteConfig(COIN, COST_RANGE, DEFAULT_GIFT_ID, defaultGift.getGicon()));
        } else {
            vo.setVoteConfig(new CheckVoteVO.VoteConfig(DIAMOND, TEST_COST_RANGE, DEFAULT_GIFT_ID, defaultGift.getGicon()));
        }
        RoomRoleData roleData = memberDao.getRoleData(req.getRoomId(), req.getUid());
        vo.setCanCreate(roleData.isAdmin() ? 1 : 0);
        int voteId = voteRedis.getRoomVote(req.getRoomId());
        if (voteId == 0) {
            return vo;
        }
        VoteData data = voteDao.findData(voteId);
        if (data == null) {
            logger.error("can not find vote data. voteId={}", voteId);
            return vo;
        }
        List<VoteOptionData> list = voteOptionDao.findList(voteId);
        return buildCheckVoteVO(req.getUid(), data, list, vo);
    }

    /**
     * 问答投票
     */
    public VoteInfoVO quizVote(VoteDTO req) {
        if (StringUtils.isEmpty(req.getRoomId()) || CollectionUtils.isEmpty(req.getOptionList())) {
            logger.error("quiz vote param error. roomId={} optionList={}", req.getRoomId(), req.getOptionList());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        VoteData data = voteDao.findData(req.getRoomId(), req.getVoteId());
        if (data == null || data.getType() != QUIZ_VOTE) {
            logger.error("can not find quiz vote data. voteId={}", req.getVoteId());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        // 单选投票不能多选
        if (data.getOptionType() == SINGLE_CHOICE && req.getOptionList().size() > 1) {
            logger.info("Single choice cannot vote for more than one. uid={} voteId={} optionList.size={}", req.getUid(), req.getVoteId(), req.getOptionList().size());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        // 校验是否投票已结束
        if (DateHelper.getNowSeconds() >= data.getEndTime()) {
            logger.info("Voting time ends. uid={}, voteId={}", req.getUid(), req.getVoteId());
            throw new CommonException(ActivityHttpCode.VOTING_TIME_ENDS);
        }
        // 校验是否已投票
        if (voteRedis.hasAlreadyVoted(req.getVoteId(), req.getUid())) {
            logger.info("Each person can only vote once in each round of voting. uid={}", req.getUid());
            throw new CommonException(ActivityHttpCode.EACH_PERSON_CAN_ONLY_VOTE_ONCE);
        }
        if (data.getCostBeans() > 0) {
            if (data.getFeeType() == COIN) {
                if (!heartRecordDao.changeHeart(req.getUid(), -data.getCostBeans(), TITLE, QA_VOTING_REMARK)) {
                    logger.info("user not enough heart. uid={} change={}", req.getUid(), data.getCostBeans());
                    throw new CommonException(ActivityHttpCode.COIN_NOT_ENOUGH);
                }
            } else {
                ApiResult<String> result = dataCenterService.reduceBeans(buildMoneyDetailReq(req.getUid(), req.getRoomId(), data.getCostBeans()));
                if (!result.isOk()) {
                    if (1 == result.getCode().getCode()) {
                        throw new CommonException(ActivityHttpCode.NOT_ENOUGH_DIAMOND);
                    }
                    logger.error("reduce beans error, uid={} msg={}", req.getUid(), result.getCode().getMsg());
                    throw new CommonException(HttpCode.SERVER_ERROR);
                }
            }
        }
        List<VoteOptionData> allOptionList = voteOptionDao.findList(req.getVoteId());
        List<VoteOptionData> myOptionList = allOptionList.stream().filter(o -> req.getOptionList().contains(o.getId())).collect(Collectors.toList());
        // 校验选项id是否正确
        if (CollectionUtils.isEmpty(myOptionList)) {
            logger.error("can not find quiz vote option data. uid={} voteId={} optionList={}", req.getUid(), req.getVoteId(), Arrays.toString(req.getOptionList().toArray()));
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        List<Integer> myOptionIdList = myOptionList.stream().map(VoteOptionData::getId).collect(Collectors.toList());
        voteOptionDao.incOptionVotesNum(req.getVoteId(), myOptionIdList);
        voteRedis.saveVoteUserId(req.getVoteId(), req.getUid());
        voteRedis.saveUserVotingOption(req.getVoteId(), req.getUid(), myOptionIdList);
        sendRoomVotePushMsg(VoteService.MSG_STATUS_RUNNING, data, allOptionList, null);
        doReportVoteLogEvent(req.getUid(), 0, data, myOptionList);
        VoteInfoVO vo = new VoteInfoVO();
        buildVoteInfoVO(vo, data, allOptionList);
        vo.setVotingUserNum(voteRedis.getVotingUserNum(req.getVoteId()));
        vo.getOptionList().forEach(k -> k.setSelfVoted(voteRedis.hasAlreadyVotedOption(req.getVoteId(), req.getUid(), k.getId()) ? 1 : 0));
        return vo;
    }

    private MoneyDetailReq buildMoneyDetailReq(String uid, String roomId, Integer costBeans) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setAtype(A_TYPE);
        moneyDetailReq.setChanged(-costBeans);
        moneyDetailReq.setTitle(TITLE);
        moneyDetailReq.setDesc("");
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        return moneyDetailReq;
    }

    public void doReportVoteLogEvent(String uid, int giftNum, VoteData data, List<VoteOptionData> optionList) {
        VoteLogEvent event = new VoteLogEvent();
        event.setUid(uid);
        event.setVote_id(data.getId());
        event.setRoom_id(data.getRoomId());
        event.setVote_type(data.getType());
        event.setCtime(DateHelper.getNowSeconds());
        if (data.getType() == GIFT_VOTE) {
            event.setVote_num(giftNum);
            event.setVote_option_order(optionList.get(0).getOptionOrder());
            event.setVote_option_content(optionList.get(0).getOptionContent());
            event.setGift_id(data.getGiftId());
            GiftData giftData = giftDao.getGiftFromCache(data.getGiftId());
            event.setGift_name(giftData != null ? giftData.getGname() : "");
            event.setVote_cost_type(giftData != null ? giftData.getGtype() : 1);
            event.setVote_cost(giftData != null ? giftData.getPrice() : 0);
            eventReport.track(new EventDTO(event));
        } else {
            event.setVote_num(1);
            for (VoteOptionData optionData : optionList) {
                event.setVote_option_order(optionData.getOptionOrder());
                event.setVote_option_content(optionData.getOptionContent());
                event.setVote_cost_type(data.getFeeType() == COIN ? 2 : 1);
                event.setVote_cost(data.getCostBeans());
                eventReport.track(new EventDTO(event));
            }
        }
    }

    /**
     * 结束投票
     */
    public CloseVoteVO closeVote(VoteDTO req) {
        RoomActorDetailData detailData = roomActorCache.getData(req.getRoomId(), req.getUid(), true);
        if (detailData.getViceHost() != 1 && detailData.getRole() != RoomRoleType.HOST && detailData.getRole() != RoomRoleType.MANAGER) {
            logger.info("Voting promoters or room owner and admin can close voting. uid={} voteId={}", req.getUid(), req.getVoteId());
            throw new CommonException(ActivityHttpCode.OWNER_AND_ADMIN_CAN_CLOSE_VOTING);
        }
        VoteData data = voteDao.findData(req.getRoomId(), req.getVoteId());
        if (data == null) {
            logger.error("can not find vote data. roomId={}, voteId={}", req.getRoomId(), req.getVoteId());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        int nowTime = DateHelper.getNowSeconds();
        if (nowTime >= data.getEndTime()) {
            logger.info("Voting time ends. uid={}, voteId={}", req.getUid(), req.getVoteId());
            throw new CommonException(ActivityHttpCode.VOTING_TIME_ENDS);
        }
        closeVote(nowTime, VOTE_STATUS_END, data, detailData);
        CloseVoteVO vo = new CloseVoteVO();
        buildVoteInfoVO(vo, data, voteOptionDao.findList(req.getVoteId()));
        vo.setOptUid(req.getUid());
        vo.setOptName(detailData.getName());
        vo.getOptionList().forEach(k -> k.setSelfVoted(voteRedis.hasAlreadyVotedOption(req.getVoteId(), req.getUid(), k.getId()) ? 1 : 0));
        return vo;
    }

    /**
     * 获取投票记录
     */
    public PageVO<VoteRecordVO> getVoteRecord(VoteDTO req) {
        // 记录只展示房间最近14天的数据
        int timestamp = DateHelper.getNowSeconds() - 14 * 24 * 60 * 60;
        int pageSize = 10;
        List<VoteData> voteRecordList = voteDao.getVoteRecordPage(req.getType(), req.getRoomId(), timestamp, req.getPage(), pageSize);
        if (CollectionUtils.isEmpty(voteRecordList)) {
            return new PageVO<>(Collections.emptyList(), "");
        }
        List<VoteRecordVO> list = new ArrayList<>();
        for (VoteData voteData : voteRecordList) {
            VoteRecordVO voteRecordVO = new VoteRecordVO();
            voteRecordVO.setId(voteData.getId());
            voteRecordVO.setRoomId(voteData.getRoomId());
            voteRecordVO.setType(voteData.getType());
            voteRecordVO.setTitle(voteData.getTitle());
            voteRecordVO.setCreator(voteData.getCreator());
            ActorData creator = actorDao.getActorDataFromCache(voteData.getCreator());
            voteRecordVO.setCreatorName(creator != null ? creator.getName() : "");
            voteRecordVO.setCreatorHead(creator != null ? ImageUrlGenerator.generateRoomUserUrl(creator.getHead()) : "");
            voteRecordVO.setCtime(voteData.getCtime());
            list.add(voteRecordVO);
        }
        String nextUrl = voteRecordList.size() >= pageSize ? String.valueOf(req.getPage() + 1) : "";
        return new PageVO<>(list, nextUrl);
    }

    /**
     * 获取投票记录详情
     */
    public VoteRecordDetailVO getVoteRecordDetail(VoteDTO req) {
        VoteData data = voteDao.findData(req.getVoteId());
        if (data == null) {
            logger.error("can not find vote data. voteId={}", req.getVoteId());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        VoteRecordDetailVO vo = new VoteRecordDetailVO();
        BeanUtils.copyProperties(data, vo);
        if (data.getStatus() == VOTE_STATUS_RUNNING) {
            vo.setVotingUserNum(voteRedis.getVotingUserNum(req.getVoteId()));
        }
        ActorData creator = actorDao.getActorDataFromCache(data.getCreator());
        vo.setCreatorName(creator != null ? creator.getName() : "");
        vo.setStatus(voteStatusToMsgStatus(data.getStatus()));
        List<VoteOptionData> optionList = voteOptionDao.findList(req.getVoteId());
        GiftData giftData = null;
        if (data.getType() == GIFT_VOTE) {
            giftData = giftDao.getGiftFromCache(data.getGiftId());
        }
        List<VoteRecordDetailVO.Option> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(optionList)) {
            if (data.getType() == GIFT_VOTE && data.getStatus() != VOTE_STATUS_RUNNING) {
                // 礼物投票按票数倒序排序，相同排名按投票列表先后顺序排列
                optionList.sort(VOTES_NUM_DESC.thenComparing(ORDER_ASC));
            } else {
                // 问答投票按投票列表先后顺序排列
                optionList.sort(ORDER_ASC);
            }
            for (VoteOptionData optionData : optionList) {
                VoteRecordDetailVO.Option option = new VoteRecordDetailVO.Option();
                option.setId(optionData.getId());
                if (data.getType() == GIFT_VOTE) {
                    option.setAid(optionData.getOptionContent());
                    option.setGiftId(data.getGiftId());
                    option.setGiftIcon(giftData != null ? giftData.getGicon() : "");
                    ActorData actorData = actorDao.getActorDataFromCache(optionData.getOptionContent());
                    option.setUserName(actorData != null ? actorData.getName() : "");
                    option.setUserHead(actorData != null ? ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()) : "");
                } else {
                    option.setOptionContent(optionData.getOptionContent());
                    option.setSelfVoted(voteRedis.hasAlreadyVotedOption(req.getVoteId(), req.getUid(), optionData.getId()) ? 1 : 0);
                }
                if (!(data.getType() == QUIZ_VOTE && data.getStatus() == VOTE_STATUS_RUNNING)) {
                    option.setVotesNum(optionData.getVotesNum());
                }
                list.add(option);
            }
        }
        vo.setOptionList(list);
        return vo;
    }

    public VoteData buildVoteData(VoteDTO req, int nowTime) {
        VoteData data = new VoteData();
        data.setRoomId(req.getRoomId());
        data.setDuration(req.getDuration());
        if (req.getDuration() == 0) {
            // duration为0是手动结束，投票持续时间默认为配置的最大持续时间
            int maxDurationTime = ServerConfig.isNotProduct() ? MAX_DURATION_TIME_DEBUG : MAX_DURATION_TIME_PRO;
            data.setEndTime(nowTime + maxDurationTime);
        } else {
            data.setEndTime(nowTime + req.getDuration() * 60);
        }
        data.setTitle(req.getTitle());
        data.setVotingUserNum(0);
        data.setStatus(VOTE_STATUS_RUNNING);
        data.setCreator(req.getUid());
        data.setCtime(nowTime);
        if (req.getType() == GIFT_VOTE) {
            data.setType(GIFT_VOTE);
            data.setGiftId(req.getGiftId());
            data.setFeeType(0);
            data.setCostBeans(0);
            data.setOptionType(SINGLE_CHOICE);
        } else {
            data.setType(QUIZ_VOTE);
            data.setGiftId(0);
            data.setFeeType(req.getFeeType());
            data.setCostBeans(req.getCostBeans());
            data.setOptionType(req.getOptionType());
        }
        return data;
    }

    /**
     * 校验创建投票的参数
     */
    private void checkCreateParam(VoteDTO req) {
        // 投票标题不能为空，且长度不能大于120
        if (StringUtils.isEmpty(req.getTitle()) || req.getTitle().length() > 120) {
            logger.info("title param error. title={}", req.getTitle());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        // 投票选项不能为空
        if (CollectionUtils.isEmpty(req.getOptionContentList())) {
            logger.info("optionContentList can not be empty.");
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        int optionSize = req.getOptionContentList().size();
        if (req.getType() == GIFT_VOTE) {
            // 礼物投票参数校验
            if (giftDao.getGiftFromCache(req.getGiftId()) == null) {
                logger.info("giftId param error. giftId={}", req.getGiftId());
                throw new CommonException(ActivityHttpCode.PARAM_ERROR);
            }
            // 礼物投票选项数量限制2~10
            if (optionSize < 2 || optionSize > 10) {
                logger.info("optionContentList param error. optionContentList.size={}", optionSize);
                throw new CommonException(ActivityHttpCode.PARAM_ERROR);
            }
        } else if (req.getType() == QUIZ_VOTE) {
            // 问答投票选项数量限制2~5
            if (optionSize < 2 || optionSize > 5) {
                logger.info("optionContentList param error. optionContentList.size={}", optionSize);
                throw new CommonException(ActivityHttpCode.PARAM_ERROR);
            }
            // 校验费用范围
            Integer[] costRange = ServerConfig.isProduct() ? COST_RANGE : TEST_COST_RANGE;
            if (req.getCostBeans() < costRange[0] || req.getCostBeans() > costRange[1]) {
                logger.info("costBeans param error. costBeans={}", req.getCostBeans());
                throw new CommonException(ActivityHttpCode.PARAM_ERROR);
            }
        } else {
            logger.info("type param error. type={}", req.getType());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        String detectText = req.getType() == GIFT_VOTE ? req.getTitle() : req.getTitle() + Arrays.toString(req.getOptionContentList().toArray());
        // 脏词检测
        if (detectService.detectText(new TextDTO(detectText, DetectOriginConstant.USER_VOTE, req.getUid())).getData().getIsSafe() == 0) {
            logger.info("The vote are not allowed to contain dirty words. detectText={}", detectText);
            throw new CommonException(ActivityHttpCode.THE_VOTE_ARE_NOT_ALLOWED_CONTAIN_DIRTY_WORDS);
        }
    }

    private CheckVoteVO buildCheckVoteVO(String uid, VoteData data, List<VoteOptionData> optionDataList, CheckVoteVO vo) {
        BeanUtils.copyProperties(data, vo, "votingUserNum");
        vo.setVotingUserNum(voteRedis.getVotingUserNum(data.getId()));
        GiftData giftData = giftDao.getGiftFromCache(data.getGiftId());
        vo.setGiftIcon(giftData != null ? giftData.getGicon() : "");
        List<CheckVoteVO.VoteOption> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(optionDataList)) {
            for (VoteOptionData optionData : optionDataList) {
                CheckVoteVO.VoteOption voteOption = new CheckVoteVO.VoteOption();
                voteOption.setId(optionData.getId());
                voteOption.setOptionOrder(optionData.getOptionOrder());
                voteOption.setOptionContent(optionData.getOptionContent());
                if (data.getType() == GIFT_VOTE) {
                    voteOption.setVotesNum(optionData.getVotesNum());
                    voteOption.setIsSelf(optionData.getOptionContent().equals(uid) ? 1 : 0);
                    ActorData actorData = actorDao.getActorDataFromCache(optionData.getOptionContent());
                    voteOption.setAid(actorData != null ? actorData.getUid() : "");
                    voteOption.setUserName(actorData != null ? actorData.getName() : "");
                    voteOption.setUserHead(actorData != null ? ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()) : "");
                    voteOption.setGiftIcon(giftData != null ? giftData.getGicon() : "");
                } else {
                    voteOption.setSelfVoted(voteRedis.hasAlreadyVotedOption(data.getId(), uid, optionData.getId()) ? 1 : 0);
                }
                list.add(voteOption);
            }
        }
        vo.setStatus(voteStatusToMsgStatus(data.getStatus()));
        vo.setOptionList(list);
        return vo;
    }

    private Integer voteStatusToMsgStatus(int status) {
        if (status == VOTE_STATUS_RUNNING) {
            return MSG_STATUS_RUNNING;
        } else if (status == VOTE_STATUS_END) {
            return MSG_STATUS_END;
        } else {
            return MSG_STATUS_AUTO_END;
        }
    }

    /**
     * 关闭投票
     */
    private void closeVote(int endTime, int status, VoteData data, RoomActorDetailData actorData) {
        data.setVotingUserNum(voteRedis.getVotingUserNum(data.getId()));
        data.setEndTime(endTime);
        data.setStatus(status);
        voteDao.update(data);
        // 清除相关redis数据
        voteRedis.removeVotingCloseTime(data.getId());
        voteRedis.removeRoomVote(data.getRoomId());
        // 发送投票结束消息
        sendRoomVotePushMsg(status == VOTE_STATUS_END ? MSG_STATUS_END : MSG_STATUS_AUTO_END, data, voteOptionDao.findList(data.getId()), actorData);
        // 数数埋点
        doReportVoteResultEvent(data, actorData == null);
        sendPlayVoteMsg(data);
    }

    /**
     * 投票结束埋点
     */
    private void doReportVoteResultEvent(VoteData data, boolean isAutoEnd) {
        VoteResultLogEvent event = new VoteResultLogEvent();
        event.setUid(data.getCreator());
        event.setVote_id(data.getId());
        event.setVote_title(data.getTitle());
        event.setVote_type(data.getType());
        event.setVote_option_type(data.getOptionType());
        event.setVote_duration(data.getEndTime() - data.getCtime());
        event.setVote_start_time(data.getCtime());
        event.setVote_end_type(isAutoEnd ? 2 : 1);
        event.setRoom_id(data.getRoomId());
        if (data.getType() == GIFT_VOTE) {
            event.setGift_id(data.getGiftId());
            GiftData giftData = giftDao.getGiftFromCache(data.getGiftId());
            event.setGift_name(giftData != null ? giftData.getGname() : "");
            event.setVote_cost_type(giftData != null ? giftData.getGtype() : 1);
            event.setVote_cost(giftData != null ? giftData.getPrice() : 0);
        } else {
            event.setVote_cost_type(data.getFeeType() == COIN ? 2 : 1);
            event.setVote_cost(data.getCostBeans());
        }
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 发送玩投票msg
     */
    private void sendPlayVoteMsg(VoteData data) {
        Set<String> voteSetUser = voteRedis.getVoteUserId(data.getId());
        if (CollectionUtils.isEmpty(voteSetUser)){
            return;
        }
        for (String voteUid : voteSetUser) {
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(voteUid, data.getRoomId(), "", String.valueOf(data.getId()), CommonMqTaskConstant.PLAY_VOTE, 1));
        }
    }

    /**
     * 发送投票消息
     */
    public void sendRoomVotePushMsg(int status, VoteData data, List<VoteOptionData> optionList, RoomActorDetailData optUser) {
        if (data == null) {
            return;
        }
        List<VoteOptionData> optionDataList = new ArrayList<>(optionList);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomVotePushMsg msg = new RoomVotePushMsg();
                msg.setVoteId(data.getId());
                msg.setType(data.getType());
                msg.setStatus(status);
                msg.setTitle(data.getTitle());
                msg.setDuration(data.getDuration());
                msg.setOptionType(data.getOptionType());
                msg.setCtime(data.getCtime());
                msg.setEndTime(data.getEndTime());
                msg.setFeeType(data.getFeeType());
                msg.setCostBeans(data.getCostBeans());
                if (status != MSG_STATUS_CREATE) {
                    msg.setVotingUserNum(voteRedis.getVotingUserNum(data.getId()));
                }
                if (optUser != null) {
                    msg.setOpt_user(buildUnameObject(optUser));
                }
                GiftData giftData = null;
                if (data.getType() == GIFT_VOTE) {
                    giftData = giftDao.getGiftFromCache(data.getGiftId());
                }
                List<VoteOptionInfoObject> optionList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(optionDataList)) {
                    if (data.getType() == GIFT_VOTE && (status == MSG_STATUS_END || status == MSG_STATUS_AUTO_END)) {
                        optionDataList.sort(VOTES_NUM_DESC.thenComparing(ORDER_ASC));
                    } else {
                        optionDataList.sort(ORDER_ASC);
                    }
                    for (VoteOptionData optionData : optionDataList) {
                        VoteOptionInfoObject optionInfoObject = new VoteOptionInfoObject();
                        if (data.getType() == GIFT_VOTE) {
                            ActorData actorData = actorDao.getActorDataFromCache(optionData.getOptionContent());
                            optionInfoObject.setAid(optionData.getOptionContent());
                            optionInfoObject.setUserName(actorData != null ? actorData.getName() : "");
                            optionInfoObject.setUserHead(actorData != null ? ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()) : "");
                            optionInfoObject.setGiftId(data.getGiftId());
                            optionInfoObject.setGiftIcon(giftData != null ? giftData.getGicon() : "");
                        } else {
                            optionInfoObject.setOptionContent(optionData.getOptionContent());
                        }
                        optionInfoObject.setId(optionData.getId());
                        optionInfoObject.setVotesNum(optionData.getVotesNum());
                        optionList.add(optionInfoObject);
                    }
                }
                msg.setOptionList(optionList);
                roomWebSender.sendRoomWebMsg(data.getRoomId(), optUser != null ? optUser.getAid() : "", msg, status != MSG_STATUS_RUNNING);
            }
        });
    }
}
