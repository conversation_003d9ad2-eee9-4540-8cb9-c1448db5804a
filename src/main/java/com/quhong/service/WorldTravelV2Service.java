package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityDailyRankingEvent;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.bo.PunchCardTaskData;
import com.quhong.data.vo.LikeVO;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.WorldTravelV2VO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.redis.WorldTravelV2Redis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class WorldTravelV2Service extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(WorldTravelService.class);

    private static final PunchCardTaskData LOGIN_TASK = new PunchCardTaskData("task_1", "Yemen (Al Saleh Mosque)", "(جامع الصالح)اليمن ", 0, 0, "worldTravelTask1");

    private static final List<PunchCardTaskData> TASK_LIST = Arrays.asList(
            new PunchCardTaskData("task_2", "Saudi Arabia (Kingdom Tower)", "(برج المملكة)المملكة العربية السعودية", 1, 100, "worldTravelTask2"),
            new PunchCardTaskData("task_3", "UAE (Burj Al Arab)", "(برج العرب)الإمارات العربية المتحدة", 2, 500, "worldTravelTask3"),
            new PunchCardTaskData("task_4", "Kuwait (Kuwait Towers)", "(أبراج الكويت)الكويت", 3, 2000, "worldTravelTask4"),
            new PunchCardTaskData("task_5", "Iraq (Baghdad Tower)", "(برج بغداد)العراق", 4, 5000, "worldTravelTask5"),
            new PunchCardTaskData("task_6", "Jordan (Petra)", "(البتراء)الأردن", 5, 12000, "worldTravelTask6"),
            new PunchCardTaskData("task_7", "Syria (Aleppo Citadel)", "(قلعة حلب)سوريا", 6, 30000, "worldTravelTask7"),
            new PunchCardTaskData("task_8", "Lebanon (Beirut Cube)", "(مكعب بيروت)لبنان", 7, 70000, "worldTravelTask8"),
            new PunchCardTaskData("task_9", "Morocco (Hassan II Mosque)", "(مسجد الحسن الثاني)المغرب", 8, 120000, "worldTravelTask9"),
            new PunchCardTaskData("task_10","Egypt (Pyramids)", "(الأهرامات)مصر",  9, 200000, "worldTravelTask10")
    );

    private static final String ACTIVITY_TITLE_EN = "World Landmarks Check-in";
    private static final String ACTIVITY_TITLE_AR = "عجائب الدنيا";


    private static final String MOMENT_ORIGIN = "worldTravel";

    private static final String SHARE_DESC_EN = "I have landmark %s , click to join the activity with me, travelling around the world.";
    private static final String SHARE_DESC_AR = "لدي نقطة النزول %s ، انقر للانضمام إلى النشاط معي ، والسفر حول العالم.";
    private static final String SHARE_CONTENT_EN = "Shared an interesting activity “The wonders of the world” to you";
    private static final String SHARE_CONTENT_AR = "تم شارك النشاط رائع لك ،و هو “عجائب الدنيا”";

    private static final String SHARE_ICON_EN = "https://cdn3.qmovies.tv/youstar/op_1723688850_FJRK.png";
    private static final String SHARE_ICON_AR = "https://cdn3.qmovies.tv/youstar/op_1723688850_FJRK.png";
    private static final String ACTIVITY_ID = "66bdb980d7eb80bbaa44e27e";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/city_check/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/city_check/?activityId=%s", ACTIVITY_ID);

    private static final String DIAMOND_REWARD_NOTIFY_EN = "Congratulations on %s getting %d";
    private static final String DIAMOND_REWARD_NOTIFY_AR = "مبروك لـ %s على حصوله على %d";

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private WorldTravelV2Redis worldTravelV2Redis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private EventReport eventReport;

    public WorldTravelV2VO pageInfo(String uid, String activityId, int slang) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        WorldTravelV2VO vo = new WorldTravelV2VO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        int curTime = DateHelper.getNowSeconds();
        boolean isRunning = curTime >= activityData.getStartTime() && curTime <= activityData.getEndTime();
        if (isRunning) {
            // 登录奖励
            getLoginReward(activityId, uid);
        }
        List<WorldTravelV2VO.WorldConfig> worldConfigList = new ArrayList<>();
        // 登录任务
        WorldTravelV2VO.WorldConfig worldConfigVo = new WorldTravelV2VO.WorldConfig();
        worldConfigVo.setWorldKey(LOGIN_TASK.getKey());
        worldConfigVo.setStatus(isRunning ? 1 : 0);
        worldConfigVo.setLikes(worldTravelV2Redis.getLikeNum(activityId, LOGIN_TASK.getKey()));
        worldConfigVo.setLikeStatus(worldTravelV2Redis.hasLikeRecord(activityId, uid, LOGIN_TASK.getKey()) ? 1 : 0);
        worldConfigList.add(worldConfigVo);
        // 发送钻石打卡任务
        int rewardLevel = worldTravelV2Redis.getRewardLevel(activityId, uid);
        for (PunchCardTaskData data : TASK_LIST) {
            WorldTravelV2VO.WorldConfig worldConfig = new WorldTravelV2VO.WorldConfig();
            worldConfig.setWorldKey(data.getKey());
            worldConfig.setStatus(rewardLevel >= data.getLevel() ? 1 : 0);
            worldConfig.setLikes(worldTravelV2Redis.getLikeNum(activityId, data.getKey()));
            worldConfig.setLikeStatus(worldTravelV2Redis.hasLikeRecord(activityId, uid, data.getKey()) ? 1 : 0);
            worldConfigList.add(worldConfig);
        }
        vo.setWorldList(worldConfigList);
        vo.setFinishedTaskNum(isRunning ? rewardLevel + 1 : 0);
        // 打卡排行榜
        Map<String, Integer> rankingMap = worldTravelV2Redis.getRankingMap(activityId, 10);
        vo.setTravelRankingList(getRankingList(rankingMap));
        vo.setMyTravelRank(getMyRanking(activityId, uid, actorData));
        vo.setAwardNotifyList(worldTravelV2Redis.getAwardNotifyList(activityId, slang));
        vo.setDailyDataList(getDailyDataList(activityId, activityData.getStartTime(), activityData.getEndTime(), uid, actorData));
        return vo;
    }

    private List<WorldTravelV2VO.DailyData> getDailyDataList(String activityId, int startTime, int endTime, String uid, ActorData actorData) {
        List<WorldTravelV2VO.DailyData> list = new ArrayList<>();
        LocalDate startDate = DateSupport.ARABIAN.getLocalDate(startTime * 1000L);
        LocalDate endDate = DateSupport.ARABIAN.getLocalDate(endTime * 1000L);
        LocalDate today = DateSupport.ARABIAN.getToday();

        while (!startDate.isAfter(endDate)) {
            OtherMyRankVO myRankVO = new OtherMyRankVO();
            myRankVO.setSendingScore(0);
            myRankVO.setRank(-1);
            myRankVO.setName(actorData.getName());
            myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

            String strDate = DateSupport.format(startDate);
            String showDate = DateSupport.ARABIAN.dm(startDate);
            if (startDate.isAfter(today)) {
                list.add(new WorldTravelV2VO.DailyData("", showDate, 0, "", Collections.emptyList(), myRankVO));
            } else {
                int status = startDate.isEqual(today) ? 1 : 2;
                Map<String, Integer> rankingMap = worldTravelV2Redis.getRankingMap(activityId, strDate, 10);
                List<OtherRankingListVO> rankingList = getRankingList(rankingMap);
                String top1Head = rankingList.size() >= 1 ? rankingList.get(0).getHead() : "";
                list.add(new WorldTravelV2VO.DailyData("", showDate, status, top1Head, rankingList, getMyRanking(activityId, strDate, uid, actorData)));
            }
            startDate = startDate.plusDays(1);
        }
        return list;
    }

    private void getLoginReward(String activityId, String uid) {
        synchronized (stringPool.intern(uid)) {
            if (worldTravelV2Redis.hasLoginRecord(activityId, uid)) {
                return;
            }
            int rewardLevel = worldTravelV2Redis.getRewardLevel(activityId, uid);
            // 更新打卡榜
            worldTravelV2Redis.updateRankingScore(activityId, uid, rewardLevel + 1);
            logger.info("send activity login reward. activityId={} uid={} rewardKey={}", uid, activityId, LOGIN_TASK.getRewardKey());
            // 下发登录活动奖励
            sendReward(activityId, uid, LOGIN_TASK.getRewardKey());
            doReportEvent(activityId, uid, 1);
            worldTravelV2Redis.addLoginRecord(activityId, uid);
        }
    }

    private List<OtherRankingListVO> getRankingList(Map<String, Integer> rankingMap) {
        List<OtherRankingListVO> list = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO vo = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            vo.setUid(aid);
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            vo.setName(rankActor.getName());
            vo.setScore(entry.getValue());
            vo.setRank(rank);
            list.add(vo);
            rank += 1;
        }
        return list;
    }


    private OtherMyRankVO getMyRanking(String activityId, String uid, ActorData actorData) {
        OtherMyRankVO vo = new OtherMyRankVO();
        vo.setSendingScore(worldTravelV2Redis.getRankingScore(activityId, uid));
        int rank = worldTravelV2Redis.getRank(activityId, uid);
        vo.setUid(uid);
        vo.setRank(rank == 0 || rank > 99 ? -1 : rank);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    private OtherMyRankVO getMyRanking(String activityId, String strDate, String uid, ActorData actorData) {
        OtherMyRankVO vo = new OtherMyRankVO();
        vo.setSendingScore(worldTravelV2Redis.getRankingScore(activityId, strDate, uid));
        int rank = worldTravelV2Redis.getRank(activityId, strDate, uid);
        vo.setRank(rank == 0 || rank > 99 ? -1 : rank);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    private void onActivityTime(String activityId) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        int curTime = DateHelper.getNowSeconds();
        if (curTime < activityData.getStartTime() || curTime > activityData.getEndTime()) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }

    public LikeVO like(String uid, String activityId, String worldKey, Integer slang) {
        LikeVO likeVO = new LikeVO();
        onActivityTime(activityId);
        Map<String, PunchCardTaskData> taskMap = TASK_LIST.stream().collect(Collectors.toMap(PunchCardTaskData::getKey, Function.identity()));
        taskMap.put(LOGIN_TASK.getKey(), LOGIN_TASK);
        PunchCardTaskData taskData = taskMap.get(worldKey);
        if (taskData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        synchronized (stringPool.intern(uid)) {
            if (worldTravelV2Redis.hasLikeRecord(activityId, uid, worldKey)) {
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_LIKE);
            }
            likeVO.setLikeStatus(worldTravelV2Redis.addLikeRecord(activityId, uid, worldKey));
            likeVO.setLikes(worldTravelV2Redis.getLikeNum(activityId, worldKey));
            // 发布朋友圈
            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            String momentText = slang == SLangType.ARABIC ? SHARE_DESC_AR : SHARE_DESC_EN;
            String addressName = slang == SLangType.ARABIC ? taskData.getNameAr() : taskData.getName();
            momentText = String.format(momentText, addressName);
            publishMomentDTO.setText(momentText);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(MOMENT_ORIGIN);
            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setAction(ACTIVITY_URL);
            quote.setIcon(slang == SLangType.ARABIC ? SHARE_ICON_AR : SHARE_ICON_EN);
            quote.setContent(slang == SLangType.ARABIC ? SHARE_CONTENT_AR : SHARE_CONTENT_EN);
            quote.setType(6);
            publishMomentDTO.setQuote(quote);
            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if (result.getCode() == 20) {
                logger.info("newYearExpect level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }
            if (result.isError()) {
                logger.error("newYearExpect error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            return likeVO;
        }
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String uid = giftData.getFrom_uid();
        int totalBeans = giftData.getNumber() * giftData.getAid_list().size() * giftData.getPrice();
        synchronized (stringPool.intern(uid)) {
            int dailySendGiftBeans = worldTravelV2Redis.incSendGiftBeans(activityId, uid, totalBeans);
            int rewardLevel = worldTravelV2Redis.getRewardLevel(activityId, uid);
            if (rewardLevel >= 9) {
                return;
            }
            int curLevel = getLevel(dailySendGiftBeans);
            // 打卡榜
            boolean hasLoginRecord = worldTravelV2Redis.hasLoginRecord(activityId, uid);
            worldTravelV2Redis.updateRankingScore(activityId, uid, curLevel + (hasLoginRecord ? 1 : 0));
            if (curLevel > rewardLevel) {
                // 下发打卡奖励
                Map<Integer, String> rewardKeyMap = TASK_LIST.stream().collect(Collectors.toMap(PunchCardTaskData::getLevel, PunchCardTaskData::getRewardKey));
                for (int i = rewardLevel + 1; i <= curLevel; i++) {
                    logger.info("send reward. uid={} curLevel={} rewardLevel={} rewardKey={}", uid, curLevel, rewardLevel, rewardKeyMap.get(i));
                    sendReward(activityId, uid, rewardKeyMap.get(i));
                    doReportEvent(activityId, uid, i + 1);
                }
                worldTravelV2Redis.setRewardLevel(activityId, uid, curLevel);
            }
        }
    }

    private void sendReward(String activityId, String uid, String rewardKey) {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(rewardKey);
        if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
            logger.error("can not find resourceKeyConfigData. rewardKey={}", rewardKey);
            return;
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceKeyConfigData.getResourceMetaList()) {
            resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, "", 0);
            if (resourceMeta.getResourceType() == -2) {
                String strNotifyEn = String.format(DIAMOND_REWARD_NOTIFY_EN, actorData.getName(), resourceMeta.getResourceNumber());
                String strNotifyAr = String.format(DIAMOND_REWARD_NOTIFY_AR, actorData.getName(), resourceMeta.getResourceNumber());
                worldTravelV2Redis.addAwardNotify(activityId, SLangType.ENGLISH, strNotifyEn);
                worldTravelV2Redis.addAwardNotify(activityId, SLangType.ARABIC, strNotifyAr);
            }
        }
    }

    private void doReportEvent(String activityId, String uid, int stage) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setActivity_name("World Landmarks Check-in");
        event.setActive_id(activityId);
        event.setChannel("");
        event.setActivity_stage(stage);
        event.setCost_activity_ticket(0);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private int getLevel(int beans) {
        for (int i = TASK_LIST.size() - 1; i >= 0; i--) {
            if (beans >= TASK_LIST.get(i).getRewardLimit()) {
                return TASK_LIST.get(i).getLevel();
            }
        }
        return 1;
    }

    public void dailyRankingEventReport() {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        if (activityData == null) {
            logger.info("dailyRankingEventReport param error, can not find activity data. activityId={}", ACTIVITY_ID);
            return;
        }
        String yesterdayStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
        Map<String, Integer> rankingMap = worldTravelV2Redis.getRankingMap(ACTIVITY_ID, yesterdayStr, 10);
        if (CollectionUtils.isEmpty(rankingMap)) {
            logger.info("dailyRankingEventReport rankingMap is empty. activityId={} strDate={}", ACTIVITY_ID, yesterdayStr);
            return;
        }
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            ActivityDailyRankingEvent event = new ActivityDailyRankingEvent();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            if(rank == 1){
                resourceKeyHandlerService.sendResourceData(aid, "worldTravelDailyTop1", 905, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, "", 0);
            }
            event.setUid(aid);
            event.setDate(yesterdayStr);
            event.setRank(rank);
            event.setRank_value(entry.getValue());
            event.setActivity_name("World Landmarks Check-in");
            event.setActive_id(ACTIVITY_ID);
            eventReport.track(new EventDTO(event));
            rank += 1;
        }
    }
}
