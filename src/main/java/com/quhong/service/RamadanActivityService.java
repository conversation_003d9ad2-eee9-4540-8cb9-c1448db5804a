package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.msg.obj.LuckyGiftRewardObject;
import com.quhong.msg.room.RoomLuckGiftRewardMsg;
import com.quhong.mysql.dao.RamadanEndDao;
import com.quhong.mysql.dao.RamadanRecordDao;
import com.quhong.mysql.data.RamadanEndData;
import com.quhong.mysql.data.RamadanRecordData;
import com.quhong.redis.RamadanEndRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
public class RamadanActivityService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(RamadanActivityService.class);

    @Resource
    private RamadanEndRedis ramadanEndRedis;
    @Resource
    private RamadanEndDao ramadanEndDao;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;
    @Resource
    private RamadanActivityService ramadanActivityService;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private RamadanRecordDao ramadanRecordDao;

    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final String SHARE_EN = "I love this dish and it's worthy of my support.";
    private static final String SHARE_AR = "أنا أحب هذا الطبق وهو يستحق دعمي";
    private static final String ACTIVITY_RAMADAN_ORIGIN = "activity_ramadan";
    private static final String ACTIVITY_NAME = "Ramadan Food";
    private static final List<String> BOX_LIST = Arrays.asList("box1", "box2", "box3");
    private static final int LIMIT_INIT_POOL = 10;
    private static final int ZERO_INIT_POOL = 0;

    private void isActivityTime(String activityId){
        int curTime = DateHelper.getNowSeconds();

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if(activityData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        if (curTime < startTime || curTime > endTime){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }


    @Cacheable(value = "getRamadanEndList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<RamadanEndData> getRamadanEndList() {
        return ramadanEndDao.selectList();
    }


    public RamadanEndVO ramadanConfig(String uid, String activityId, int slang) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if(activityData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        RamadanEndVO vo = new  RamadanEndVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if(actorData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        vo.setUserRid(actorData.getRid());
        vo.setUserName(actorData.getName());
        vo.setUserHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

        Map<String, Integer> ramadanMap =  ramadanEndRedis.getRamadanEndAll(uid);
        List<RamadanEndVO.FoodInfo> foodInfoList = new ArrayList<>();
        List<RamadanEndData> ramadanEndDataList = ramadanActivityService.getRamadanEndList();
        int totalFood = 0;

        for (RamadanEndData data : ramadanEndDataList) {
            if(data.getShowStatus() == 0){
                continue;
            }

            RamadanEndVO.FoodInfo foodInfo = new RamadanEndVO.FoodInfo();
            foodInfo.setFoodKey(data.getFoodKey());
            foodInfo.setFoodName(slang == SLangType.ARABIC ? data.getFoodNameAr() : data.getFoodNameEn());
            foodInfo.setFoodDesc(slang == SLangType.ARABIC ? data.getFoodDescAr() : data.getFoodDescEn());
            foodInfo.setFoodStatus(ramadanMap.getOrDefault(data.getFoodKey(), 0));
            foodInfo.setFoodIcon(data.getFoodIcon());
            foodInfo.setFoodIconBack(data.getFoodIconBack());
            foodInfo.setLikeNum(ramadanEndRedis.getLikeFoodNums(data.getFoodKey()));
            foodInfo.setLikeStatus(ramadanEndRedis.isLikeFood(data.getFoodKey(), uid));

            totalFood += foodInfo.getFoodStatus();
            foodInfoList.add(foodInfo);
        }

        Map<String, Integer> ramadanBoxMap = ramadanEndRedis.getRamadanBoxAll(uid);

        for (String box: BOX_LIST){
            int boxStatus = ramadanBoxMap.getOrDefault(box, -1);
            if(BOX_LIST.get(0).equals(box)){
                vo.setBox1Status(boxStatus);
                if(totalFood >= 5  && boxStatus == -1){
                    vo.setBox1Status(0);
                }
            }

            if(BOX_LIST.get(1).equals(box)){
                vo.setBox2Status(boxStatus);
                if(totalFood >= 7  && boxStatus == -1){
                    vo.setBox2Status(0);
                }
            }

            if(BOX_LIST.get(2).equals(box)){
                vo.setBox3Status(boxStatus);
                if(totalFood >= 9  && boxStatus == -1){
                    vo.setBox3Status(0);
                }
            }
        }

        vo.setFoodInfoList(foodInfoList);
        vo.setFoodCard(totalFood);

        return vo;
    }


    public void ramadanEndLike(String uid, String activityId, String foodKey) {
        isActivityTime(activityId);
        if(!ramadanEndRedis.isLikeFood(foodKey, uid)){
            ramadanEndRedis.setLikeFoodNums(foodKey, uid);
        }else {
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE);
        }
    }


    public RamadanBoxVO ramadanBox(String uid, String activityId, String boxKey, int slang) {
        // isActivityTime(activityId);
        if(!BOX_LIST.contains(boxKey)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        synchronized (stringPool.intern(uid + boxKey)) {
            Map<String, Integer> ramadanMap =  ramadanEndRedis.getRamadanEndAll(uid);
            List<RamadanEndData> ramadanEndDataList = ramadanActivityService.getRamadanEndList();
            int totalFood = 0;
            for (RamadanEndData data : ramadanEndDataList) {
                totalFood += ramadanMap.getOrDefault(data.getFoodKey(), 0);
            }

            if(totalFood < 5){
                throw new CommonH5Exception(ActivityHttpCode.COIN_NOT_ENOUGH);
            }

            Map<String, Integer> ramadanBoxMap = ramadanEndRedis.getRamadanBoxAll(uid);
            int boxStatus = ramadanBoxMap.getOrDefault(boxKey, 0);
            if(boxStatus >= 1){
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE);
            }

            if ((BOX_LIST.get(1).equals(boxKey) && totalFood < 7) || (BOX_LIST.get(2).equals(boxKey) && totalFood < 9)){
                throw new CommonH5Exception(ActivityHttpCode.COIN_NOT_ENOUGH);
            }

            RamadanBoxVO vo = new RamadanBoxVO();
            ramadanEndRedis.setRamadanBoxKey(uid, boxKey, 1);
            List<ActivityCommonConfig.RamadanAwardConfig> ramadanAwardConfigList = activityCommonConfig.getRamadanConfig().get(boxKey);
            List<RamadanBoxVO.BoxInfo> boxInfos = new ArrayList<>();


            for (ActivityCommonConfig.RamadanAwardConfig config : ramadanAwardConfigList) {
                RamadanBoxVO.BoxInfo boxInfo = new RamadanBoxVO.BoxInfo();
                boxInfo.setName(slang == SLangType.ARABIC ? config.getNameAr() : config.getNameEn());
                boxInfo.setIcon(config.getIcon());
                boxInfos.add(boxInfo);

                distributionService.sendRewardResource(uid, config.getSourceId(),
                        ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), config.getRewardTime(), config.getRewardNum(), ACTIVITY_NAME, ACTIVITY_NAME, 0);
            }
            vo.setBoxInfoList(boxInfos);
            return vo;

        }
    }





    public void ramadanPicturePush(String uid, String activityId, String picture, int slang) {
        isActivityTime(activityId);

        if(StringUtils.isEmpty(picture)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(uid)) {

            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();

            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText(slang == SLangType.ARABIC ? SHARE_AR : SHARE_EN);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(ACTIVITY_RAMADAN_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("3500");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));
            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if(result.getCode() == 20){
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
        }
    }


    public RamadanRecordVO ramadanRecord(String uid, int page, int slang) {
        int pageSize = 10;

        List<RamadanRecordData> recordList = ramadanRecordDao.selectList(uid, page, pageSize);
        RamadanRecordVO ramadanRecordVO = new  RamadanRecordVO();
        List<RamadanRecordVO.RamadanRecord> ramadanRecordList = new ArrayList<>();
        List<RamadanEndData> ramadanEndDataList = ramadanActivityService.getRamadanEndList();
        Map<String, RamadanEndData> rewardConfigMap = ramadanEndDataList.stream().collect(Collectors.toMap(RamadanEndData::getFoodKey, Function.identity()));
        RamadanEndData ramadanEndData = null;
        for(RamadanRecordData recordData: recordList){
            RamadanRecordVO.RamadanRecord record = new RamadanRecordVO.RamadanRecord();
            ramadanEndData = rewardConfigMap.get(recordData.getFoodKey());
            record.setName(slang == SLangType.ARABIC ? ramadanEndData.getFoodNameAr() : ramadanEndData.getFoodNameEn());
            record.setIcon(ramadanEndData.getFoodIcon());
            record.setCtime(recordData.getCtime());
            ramadanRecordList.add(record);
        }

        ramadanRecordVO.setRamadanRecordList(ramadanRecordList);
        if(recordList.size() < pageSize){
            ramadanRecordVO.setNextUrl(-1);
        }else {
            ramadanRecordVO.setNextUrl(page + 1);
        }
        return ramadanRecordVO;
    }



    // 发送礼物抽奖
    public void needDrawPool(List<RamadanEndData> ramadanEndDataList){

        List<String> poolList = new ArrayList<>();
        for(RamadanEndData ramadanEndData: ramadanEndDataList){
            int cardSize = (int) (Double.parseDouble(ramadanEndData.getRateNum()) * 10000);
            for (int i=0; i < cardSize; i++){
                poolList.add(ramadanEndData.getFoodKey());
            }
        }
        Collections.shuffle(poolList);
        ramadanEndRedis.initPoolSize(poolList);
    }

    public void initDrawPool(List<RamadanEndData> ramadanEndDataList){

        int poolSize = ramadanEndRedis.getPoolSize();
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    needDrawPool(ramadanEndDataList);
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            needDrawPool(ramadanEndDataList);
        }
    }

    public String drawCard(List<RamadanEndData> ramadanEndDataList){
        initDrawPool(ramadanEndDataList);
        String cardKey = ramadanEndRedis.drawCardKey();

        if (StringUtils.isEmpty(cardKey)) {
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return cardKey;
    }

    public void ramadanGiftHandle(SendGiftData giftData){
        int totalNum = giftData.getNumber() * giftData.getAid_list().size();
        List<RamadanEndData> ramadanEndDataList = ramadanActivityService.getRamadanEndList();
        Map<String, RamadanEndData> rewardConfigMap = ramadanEndDataList.stream().collect(Collectors.toMap(RamadanEndData::getFoodKey, Function.identity()));

        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();
        // if(StringUtils.isEmpty(roomId)){
        //     return;
        // }

        Map<String, Integer> rewardMap = new HashMap<>();
        ActorData actorData = actorDao.getActorDataFromCache(fromUid);

        int curTime = DateHelper.getNowSeconds();

        for (int i=0; i < totalNum; i++){
            String cardKey = this.drawCard(ramadanEndDataList);
            RamadanRecordData recordData = new RamadanRecordData();
            recordData.setUid(fromUid);
            recordData.setFoodKey(cardKey);
            recordData.setCtime(curTime);
            ramadanRecordDao.insertOne(recordData);


            if (!cardKey.equals("j")){
                rewardMap.compute(cardKey, (k, v) -> {
                    if (null == v) {
                        v = 1;
                    }else {
                        v += 1;
                    }
                    return v;
                });
            }
        }

        if (!rewardMap.isEmpty()){
            RoomLuckGiftRewardMsg rewardMsg = new RoomLuckGiftRewardMsg();
            List<LuckyGiftRewardObject> luckyGiftRewardList = new ArrayList<>();

            for (String key : rewardMap.keySet()) {
                RamadanEndData ramadanEndData = rewardConfigMap.get(key);

                ramadanEndRedis.setRamadanEndKey(fromUid, key, 1);


                LuckyGiftRewardObject rewardObject = new LuckyGiftRewardObject();
                rewardObject.setIcon(ramadanEndData.getFoodIcon());
                rewardObject.setName(actorData.getSlang() == 1 ? ramadanEndData.getFoodNameEn() : ramadanEndData.getFoodNameAr());
                rewardObject.setType(2);
                rewardObject.setValue(rewardMap.get(key));
                luckyGiftRewardList.add(rewardObject);
            }

            rewardMsg.setAid(fromUid);
            rewardMsg.setA_type(0);
            rewardMsg.setType(2);
            rewardMsg.setLucky_gift_reward(luckyGiftRewardList);
            marsMsgActivityService.asyncSendPlayerMsg(roomId, fromUid, fromUid, rewardMsg, false);
        }
    }

}
