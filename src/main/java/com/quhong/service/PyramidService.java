package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.PyramidVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class PyramidService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(PyramidService.class);
    private static final String LEVEL_KEY = "currentLevel";
    private static final String ENERGY_KEY = "energyValue";
    private static final String BOX_1_KEY = "box1Status";
    private static final String BOX_2_KEY = "box2Status";
    private static final String BOX_3_KEY = "box3Status";
    private static final String BOX_4_KEY = "box4Status";
    private static final String BOX_5_KEY = "box5Status";
    private static final String ACTIVITY_NAME = "Pyramid";
    private static final String ACTIVITY_NAME_RANKING = "PyramidRanking";
    private static final List<String> BOX_LIST = Arrays.asList(BOX_1_KEY, BOX_2_KEY, BOX_3_KEY, BOX_4_KEY, BOX_5_KEY);
    private static final Interner<String> stringPool = Interners.newWeakInterner();


    // 升级条件
    private static final Map<Integer, Integer> UP_LEVEL_MAP = new HashMap<>();
    static {
        UP_LEVEL_MAP.put(0, 100000);
        UP_LEVEL_MAP.put(1, 300000);
        UP_LEVEL_MAP.put(2, 500000);
        UP_LEVEL_MAP.put(3, 700000);
        UP_LEVEL_MAP.put(4, 1000000);
        UP_LEVEL_MAP.put(5, 0);
    }


    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private PyramidService pyramidService;


    private String getHashActivityId(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }

    public PyramidVO pyramidConfig(String activityId, String uid) {

        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivity(activityId);
        Map<String, Integer> taskNumMap =  activityCommonRedis.getCommonHashAll(getHashActivityId(activityId, uid));
        PyramidVO vo = JSON.parseObject(JSON.toJSONString(taskNumMap), PyramidVO.class);
        vo.setStartTime(otherRankingActivityData.getStartTime());
        vo.setEndTime(otherRankingActivityData.getEndTime());

        List<String> rollRecordList = activityCommonRedis.getCommonListRecord(activityId);
        List<PyramidVO.RollRecord> rollRecords = new ArrayList<>();

        for (String record : rollRecordList) {
            try {
                String[]  splitArray =  record.split("-");
                ActorData actorData = actorDao.getActorDataFromCache(splitArray[0]);
                String boxName = pyramidService.getBoxName(splitArray[1]);

                PyramidVO.RollRecord rollRecord = new PyramidVO.RollRecord();
                rollRecord.setName(actorData.getName());
                rollRecord.setBoxName(boxName);
                rollRecords.add(rollRecord);
            }catch (Exception e){
                logger.error("pyramidConfig error: {}", e.getMessage(), e);
            }

        }
        vo.setRollRecordList(rollRecords);
        return vo;
    }


    private String getBoxName(String boxType){
        switch (boxType){
            case BOX_1_KEY:
                return "Lv.1";
            case BOX_2_KEY:
                return "Lv.2";
            case BOX_3_KEY:
                return "Lv.3";
            case BOX_4_KEY:
                return "Lv.4";
            case BOX_5_KEY:
                return "Lv.5";
            default:
                return "";
        }

    }

    public void pyramidGetAward(String activityId, String uid, String boxType) {

        if(!BOX_LIST.contains(boxType)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivity(activityId);

        int currentTime = DateHelper.getNowSeconds();
        if(currentTime < otherRankingActivityData.getStartTime() || currentTime > otherRankingActivityData.getEndTime()){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        synchronized (stringPool.intern(ACTIVITY_NAME + uid)) {
            int boxStatus =  activityCommonRedis.getCommonHashValue(getHashActivityId(activityId, uid), boxType);

            if(boxStatus != 1){
                throw new CommonH5Exception(ActivityHttpCode.NOT_ANSWER_AGAIN);
            }

            switch (boxType){
                case BOX_1_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 3000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
                case BOX_2_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 6000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 309, ActivityRewardTypeEnum.getEnumByName("background"), 1, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
                case BOX_3_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 8000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 309, ActivityRewardTypeEnum.getEnumByName("background"), 1, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 175, ActivityRewardTypeEnum.getEnumByName("buddle"), 1, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
                case BOX_4_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 10000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 309, ActivityRewardTypeEnum.getEnumByName("background"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 175, ActivityRewardTypeEnum.getEnumByName("buddle"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 517, ActivityRewardTypeEnum.getEnumByName("mic"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
                case BOX_5_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 10000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 309, ActivityRewardTypeEnum.getEnumByName("background"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 175, ActivityRewardTypeEnum.getEnumByName("buddle"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 517, ActivityRewardTypeEnum.getEnumByName("mic"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 168, ActivityRewardTypeEnum.getEnumByName("ride"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 22, ActivityRewardTypeEnum.getEnumByName("honor_title"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
            }
            activityCommonRedis.setCommonHashNum(getHashActivityId(activityId, uid), boxType, 2);
        }
    }

    private int getTimesEnergyNum(int level){
        switch (level){
            case 0:
            case 1:
                return 1;
            case 2:
            case 3:
                return 2;
            case 4:
                return 3;
            default:
                return 0;
        }
    }

    private String getBoxStatusKey(int level){
        switch (level){
            case 1:
                return BOX_1_KEY;
            case 2:
                return BOX_2_KEY;
            case 3:
                return BOX_3_KEY;
            case 4:
                return BOX_4_KEY;
            case 5:
                return BOX_5_KEY;
            default:
                return "";
        }
    }


    public void handleGift(SendGiftData sendGiftData, String activityId) {
        int totalBeans = sendGiftData.getPrice() * sendGiftData.getNumber() * sendGiftData.getAid_list().size();
        String from_uid = sendGiftData.getFrom_uid();

        synchronized (stringPool.intern(ACTIVITY_NAME_RANKING + from_uid)) {

            int leftIncBeans = totalBeans;
            while (leftIncBeans > 0){
                int currentLevel = activityCommonRedis.getCommonHashValue(getHashActivityId(activityId, from_uid), LEVEL_KEY);
                int upLevelEnergy = UP_LEVEL_MAP.getOrDefault(currentLevel, 0);

                if(upLevelEnergy <= 0){
                    return;
                }
                int currentEnergy = activityCommonRedis.getCommonHashValue(getHashActivityId(activityId, from_uid), ENERGY_KEY);
                int incTimes = pyramidService.getTimesEnergyNum(currentLevel);
                int incUserEnergy = leftIncBeans * incTimes;

                int afterEnergy = currentEnergy + incUserEnergy;

                if(afterEnergy >= upLevelEnergy){
                    // 升级
                    activityCommonRedis.setCommonHashNum(getHashActivityId(activityId, from_uid), ENERGY_KEY, upLevelEnergy);

                    int afterLevel = currentLevel + 1;
                    activityCommonRedis.setCommonHashNum(getHashActivityId(activityId, from_uid), LEVEL_KEY, afterLevel);

                    String boxKey = pyramidService.getBoxStatusKey(afterLevel);
                    activityCommonRedis.setCommonHashNum(getHashActivityId(activityId, from_uid), boxKey, 1);

                    String rollData = from_uid + "-" + boxKey;
                    activityCommonRedis.addCommonListRecord(activityId, rollData);
                    int leftEnergy = afterEnergy - upLevelEnergy;
                    leftIncBeans = leftEnergy / incTimes;

                    logger.info("handlePyramidGift upLevel from_uid: {}, currentEnergy:{}, incUserEnergy:{}, afterEnergy:{}, incTimes:{}, leftEnergy:{}, leftIncBeans:{}", from_uid, currentEnergy, incUserEnergy, afterEnergy, incTimes, leftEnergy, leftIncBeans);
                }else {
                    activityCommonRedis.incCommonHashNum(getHashActivityId(activityId, from_uid), ENERGY_KEY, incUserEnergy);
                    leftIncBeans = 0;
                }

                logger.info("handlePyramidGift info from_uid: {}, currentEnergy:{}, incUserEnergy:{}, afterEnergy:{}, incTimes:{}, leftIncBeans:{}", from_uid, currentEnergy, incUserEnergy, afterEnergy, incTimes, leftIncBeans);
            }

        }
    }

}
