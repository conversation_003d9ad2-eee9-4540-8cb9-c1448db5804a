package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.quhong.analysis.BackendReviewRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.ScoreRecordEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.SharingOfficerDTO;
import com.quhong.data.vo.SharingOfficerVO;
import com.quhong.dto.ImageDTO;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.SharingOfficerLingGanDao;
import com.quhong.mysql.dao.SharingOfficerUploadLogDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.SharingOfficerLingGanData;
import com.quhong.mysql.data.SharingOfficerUploadLogData;
import com.quhong.vo.DetectVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 分享官活动
 */
@Service
public class SharingOfficerService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(SharingOfficerService.class);
    private static final String ACTIVITY_TITLE_EN = "Sharing Officer";
    private static final String ACTIVITY_TITLE_AR = "Sharing Officer";
    public static final String ACTIVITY_ID = "67d418726818517c9c49231c";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/sharing_officer/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/sharing_officer/?activityId=%s", ACTIVITY_ID);
            private static final String CDN_DOMAIN = "https://cloudcdn.qmovies.tv/";

    public static final Map<Integer, String> MATCH_IMAGE_TEXT_MAP = new HashMap<Integer, String>() {
        {
            put(SharingOfficerUploadLogDao.CHANNEL_SNAPCHAT_TYPE, "SnapChat"); //
            put(SharingOfficerUploadLogDao.CHANNEL_INSTAGRAM_TYPE, "InStagram"); //
            put(SharingOfficerUploadLogDao.CHANNEL_TIKTOK_TYPE, "TikTok"); //
        }
    };
    public static final List<Integer> LEVEL_LIST_TYPE = Arrays.asList(SharingOfficerUploadLogDao.LEVEL_1_TYPE,
            SharingOfficerUploadLogDao.LEVEL_2_TYPE,
            SharingOfficerUploadLogDao.LEVEL_3_TYPE);

    private static final int DAY_7 = 7;


    public static final Map<Integer, String> LEVEL_SUB_EVENT_SCENE_MAP = new HashMap<Integer, String>() {
        {
            put(SharingOfficerUploadLogDao.LEVEL_1_TYPE, "primary"); //
            put(SharingOfficerUploadLogDao.LEVEL_2_TYPE, "middle"); //
            put(SharingOfficerUploadLogDao.LEVEL_3_TYPE, "senior"); //
        }
    };

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private IDetectService detectService;
    @Resource
    private SharingOfficerUploadLogDao sharingOfficerUploadLogDao;
    @Resource
    private SharingOfficerLingGanDao sharingOfficerLingGanDao;


    public SharingOfficerVO sharingOfficerInfo(String activityId, String uid) {
        SharingOfficerVO vo = new SharingOfficerVO();
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        vo.setStartTime(activityData.getStartTime());
        int endTime = DateHelper.ARABIAN.getWeekEndDateTime();
        vo.setEndTime(endTime);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

        List<SharingOfficerLingGanData> sharingGanDataList = sharingOfficerLingGanDao.selectList();
        if (!CollectionUtils.isEmpty(sharingGanDataList)) {
            SharingOfficerVO.LingGan lingGanVO = new SharingOfficerVO.LingGan();
            List<String> noticeList = new ArrayList<>();
            List<String> imagesList = new ArrayList<>();
            List<String> videoList = new ArrayList<>();
            for (SharingOfficerLingGanData item : sharingGanDataList) {
                fillToList(item.getNoticeList(), noticeList);
                fillToList(item.getImagesList(), imagesList);
                fillToList(item.getVideoList(), videoList);
            }
            lingGanVO.setNoticeList(noticeList);
            lingGanVO.setImagesList(imagesList);
            lingGanVO.setVideoUrl(videoList.isEmpty() ? null : videoList.get(0));
            vo.setLingGanVO(lingGanVO);
        }


        SharingOfficerVO.ChannelStatus channel1Status = new SharingOfficerVO.ChannelStatus();
        SharingOfficerVO.ChannelStatus channel2Status = new SharingOfficerVO.ChannelStatus();
        SharingOfficerVO.ChannelStatus channel3Status = new SharingOfficerVO.ChannelStatus();
        channel1Status.setChannelStatusList(Arrays.asList(0, 0, 0));
        channel2Status.setChannelStatusList(Arrays.asList(0, 0, 0));
        channel3Status.setChannelStatusList(Arrays.asList(0, 0, 0));

        int weekEndTime = DateHelper.ARABIAN.getWeekEndDateTime();
        int weekStartTime = endTime - (int) TimeUnit.DAYS.toSeconds(DAY_7);


        List<SharingOfficerUploadLogData> officerUploadLogDataList = sharingOfficerUploadLogDao.selectListByUid(uid, activityData.getStartTime(), activityData.getEndTime());
//        List<SharingOfficerUploadLogData> weekLogDataList = sharingOfficerUploadLogDao.selectListByUid(uid, weekStartTime, weekEndTime);
//        Set<Integer> timeSet = new HashSet<>();
        int timeCount = 0;
        if (!CollectionUtils.isEmpty(officerUploadLogDataList)) {
            for (SharingOfficerUploadLogData item : officerUploadLogDataList) {
                int cTime = item.getCtime();
                int state = item.getState();
                if (state == 1) {
                    if (item.getLevelType() == SharingOfficerUploadLogDao.LEVEL_1_TYPE) {
                        fillChannelStatus(channel1Status, item);
                    } else if (item.getLevelType() == SharingOfficerUploadLogDao.LEVEL_2_TYPE) {
                        if (cTime >= weekStartTime && cTime < weekEndTime) {
                            fillChannelStatus(channel2Status, item);
                        }
                    } else if (item.getLevelType() == SharingOfficerUploadLogDao.LEVEL_3_TYPE) {
                        if (cTime >= weekStartTime && cTime < weekEndTime) {
                            fillChannelStatus(channel3Status, item);
                        }
                    }
                    timeCount++;
                }
            }
            vo.setUploadLogList(officerUploadLogDataList);
        }

        vo.setTimes(timeCount);
        vo.setChannel1Status(channel1Status);
        vo.setChannel2Status(channel2Status);
        vo.setChannel3Status(channel3Status);
        vo.setMaxSize(12);
        return vo;
    }

    public SharingOfficerVO sharingOfficerRewardRecord(String activityId, String uid, int page) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        int pageSize = 20;
        SharingOfficerUploadLogData filterData = new SharingOfficerUploadLogData();
        filterData.setState(1);
        IPage<SharingOfficerUploadLogData> iPage = sharingOfficerUploadLogDao.selectPage(filterData,
                page, pageSize, activityData.getStartTime(), activityData.getEndTime());
        List<SharingOfficerUploadLogData> officerUploadLogDataList = iPage.getRecords();
        List<SharingOfficerVO.Record> recordList = new ArrayList<>();
        SharingOfficerVO vo = new SharingOfficerVO();
        if (!CollectionUtils.isEmpty(officerUploadLogDataList)) {
            for (SharingOfficerUploadLogData item : officerUploadLogDataList) {
                SharingOfficerVO.Record record = new SharingOfficerVO.Record();
                ActorData actorData = actorDao.getActorDataFromCache(item.getUid());
                record.setName(actorData.getName());
                recordList.add(record);
            }
            vo.setRecordList(recordList);
            vo.setNextPage(officerUploadLogDataList.size() < pageSize ? "" : String.valueOf(page + 1));
        }
        return vo;
    }

    /**
     * 提交分享官
     */
    public void submitSharingOfficer(SharingOfficerDTO dto) {
        String activityId = dto.getActivityId();
        String uid = dto.getUid();
        String imageUrl = dto.getImageUrl();
        String videoUrl = dto.getVideoUrl();
        int levelType = dto.getLevelType();
        int channelType = dto.getChannelType();
        if (StringUtils.isEmpty(imageUrl) || !LEVEL_LIST_TYPE.contains(levelType) || !MATCH_IMAGE_TEXT_MAP.containsKey(channelType)) {
            logger.info("param error imageUrl:{} levelType:{} channelType:{}", imageUrl, levelType, channelType);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        imageUrl = CDN_DOMAIN + imageUrl;

        OtherRankingActivityData activityData = checkActivityTime(activityId);
        int startTime = 0;
        int endTime = 0;
        if (levelType == SharingOfficerUploadLogDao.LEVEL_1_TYPE) {
            startTime = activityData.getStartTime();
            endTime = activityData.getEndTime();
        } else if (levelType == SharingOfficerUploadLogDao.LEVEL_2_TYPE) {
            endTime = DateHelper.ARABIAN.getWeekEndDateTime();
            startTime = endTime - (int) TimeUnit.DAYS.toSeconds(DAY_7);
        } else if (levelType == SharingOfficerUploadLogDao.LEVEL_3_TYPE) {
            endTime = DateHelper.ARABIAN.getWeekEndDateTime();
            startTime = endTime - (int) TimeUnit.DAYS.toSeconds(DAY_7);
        }
        List<SharingOfficerUploadLogData> uploadLogDataList = sharingOfficerUploadLogDao.selectListByUidType(uid, levelType,
                channelType, startTime, endTime);
        if (!CollectionUtils.isEmpty(uploadLogDataList)) {
            logger.info("already submit uploadLogData:{}", JSON.toJSONString(uploadLogDataList.get(0)));
            throw new CommonH5Exception(ActivityHttpCode.SHARING_OFFICER_ALREADY_SUBMIT);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (!StringUtils.isEmpty(actorData.getTn_id())) {
            int deviceCount = sharingOfficerUploadLogDao.selectCountByTnIdLevel(actorData.getTn_id(), levelType, startTime, endTime);
            if (deviceCount >= 9) {
                logger.info("sharing_officer_device_max_num submit levelType:{} deviceCount:{}", levelType, deviceCount);
                if (levelType == SharingOfficerUploadLogDao.LEVEL_1_TYPE) {
                    throw new CommonH5Exception(ActivityHttpCode.SHARING_OFFICER_DEVICE_MAX_NUM);
                } else {
                    throw new CommonH5Exception(ActivityHttpCode.SHARING_OFFICER_DEVICE_WEEK_MAX_NUM);
                }
            }
        }
        int state = 0;
        if (levelType == SharingOfficerUploadLogDao.LEVEL_1_TYPE) {
            DetectVO detectVO = detectService.detectImage(new ImageDTO(imageUrl, DetectOriginConstant.ACTIVITY_SHARING_OFFICER_HEAD)).getData();
            logger.info("imageUrl:{} detectVO:{}", imageUrl, JSON.toJSONString(detectVO));
            String hitText = detectVO.getHitText();
            if (!StringUtils.isEmpty(hitText) && hitText.contains(MATCH_IMAGE_TEXT_MAP.get(channelType))) {
                // 识别成功下发奖励
                state = 1;
                resourceKeyHandlerService.sendResourceData(uid, SharingOfficerUploadLogDao.MATCH_LEVEL_RES_MAP.get(levelType),
                        ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN);
            }
        }
        int now = DateHelper.getNowSeconds();
        SharingOfficerUploadLogData logData = new SharingOfficerUploadLogData();
        logData.setUid(uid);
        logData.setTnId(actorData.getTn_id());
        logData.setImageUrl(imageUrl);
        if (!StringUtils.isEmpty(videoUrl)) {
            logData.setVideoUrl(CDN_DOMAIN + videoUrl);
        }
        logData.setState(state);
        logData.setChannelType(channelType);
        logData.setLevelType(levelType);
        logData.setCtime(now);
        logData.setMtime(now);
        sharingOfficerUploadLogDao.insert(logData);
        doInsertReportEvent(logData);
    }

    public boolean uploadLimit(String activityId, String uid, int cmd, String fileType) {
        String key = String.format("SharingOfficer-%s-%s-%s", activityId, DateHelper.ARABIAN.formatDateInDay(), fileType);
        if (cmd == 1) {
            activityCommonRedis.incrCommonZSetRankingScoreSimple(key, uid, 1);
        } else if (cmd == 2) {
            int num = activityCommonRedis.getCommonZSetRankingScore(key, uid);
            return num >= 10;
        }
        return true;
    }

    private void fillChannelStatus(SharingOfficerVO.ChannelStatus channelStatus, SharingOfficerUploadLogData item) {
        List<Integer> channelStatusList = channelStatus.getChannelStatusList();
        if (item.getChannelType() == SharingOfficerUploadLogDao.CHANNEL_SNAPCHAT_TYPE) {
            channelStatus.setSnapchatState(1);
            channelStatusList.set(0, 1);
        } else if (item.getChannelType() == SharingOfficerUploadLogDao.CHANNEL_INSTAGRAM_TYPE) {
            channelStatus.setInstagramState(1);
            channelStatusList.set(1, 1);
        } else if (item.getChannelType() == SharingOfficerUploadLogDao.CHANNEL_TIKTOK_TYPE) {
            channelStatus.setTiktokState(1);
            channelStatusList.set(2, 1);
        }
    }

    private void fillToList(String itemNameListStr, List<String> toList) {
        if (!StringUtils.isEmpty(itemNameListStr)) {
            String[] nameArray = itemNameListStr.split(",");
            List<String> nameList = Arrays.asList(nameArray);
            toList.addAll(nameList);
        }
    }

    private void doInsertReportEvent(SharingOfficerUploadLogData logData) {
        BackendReviewRecordEvent eventProperties = new BackendReviewRecordEvent();
        eventProperties.setUid(logData.getUid());
        eventProperties.setCreate_ctime(logData.getCtime());
        eventProperties.setModify_ctime(logData.getMtime());
        eventProperties.setScene("social_media_activities");
        eventProperties.setSub_scene(LEVEL_SUB_EVENT_SCENE_MAP.getOrDefault(logData.getLevelType(), ""));
        eventProperties.setReview_status(logData.getState());
        EventDTO eventDTO = new EventDTO(eventProperties);
        eventDTO.setEventId(getEventKey(logData.getUid(), logData.getCtime()));
        eventReport.trackUpdate(eventDTO);
    }


    private String getEventKey(String uid, int ctime) {
        return String.format("%s-%s", uid, ctime);
    }

}
