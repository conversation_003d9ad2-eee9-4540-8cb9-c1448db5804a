package com.quhong.service;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.OperationRoomWashVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.OtherSupportUserVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 丛林之王
 */
@Service
public class KingOfJungleService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(KingOfJungleService.class);
    public static final String ACTIVITY_CONTAINS_NAME = "King Of Jungle";
    private static final String ACTIVITY_TITLE_EN = "Activity King Of Jungle";
    private static final List<String> ALL_LOOK_USER = Arrays.asList("5ddad30b0ca307a634ec01cb", "5cdf784961d047a4adf44064", "5cb6fc80644f8e002a90cfc2");
    private static final List<String> OPERATION_ROOM_LIST = Arrays.asList("r:6063258e7a505b70e2bed1de", "r:5db8575dabb01a005f968a93", "r:622e9cc874bb1a9f4c763f4f", "r:5cb2f98c66dc63003ebfe3f2", "r:5c190cd066dc63009d6527df", "r:5c6cd10f66dc63516adcb6b3", "r:5cc9473a66dc630025bf7645", "r:5ee27069484060c13abb07e8", "r:5c5cbfc066dc630026cb6262", "r:609d99f88bb2d92d1885ddba", "r:5db52b44ab4f1e4128e0fc96", "r:6111b324830ba10ccf89fe70", "r:5e4044b5b271b6040a98f23e", "r:5d38de23ad59835f1f8bbf49", "r:5f6769ac15a6c3af461ca69e", "r:5d6b01e50ddfe7811341c5a7", "r:5c80e09c66dc63003dc4de58", "r:5c334f6b66dc63011a81a3a5", "r:5cc388df66dc63002932e454", "r:634454c6202c0143cdef7ba1", "r:5c65a64966dc63003ebe124d", "r:5fb920c22a8c6468a363dbd8", "r:5d38f757abb01a005b455a75", "r:600c00bf32a61091e2d5f187", "r:60dd015bf4d233799ebac629", "r:5fafa436a3639186a55df9d8", "r:5cc3a57066dc63002932e568", "r:5d30abb6c47ba4e2a4df514c", "r:5da4da74add87696168edb44", "r:5d527bd3abb01a004db9d300", "r:600806c21b666cec90d405c0", "r:5dcf5fb977cc7c7e1f8d4029", "r:63159048085609557104c254", "r:5aed7d7f1bad489359765888", "r:5f16d69babb01a004fdfe5d4", "r:60d0a3a691965616273453bf", "r:5ea7a1c3abb01a00856e5520", "r:5c371f5e66dc630027f609f6", "r:5bc8a56266dc6300f162f3eb", "r:60254fa3abb01a0032b55944", "r:5bd8b67a66dc6300b50461e4", "r:5c90662a66dc630026d3965a", "r:5e03fcd2abb01a006a4ee3db", "r:5d8b945178cf5ea933183b5b");

    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private WhiteTestDao whiteTestDao;

    private String getKingOfJungleRoomKey(String activityId) {
        return String.format("kingOfJungleRoom:%s", activityId);
    }

    private String getSupportRoomUserKey(String activityId, String roomId) {
        return String.format("jungleSupportRoomUser:%s:%s", activityId, roomId);
    }


    public OperationRoomWashVO kingOfJungleInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        OperationRoomWashVO vo = new OperationRoomWashVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());


        ActorData actorData = actorDao.getActorDataFromCache(uid);
        List<OtherRankingListVO> otherRankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        String hostRoomId = RoomUtils.formatRoomId(uid);

        String operationRoomWashRankKey = getKingOfJungleRoomKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMapByScore(operationRoomWashRankKey, 40, 100000);
        int rank = 1;
        int flag = -1;

        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();

            String roomId = entry.getKey();
            rankingListVO.setScoreStr(entry.getValue().toString());
            rankingListVO.setRoomId(roomId);
            rankingListVO.setRank(rank);
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
            rankingListVO.setName(roomData.getName());
            rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));

            String supportUserKey = getSupportRoomUserKey(activityId, roomId);
            List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 3);

            List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
            for (String supportUid : supportUserList) {
                OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                supportUserVO.setName(supportActorData.getName());
                supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                supportUserVO.setUid(supportUid);
                supportUserVOList.add(supportUserVO);
            }
            rankingListVO.setSupportUserList(supportUserVOList);
            if (roomId.equals(hostRoomId)) {
                flag = rank;
                BeanUtils.copyProperties(rankingListVO, myRank);
            }
            rank += 1;
            otherRankingList.add(rankingListVO);
        }

        if (flag > 0) {
            List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
            List<OtherSupportUserVO> srcSupportUserVOList = myRank.getSupportUserList();
            for (OtherSupportUserVO srcSupportUid : srcSupportUserVOList) {
                OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                BeanUtils.copyProperties(srcSupportUid, supportUserVO);
                supportUserVOList.add(supportUserVO);
            }
            myRank.setSupportUserList(supportUserVOList);
        } else {
            int myRankInt = activityCommonRedis.getCommonZSetRank(operationRoomWashRankKey, hostRoomId);
            myRank.setScoreStr(String.valueOf(activityCommonRedis.getCommonZSetRankingScore(operationRoomWashRankKey, hostRoomId)));
            myRank.setRoomId(hostRoomId);
            myRank.setRank(myRankInt);
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(hostRoomId);
            if (roomData != null) {
                myRank.setName(roomData.getName());
                myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            } else {
                myRank.setName(actorData.getName());
                myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            }
            List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
            if (myRankInt != 0) {
                String supportUserKey = getSupportRoomUserKey(activityId, hostRoomId);
                List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 3);
                for (String supportUid : supportUserList) {
                    OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                    ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                    supportUserVO.setName(supportActorData.getName());
                    supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                    supportUserVO.setUid(supportUid);
                    supportUserVOList.add(supportUserVO);
                }
            }
            myRank.setSupportUserList(supportUserVOList);
        }
        vo.setOtherRankingList(otherRankingList);
        vo.setMyRank(myRank);
        return vo;
    }

    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        int totalNum = giftData.getNumber() * giftData.getAid_list().size();
        String roomId = giftData.getRoomId();
        String fromUid = giftData.getFrom_uid();

        String kingOfJungleRoomKey = getKingOfJungleRoomKey(activityId);
        String supportRoomUserKey = getSupportRoomUserKey(activityId, roomId);
        activityCommonRedis.incrCommonZSetRankingScore(kingOfJungleRoomKey, roomId, totalNum);
        activityCommonRedis.incrCommonZSetRankingScore(supportRoomUserKey, fromUid, totalNum);
    }


    // 总榜排行榜奖励
    public void distributionTotalRanking(String activityId) {
        try {
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getKingOfJungleRoomKey(activityId), 10);
            int rank = 1;
            Set<String> supportUserSet = new HashSet<>();
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                String roomId = entry.getKey();
                String rankHostId = RoomUtils.getRoomHostId(roomId);
                String resourceKey = null;
                String supportUserKey = getSupportRoomUserKey(activityId, roomId);
                List<String> supportUserList;
                switch (rank) {
                    case 1:
                        resourceKey = "kingOfJungleTop1";
                        supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 3);
                        supportUserSet.addAll(supportUserList);
                        break;
                    case 2:
                        resourceKey = "kingOfJungleTop2";
                        supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 3);
                        supportUserSet.addAll(supportUserList);
                        break;
                    case 3:
                        resourceKey = "kingOfJungleTop3";
                        supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 3);
                        supportUserSet.addAll(supportUserList);
                        break;
                    case 4:
                        resourceKey = "kingOfJungleTop4";
                        break;
                    case 5:
                        resourceKey = "kingOfJungleTop5";
                        break;
                    default:
                        resourceKey = "kingOfJungleTop6-10";
                }
                resourceKeyHandlerService.sendResourceData(rankHostId, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, "", "");
                rank += 1;
            }
            if (!CollectionUtils.isEmpty(supportUserSet)) {
                logger.info("send resource supportUserSet:{}", supportUserSet);
                for (String uid : supportUserSet) {
                    resourceKeyHandlerService.sendResourceData(uid, "kingOfJungleSupport", ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, "", "");
                }
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }


}
