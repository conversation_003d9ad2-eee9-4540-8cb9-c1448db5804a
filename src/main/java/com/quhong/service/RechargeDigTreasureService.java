package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RechargeDigTreasureShovelRecordEvent;
import com.quhong.analysis.StarBeatGameLogEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.bo.DiTreasureRecordBO;
import com.quhong.data.bo.DiTreasureStepBO;
import com.quhong.data.dto.DiTreasureDrawDTO;
import com.quhong.data.vo.DiTreasureVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.handler.ActivityRechargeHandler;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 充值挖宝
 */
@Service
public class RechargeDigTreasureService extends OtherActivityService implements ActivityRechargeHandler {


    private static final Logger logger = LoggerFactory.getLogger(RechargeDigTreasureService.class);
    private static final String ACTIVITY_TITLE_EN = "Recharge to Dig Treasure";
    private static final String ACTIVITY_TITLE_AR = "اشحن لحفر الكنز";
    private static final String ACTIVITY_DESC = "Recharge to Dig Treasure Reward";
    private static final String ACTIVITY_ID = "670f6e0ae0bd734e744f3668";
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/game/op_1720608261_7.png";
    private static final List<String> POOL_KEY_LIST = Arrays.asList("rechargeDigTreasurePool1", "rechargeDigTreasurePool2", "rechargeDigTreasurePool3");
    private static final Integer RECORD_PAGE_SIZE = 10;

    private static final List<Integer> DRAW_LEVEL_LIST = Arrays.asList(3, 8);
    private static final List<String> DRAW_LEVEL_KEY_LIST = Arrays.asList("rechargeDigTreasureLevel3", "rechargeDigTreasureLevel8");

    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/dig_treasure/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/dig_treasure/?activityId=%s", ACTIVITY_ID);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    // 剩余铲子数量
    private static final String DIG_TREASURE_BALANCE = "balance";
    private static final String DIG_TREASURE_LEFT_NUM = "leftNum";
    private static final String DIG_TREASURE_CURRENT_ROUND = "round";        // 当前轮次
    private static final String DIG_TREASURE_ROUND_TIMES = "roundTimes";     // 当前轮次第n步
    private static final Integer DIG_TREASURE_RATE_BEAN = 110;
    private static final Integer DIG_TREASURE_MAX_STEP = 8;
    private static final Integer DIG_TREASURE_FLUSH_NUM = 1;

    private static final List<DiTreasureStepBO> DI_STEP_LIST = new ArrayList<DiTreasureStepBO>(8) {{
        add(new DiTreasureStepBO(1, 1, 357));
        add(new DiTreasureStepBO(2, 3, 356));
        add(new DiTreasureStepBO(3, 8, 353));
        add(new DiTreasureStepBO(4, 15, 345));
        add(new DiTreasureStepBO(5, 30, 330));
        add(new DiTreasureStepBO(6, 50, 300));
        add(new DiTreasureStepBO(7, 100, 250));
        add(new DiTreasureStepBO(8, 150, 150));
    }};

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private EventReport eventReport;

    private String getHashActivityId(String activityId, String uid){
        return String.format("digTreasure:%s:%s", activityId, uid);
    }

    // 抽奖相关的每日key
    private String getDailyDate(String activityId){
        return String.format("dailyDate:%s", activityId);
    }

    private String getDailyDrawHashKey(String activityId, String uid, String currentDate){
        return String.format("digDailyDraw:%s:%s:%s", activityId, uid, currentDate);
    }

    private String getResourceRoundHKey(String metaId, int round){
        return String.format("%s:%s", metaId, round);
    }

    private String getDrawTreasureLevel(String activityId, String currentDate, int round){
        return String.format("drawTreasureLevel:%s:%s:%s", activityId, currentDate, round);
    }

    public String getRecordActivityKey(String activityId, String uid){
        return String.format("record:%s:%s", activityId, uid);
    }

    public DiTreasureVO rechargeDigTreasureConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        DiTreasureVO vo = new DiTreasureVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userMapData = activityCommonRedis.getCommonHashAll(hashActivityId);

        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        String dailyDrawHashKey = getDailyDrawHashKey(activityId, uid, currentDate);
        Map<String, Integer> dailyDrawMapData = activityCommonRedis.getCommonHashAll(dailyDrawHashKey);

        vo.setBalance(userMapData.getOrDefault(DIG_TREASURE_BALANCE, 0));
        vo.setRoundTimes(dailyDrawMapData.getOrDefault(DIG_TREASURE_ROUND_TIMES, 0));

        // 设置下一次抽奖所需铲子数、本轮一次性抽完需要的铲子数、下次抽奖需要充值的总钻石数、本轮一次性抽完需要充值的总金币数
        Map<Integer, DiTreasureStepBO> diTreasureStepBOMap = DI_STEP_LIST.stream().collect(Collectors.toMap(DiTreasureStepBO::getStep, Function.identity()));
        int nextStep = vo.getRoundTimes() + 1;
        DiTreasureStepBO nextStepBO = diTreasureStepBOMap.get(nextStep);
        if(nextStepBO == null){
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }

        vo.setNextDrawNeed(nextStepBO.getCost());
        vo.setRoundAllDrawNeed(nextStepBO.getAllCost());
        vo.setOnceDrawNeedTotalGold(nextStepBO.getCost() * DIG_TREASURE_RATE_BEAN);
        vo.setRoundAllDrawNeedTotalGold(nextStepBO.getAllCost() * DIG_TREASURE_RATE_BEAN);

        int leftNum = userMapData.getOrDefault(DIG_TREASURE_LEFT_NUM, 0);
        int currGoldBalance = vo.getBalance() * DIG_TREASURE_RATE_BEAN + leftNum;
        vo.setCurrGoldBalance(currGoldBalance);

        // 设置抽奖奖励配置
        int round = dailyDrawMapData.getOrDefault(DIG_TREASURE_CURRENT_ROUND, 0);
        List<ResourceKeyConfigData> keyConfigList = resourceKeyConfigDao.findListByKeys(new HashSet<>(POOL_KEY_LIST));
        List<DiTreasureVO.DiResourceVO> drawAwardList = keyConfigList.stream().flatMap(keyConfig -> keyConfig.getResourceMetaList().stream()
                        .map(resourceMeta -> {
                            DiTreasureVO.DiResourceVO diResourceVO = new DiTreasureVO.DiResourceVO();
                            BeanUtils.copyProperties(resourceMeta, diResourceVO);
                            int draw = dailyDrawMapData.getOrDefault(getResourceRoundHKey(resourceMeta.getMetaId(), round), 0);
                            diResourceVO.setDraw(draw);
                            return diResourceVO;
                        }))
                .collect(Collectors.toList());
        vo.setDrawAwardList(drawAwardList);
        return vo;
    }

    private List<ResourceKeyConfigData.ResourceMeta> getDrawPoolList(Map<String, Integer> dailyDrawMapData, int drawStep) {
        String poolKey = null;
        switch (drawStep) {
            case 1:
            case 2:
                poolKey = POOL_KEY_LIST.get(0);
                break;
            case 3:
            case 4:
                poolKey = POOL_KEY_LIST.get(1);
                break;
            case 5:
            case 6:
            case 7:
            case 8:
                poolKey = POOL_KEY_LIST.get(2);
                break;
        }

        if(StringUtils.isEmpty(poolKey)){
            logger.error("getDrawPoolList key not find drawStep:{}", drawStep);
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }

        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(poolKey);
        if(resourceKeyConfigData == null){
            logger.error("getDrawPoolList not find resourceKeyConfigData:{}", drawStep);
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }

        int round = dailyDrawMapData.getOrDefault(DIG_TREASURE_CURRENT_ROUND, 0);
        return resourceKeyConfigData.getResourceMetaList().stream().
                filter(item -> dailyDrawMapData.getOrDefault(getResourceRoundHKey(item.getMetaId(), round), 0) <= 0).
                collect(Collectors.toList());
    }

    public String getMetaIdByProbability(Map<String, Integer> sourceMap) {
        try {
            if (CollectionUtils.isEmpty(sourceMap)) {
                logger.info("getAwardIdByProbability sourceMap is empty");
                return null;
            }
            int total = 0;
            Map<Integer, String> mapRatio = new HashMap<>();
            List<Integer> ratioList = new ArrayList<>(); // 这样大于等于0，小于1000取第一个（key为1000） 、大于等于1000，小于2000取第二个 （key为2000）
            for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
                String awardId = entry.getKey();
                int value = entry.getValue();
                if (value <= 0) {
                    continue;
                }
                total += value;
                mapRatio.put(total, awardId);
                ratioList.add(total);
            }
            if (total == 0) {
                logger.info("getAwardIdByProbability total is zero sourceMap={} ", sourceMap);
                return null;
            }
            int ratio = ThreadLocalRandom.current().nextInt(0, total);//0-(total-1)
            if (!ratioList.contains(ratio)) {
                ratioList.add(ratio);
                Collections.sort(ratioList);
            }

            int index = ratioList.indexOf(ratio);
            int destNum = ratioList.get(index + 1);
            return mapRatio.get(destNum);
        } catch (Exception e) {
            logger.error("getAwardIdByProbability error sourceMap:{} msg:{}", sourceMap, e.getMessage(), e);
            return null;
        }
    }

    private Map<String, Integer> getCalcMap(List<ResourceKeyConfigData.ResourceMeta> resourceMetaList) {
        Map<String, Integer> calcMap = new HashMap<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceMetaList) {
            try {
                int rate = (int) (Float.parseFloat(resourceMeta.getRateNumber()) * 1000);
                if (rate >= 0) {
                    calcMap.put(resourceMeta.getMetaId(), rate);
                }
            } catch (Exception e) {
                logger.info("resourceMeta:{} error:{}", JSONObject.toJSONString(resourceMeta), e.getMessage(), e);
                throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
            }
        }
        return calcMap;
    }

    public DiTreasureVO rechargeDigTreasureDraw(DiTreasureDrawDTO dto) {
        String activityId = dto.getActivityId();
        String uid = dto.getUid();
        boolean drawAll = dto.isDrawAll();
        checkActivityTime(activityId);

        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userMapData = activityCommonRedis.getCommonHashAll(hashActivityId);

            String currentDate = DateHelper.ARABIAN.formatDateInDay();
            String dailyDrawHashKey = getDailyDrawHashKey(activityId, uid, currentDate);
            Map<String, Integer> dailyDrawMapData = activityCommonRedis.getCommonHashAll(dailyDrawHashKey);

            // 获取当前步数、 获取所需铲子数量、 需要抽的步数
            int currentStep = dailyDrawMapData.getOrDefault(DIG_TREASURE_ROUND_TIMES, 0);
            int nextStep =  currentStep + 1;
            Map<Integer, DiTreasureStepBO> diTreasureStepBOMap = DI_STEP_LIST.stream().collect(Collectors.toMap(DiTreasureStepBO::getStep, Function.identity()));
            DiTreasureStepBO nextStepBO = diTreasureStepBOMap.get(nextStep);
            if(nextStepBO == null){
                throw new CommonH5Exception(HttpCode.SERVER_ERROR);
            }
            int balance = userMapData.getOrDefault(DIG_TREASURE_BALANCE, 0);
            int needPower = drawAll ? nextStepBO.getAllCost() : nextStepBO.getCost();   // 所需铲子数量
            int overStep = drawAll ? DIG_TREASURE_MAX_STEP : nextStep;           // 需要抽的步数
            if(balance < needPower){
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }

            // 扣除铲子数量、 抽奖、 记录抽奖历史
            activityCommonRedis.incCommonHashNum(hashActivityId, DIG_TREASURE_BALANCE, -needPower);
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            List<String> drawKeyList = new ArrayList<>();
            List<DiTreasureVO.DiResourceVO> drawAwardList = new ArrayList<>();

            int round = dailyDrawMapData.getOrDefault(DIG_TREASURE_CURRENT_ROUND, 0);
            int drawNum = 0;
            while (currentStep < overStep){
                currentStep += 1;
                drawNum += 1;
                List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = this.getDrawPoolList(dailyDrawMapData, currentStep);
                Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceMetaList.stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));

                // 抽奖
                Map<String, Integer> calcMap = this.getCalcMap(resourceMetaList);
                String drawMetaId = this.getMetaIdByProbability(calcMap);
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(drawMetaId);

                // 设置返回对象
                DiTreasureVO.DiResourceVO diResourceVO = new DiTreasureVO.DiResourceVO();
                BeanUtils.copyProperties(resourceMeta, diResourceVO);
                diResourceVO.setDraw(1);
                drawAwardList.add(diResourceVO);

                // 奖励下发
                resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR,
                        ACTIVITY_TITLE_EN, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);

                String resourceRoundHKey = getResourceRoundHKey(drawMetaId, round);
                activityCommonRedis.setCommonHashNum(dailyDrawHashKey, resourceRoundHKey, 1);
                dailyDrawMapData.put(resourceRoundHKey, 1);

                // 统计层级奖励
                this.handleDrawTreasure(uid, currentDate, round, 1);

                // 记录历史记录
                drawKeyList.add(drawMetaId);
            }

            if(currentStep >= DIG_TREASURE_MAX_STEP){
                activityCommonRedis.incCommonHashNum(dailyDrawHashKey, DIG_TREASURE_CURRENT_ROUND, 1);
                activityCommonRedis.setCommonHashNum(dailyDrawHashKey, DIG_TREASURE_ROUND_TIMES, 0);
            }else {
                activityCommonRedis.setCommonHashNum(dailyDrawHashKey, DIG_TREASURE_ROUND_TIMES, currentStep);
            }

            String recordActivityKey = getRecordActivityKey(activityId, uid);
            activityCommonRedis.addCommonListRecord(recordActivityKey, JSONObject.toJSONString(new DiTreasureRecordBO(needPower, 0, DateHelper.getNowSeconds(), drawKeyList)));

            DiTreasureVO vo = new DiTreasureVO();
            vo.setDrawAwardList(drawAwardList);
            this.doReportEvent(uid, needPower, 1, drawNum, round, drawAll, drawAwardList);

            return vo;

        }
    }

    private void doReportEvent(String uid, int needPower, int useWay, int drawNum, int round, Boolean drawAll, List<DiTreasureVO.DiResourceVO> drawAwardList) {
        StarBeatGameLogEvent event = new StarBeatGameLogEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE_EN);
        event.setSence_detail(useWay);
        event.setRoom_id("");
        event.setCost_diamonds(needPower);
        event.setDraw_nums(drawNum);
        event.setDesc(String.valueOf(round));
        if(drawAll != null){
            event.setDraw_detail(drawAll ? "draw more" : "draw once");
        }
        event.setDraw_round(round);
        event.setDraw_result(JSON.toJSONString(drawAwardList));
        eventReport.track(new EventDTO(event));
    }

    public void handleDrawTreasure(String uid, String currentDate, int round, int value) {
        String drawTreasureLevelKey = getDrawTreasureLevel(ACTIVITY_ID, currentDate, round);
        int currentNum = activityCommonRedis.getCommonZSetRankingScore(drawTreasureLevelKey, uid);
        while (value > 0){
            List<Integer> tempLevelNumList = new ArrayList<>(DRAW_LEVEL_LIST);
            int currentLevelIndex;
            if(tempLevelNumList.contains(currentNum)){
                currentLevelIndex = tempLevelNumList.indexOf(currentNum);
            }else {
                tempLevelNumList.add(currentNum);
                tempLevelNumList.sort(Integer::compare);
                currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
            }

            int upLevelIndex = currentLevelIndex + 1;
            if(upLevelIndex >= DRAW_LEVEL_LIST.size()){
                activityCommonRedis.incrCommonZSetRankingScore(drawTreasureLevelKey, uid, value);
                value = 0;
            }else {
                int upLevelNum = DRAW_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                if(value >= needUpNum){                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                    currentNum = currentNum + needUpNum;
                    value  = value - needUpNum;
                    activityCommonRedis.incrCommonZSetRankingScore(drawTreasureLevelKey, uid, needUpNum);
                    String resKey = DRAW_LEVEL_KEY_LIST.get(upLevelIndex);
                    resourceKeyHandlerService.sendResourceData(uid, resKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                }else {
                    activityCommonRedis.incrCommonZSetRankingScore(drawTreasureLevelKey, uid, value);
                    value = 0;
                }
            }
        }
    }

    public void rechargeDigTreasureRefresh(String activityId, String uid) {
        checkActivityTime(activityId);
        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userMapData = activityCommonRedis.getCommonHashAll(hashActivityId);
            int balance = userMapData.getOrDefault(DIG_TREASURE_BALANCE, 0);
            if(balance < DIG_TREASURE_FLUSH_NUM){
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            activityCommonRedis.incCommonHashNum(hashActivityId, DIG_TREASURE_BALANCE, -DIG_TREASURE_FLUSH_NUM);

            String currentDate = DateHelper.ARABIAN.formatDateInDay();
            String dailyDrawHashKey = getDailyDrawHashKey(activityId, uid, currentDate);
            int afterRound = activityCommonRedis.incCommonHashNum(dailyDrawHashKey, DIG_TREASURE_CURRENT_ROUND, 1);
            activityCommonRedis.setCommonHashNum(dailyDrawHashKey, DIG_TREASURE_ROUND_TIMES, 0);

            String recordActivityKey = getRecordActivityKey(activityId, uid);
            activityCommonRedis.addCommonListRecord(recordActivityKey, JSONObject.toJSONString(new DiTreasureRecordBO(DIG_TREASURE_FLUSH_NUM, 1, DateHelper.getNowSeconds(), Collections.emptyList())));

            this.doReportEvent(uid, DIG_TREASURE_FLUSH_NUM, 2,1, afterRound, null, Collections.emptyList());
        }
    }



    private List<ResourceKeyConfigData.ResourceMeta> getResourceMetaList(List<String> keyList){
        List<ResourceKeyConfigData> keyConfigList = resourceKeyConfigDao.findListByKeys(new HashSet<>(keyList));
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = new ArrayList<>();
        for (ResourceKeyConfigData keyConfigData: keyConfigList) {
            resourceMetaList.addAll(keyConfigData.getResourceMetaList());
        }
        return resourceMetaList;
    }


    public DiTreasureVO rechargeDigTreasureRecord(String activityId, String uid, int page) {

        DiTreasureVO vo = new DiTreasureVO();
        List<DiTreasureVO.RecordVO> recordList = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        String recordListKey = getRecordActivityKey(activityId, uid);
        List<String> recordKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = getResourceMetaList(POOL_KEY_LIST).stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));

        for (String recordData : recordKeyTimeList) {
            DiTreasureRecordBO recordBO = JSONObject.parseObject(recordData, DiTreasureRecordBO.class);
            DiTreasureVO.RecordVO recordVO = new DiTreasureVO.RecordVO();

            recordVO.setConsumer(recordBO.getConsumer());
            recordVO.setUseWay(recordBO.getUseWay());
            recordVO.setCtime(recordBO.getCtime());
            List<DiTreasureVO.DiResourceVO> drawAwardList = new ArrayList<>();
            for (String drawKey: recordBO.getDrawKeyList()) {
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(drawKey);
                if(resourceMeta == null){
                    continue;
                }

                DiTreasureVO.DiResourceVO diResourceVO = new DiTreasureVO.DiResourceVO();
                BeanUtils.copyProperties(resourceMeta, diResourceVO);
                drawAwardList.add(diResourceVO);
            }
            recordVO.setDrawAwardList(drawAwardList);
            recordList.add(recordVO);
        }
        vo.setRecordList(recordList);
        if (recordList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    @Override
    public void process(RechargeInfo rechargeInfo) {

        int rechargeDiamond = rechargeInfo.getRechargeDiamond();
        String uid = rechargeInfo.getUid();

        int rechargeType  = rechargeInfo.getRechargeType();
        if(rechargeType != 1){
            logger.info("not honor recharge return rechargeInfo:{}", rechargeInfo);
            return;
        }

        // if (!whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)){
        //     return;
        // }

        if(!inActivityTime(ACTIVITY_ID)){
            return;
        }

        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(ACTIVITY_ID, uid);
            Map<String, Integer> userMapData = activityCommonRedis.getCommonHashAll(hashActivityId);

            int balance = userMapData.getOrDefault(DIG_TREASURE_BALANCE, 0);
            int leftNum = userMapData.getOrDefault(DIG_TREASURE_LEFT_NUM, 0);
            int afterNum = rechargeDiamond + leftNum;

            int incNum = afterNum / DIG_TREASURE_RATE_BEAN;
            leftNum = afterNum % DIG_TREASURE_RATE_BEAN;
            logger.info("rechargeDig process uid:{} rechargeDiamond:{}, incNum:{}, leftNum:{}", uid, rechargeDiamond, incNum, leftNum);
            int afterBalance = activityCommonRedis.incCommonHashNum(hashActivityId, DIG_TREASURE_BALANCE, incNum);
            activityCommonRedis.setCommonHashNum(hashActivityId, DIG_TREASURE_LEFT_NUM, leftNum);

            if(incNum > 0){
                RechargeDigTreasureShovelRecordEvent event = new RechargeDigTreasureShovelRecordEvent();
                event.setUid(uid);
                event.setCtime(DateHelper.getNowSeconds());
                event.setBefore_shovel_number(balance);
                event.setShovel_number(incNum);
                event.setAfter_shovel_number(afterBalance);
                eventReport.track(new EventDTO(event));
            }
        }
    }
}
