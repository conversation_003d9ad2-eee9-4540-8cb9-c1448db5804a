package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.WomenSupportVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.room.UserCommonPopupMessage;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class WomenSupportService extends OtherActivityService implements DailyTaskHandler {


    private static final Logger logger = LoggerFactory.getLogger(WomenSupportService.class);
    private static final String ACTIVITY_TITLE_EN = "Super Queen";
    private static final String ACTIVITY_TASK_TITLE_EN = "Super Queen-%s queen task reward";
    private static final String ACTIVITY_FINISH_DAILY_TITLE_EN = "Super Queen-%s queen reach reward";
    private static final String ACTIVITY_RANK_TITLE_EN = "Super Queen-%s queen rank reward";
    private static final String ACTIVITY_TITLE_AR = "الملكة الخارقة";
    private static final String ACTIVITY_DESC = "Super Queen Reward";
    private static final String ACTIVITY_ID = "66daccc8a3ba14041bdcef35";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/super_queen/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/super_queen/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/op_1725882434_FJRK.png";
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final List<Integer> QUEEN_LIST = Arrays.asList(1, 2, 3, 4);  // 慷慨、 魅力、热情、派对
    private static final List<String> QUEEN_TITLE_LIST = Arrays.asList("generous", "glamour", "enthusiasm", "party");  // 慷慨、 魅力、热情、派对

    private static final String QUEEN_LEVEL_FORMAT = "queen%sLevel%s";
    private static final String QUEEN_DAILY_REWARD_KEY = "queen%sDailyReward";
    private static final List<String> QUEEN_LEVEL_KEY_LIST = Arrays.asList("queen1Level1", "queen1Level2", "queen1Level3", "queen1Level4", "queen1Level5", "queen1Level6", "queen1Level7", "queen1Level8",
            "queen2Level1", "queen2Level2", "queen2Level3", "queen2Level4", "queen2Level5", "queen2Level6", "queen2Level7", "queen2Level8",
            "queen3Level1", "queen3Level2", "queen3Level3", "queen3Level4", "queen3Level5", "queen3Level6", "queen3Level7", "queen3Level8",
            "queen4Level1", "queen4Level2", "queen4Level3", "queen4Level4", "queen4Level5", "queen4Level6", "queen4Level7", "queen4Level8"
            );
    private static final List<String> QUEEN_1_RES_KEY_LIST = QUEEN_LEVEL_KEY_LIST.subList(0, 8);
    private static final List<String> QUEEN_2_RES_KEY_LIST = QUEEN_LEVEL_KEY_LIST.subList(8, 16);
    private static final List<String> QUEEN_3_RES_KEY_LIST = QUEEN_LEVEL_KEY_LIST.subList(16, 24);
    private static final List<String> QUEEN_4_RES_KEY_LIST = QUEEN_LEVEL_KEY_LIST.subList(24, 32);
    private static final Map<Integer, List<String>> QUEEN_RES_KEY_MAP = new HashMap<>();
    private static final Map<Integer, List<Integer>> QUEEN_LEVEL_POINT_MAP = new HashMap<>();
    private static final Map<Integer, List<String>> QUEEN_TYPE_TASK_ITEM_MAP = new HashMap<>();
    private static final String QUEEN_FINISH_RES_KEY = "queen%sCompleteReward";
    private static final String QUEEN_LEFT_BEAN = "leftBean";

    // 每个子项数据
    private static final String QUEEN_DAILY_TOTAL_POINT = "dailyTotalPoint";   // 每个女王每日获得积分总和
    private static final String QUEEN_SEND_GIFT_KEY = "sendGift";
    private static final String QUEEN_SEND_GIFT_POINT_KEY = "sendGiftPoint";
    private static final String QUEEN_SEND_GIFT_DIAMOND_KEY = "sendGiftDiamond";
    private static final String QUEEN_RECEIVE_GIFT_KEY = "receiveGift";
    private static final String QUEEN_RECEIVE_GIFT_POINT_KEY = "receiveGiftPoint";
    private static final String QUEEN_RECEIVE_GIFT_DIAMOND_KEY = "receiveGiftDiamond";
    private static final String QUEEN_INVITE_USER_KEY = "inviteUser";
    private static final String QUEEN_INVITE_USER_POINT_KEY = "invitePoint";
    private static final String QUEEN_FRIEND_USER_KEY = "friendUser";
    private static final String QUEEN_FRIEND_USER_POINT_KEY = "friendPoint";
    private static final String QUEEN_MEMBER_USER_KEY = "memberUser";
    private static final String QUEEN_MEMBER_USER_POINT_KEY = "memberPoint";
    private static final List<String> QUEEN_1_KEY_LIST = Arrays.asList(QUEEN_SEND_GIFT_KEY, QUEEN_SEND_GIFT_POINT_KEY, QUEEN_SEND_GIFT_DIAMOND_KEY);
    private static final List<String> QUEEN_2_KEY_LIST = Arrays.asList(QUEEN_RECEIVE_GIFT_KEY, QUEEN_RECEIVE_GIFT_POINT_KEY, QUEEN_RECEIVE_GIFT_DIAMOND_KEY);
    private static final List<String> QUEEN_3_KEY_LIST = Arrays.asList(QUEEN_INVITE_USER_KEY, QUEEN_INVITE_USER_POINT_KEY, QUEEN_FRIEND_USER_KEY, QUEEN_FRIEND_USER_POINT_KEY, QUEEN_SEND_GIFT_KEY, QUEEN_SEND_GIFT_POINT_KEY, QUEEN_RECEIVE_GIFT_KEY, QUEEN_RECEIVE_GIFT_POINT_KEY);
    private static final List<String> QUEEN_4_KEY_LIST = Arrays.asList(QUEEN_MEMBER_USER_KEY, QUEEN_MEMBER_USER_POINT_KEY, QUEEN_INVITE_USER_KEY, QUEEN_INVITE_USER_POINT_KEY, QUEEN_SEND_GIFT_KEY, QUEEN_SEND_GIFT_POINT_KEY, QUEEN_SEND_GIFT_DIAMOND_KEY);
    private static final Integer QUEEN_1_2_GIFT_MAX_NUM = 3000;
    private static final Integer QUEEN_4_GIFT_MAX_NUM = 2000;
    private static final Integer QUEEN_3_GIFT_MAX_NUM = 600;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        QUEEN_RES_KEY_MAP.put(1, QUEEN_1_RES_KEY_LIST);
        QUEEN_RES_KEY_MAP.put(2, QUEEN_2_RES_KEY_LIST);
        QUEEN_RES_KEY_MAP.put(3, QUEEN_3_RES_KEY_LIST);
        QUEEN_RES_KEY_MAP.put(4, QUEEN_4_RES_KEY_LIST);

        QUEEN_TYPE_TASK_ITEM_MAP.put(1, QUEEN_1_KEY_LIST);
        QUEEN_TYPE_TASK_ITEM_MAP.put(2, QUEEN_2_KEY_LIST);
        QUEEN_TYPE_TASK_ITEM_MAP.put(3, QUEEN_3_KEY_LIST);
        QUEEN_TYPE_TASK_ITEM_MAP.put(4, QUEEN_4_KEY_LIST);

        QUEEN_LEVEL_POINT_MAP.put(1, Arrays.asList(16, 100, 233, 533, 1333, 2666, 4333, 10999));
        QUEEN_LEVEL_POINT_MAP.put(2, Arrays.asList(16, 100, 233, 533, 1333, 2666, 4333, 10999));
        QUEEN_LEVEL_POINT_MAP.put(3, Arrays.asList(30, 90, 180, 270, 750, 1500, 2250, 4000));
        QUEEN_LEVEL_POINT_MAP.put(4, Arrays.asList(30, 90, 180, 270, 750, 1500, 2250, 4000));
    }

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private EventReport eventReport;
    @Resource
    private WhiteTestDao whiteTestDao;

    private String getHashActivityId(String activityId, String uid){
        return String.format("womenSupport:%s:%s", activityId, uid);
    }

    private String getQueenShowStatusKey(Integer queenType){
        return String.format("queenShow:%s", queenType);
    }

    private String getDailyDate(String activityId){
        return String.format("dailyDate:%s", activityId);
    }

    private String getQueenTypeItemKey(Integer queenType, String item){
        return String.format("%s:%s", queenType, item);
    }

    // 每日hashKey
    private String getDailyHashActivityId(String activityId, String uid, String dateStr){
        return String.format("womenDailySupport:%s:%s:%s", activityId, uid, dateStr);
    }

    // 每种女王设备去重Key
    private String getBecomeQueenTnSetKey(String activityId, int queenType){
        return String.format("becomeQueenTn:%s:%s", activityId, queenType);
    }

    // 每种女王去重Key
    private String getBecomeQueenSetKey(String activityId, int queenType){
        return String.format("becomeQueen:%s:%s", activityId, queenType);
    }

    // 用户同一个操作多次行为去重key
    private String getDailySetActivityId(String activityId, Integer queenType, String itemType, String uid, String dateStr){
        return String.format("womenDailySet:%s:%s:%s:%s:%s", activityId, queenType, itemType, uid, dateStr);
    }

    // 同一个设备多个账号一个操作去重key
    private String getDailySameTnMoreUserSetKey(String activityId, Integer queenType, String itemType, String uid, String dateStr){
        return String.format("womenDailySameTnMoreUserSet:%s:%s:%s:%s:%s", activityId, queenType, itemType, uid, dateStr);
    }

    // 一个设备同一个操作只能对3个不同设备增加积分去重key
    private String getDailyOneTnAddSetKey(String activityId, Integer queenType, String itemType, String tnId, String dateStr){
        return String.format("womenDailyOneTnAddSet:%s:%s:%s:%s:%s", activityId, queenType, itemType, tnId, dateStr);
    }

    // 每个等级节点奖励设备去重
    private String getQueenLevelRewardSetKey(String activityId, int queenType, String resKey){
        return String.format("queenLevelReward:%s:%s:%s", activityId, queenType, resKey);
    }


    // 积分无上限排行榜Key
    private String getQueenPointAllRankKey(String activityId, int queenType){
        return String.format("queenPointAllRank:%s:%s", activityId, queenType);
    }

    // 有积分上限榜单key
    private String getQueenPointRankKey(String activityId, int queenType){
        return String.format("queenPointRank:%s:%s", activityId, queenType);
    }

    // 女王排行榜Key
    private String getQueenHallRankKey(String activityId, int queenType){
        return String.format("queenHallRank:%s:%s", activityId, queenType);
    }

    private Map<String, ResourceKeyConfigData> getResourceDataMap(){
        List<ResourceKeyConfigData> resourceKeyConfigDataList = resourceKeyConfigDao.findListByKeys(new HashSet<>(QUEEN_LEVEL_KEY_LIST));
        return resourceKeyConfigDataList.stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));
    }

    public WomenSupportVO womenSupportConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        WomenSupportVO vo = new WomenSupportVO();

        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setGender(actorData.getFb_gender());
        vo.setName(actorData.getName());
        vo.setUid(uid);
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));


        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        vo.setFirstEntry(userDataMap.getOrDefault("firstEntry", 0));

        if(vo.getGender() == 1){
            vo.setFirstEntry(1);
        }
        if(vo.getFirstEntry() == 0){
            activityCommonRedis.setCommonHashNum(hashActivityId, "firstEntry", 1);
        }

        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        String dailyHashActivityId = getDailyHashActivityId(activityId, uid, dateStr);
        Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        Map<String, ResourceKeyConfigData> resourceKeyConfigDataMap = this.getResourceDataMap();

        List<WomenSupportVO.QueenTask> queenTaskList = new ArrayList<>();
        for (int queenType : QUEEN_LIST) {
            WomenSupportVO.QueenTask queenTask = new WomenSupportVO.QueenTask();
            queenTask.setQueenType(queenType);
            queenTask.setQueenStatus(userDataMap.getOrDefault(this.getQueenShowStatusKey(queenType), 0));

            // 设置已弹窗状态
            if(queenTask.getQueenStatus() == 1){
                activityCommonRedis.setCommonHashNum(hashActivityId, this.getQueenShowStatusKey(queenType), 2);
            }
            queenTask.setDailyTotalPoint(userDailyDataMap.getOrDefault(this.getQueenTypeItemKey(queenType, QUEEN_DAILY_TOTAL_POINT), 0));
            queenTask.setTotalPoint(activityCommonRedis.getCommonZSetRankingScore(this.getQueenPointRankKey(activityId, queenType), uid));

            List<WomenSupportVO.ItemConfig> itemConfigList = new ArrayList<>();
            List<String> itemList = QUEEN_TYPE_TASK_ITEM_MAP.get(queenType);
            for (String item: itemList) {
                WomenSupportVO.ItemConfig itemConfig = new WomenSupportVO.ItemConfig();
                itemConfig.setItemName(item);
                itemConfig.setItemPoint(userDailyDataMap.getOrDefault(this.getQueenTypeItemKey(queenType, item), 0));
                itemConfigList.add(itemConfig);
            }
            queenTask.setItemConfigList(itemConfigList);


            // 设置等级奖励
            List<Integer> pointConfigList = QUEEN_LEVEL_POINT_MAP.getOrDefault(queenType, Collections.emptyList());
            List<WomenSupportVO.TaskPointConfig> taskPointConfigList = new ArrayList<>();
            int index = 1;
            for (Integer pointNum : pointConfigList) {
                String resourceKey = String.format(QUEEN_LEVEL_FORMAT, queenType, index);
                WomenSupportVO.TaskPointConfig taskPointConfig = new WomenSupportVO.TaskPointConfig();
                ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDataMap.get(resourceKey);
                if (resourceKeyConfigData != null){
                    BeanUtils.copyProperties(resourceKeyConfigData, taskPointConfig);;
                }
                taskPointConfig.setPointNum(pointNum);
                taskPointConfigList.add(taskPointConfig);
                index += 1;
            }
            queenTask.setTaskPointConfigList(taskPointConfigList);
            queenTaskList.add(queenTask);
        }
        vo.setQueenTaskList(queenTaskList);
        return vo;
    }

    public WomenSupportVO womenSupportPointRank(String activityId, String uid, int queenType) {
        WomenSupportVO vo = new WomenSupportVO();
        String queenRankKey = this.getQueenPointAllRankKey(activityId, queenType);
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        makeOtherRankingData(rankingList, myRank, queenRankKey, uid, 10, true);
        vo.setRankingListVOList(rankingList);
        vo.setMyRankVO(myRank);
        return vo;
    }

    public void womenSupportAddQueen(String activityId, String uid, int queenType) {
        checkActivityTime(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        if(actorData == null || StringUtils.isEmpty(tnId)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "tnId is Empty");
        }

        String becomeQueenSetKey = this.getBecomeQueenSetKey(activityId, queenType);
        if(activityCommonRedis.isCommonSetData(becomeQueenSetKey, uid) > 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "already become this queen");
        }
        String tnSetKey = this.getBecomeQueenTnSetKey(activityId, queenType);
        activityCommonRedis.addCommonSetData(tnSetKey, tnId);
        activityCommonRedis.addCommonSetData(becomeQueenSetKey, uid);
        String hashActivityId = this.getHashActivityId(activityId, uid);
        activityCommonRedis.setCommonHashNum(hashActivityId, this.getQueenShowStatusKey(queenType), 1);
        // activityCommonRedis.addCommonZSetRankingScore(this.getQueenHallRankKey(activityId, queenType), uid, DateHelper.getNowSeconds());
        String queenFinishKey = String.format(QUEEN_FINISH_RES_KEY, queenType);

        String queenName = QUEEN_TITLE_LIST.get(queenType - 1);
        String title = String.format(ACTIVITY_FINISH_DAILY_TITLE_EN, queenName);
        this.sendOneResourceData(activityId, actorData, queenFinishKey, 905, title, ACTIVITY_TITLE_AR, title, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);

    }

    public WomenSupportVO womenSupportHallRank(String activityId, String uid, int queenType, int page) {
        WomenSupportVO vo = new WomenSupportVO();

        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = start + RECORD_PAGE_SIZE;

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        String queenRankKey = this.getQueenHallRankKey(activityId, queenType);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRangeRankingMapByPage(queenRankKey, start, end);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(activityCommonRedis.getCommonZSetRankingScore(this.getQueenPointAllRankKey(activityId, queenType), aid));
            rankingVO.setRankDate(String.valueOf(entry.getValue()));
            rankingVO.setRank(rank);
            rankingVO.setRidData(rankActor.getRidData());

            if (aid.equals(uid)) {
                BeanUtils.copyProperties(rankingVO, myRank);
                myRank.setRidData(rankActor.getRidData());
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if (myRank.getRank() == null || myRank.getRank() == 0) {
            myRank.setName(actorData.getName());
            myRank.setUid(uid);
            myRank.setRidData(actorData.getRidData());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRank.setScore(activityCommonRedis.getCommonZSetRankingScore(this.getQueenPointAllRankKey(activityId, queenType), uid));
            myRank.setRankDate("");
            myRank.setRank(-1);
        }

        vo.setRankingListVOList(rankingList);
        vo.setNextUrl(rankingList.size() >= RECORD_PAGE_SIZE ? page + 1 : -1);
        vo.setMyRankVO(myRank);
        return vo;
    }

    public void sendLevelResourceData(ActorData actorData, String resKey, int queenType) {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resKey);
        if(resourceKeyConfigData == null){
            logger.error("sendOneResourceData not find resKey={}", resKey);
            return;
        }

        String uid = actorData.getUid();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceKeyConfigData.getResourceMetaList()) {
            String queenName = QUEEN_TITLE_LIST.get(queenType - 1);
            String title = String.format(ACTIVITY_TASK_TITLE_EN, queenName);
            resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, title, ACTIVITY_TITLE_AR, title, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
            UserCommonPopupMessage userMsg = new UserCommonPopupMessage();
            userMsg.setUid(uid);
            userMsg.setIcon(resourceMeta.getResourceIcon());
            userMsg.setTitleEn(ACTIVITY_TITLE_EN);
            userMsg.setTitleAr(ACTIVITY_TITLE_AR);
            userMsg.setTextEn("You have received a reward, go check it out now!");
            userMsg.setTextAr("كنت قد حصلت على مكافآت سريعة للتحقق !");
            userMsg.setActionType(0);
            userMsg.setActionValue(ACTIVITY_URL);
            roomWebSender.sendPlayerWebMsg("", uid, uid, userMsg, false);
        }
    }

    public void sendOneResourceData(String activityId, ActorData actorData, String resKey, int aType, String titleEn, String titleAr, String desc, String actionUrl,
                                    String broadcastIcon, int getWay) {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resKey);
        if(resourceKeyConfigData == null){
            logger.error("sendOneResourceData not find resKey={}", resKey);
            return;
        }

        if (actorData == null) {
            return;
        }
        String uid = actorData.getUid();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        int endTime = activity.getEndTime() + 10 * 86400;
        int currentTime = DateHelper.getNowSeconds();
        if(currentTime > endTime){
            logger.error("sendOneResourceData timeOut uid={}, resKey={}", uid, resKey);
        }
        int leftDays = (endTime - currentTime) / 86400;
        leftDays = leftDays == 0 ? 1 : leftDays;
        for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceKeyConfigData.getResourceMetaList()) {

            if(resourceMeta.getResourceType() != BaseDataResourcesConstant.TYPE_BADGE){
                resourceMeta.setResourceTime(leftDays);
            }
            logger.info("sendResourceData toUid={}, resourceMeta={}", uid, JSONObject.toJSONString(resourceMeta));
            if (resourceMeta.getResourceType() == -1) {  // 其他奖励下发
                return;
            } else if (resourceMeta.getResourceType() == -3) {
                heartRecordDao.changeHeart(uid, resourceMeta.getResourceNumber(), titleEn, desc);
            } else if (resourceMeta.getResourceType() == -2) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(uid);
                moneyDetailReq.setAtype(aType);
                moneyDetailReq.setChanged(resourceMeta.getResourceNumber());
                moneyDetailReq.setTitle(titleEn);
                moneyDetailReq.setDesc(desc);
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            } else {
                ResourcesDTO resourcesDTO = new ResourcesDTO();
                resourcesDTO.setUid(uid);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                    resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
                }
                resourcesDTO.setResId(String.valueOf(resourceMeta.getResourceId()));
                resourcesDTO.setResType(resourceMeta.getResourceType());
                resourcesDTO.setItemsSourceDetail(desc);
                resourcesDTO.setDesc(desc);
                if (resourceMeta.getResourceType() == BaseDataResourcesConstant.TYPE_HONOR_TITLE) {
                    resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
                } else {
                    resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
                }
                resourcesDTO.setOfficialMsg(0);
                resourcesDTO.setmTime(DateHelper.getNowSeconds());
                resourcesDTO.setNum(resourceMeta.getResourceNumber() != 0 ? resourceMeta.getResourceNumber() : 1);
                resourcesDTO.setDays(resourceMeta.getResourceTime() != 0 ? resourceMeta.getResourceTime() : -1);
                resourcesDTO.setGetWay(getWay != 0 ? getWay : 1);
                mqSenderService.asyncHandleResources(resourcesDTO);
            }
            resourceKeyHandlerService.pushBroadcastScreenMsg(actorData, resourceMeta, actionUrl, titleEn, titleAr, broadcastIcon);
        }
    }

    private void doLevelReportEvent(String activityId, String uid, int stage, int index, int rewardFlag) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        String stageStr = String.format("%s0%s", stage, index+1);
        event.setUid(uid);
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_stage(Integer.valueOf(stageStr));
        event.setIs_rewards(rewardFlag);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void doScoreReportEvent(String uid, int changed, int queenType, String changedDesc) {
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(20);
        event.setScore_changed_detail(queenType);
        event.setScore_changed_desc(changedDesc);
        eventReport.track(new EventDTO(event));
    }

    public void incTotalPoint(String activityId, String uid, int queenType, int incPoint) {
        synchronized (stringPool.intern("incTotalPoint" + uid)) {
            String queenPointRankKey = this.getQueenPointRankKey(activityId, queenType);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(queenPointRankKey, uid);
            List<String> queenResKeyList = QUEEN_RES_KEY_MAP.get(queenType);
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            String tnId = actorData.getTn_id();
            if(StringUtils.isEmpty(tnId)){
                logger.error("incTotalPoint tnId empty uid:{} queenType:{}", uid, queenType);
                return;
            }

            while (incPoint > 0){
                List<Integer> queenLevelList = QUEEN_LEVEL_POINT_MAP.get(queenType);
                List<Integer> tempLevelNumList = new ArrayList<>(queenLevelList);
                int currentLevelIndex = 0;
                if(tempLevelNumList.contains(currentNum)){
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                }else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if(upLevelIndex >= queenLevelList.size()){
                    activityCommonRedis.incrCommonZSetRankingScore(queenPointRankKey, uid, incPoint);
                    incPoint = 0;
                }else {
                    int upLevelNum = queenLevelList.get(upLevelIndex);           // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if(incPoint >= needUpNum){                                   // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        incPoint  = incPoint - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(queenPointRankKey, uid, needUpNum);

                        // 每个节点设备只下发一次
                        String resKey = queenResKeyList.get(upLevelIndex);
                        String queenLevelRewardSetKey  = this.getQueenLevelRewardSetKey(activityId, queenType, resKey);
                        int queenLevelRewardTnFlag = activityCommonRedis.isCommonSetData(queenLevelRewardSetKey, tnId);
                        int rewardFlag = 0;
                        if(queenLevelRewardTnFlag <= 0){
                            activityCommonRedis.addCommonSetData(queenLevelRewardSetKey, tnId);
                            this.sendLevelResourceData(actorData, resKey, queenType);
                            rewardFlag = 1;
                        }
                        this.doLevelReportEvent(activityId, uid, queenType, upLevelIndex, rewardFlag);

                        // 如果是最高索引就成为身份
                        logger.info("incTotalPoint uid:{}, upLevel:{} queenLevelList-size:{}", uid,upLevelIndex, queenLevelList.size());
                        if (upLevelIndex >= queenLevelList.size() - 1){
                            String becomeQueenSetKey = this.getBecomeQueenSetKey(activityId, queenType);
                            if(activityCommonRedis.isCommonSetData(becomeQueenSetKey, uid) <= 0){
                                String tnSetKey = this.getBecomeQueenTnSetKey(activityId, queenType);
                                int tnFlag = activityCommonRedis.isCommonSetData(tnSetKey, tnId);
                                if(tnFlag <= 0){
                                    activityCommonRedis.addCommonSetData(tnSetKey, tnId);
                                    activityCommonRedis.addCommonSetData(this.getBecomeQueenSetKey(activityId, queenType), uid);
                                    String hashActivityId = this.getHashActivityId(activityId, uid);
                                    activityCommonRedis.setCommonHashNum(hashActivityId, this.getQueenShowStatusKey(queenType), 1);
                                    activityCommonRedis.addCommonZSetRankingScore(this.getQueenHallRankKey(activityId, queenType), uid, DateHelper.getNowSeconds());
                                    String queenFinishKey = String.format(QUEEN_FINISH_RES_KEY, queenType);

                                    String queenName = QUEEN_TITLE_LIST.get(queenType - 1);
                                    String title = String.format(ACTIVITY_FINISH_DAILY_TITLE_EN, queenName);
                                    this.sendOneResourceData(activityId, actorData, queenFinishKey, 905, title, ACTIVITY_TITLE_AR, title, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
                                }
                            }
                        }
                    }else {
                        activityCommonRedis.incrCommonZSetRankingScore(queenPointRankKey, uid, incPoint);
                        incPoint = 0;
                    }
                }
            }
        }
    }

    // 发送或接收礼物增加积分
    private void incSendReceiveGiftQueenPoint(String activityId, String uid, int queenType, int incNumber, int incBeans,
                                              int maxNum, int modNum, int incType, String dateStr){
        synchronized (stringPool.intern("incQueenPoint" + uid)) {
            // 无积分上限增加积分
            activityCommonRedis.incrCommonZSetRankingScore(this.getQueenPointAllRankKey(activityId, queenType), uid, incBeans / modNum);


            String dailyHashActivityId = this.getDailyHashActivityId(activityId, uid, dateStr);
            Map<String, Integer> dailyUserDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            String queenGiftPointKey = incType == 0 ? QUEEN_SEND_GIFT_POINT_KEY : QUEEN_RECEIVE_GIFT_POINT_KEY;
            String queenGiftNumKey = incType == 0 ? QUEEN_SEND_GIFT_KEY : QUEEN_RECEIVE_GIFT_KEY;
            String queenGiftBeanKey = incType == 0 ? QUEEN_SEND_GIFT_DIAMOND_KEY : QUEEN_RECEIVE_GIFT_DIAMOND_KEY;

            int dailyGetPoint = dailyUserDataMap.getOrDefault(this.getQueenTypeItemKey(queenType, queenGiftPointKey), 0);
            if(dailyGetPoint >= maxNum){
                return;
            }

            String leftBeanKey = this.getQueenTypeItemKey(queenType, QUEEN_LEFT_BEAN);
            int queenLeftBean = dailyUserDataMap.getOrDefault(leftBeanKey, 0);
            incBeans += queenLeftBean;
            int incPoint = incBeans / modNum;
            int afterQueenLeftBean = incBeans % modNum;

            int leftIncPoint = maxNum - dailyGetPoint;
            incPoint = Math.min(incPoint, leftIncPoint);

            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, queenGiftPointKey), incPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, QUEEN_DAILY_TOTAL_POINT), incPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, queenGiftNumKey), incNumber);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, queenGiftBeanKey), incBeans);
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, leftBeanKey, afterQueenLeftBean);
            this.incTotalPoint(activityId, uid, queenType, incPoint);
            this.doScoreReportEvent(uid, incPoint, queenType, queenGiftNumKey);
        }
    }

    // 发送给新用户或接收新用户礼物增加积分
    private void incSendReceiveGiftNewUserQueenPoint(String activityId, String uid, int queenType, int incNumber, int maxNum, int incType, String dateStr){
        synchronized (stringPool.intern("incQueenPoint" + uid)) {

            // 无积分上限增加积分
            activityCommonRedis.incrCommonZSetRankingScore(this.getQueenPointAllRankKey(activityId, queenType), uid, incNumber);

            String dailyHashActivityId = this.getDailyHashActivityId(activityId, uid, dateStr);
            Map<String, Integer> dailyUserDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            String queenGiftPointKey = incType == 0 ? QUEEN_SEND_GIFT_POINT_KEY : QUEEN_RECEIVE_GIFT_POINT_KEY;
            String queenGiftNumKey = incType == 0 ? QUEEN_SEND_GIFT_KEY : QUEEN_RECEIVE_GIFT_KEY;
            int dailyGetPoint = dailyUserDataMap.getOrDefault(this.getQueenTypeItemKey(queenType, queenGiftPointKey), 0);
            if(dailyGetPoint >= maxNum){
                return;
            }
            int leftIncPoint = maxNum - dailyGetPoint;
            int incPoint = Math.min(incNumber, leftIncPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, queenGiftPointKey), incPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, QUEEN_DAILY_TOTAL_POINT), incPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, queenGiftNumKey), incPoint);
            this.incTotalPoint(activityId, uid, queenType, incPoint);
            this.doScoreReportEvent(uid, incPoint, queenType, queenGiftNumKey);
        }
    }


    public void handleGiftMqMsg(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();

        if (whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
            // 屏蔽白名单
            return;
        }


        String roomId = giftData.getRoomId();
        Set<String> aidList = giftData.getAid_list();
        String dateStr = DateHelper.ARABIAN.formatDateInDay();

        ActorData actorData = actorDao.getActorDataFromCache(fromUid);
        String sendTnId = actorData.getTn_id();
        boolean sendNewUser = ActorUtils.isNewRegisterActor(fromUid, 7);
        int incNumber = giftData.getNumber();
        int incBean = incNumber * giftData.getPrice();

        // queen4 增加房间发送礼物获得积分;
        if (!StringUtils.isEmpty(roomId)){
            String hostUid = RoomUtils.getRoomHostId(roomId);
            ActorData hostActorData = actorDao.getActorDataFromCache(hostUid);
            int totalNum = giftData.getNumber() * giftData.getAid_list().size();
            int totalBean = incNumber * giftData.getPrice();
            if(hostActorData.getFb_gender() == 2){
                this.incSendReceiveGiftQueenPoint(activityId, hostUid, QUEEN_LIST.get(3), totalNum, totalBean, QUEEN_4_GIFT_MAX_NUM, 3, 0, dateStr);
            }
        }

        for (String aid : aidList) {

            ActorData aidActorData = actorDao.getActorDataFromCache(aid);
            String receiveTnId = aidActorData.getTn_id();
            if(sendTnId.equals(receiveTnId)){
                return;
            }

            if(actorData.getFb_gender() == 2){
                // queen1 增加发送礼物获得积分
                this.incSendReceiveGiftQueenPoint(activityId, fromUid, QUEEN_LIST.get(0), incNumber, incBean, QUEEN_1_2_GIFT_MAX_NUM, 3, 0, dateStr);
            }

            if(aidActorData.getFb_gender() == 2){
                // queen2 增加接收礼物获得积分
                this.incSendReceiveGiftQueenPoint(activityId, aid, QUEEN_LIST.get(1), incNumber, incBean, QUEEN_1_2_GIFT_MAX_NUM, 3, 1, dateStr);

                // queen3 接收7天新用户给的礼物
                if(sendNewUser){
                    this.incSendReceiveGiftNewUserQueenPoint(activityId, aid, QUEEN_LIST.get(2), incNumber, QUEEN_3_GIFT_MAX_NUM, 1, dateStr);
                }
            }

            // queen3 发送给7天新用户
            boolean aidNewUser = ActorUtils.isNewRegisterActor(aid, 7);
            if(aidNewUser && actorData.getFb_gender() == 2){
                this.incSendReceiveGiftNewUserQueenPoint(activityId, fromUid, QUEEN_LIST.get(2), incNumber, QUEEN_3_GIFT_MAX_NUM, 0, dateStr);
            }
        }
    }

    // 统一邀请上麦、房间会员、加好友加积分方法
    private void incActionUserQueenPoint(String activityId, String uid, String aid, int queenType, String actionType, int incPoint, int maxNum, String dateStr){
        logger.info("incMemberUserQueenPoint uid:{}, aid:{}", uid, aid);
        synchronized (stringPool.intern("incQueenPoint" + uid)) {


            ActorData actorData = actorDao.getActorDataFromCache(uid);
            ActorData aidActorData = actorDao.getActorDataFromCache(aid);
            String uidTnId = actorData.getTn_id();
            String aidTnId = aidActorData.getTn_id();

            // 1、同一个设备相互行为不加积分
            if(uidTnId.equals(aidTnId) || actorData.getFb_gender() != 2){
                logger.info("incMemberUserQueenPoint 同一个设备相互行为不加积分 uid:{}, uidTnId:{}, aid:{} aidTnId:{} actionType:{}, queenType:{} incPoint:{}, dateStr:{}", uid, uidTnId, aid, aidTnId, actionType, queenType, incPoint, dateStr);
                return;
            }

            // 2、 账号多次行为去重 -> 不加积分
            String dailySetKey = this.getDailySetActivityId(activityId, queenType, actionType, uid, dateStr);
            int setFlag = activityCommonRedis.isCommonSetData(dailySetKey, aid);
            if(setFlag > 0){
                logger.info("incMemberUserQueenPoint 账号多次行为去重 uid:{}, uidTnId:{}, aid:{} aidTnId:{} actionType:{}, queenType:{} incPoint:{}, dateStr:{}", uid, uidTnId, aid, aidTnId, actionType, queenType, incPoint, dateStr);
                return;
            }

            // 3、同一个设备多个账号对其他设备用户一个操作去重key -> 不加积分
            String dailySameTnMoreUserKey = this.getDailySameTnMoreUserSetKey(activityId, queenType, actionType, uid, dateStr);
            int sameTnFlag = activityCommonRedis.isCommonSetData(dailySameTnMoreUserKey, aidTnId);
            if(sameTnFlag > 0){
                logger.info("incMemberUserQueenPoint 同一个设备多个账号对其他设备用户一个操作去重 uid:{}, uidTnId:{}, aid:{} aidTnId:{} actionType:{}, queenType:{} incPoint:{}, dateStr:{}", uid, uidTnId, aid, aidTnId, actionType, queenType, incPoint, dateStr);
                return;
            }

            // 4、一个设备同一个操作只能对3个不同设备增加积分
            String dailyOneTnAddSetKey = this.getDailyOneTnAddSetKey(activityId, queenType, actionType, aidTnId, dateStr);
            int oneTnAddSetNum = activityCommonRedis.getCommonSetNum(dailyOneTnAddSetKey);
            if(oneTnAddSetNum >= 3){
                logger.info("incMemberUserQueenPoint 一个设备同一个操作只能对3个不同设备增加积分 uid:{}, uidTnId:{}, aid:{} aidTnId:{} actionType:{}, queenType:{} incPoint:{}, dateStr:{}", uid, uidTnId, aid, aidTnId, actionType, queenType, incPoint, dateStr);
                return;
            }

            // 无积分上限增加积分
            activityCommonRedis.incrCommonZSetRankingScore(this.getQueenPointAllRankKey(activityId, queenType), uid, incPoint);

            String userPointKey = null;
            String userNumKey = null;
            if (QUEEN_INVITE_USER_KEY.equals(actionType)){
                userPointKey = QUEEN_INVITE_USER_POINT_KEY;
                userNumKey = QUEEN_INVITE_USER_KEY;
            } else if (QUEEN_FRIEND_USER_KEY.equals(actionType)) {
                userPointKey = QUEEN_FRIEND_USER_POINT_KEY;
                userNumKey = QUEEN_FRIEND_USER_KEY;
            } else if (QUEEN_MEMBER_USER_KEY.equals(actionType)) {
                userPointKey = QUEEN_MEMBER_USER_POINT_KEY;
                userNumKey = QUEEN_MEMBER_USER_KEY;
            }else {
                logger.info("incMemberUserQueenPoint not find actionType:{}", actionType);
                return;
            }


            String dailyHashActivityId = this.getDailyHashActivityId(activityId, uid, dateStr);
            Map<String, Integer> dailyUserDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int dailyGetPoint = dailyUserDataMap.getOrDefault(this.getQueenTypeItemKey(queenType, userPointKey), 0);
            if(dailyGetPoint >= maxNum){
                return;
            }

            int leftIncPoint = maxNum - dailyGetPoint;
            incPoint = Math.min(incPoint, leftIncPoint);

            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, userPointKey), incPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, QUEEN_DAILY_TOTAL_POINT), incPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, this.getQueenTypeItemKey(queenType, userNumKey), 1);
            activityCommonRedis.addCommonSetData(dailySetKey, aid);
            activityCommonRedis.addCommonSetData(dailySameTnMoreUserKey, aidTnId);
            activityCommonRedis.addCommonSetData(dailyOneTnAddSetKey, uidTnId);
            this.incTotalPoint(activityId, uid, queenType, incPoint);
            this.doScoreReportEvent(uid, incPoint, queenType, userNumKey);
        }
    }


    public void handleMqMsg(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        if(!inActivityTime(ACTIVITY_ID)){
            return;
        }

        // TODO
        // if (ServerConfig.isProduct() && !whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
        //     // 灰度测试
        //     return;
        // }

        // String dateStr = activityCommonRedis.getCommonStrValue(this.getDailyDate(ACTIVITY_ID));
        String dateStr = DateHelper.ARABIAN.formatDateInDay();

        boolean aidNewUser = false;
        if(!StringUtils.isEmpty(aid)){
            aidNewUser = ActorUtils.isNewRegisterActor(aid, 7);
        }

        if (CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL.equals(data.getItem())) {
            // queen3 邀请新用户有效上麦
            if(aidNewUser){
                this.incActionUserQueenPoint(ACTIVITY_ID, uid, aid, QUEEN_LIST.get(2), QUEEN_INVITE_USER_KEY,5, 25, dateStr);
            }
            // queen4 邀请用户有效上麦
            this.incActionUserQueenPoint(ACTIVITY_ID, uid, aid, QUEEN_LIST.get(3), QUEEN_INVITE_USER_KEY,5, 250, dateStr);

        } else if (CommonMqTaskConstant.ADD_FRIEND.equals(data.getItem())) {
            // queen3 和新用户成为朋友
            if(aidNewUser){
                this.incActionUserQueenPoint(ACTIVITY_ID, uid, aid, QUEEN_LIST.get(2), QUEEN_FRIEND_USER_KEY,10, 100, dateStr);
            }
        } else if (CommonMqTaskConstant.JOIN_ROOM_MEMBER.equals(data.getItem())) {
            // queen4 女性用户个人房间Member每新增1人
            String roomId = data.getRoomId();
            if(StringUtils.isEmpty(roomId)){
                return;
            }
            String hostUid = RoomUtils.getRoomHostId(roomId);
            this.incActionUserQueenPoint(ACTIVITY_ID, hostUid, uid, QUEEN_LIST.get(3), QUEEN_MEMBER_USER_KEY,15, 750, dateStr);
        }

    }

    public void distributionTotalRanking(String activityId) {
        try{

            for (Integer queenType : QUEEN_LIST) {
                String queenPointAllRankKey = this.getQueenPointAllRankKey(activityId, queenType);
                Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(queenPointAllRankKey, 10);
                int rank = 1;
                for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                    String aid = entry.getKey();
                    String resourceKey = null;
                    if(rank < 4){
                        resourceKey = String.format("queenPointRank%s", rank);
                    }else {
                        resourceKey = "queenPointRank4-10";
                    }

                    String queenName = QUEEN_TITLE_LIST.get(queenType - 1);
                    String title = String.format(ACTIVITY_RANK_TITLE_EN, queenName);
                    resourceKeyHandlerService.sendResourceData(aid, resourceKey, title, ACTIVITY_TITLE_AR, title, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                    rank += 1;
                }
            }
        }catch (Exception e){
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }


    @Override
    public void dailyTaskRun(String dateStr) {
        try{
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if(activityData == null){
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }
            logger.info("dailyTaskRun Horse Racing Challenge");

            for (Integer queenType : QUEEN_LIST) {
                String queenDailyResKey= String.format(QUEEN_DAILY_REWARD_KEY, queenType);
                String becomeQueenKey = this.getBecomeQueenSetKey(ACTIVITY_ID, queenType);
                Set<String> allQueenMember = activityCommonRedis.getCommonSetMember(becomeQueenKey);
                for (String queenUid : allQueenMember) {
                    String queenName = QUEEN_TITLE_LIST.get(queenType - 1);
                    String title = String.format(ACTIVITY_FINISH_DAILY_TITLE_EN, queenName);
                    resourceKeyHandlerService.sendResourceData(queenUid, queenDailyResKey, title, ACTIVITY_TITLE_AR, title, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                }
            }
        }catch (Exception e){
            logger.error("distribution Horse Racing Challenge error: {}", e.getMessage(), e);
        }
    }
}
