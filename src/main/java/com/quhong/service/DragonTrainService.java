package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.ShareActivityDTO;
import com.quhong.data.vo.DragonTrainVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.NationalDayV2Data;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DragonTrainService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(DragonTrainService.class);
    private static final String STONE_NUM_KEY = "stoneNum";                // 石头数量
    private static final String LEFT_BEAN_NUM_KEY = "leftBean";            // 剩余钻石数
    private static final String TRAIN_DRAGON_NUM_KEY = "trainNum";         // 驯服次数
    private static final String TEAM_ID_KEY = "teamId";                    // 队伍id-Key
    public static final String ACTIVITY_ID = "68234423730bea29b421988e";
    private static final List<Integer> DRAW_NUM_LIST = Arrays.asList(1, 10, 50);
    private static final String POOL_SIZE_PAY_1_KEY = "DragonTrainingDraw1";    // 黑龙抽奖key
    private static final String POOL_SIZE_PAY_KEY = "DragonTrainingDraw2";      // 金龙付费用户key
    private static final String POOL_SIZE_NO_PAY_KEY = "DragonTrainingDraw3";   // 金龙非付费用户key
    private static final Integer LIMIT_INIT_POOL = 30;
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final List<Integer> TEAM_SCORE_LEVEL_LIST = Arrays.asList(30, 60, 90);
    private static final List<String> TEAM_LEVEL_KEY_LIST = Arrays.asList("DragonTrainingTeam1", "DragonTrainingTeam2", "DragonTrainingTeam3");
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private EventReport eventReport;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;


    // 抽奖相关的每日key
    private String getDailyDate(String activityId){
        return String.format("dailyDate:%s", activityId);
    }

    // 个人数据信息key
    private String getHashActivityId(String activityId, String uid){
        return String.format("dragonTrain:%s:%s", activityId, uid);
    }

    // 团队成员key
    private String getListTeamMemberKey(String activityId, String teamId){
        return String.format("teamMember:%s:%s", activityId, teamId);
    }

    // 团队榜单key
    private String getZSetTeamRankKey(String activityId){
        return String.format("teamRank:%s", activityId);
    }

    // 抽奖品Key
    private String getListDrawPrizeKey(String activityId, String resKey){
        return String.format("drawPrize:%s:%s", activityId, resKey);
    }
    // 滚动记录
    private String getListScrollKey(String activityId){
        return String.format("scroll:%s", activityId);
    }

    // 历史记录Key
    private String getListHistoryDrawKey(String activityId, String uid){
        return String.format("historyDraw:%s:%s", activityId, uid);
    }

    // 限制每日分享次数
    private String getShareLimitKey(String activityId, String uid, String aid){
        return String.format("shareLimit:%s:%s:%s", activityId, uid, aid);
    }


    private int getTrainTotalScore(int score) {
        if (score < 100) {
            return 100;
        }
        return Integer.MAX_VALUE;
    }

    private int getCurrLevelScore(int score) {
        if (score < 100) {
            return 1;
        }
        return 2;
    }

    public DragonTrainVO dragonTrainConfig(String activityId, String uid) {
        DragonTrainVO vo = new DragonTrainVO();
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        vo.setStoneNum(Integer.valueOf(userDataMap.getOrDefault(STONE_NUM_KEY, "0")));

        vo.setTrainNum(Integer.valueOf(userDataMap.getOrDefault(TRAIN_DRAGON_NUM_KEY, "0")));
        vo.setDragonLevel(getCurrLevelScore(vo.getTrainNum()));
        vo.setTrainTotalNum(getTrainTotalScore(vo.getTrainNum()));

        String teamId = userDataMap.get(TEAM_ID_KEY);
        DragonTrainVO.DragonTeamConfig teamInfoVO = new DragonTrainVO.DragonTeamConfig();
        if (ObjectUtils.isEmpty(teamId)){
            // 未组队成功则显示只有自己在队伍中
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            teamInfoVO.setTeamName(actorData.getName());
            teamInfoVO.setTeamLeadUid(uid);
            List<DragonTrainVO.TeamInfo> teamMemberList = new ArrayList<>();
            DragonTrainVO.TeamInfo teamInfo = new DragonTrainVO.TeamInfo();
            teamInfo.setUid(uid);
            teamInfo.setName(actorData.getName());
            teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            teamMemberList.add(teamInfo);
            teamInfoVO.setTeamMemberList(teamMemberList);
        }else {
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 3);
            List<DragonTrainVO.TeamInfo> teamMemberList = new ArrayList<>();
            teamInfoVO.setTeamId(teamId);
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0){
                    teamInfoVO.setTeamName(actorData.getName());
                    teamInfoVO.setTeamLeadUid(memberUid);
                }
                DragonTrainVO.TeamInfo teamInfo = new DragonTrainVO.TeamInfo();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamMemberList.add(teamInfo);
            }
            teamInfoVO.setTeamMemberList(teamMemberList);
        }
        vo.setTeamInfo(teamInfoVO);

        // 设置抽奖roll
        List<String> drawRecordList = activityCommonRedis.getCommonListRecord(getListScrollKey(activityId));
        List<DragonTrainVO.RollRecordData> rollRecordList = new ArrayList<>();
        for (String record : drawRecordList) {
            DragonTrainVO.RollRecordData rollRecordData = JSONObject.parseObject(record, DragonTrainVO.RollRecordData.class);
            ActorData actorData = actorDao.getActorDataFromCache(rollRecordData.getUid());
            rollRecordData.setName(actorData.getName());
            rollRecordData.setHead(actorData.getHead());
            rollRecordList.add(rollRecordData);
        }
        vo.setRollRecordList(rollRecordList);
        return vo;
    }

    public DragonTrainVO dragonTrainTeamRank(String activityId, String uid) {
        DragonTrainVO vo = new DragonTrainVO();
        // 设置榜单数据
        List<DragonTrainVO.DragonTeamConfig> teamRankList = new ArrayList<>();
        Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId), 10);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
            String rankTeamId = entry.getKey();
            List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, rankTeamId), 3);
            DragonTrainVO.DragonTeamConfig dragonTeamConfig = new DragonTrainVO.DragonTeamConfig();
            List<DragonTrainVO.TeamInfo> teamMemberList = new ArrayList<>();
            for (int i = 0; i < teamMemberUidList.size(); i++) {
                String memberUid = teamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                if (i == 0){
                    dragonTeamConfig.setTeamName(actorData.getName());
                    dragonTeamConfig.setTeamLeadUid(memberUid);
                }
                DragonTrainVO.TeamInfo teamInfo = new DragonTrainVO.TeamInfo();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamMemberList.add(teamInfo);
            }
            dragonTeamConfig.setTeamId(rankTeamId);
            dragonTeamConfig.setTeamScore(entry.getValue());
            dragonTeamConfig.setTeamMemberList(teamMemberList);
            dragonTeamConfig.setRank(rank);
            teamRankList.add(dragonTeamConfig);
            rank += 1;
        }
        vo.setTeamRankList(teamRankList);

        // 设置我的队伍数据
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
        String myTeamId = userDataMap.get(TEAM_ID_KEY);
        if (!ObjectUtils.isEmpty(myTeamId)){
            DragonTrainVO.DragonTeamConfig myTeamConfig = new DragonTrainVO.DragonTeamConfig();
            List<String> myTeamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, myTeamId), 3);
            List<DragonTrainVO.TeamInfo> teamMemberList = new ArrayList<>();
            for (int i = 0; i < myTeamMemberUidList.size(); i++) {
                String memberUid = myTeamMemberUidList.get(i);
                ActorData actorData = actorDao.getActorDataFromCache(memberUid);
                String memberActivityId = getHashActivityId(activityId, memberUid);
                Map<String, String> memberDataMap = memberUid.equals(uid) ? userDataMap : activityCommonRedis.getCommonHashAllMapStr(memberActivityId);
                int trainNum = Integer.parseInt(memberDataMap.getOrDefault(TRAIN_DRAGON_NUM_KEY, "0"));
                if (i == 0){
                    myTeamConfig.setTeamName(actorData.getName());
                    myTeamConfig.setTeamLeadUid(memberUid);
                }
                DragonTrainVO.TeamInfo teamInfo = new DragonTrainVO.TeamInfo();
                teamInfo.setUid(memberUid);
                teamInfo.setName(actorData.getName());
                teamInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                teamInfo.setScore(trainNum);
                teamMemberList.add(teamInfo);
            }
            myTeamConfig.setTeamId(myTeamId);
            int myTeamScore = activityCommonRedis.getCommonZSetRankingScore(getZSetTeamRankKey(activityId), myTeamId);
            myTeamConfig.setTeamScore(myTeamScore);
            myTeamConfig.setTeamMemberList(teamMemberList);

            int myRank = activityCommonRedis.getCommonZSetRank(getZSetTeamRankKey(activityId), myTeamId);
            myTeamConfig.setRank(myRank);

            // 处理距离上一名分差
            int muchScore = 0;
            int lastRank = myRank - 1;
            if (lastRank > 0){
                // 只获取上一名的数据
                Map<String, Integer> lastRankMap = activityCommonRedis.getCommonRankingMapByPage(getZSetTeamRankKey(activityId), lastRank - 1, lastRank);
                if (!CollectionUtils.isEmpty(lastRankMap)){
                    // 只取第一个元素，即上一名的分数
                    for (Map.Entry<String, Integer> entry : lastRankMap.entrySet()) {
                        int lastScore = entry.getValue();
                        logger.info("dragonTrainTeamRank lastRankMap rankTeamId:{} lastScore:{} myTeamScore:{}", entry.getKey(), lastScore, myTeamScore);
                        // 计算分差：上一名分数 - 我的分数
                        muchScore = lastScore - myTeamScore;
                        break; // 只处理第一个元素
                    }
                }
            }
            myTeamConfig.setMuchScore(muchScore);
            vo.setTeamInfo(myTeamConfig);
        }
        return vo;
    }

    /**
     * 驯龙礼物发送处理
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        synchronized (stringPool.intern("dragonTrainStone" + fromUid)) {
            int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            String hashActivityId = getHashActivityId(activityId, fromUid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            int leftBean = Integer.parseInt(userDataMap.getOrDefault(LEFT_BEAN_NUM_KEY, "0"));
            totalBeans += leftBean;
            int incNum = totalBeans / 100;
            leftBean = totalBeans % 100;
            if (incNum > 0){
                activityCommonRedis.incCommonHashNum(hashActivityId, STONE_NUM_KEY, incNum);
                doReportSpecialItemsEvent(activityId, fromUid, 1, incNum, 1);
            }
            activityCommonRedis.setCommonHashNum(hashActivityId, LEFT_BEAN_NUM_KEY, leftBean);
        }
    }

    /**
     * 驯龙
     * @param activityId: 活动id
     * @param uid: 用户uid
     * @param amount: 次数
     * @return vo
     */

    public DragonTrainVO dragonTrainDraw(String activityId, String uid, int amount) {
        checkActivityTime(activityId);
        if(!DRAW_NUM_LIST.contains(amount)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        DragonTrainVO vo = new DragonTrainVO();
        synchronized (stringPool.intern("dragonTrainStone" + uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            int stoneNum = Integer.parseInt(userDataMap.getOrDefault(STONE_NUM_KEY, "0"));
            if (stoneNum <= 0 || stoneNum < amount) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            int trainDragonNum = Integer.parseInt(userDataMap.getOrDefault(TRAIN_DRAGON_NUM_KEY, "0"));

            // 扣抽奖次数
            activityCommonRedis.incCommonHashNum(hashActivityId, STONE_NUM_KEY, -amount);
            doReportSpecialItemsEvent(activityId, uid, 2, amount, 2);
            // 增加驯服次数
            activityCommonRedis.incCommonHashNum(hashActivityId, TRAIN_DRAGON_NUM_KEY, amount);
            // 增加团队驯服次数
            String myTeamId = userDataMap.get(TEAM_ID_KEY);
            if (!ObjectUtils.isEmpty(myTeamId)){
                this.incTeamTrainScore(activityId, myTeamId, amount);
            }

            // 最近30天充值金额
            int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
            int currentTime = DateHelper.getNowSeconds();
            List<DragonTrainVO.RollRecordData> drawRecordList = new ArrayList<>();
            for (int i = 0; i < amount; i++) {
                trainDragonNum += 1;
                int currentLevel = getCurrLevelScore(trainDragonNum);
                ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig(rechargeMoney, currentLevel);

                if(resourceKeyConfigData == null){
                    throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
                }
                String resKey = resourceKeyConfigData.getKey();
                String rewardTitle = this.getResTitleByKey(resKey);
                Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));


                String drawKey = this.dragonTrainDrawSize(activityId, resourceMetaMap, resKey);
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(drawKey);
                if(resourceMeta == null){
                    continue;
                }
                resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, rewardTitle, rewardTitle, rewardTitle, "", "", 0);
                DragonTrainVO.RollRecordData drawRecord = new DragonTrainVO.RollRecordData();
                BeanUtils.copyProperties(resourceMeta, drawRecord);
                drawRecord.setUid(uid);
                drawRecord.setCtime(currentTime);
                drawRecordList.add(drawRecord);

                String jsonRecord = JSON.toJSONString(drawRecord);
                activityCommonRedis.addCommonListRecord(getListScrollKey(activityId), jsonRecord);
                activityCommonRedis.addCommonListData(getListHistoryDrawKey(activityId, uid), jsonRecord);
                doDrawPrizeRecordEvent(uid, getSenceDetail(resKey), 1, resourceMeta);
            }
            vo.setRecordList(drawRecordList);
        }
        return vo;
    }

    /**
     * 增加团队分数
     */
    private void incTeamTrainScore(String activityId, String teamId, int value){
        synchronized (stringPool.intern("incTeamTrainScore" + teamId)) {
            String teamRankKey = getZSetTeamRankKey(activityId);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(teamRankKey, teamId);
            while (value > 0){
                List<Integer> tempLevelNumList = new ArrayList<>(TEAM_SCORE_LEVEL_LIST);
                int currentLevelIndex = 0;
                if(tempLevelNumList.contains(currentNum)){
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                }else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if(upLevelIndex >= TEAM_SCORE_LEVEL_LIST.size()){
                    activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, value);
                    value = 0;
                }else {
                    int upLevelNum = TEAM_SCORE_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if(value >= needUpNum){                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        value  = value - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, needUpNum);
                        String resKey = TEAM_LEVEL_KEY_LIST.get(upLevelIndex);
                        List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 3);
                        for (String memberUid : teamMemberUidList) {
                            resourceKeyHandlerService.sendResourceData(memberUid, resKey, "Dragon Training-TeamTaskreward", "Dragon Training-TeamTaskreward");
                        }
                    }else {
                        activityCommonRedis.incrCommonZSetRankingScore(teamRankKey, teamId, value);
                        value = 0;
                    }
                }
            }
        }
    }


    /**
     * 获取抽奖配置
     */
    private ResourceKeyConfigData getResourceKeyConfig(int rechargeMoney, int level){
        if (level <= 1){
            return resourceKeyHandlerService.getConfigData(POOL_SIZE_PAY_1_KEY);
        }else {
            return rechargeMoney >= 5 ? resourceKeyHandlerService.getConfigData(POOL_SIZE_PAY_KEY) : resourceKeyHandlerService.getConfigData(POOL_SIZE_NO_PAY_KEY);
        }
    }

    /**
     * 获取中奖title
     */
    private String getResTitleByKey(String resKey){
        if (POOL_SIZE_PAY_1_KEY.equals(resKey)){
            return "Dragon Training-reward1";
        } else if (POOL_SIZE_PAY_KEY.equals(resKey)){
            return "Dragon Training-reward20221";
        }else {
            return "Dragon Training-reward30123";
        }
    }

    /**
     * 获取抽奖SenceDetail
     */
    private int getSenceDetail(String resKey){
        if (POOL_SIZE_PAY_1_KEY.equals(resKey)){
            return 1;
        } else if (POOL_SIZE_PAY_KEY.equals(resKey)){
            return 2;
        }else {
            return 3;
        }
    }

    /**
     * 使用卡片抽奖
     */
    private void initDrawPrizePool(Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String drawPrizeKey){
        int poolSize = activityCommonRedis.getCommonListSize(drawPrizeKey);
        if(poolSize <= LIMIT_INIT_POOL){
            List<String> poolList = new ArrayList<>();
            for(String prizeKey: resourceMetaMap.keySet()){
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(prizeKey);
                int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
            }
            Collections.shuffle(poolList);
            activityCommonRedis.rightPushAllCommonList(drawPrizeKey, poolList);
        }
    }

    /**
     *  抽奖
     */
    private String dragonTrainDrawSize(String activityId, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String resKey){
        String drawPrizeKey = getListDrawPrizeKey(activityId, resKey);
        this.initDrawPrizePool(resourceMetaMap, drawPrizeKey);
        String awardKey = activityCommonRedis.leftPopCommonListKey(drawPrizeKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return awardKey;
    }

    /**
     * 历史记录
     */
    public DragonTrainVO dragonTrainRecord(String activityId, String uid, int page) {
        DragonTrainVO vo = new DragonTrainVO();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        List<String> pageRecordList = activityCommonRedis.getCommonListPageRecord(getListHistoryDrawKey(activityId, uid), start, end);
        List<DragonTrainVO.RollRecordData> recordList = new ArrayList<>();
        for (String record : pageRecordList) {
            DragonTrainVO.RollRecordData rollRecordData = JSONObject.parseObject(record, DragonTrainVO.RollRecordData.class);
            ActorData actorData = actorDao.getActorDataFromCache(rollRecordData.getUid());
            rollRecordData.setName(actorData.getName());
            rollRecordData.setHead(actorData.getHead());
            recordList.add(rollRecordData);
        }
        vo.setRecordList(recordList);
        if (pageRecordList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(0);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    /**
     * 历史记录
     */
    public void dragonTrainJoinTeam(String activityId, String uid, String captainUid) {
        checkActivityTime(activityId);
        synchronized (stringPool.intern("dragonTrainJoinTeam" + captainUid)) {
            if (ObjectUtils.isEmpty(captainUid)){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            if (captainUid.equals(uid)){
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE.getCode(), "لا تستطيع التعاون مع نفسك");
            }

            ActorData captainActorData = actorDao.getActorDataFromCache(captainUid);
            // 队长不存在
            if (captainActorData == null){
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_NOT_DIAMONDS.getCode(), "المستخدم الكابتن الذي تقدمت بطلب للانضمام إليه غير موجود");
            }

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> userDataMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);
            String teamId = userDataMap.get(TEAM_ID_KEY);
            int trainNum = Integer.parseInt(userDataMap.getOrDefault(TRAIN_DRAGON_NUM_KEY, "0"));
            // 判断该用户是否已加入队伍
            if (!ObjectUtils.isEmpty(teamId)){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لقد انضممت إلى فريق آخر");
            }

            String captainHashActivityId = getHashActivityId(activityId, captainUid);
            Map<String, String> captainDataMap = activityCommonRedis.getCommonHashAllMapStr(captainHashActivityId);
            String captainTeamId = captainDataMap.get(TEAM_ID_KEY);
            // 队长没有团队id, 则加入
            if (ObjectUtils.isEmpty(captainTeamId)){
                captainTeamId = new ObjectId().toString();
                List<String> teamMemberList = new ArrayList<>();
                teamMemberList.add(captainUid);
                teamMemberList.add(uid);

                activityCommonRedis.setCommonHashData(captainHashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.rightPushAllCommonList(getListTeamMemberKey(activityId, captainTeamId), teamMemberList);
                // 处理团队分数及奖励
                int captainTrainNum = Integer.parseInt(captainDataMap.getOrDefault(TRAIN_DRAGON_NUM_KEY, "0"));
                int totalScore = trainNum + captainTrainNum;
                this.incTeamTrainScore(activityId, captainTeamId, totalScore);
                doTeamRecordEvent(activityId, uid, captainUid, captainActorData.getName());
                doTeamRecordEvent(activityId, captainUid, captainUid, captainActorData.getName());
                logger.info("dragonTrainJoinTeam1 captainTeamId:{}, captainUid:{}, uid:{}, totalScore:{}", captainTeamId, captainUid, uid, totalScore);
            }else {
                String teamMemberKey = getListTeamMemberKey(activityId, captainTeamId);
                List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(teamMemberKey, 3);
                if (CollectionUtils.isEmpty(teamMemberUidList)){
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي طلبت الانضمام إليه غير موجود");
                }
                if (teamMemberUidList.size() >= 3){
                    throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "الفريق الذي تتقدم بطلب الانضمام إليه ممتلئ");
                }
                String originCaptainUid = teamMemberUidList.get(0);
                teamMemberUidList.add(uid);
                activityCommonRedis.setCommonHashData(hashActivityId, TEAM_ID_KEY, captainTeamId);
                activityCommonRedis.addRightCommonListData(teamMemberKey, uid);
                this.incTeamTrainScore(activityId, captainTeamId, trainNum);
                doTeamRecordEvent(activityId, uid, originCaptainUid, captainActorData.getName());
                logger.info("dragonTrainJoinTeam2 captainTeamId:{}, captainUid:{}, uid:{}", captainTeamId, originCaptainUid, uid);
                if (teamMemberUidList.size() == 3){
                    for (String memberUid : teamMemberUidList) {
                        resourceKeyHandlerService.sendResourceData(memberUid, "DragonTrainingTeam", "Dragon Training-Teamreward", "Dragon Training-Teamreward");
                    }
                }
            }
        }
    }

    /**
     * 下发团队top1奖励
     */
    public void distributionTotalRanking(String activityId) {
        try {
            Map<String, Integer> teamRankMap = activityCommonRedis.getCommonRankingMap(getZSetTeamRankKey(activityId), 1);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : teamRankMap.entrySet()) {
                if (rank == 1){
                    String teamId = entry.getKey();
                    List<String> teamMemberUidList = activityCommonRedis.getCommonListRecord(getListTeamMemberKey(activityId, teamId), 3);
                    for (String rankUid : teamMemberUidList) {
                        resourceKeyHandlerService.sendResourceData(rankUid, "DragonTrainingTeamTop1", "Dragon Training-TeamTopreward", "Dragon Training-TeamTopreward");
                    }
                }
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    /**
     * 分享配置信息给好友
     */
    public void dragonTrainShare(ShareActivityDTO dto) {
        if (StringUtils.isEmpty(dto.getActivity_id()) || StringUtils.isEmpty(dto.getAid())){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        checkActivityTime(dto.getActivity_id());
        String uid = dto.getUid();
        String activityId = dto.getActivity_id();
        String aid = dto.getAid();
        if (uid.equals(aid)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        String shareLimitKey = getShareLimitKey(activityId, uid, aid);
        String shareLimit = activityCommonRedis.getCommonStrValue(shareLimitKey);
        if (!StringUtils.isEmpty(shareLimit)){
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE.getCode(), "لقد شاركت");
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("activity_id", dto.getActivity_id());
        jsonObject.put("activity_name", dto.getActivity_name());
        jsonObject.put("activity_desc", dto.getActivity_desc());
        jsonObject.put("activity_icon", dto.getActivity_icon());
        jsonObject.put("activity_banner", dto.getActivity_banner());
        jsonObject.put("activity_url", dto.getActivity_url());
        sendActivityShareMsg(dto.getUid(), dto.getAid(), jsonObject);
        activityCommonRedis.setCommonStrScore(shareLimitKey, 1);

    }


    /**
     * 上报石头数量
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int num, int source) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name("Dragon Training");
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 上报抽奖记录
     */
    private void doDrawPrizeRecordEvent(String uid, int senceDetail, int amount, ResourceKeyConfigData.ResourceMeta resourceMeta) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence("Dragon Training");
        event.setSence_detail(senceDetail);
        event.setTicket_type(0);
        event.setCost_ticket(amount);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(amount);
        event.setDraw_result(JSONObject.toJSONString(resourceMeta));
        eventReport.track(new EventDTO(event));
    }


    /**
     * 上报组队情况
     */
    private void doTeamRecordEvent(String activityId, String uid, String captainUid, String teamName) {
        TeamRecordEvent event = new TeamRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name("Dragon Training");
        event.setActive_id(activityId);
        event.setTeam_captain_uid(captainUid);
        event.setTeam_name(teamName);
        eventReport.track(new EventDTO(event));
    }


}
