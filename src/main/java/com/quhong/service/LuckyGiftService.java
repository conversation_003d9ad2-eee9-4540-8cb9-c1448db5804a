package com.quhong.service;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.vo.LuckyGiftConfigVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.GiftSendNumDao;
import com.quhong.mongo.data.GiftSendNumData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;

@Component
public class LuckyGiftService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(LuckyGiftService.class);
    private static final Integer LUCKY_GIFT_ID = ServerConfig.isProduct() ? 232 : 230;

    @Resource
    private GiftSendNumDao giftSendNumDao;


    public LuckyGiftConfigVO luckyGiftInfo(String uid, int slang) {

        LuckyGiftConfigVO vo = new LuckyGiftConfigVO();

        ActorData actorData =actorDao.getActorDataFromCache(uid);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

        GiftSendNumData giftSendNum = giftSendNumDao.getGiftSendNum(uid, LUCKY_GIFT_ID);
        vo.setScore(giftSendNum == null ? 0: giftSendNum.getGiftNum());
        vo.setLuckyGiftScrollList(Collections.emptyList());
        return vo;
    }

}
