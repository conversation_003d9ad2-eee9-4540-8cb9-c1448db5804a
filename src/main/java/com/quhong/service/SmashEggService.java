package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.SmashEggDTO;
import com.quhong.data.vo.SmashEggHomeVO;
import com.quhong.data.vo.SmashEggPlayVO;
import com.quhong.data.vo.SmashEggRankingVO;
import com.quhong.data.vo.SmashEggRecordVO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.SmashEggConfigDao;
import com.quhong.mysql.dao.SmashEggDao;
import com.quhong.mysql.data.SmashEggConfigData;
import com.quhong.mysql.data.SmashEggData;
import com.quhong.redis.SmashEggRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class SmashEggService {

    private static final Logger logger = LoggerFactory.getLogger(SmashEggService.class);
    private static final List<Integer> AMOUNT_LIST = Arrays.asList(1, 10);
    private static final int LIMIT_INIT_POOL = 30;
    private static final int ZERO_INIT_POOL = 0;
    private static final String GAME_KEY = "SmashEgg";
    private static final String GAME_NAME = "SmashEgg";
    private static final List<String> FULL_KEY_LIST = Arrays.asList("A", "B");
    private static final String WEB_NOT_SHOW = "0";
    private static final List<String> BIG_PRIZE_LIST = Arrays.asList("1", "2");
    private static final String ROOM_MSG_CONTENT_EN = "Congratulations \u202C%s getting \u202C%s. GO>>";
    private static final String ROOM_MSG_CONTENT_AR = " تهانينا ل \u202b%s لحصوله على \u202b%s. هيا بنا>>";
    private static final String GOLDEN_EGG_URL = ServerConfig.isProduct() ? "https://static.youstar.live/hit_a_golden_eggs/" : "https://test2.qmovies.tv/hit_a_golden_eggs/";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private SmashEggDao smashEggDao;
    @Resource
    private SmashEggConfigDao smashEggConfigDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private SmashEggRedis smashEggRedis;
    @Resource
    private SmashEggService smashEggService;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    protected DataCenterService dataCenterService;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;


    @Cacheable(value = "smashEggScroll", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<SmashEggData> smashEggScroll() {
        return smashEggDao.selectScrollList();
    }

    @Cacheable(value = "smashEggConfig", cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public List<SmashEggConfigData> getSmashEggConfigList() {
        return smashEggConfigDao.selectList(-1, -1);
    }


    public SmashEggHomeVO smashEggHomeInfo(HttpEnvData httpEnvData) {
        String uid = httpEnvData.getUid();
        int slang = httpEnvData.getSlang();

        SmashEggHomeVO vo = new SmashEggHomeVO();
        vo.setLucky_values(smashEggRedis.getSmashLuckyValue(uid));
        vo.setFirst_smash(1);

        // 设置滚屏记录
        List<SmashEggConfigData> smashEggConfigList = smashEggService.getSmashEggConfigList();
        Map<String, SmashEggConfigData> rewardConfigMap = smashEggConfigList.stream().collect(Collectors.toMap(SmashEggConfigData::getWinType, Function.identity()));

        List<String> rdsSmashRollRecord = smashEggRedis.getSmashRollRecordList();
        List<SmashEggHomeVO.ScrollInfo> scrollList = new ArrayList<>();
        for (String drawKey: rdsSmashRollRecord) {
            String[] split = drawKey.split("_");

            String drawUid = split[0];
            String winType = split[1];

            SmashEggConfigData smashEggConfig = rewardConfigMap.getOrDefault(winType, null);
            if(smashEggConfig == null){
                continue;
            }

            if(smashEggConfig.getRoll() == 0){
                continue;
            }

            ActorData actorData = actorDao.getActorDataFromCache(drawUid);
            SmashEggHomeVO.ScrollInfo info = new SmashEggHomeVO.ScrollInfo();
            info.setName(actorData.getName());
            info.setWin_type(winType);
            info.setDesc(slang == SLangType.ARABIC ? smashEggConfig.getNameAr() : smashEggConfig.getNameEn());
            info.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            info.setIcon(smashEggConfig.getIcon());
            scrollList.add(info);
        }
        vo.setScroll_list(scrollList);

        // 设置页面奖励
        List<SmashEggHomeVO.AwardInfo> awardInfoList = new ArrayList<>();
        for (SmashEggConfigData config : smashEggConfigList) {
            if(config.getShowPage() == 0){
                continue;
            }

            SmashEggHomeVO.AwardInfo awardInfo = new SmashEggHomeVO.AwardInfo();
            awardInfo.setAward_title(slang == SLangType.ARABIC ? config.getPageAwardAr() : config.getPageAwardEn());
            awardInfo.setAward_url(config.getPageAwardUrl());
            awardInfo.setRewardType(config.getRewardType());
            awardInfo.setRewardTimeNum(config.getRewardNum() > 0 ? config.getRewardNum() : config.getRewardTime());
            awardInfoList.add(awardInfo);
        }
        vo.setAward_list(awardInfoList);
        return vo;
    }

    public SmashEggRecordVO iosSmashEggPersonal(SmashEggDTO dto){
        SmashEggRecordVO vo = new SmashEggRecordVO();
        String uid = dto.getUid();
        int slang = dto.getSlang();
        int page = dto.getPage();
        page = page <= 0 ? 1: page;
        int pageSize = 20;

        List<SmashEggData> dataList = smashEggDao.selectPageList(uid, page, pageSize);
        Map<String, SmashEggConfigData> rewardConfigMap =  smashEggService.getSmashEggConfigList().stream().collect(Collectors.toMap(SmashEggConfigData::getWinType, Function.identity()));

        List<SmashEggRecordVO.Record> recordList = new ArrayList<>();
        for (SmashEggData data : dataList) {
            SmashEggConfigData smashEggConfig = rewardConfigMap.get(data.getWinType());
            if(smashEggConfig != null){
                SmashEggRecordVO.Record record = new SmashEggRecordVO.Record();
                record.setWinType(data.getWinType());
                record.setDesc(slang == SLangType.ARABIC ?smashEggConfig.getNameAr() : smashEggConfig.getNameEn());
                record.setIcon(smashEggConfig.getIcon());
                record.setTime(data.getCtime());
                recordList.add(record);
            }
        }

        vo.setRecord_list(recordList);
        if (dataList.size() < pageSize) {
            vo.setNextUrl("-1");
        } else {
            vo.setNextUrl(page + 1 + "");
        }
        return vo;
    }

    private Map<String, Long> getTimeStampByRankType(int rankType){
        long todayStartTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
        Map<String, Long> timeStampMap = new HashMap<>();
        timeStampMap.put("endTime", todayStartTime + 24 * 3600);
        if(rankType == 1){
            timeStampMap.put("startTime", todayStartTime);
        } else if (rankType == 2) {
            timeStampMap.put("startTime", todayStartTime - 7 * 24 * 3600);
        }else {
            timeStampMap.put("startTime", todayStartTime - 30 * 24 * 3600);
        }
        return timeStampMap;
    }


    public SmashEggRankingVO iosSmashEggRanking(SmashEggDTO dto){
        SmashEggRankingVO vo = new SmashEggRankingVO();
        String uid = dto.getUid();
        int rankType = dto.getRank_type();

        ActorData actorData = actorDao.getActorDataFromCache(uid);

        Map<String, Long> timeStampMap = getTimeStampByRankType(rankType);
        long startTime = timeStampMap.get("startTime");
        long endTime = timeStampMap.get("endTime");
        logger.info("startTime:{}, endTime:{}", startTime, endTime);
        List<SmashEggData> rankingDbList = smashEggDao.selectRankingList(startTime, endTime);

        SmashEggRankingVO.UserInfo myUserInfo = new SmashEggRankingVO.UserInfo();
        List<SmashEggRankingVO.UserInfo> rankingList = new ArrayList<>();


        int rank = 1;
        for (SmashEggData data : rankingDbList) {

            SmashEggRankingVO.UserInfo userInfo = new SmashEggRankingVO.UserInfo();
            String rankUid = data.getUid();
            ActorData rankActor = actorDao.getActorDataFromCache(rankUid);
            userInfo.setRn(String.valueOf(rank));
            userInfo.setUid(rankUid);
            userInfo.setName(rankActor.getName());
            userInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            userInfo.setDiamond(data.getConsume());

            if(uid.equals(rankUid)){
                BeanUtils.copyProperties(userInfo, myUserInfo);
            }

            rankingList.add(userInfo);
            rank += 1;
        }


        if(StringUtils.isEmpty(myUserInfo.getRn())){
            myUserInfo.setRn("30+");
            myUserInfo.setUid(uid);
            myUserInfo.setName(actorData.getName());
            myUserInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            SmashEggData mySmashEggData = smashEggDao.selectSumOne(uid, startTime, endTime);
            myUserInfo.setDiamond(mySmashEggData == null ? 0 : mySmashEggData.getConsume());

        }

        vo.setTop_list(rankingList);
        vo.setUser_info(myUserInfo);

        return vo;
    }

    private void smashEggDiamondCost(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(MoneyTypeConstant.SMASH_COST_TYPE);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(MoneyTypeConstant.SMASH_COST_TITLE);
        moneyDetailReq.setDesc(MoneyTypeConstant.SMASH_COST_DESC);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
    }

    // 初始化奖池
    private int getIntPoolPrize(String prizeSize){
        try {
            return Integer.parseInt(prizeSize);
        }catch (Exception e){
            logger.error("getIntPoolPrize error:{}", e.getMessage(), e);
            return 0;
        }
    }


    public void initSmashEggPoolSize(){
        List<String> poolList = new ArrayList<>();
        List<SmashEggConfigData> smashEggConfigList = smashEggService.getSmashEggConfigList();
        for (SmashEggConfigData smashEggConfig : smashEggConfigList) {
            if(smashEggConfig.getJoinPool() == 0){
                continue;
            }

            if (smashEggConfig.getStatus() == 0) {
                continue;
            }

            int prizeSize = getIntPoolPrize(smashEggConfig.getPrize());
            String poolKey = smashEggConfig.getWinType();
            for (int i = 0; i < prizeSize; i++) {
                poolList.add(poolKey);
            }
        }

        Collections.shuffle(poolList);
        smashEggRedis.initPoolSize(poolList);

    }


    private void initPoolSize(){

        int poolSize = smashEggRedis.getPoolSize();
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    initSmashEggPoolSize();
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            initSmashEggPoolSize();
        }
    }

    // 奖励下发
    private void smashEggDrawChargeDiamonds(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(MoneyTypeConstant.SMASH_AWARD_TYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(MoneyTypeConstant.SMASH_AWARD_TITLE);
        moneyDetailReq.setDesc(MoneyTypeConstant.SMASH_AWARD_DESC);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    private void smashEggRewardDistribute(String uid, int amount, List<String> rewardResult){

        ActorData actor = actorDao.getActorDataFromCache(uid);
        Map<String, SmashEggConfigData> rewardConfigMap =  smashEggService.getSmashEggConfigList().stream().collect(Collectors.toMap(SmashEggConfigData::getWinType, Function.identity()));
        Integer dateNum = Integer.parseInt(DateHelper.ARABIAN.formatDateInDay2());

        for (String drawKey : rewardResult) {
            SmashEggConfigData rewardConfig = rewardConfigMap.get(drawKey);
            if(rewardConfig == null){
                logger.info("smashEggReward not find rewardConfig drawKey :{}", drawKey);
                continue;
            }

            String rewardType = rewardConfig.getRewardType();
            int rewardNum = rewardConfig.getRewardNum() != null ? rewardConfig.getRewardNum():0;
            int rewardTime = rewardConfig.getRewardTime() != null ? rewardConfig.getRewardTime():0;
            switch (rewardType){
                case ResourceConstant.THANKS:
                    break;
                case ResourceConstant.DIAMOND:
                    smashEggDrawChargeDiamonds(uid, rewardNum);
                    break;
                default:
                    distributionService.sendRewardResource(uid, rewardConfig.getSourceId(),
                            ActivityRewardTypeEnum.getEnumByName(rewardConfig.getRewardType()), rewardTime, rewardNum, GAME_NAME, GAME_NAME, 0);
            }

            int curTime = DateHelper.getNowSeconds();
            SmashEggData recordData = new SmashEggData();
            recordData.setUid(uid);
            recordData.setWinType(drawKey);
            recordData.setConsume(amount == 1 ? 100 : 90);
            recordData.setDateNum(dateNum);
            recordData.setCtime(curTime);
            smashEggDao.insertOne(recordData);

            // 页面滚动记录
            if(rewardConfig.getScreen() > 0){
                smashEggRedis.addSmashRollRecord(uid, drawKey);
            }

            // 推送公屏消息
            if(rewardConfig.getScreen() > 0){
                // 非房间榜还需要发送公屏消息
                List<HighlightTextObject> list = new ArrayList<>();
                HighlightTextObject object = new HighlightTextObject();
                object.setText(actor.getName());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText(rewardConfig.getNameEn());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText(rewardConfig.getNameAr());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText("GO >>");
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText("هيا بنا>>");
                object.setHighlightColor("#FFE200");
                list.add(object);

                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setUser_name(actor.getName());
                msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
                msg.setText(String.format(ROOM_MSG_CONTENT_EN, actor.getName(), rewardConfig.getNameEn()));
                msg.setText_ar(String.format(ROOM_MSG_CONTENT_AR, actor.getName(), rewardConfig.getNameAr()));
                msg.setHighlight_text(list);
                msg.setHighlight_text_ar(list);
                msg.setWeb_url(GOLDEN_EGG_URL);
                msg.setGame_type("smash_egg");
                msg.setWeb_type(1);
                msg.setHide_user_card(1);
                msg.setWidth(375);
                msg.setHeight(560);
                marsMsgActivityService.asyncSendMsg("all", null, msg, false);
            }

        }
    }

    private String getFullKey(List<String> luckyGoodConfigList) {
        Random rand = new Random();
        return luckyGoodConfigList.get(rand.nextInt(luckyGoodConfigList.size()));
    }


    public SmashEggPlayVO iosSmashEggPlay(SmashEggDTO dto){
        SmashEggPlayVO vo = new SmashEggPlayVO();
        String uid = dto.getUid();
        int amount = dto.getAmount();

        if(!AMOUNT_LIST.contains(amount)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        Map<String, SmashEggPlayVO.PriceInfo> priceInfoMap = new HashMap<>();
        List<String> rewardResult = new ArrayList<>();

        List<SmashEggConfigData> smashEggConfigList = smashEggService.getSmashEggConfigList();
        List<String> luckyGoodConfigList = smashEggConfigList.stream().filter(item -> item.getLuckyGood() > 0).map(SmashEggConfigData::getWinType).collect(Collectors.toList());
        List<String> clearValueList = smashEggConfigList.stream().filter(item -> item.getClearValue() > 0).map(SmashEggConfigData::getWinType).collect(Collectors.toList());
        Map<String, SmashEggConfigData> rewardConfigMap =  smashEggConfigList.stream().collect(Collectors.toMap(SmashEggConfigData::getWinType, Function.identity()));

        int afterLuckyValue = 0;
        synchronized (stringPool.intern(GAME_KEY + uid)) {
            // 1、扣费
            int changed = amount == 1 ? 100 : 900;
            smashEggDiamondCost(uid, changed);

            // 2、抽奖及大奖推送
            int endTime = DateHelper.getNowSeconds() + 60*60;

            for (int i = 0; i < amount; i++) {
                String drawKey = null;

                // 初始化奖池
                initPoolSize();
                int luckyValue = smashEggRedis.getSmashLuckyValue(uid);
                if (luckyValue >= 100){
                    drawKey = getFullKey(luckyGoodConfigList);
                    afterLuckyValue = smashEggRedis.setSmashLuckyValue(uid, 0);
                    smashEggRedis.removeSmashTTL(uid);
                }else {
                    drawKey = smashEggRedis.drawSmashEggKey();
                    if(clearValueList.contains(drawKey)){
                        afterLuckyValue = smashEggRedis.setSmashLuckyValue(uid, 0);
                        smashEggRedis.removeSmashTTL(uid);
                    }else {
                        luckyValue += 1;
                        afterLuckyValue = smashEggRedis.setSmashLuckyValue(uid, luckyValue);
                        smashEggRedis.addSmashTTL(uid, endTime);
                    }
                }

                logger.info("smashEggPlay uid:{}, amount:{}, drawKey:{}, afterLuckyValue:{}", uid, amount, drawKey, afterLuckyValue);
                SmashEggConfigData smashEggConfig = rewardConfigMap.getOrDefault(drawKey, null);
                if(smashEggConfig != null){
                    rewardResult.add(drawKey);

                    SmashEggPlayVO.PriceInfo priceInfo = priceInfoMap.get(drawKey);
                    if(priceInfo != null){
                        priceInfo.setWin_num(priceInfo.getWin_num() + 1);
                    }else {
                        priceInfo = new SmashEggPlayVO.PriceInfo();
                        priceInfo.setWin_num(1);
                        priceInfo.setName(smashEggConfig.getNameEn());
                        priceInfo.setNamear(smashEggConfig.getNameAr());
                        priceInfo.setIcon(smashEggConfig.getIcon());
                        priceInfo.setPer_icon(smashEggConfig.getIcon());
                        priceInfo.setWin_type(smashEggConfig.getWinType());
                        priceInfoMap.put(drawKey, priceInfo);
                    }
                }
            }
        }

        List<SmashEggPlayVO.PriceInfo> priceInfoList = new ArrayList<>(priceInfoMap.values());
        vo.setPrice_list(priceInfoList);
        vo.setLucky_values(afterLuckyValue);

        // 4、奖励下发
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                smashEggRewardDistribute(uid, amount, rewardResult);
            }
        });
        return vo;
    }


    public void smashEggExpireTimeHandle(){
        int curTime = DateHelper.getNowSeconds();
        Map<String, Integer> rankingMap = smashEggRedis.getSmashExpireTimeMapByScore(0, curTime);

        int afterTime = curTime + 3600;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String expireUid = entry.getKey();
            int smashScore = smashEggRedis.getSmashLuckyValue(expireUid);

            logger.info("smashEggExpireTimeHandle expireUid:{}, smashScore:{}", expireUid, smashScore);
            int afterScore = smashScore - 1;
            if(afterScore > 0){
                smashEggRedis.setSmashLuckyValue(expireUid, afterScore);
                smashEggRedis.addSmashTTL(expireUid, afterTime);
            }else {
                smashEggRedis.delSmashLuckyValue(expireUid);
                smashEggRedis.removeSmashTTL(expireUid);
            }
        }

    }

}
