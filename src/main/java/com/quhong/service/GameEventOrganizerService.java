package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.*;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.RoomMemberData;
import com.quhong.mysql.dao.QuestionDao;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.dao.RoomEventSubDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.QuestionData;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.data.RoomEventSubData;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class GameEventOrganizerService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(GameEventOrganizerService.class);
    private static final String ACTIVITY_TITLE_EN = "test ramadan event star";
    private static final String ACTIVITY_TITLE_AR = "test ramadan event star";
    public static final String ACTIVITY_ID = "67cad462946370923ebcccf5";//  灰度测试 67c57cdaeba330b28506eb7b     67cad462946370923ebcccf5
    public static final int GAME_TYPE = 1; // 游戏
    public static final int MIC_TYPE = 2; // 上麦
    public static final int SUB_TYPE = 3; // 订阅
    public static final int CREATE_EVENT_TYPE = 4; // 创建的房间活动
    public static final int WATCH_VIDEO_TYPE = 5; // video
    public static final int VOTE_TYPE = 6; // vote
    public static final int PLAY_DOMINO_TYPE = 7; // 玩多米乐

    public static final int USER_SUB_TYPE = 99; // 用户订阅
    public static final int USER_MIC_TYPE = 100; // 用户参与

    public static final int USER_SUB_LIMIT = 3; // 用户订阅次数限制
    public static final int USER_MIC_LIMIT = 3; // 用户参与次数限制

    public static final int MAX_ADMIN_LIMIT = 20; //

    private static final List<Integer> SCORE_LEVEL_LIST = Arrays.asList(0, 500, 1000, 1500, 2000);

    private static final List<Integer> TIMES_LEVEL_LIST = Arrays.asList(0, 1, 2, 3, 4);

    private static final List<Integer> Q_ALL_LIST = Arrays.asList(GAME_TYPE, MIC_TYPE, SUB_TYPE, WATCH_VIDEO_TYPE, VOTE_TYPE, PLAY_DOMINO_TYPE);

    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/gameEventOrganizer2025/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/gameEventOrganizer2025/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";

    public static final int WATCH_VIDEO_MIN_TIME = 5; // 5分钟 看视频最小的积分时长

    private static final List<ActivityCommonConfig.QueenConfig> GAME_TASK_LIST = new ArrayList<>();  // 游戏任务
    private static final List<ActivityCommonConfig.QueenConfig> NEW_USER_TASK_LIST = new ArrayList<>();  // 新用户任务（上麦，订阅）
    private static final List<ActivityCommonConfig.QueenConfig> OLD_USER_TASK_LIST = new ArrayList<>();// 老用户任务（上麦，订阅）
    private static final List<ActivityCommonConfig.QueenConfig> COMMON_TASK_LIST = new ArrayList<>(); //  通用任务（video，vote，domino）

    /**
     * 榜单前10名奖励
     */
    public static final List<String> RANK_KEY_LIST = Arrays.asList(
            "gameEventOrganizerTop1", "gameEventOrganizerTop2", "gameEventOrganizerTop3"
            , "gameEventOrganizerTop4-10", "gameEventOrganizerTop4-10", "gameEventOrganizerTop4-10"
            , "gameEventOrganizerTop4-10", "gameEventOrganizerTop4-10", "gameEventOrganizerTop4-10", "gameEventOrganizerTop4-10");

    /**
     * 等级奖励
     */
    public static final Map<Integer, List<String>> LEVEL_RES_KEY_MAP = new HashMap<Integer, List<String>>() {
        {
            put(1, Arrays.asList("gameEventOrganizerLv1Host", "gameEventOrganizerLv1Manger"));
            put(2, Arrays.asList("gameEventOrganizerLv2Host", "gameEventOrganizerLv2Manger"));
            put(3, Arrays.asList("gameEventOrganizerLv3Host", "gameEventOrganizerLv3Manger"));
            put(4, Arrays.asList("gameEventOrganizerLv4Host", "gameEventOrganizerLv4Manger"));
        }
    };

    /**
     * 看播奖励
     */
    public static final Map<Integer, String> USER_RES_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(SUB_TYPE, "gameEventOrganizerUserSub"); // 订阅
            put(MIC_TYPE, "gameEventOrganizerUserMic"); // 参与
        }
    };

    /**
     * game历史记录
     */
    public static final Map<String, List<String>> HISTORY_GAME_MAP = new HashMap<String, List<String>>() {
        {
            put(CommonMqTaskConstant.PLAY_CARROM_POOL, Arrays.asList("carrom pool", "بركة كيرم"));
            put(CommonMqTaskConstant.PLAY_TRUTH_DARE_WHEEL, Arrays.asList("smart wheel", "العجلة الذكية"));
            put(CommonMqTaskConstant.PLAY_WHEEL, Arrays.asList("lucky wheel", "عجلة الحظ"));
            put(CommonMqTaskConstant.PLAY_MONSTER_CRUSH, Arrays.asList("Monster crush", " سحق الوحش"));
            put(CommonMqTaskConstant.PLAY_LUDO, Arrays.asList("ludo", "لودو"));
            put(CommonMqTaskConstant.PLAY_DOMINO, Arrays.asList("domino", "دومينو"));
        }
    };

    /**
     * 埋点事件
     */
    public static final Map<Integer, String> EVENT_TITLE_MAP = new HashMap<Integer, String>() {
        {
            put(1, "Room Event-room host reward LV1");
            put(2, "Room Event-room host reward LV2");
            put(3, "Room Event-room host reward LV3");
            put(4, "Room Event-room host reward LV4");
            put(11, "Room Event-room administrator reward LV1");
            put(12, "Room Event-room administrator reward LV2");
            put(13, "Room Event-room administrator reward LV3");
            put(14, "Room Event-room administrator reward LV4");
            put(100, "Room Event-subscribe event reward");
            put(101, "Room Event-on mic reward");
            put(102, "Room Event-rank reward");
            put(201, "Room Event-all correct");
        }
    };

    public static final Map<Integer, List<String>> EVENT_TYPE_MAP = new HashMap<Integer, List<String>>() {
        {
            put(1, Arrays.asList("Party", "الحفلة", "https://cdn3.qmovies.tv/youstar/op_sys_1675064676_2x.png"));
            put(2, Arrays.asList("Chat", "الدردشة", "https://cdn3.qmovies.tv/youstar/op_sys_1675064639_22x.png"));
            put(3, Arrays.asList("Game", "اللعب", "https://cdn3.qmovies.tv/youstar/op_sys_1673512837_82x.png"));
            put(4, Arrays.asList("Singing", "الغناء", "https://cdn3.qmovies.tv/youstar/op_sys_1675064724_142x.png"));
            put(5, Arrays.asList("Competition", "المسابقة", "https://cdn3.qmovies.tv/youstar/op_sys_1673512788_142x.png"));
            put(6, Arrays.asList("Poetry", "الشعر", "https://cdn3.qmovies.tv/youstar/op_sys_1673512729_332x.png"));
            put(7, Arrays.asList("Other", "الآخر", "https://cdn3.qmovies.tv/youstar/op_sys_1673512623_152x.png"));
        }
    };

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final int HISTORY_PAGE_SIZE = 30;
    private static final int HISTORY_MAX_SIZE = 3000;


    private static String GID = "112"; // 题库id

    private static String CORRECT_ALL_RES_KEY = "gameEventOrganizerCorrectAll";

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.WATCH_VIDEO_TIME, CommonMqTaskConstant.CREATE_VOTE, CommonMqTaskConstant.PLAY_DOMINO,
            CommonMqTaskConstant.ON_MIC_TIME, CommonMqTaskConstant.SUB_ROOM_EVENT,
            CommonMqTaskConstant.CREATE_ROOM_EVENT);

    static {
        GAME_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (0, "游戏有效局数", "游戏有效局数", "", "", 100, 20, 5, "eventDesc", "iconUrl"));

        NEW_USER_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (1, "订阅活动", "订阅活动", "", "", 200, 100, 2, "eventDesc", "iconUrl"));
        NEW_USER_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (2, "房间内上麦", "房间内上麦", "", "", 200, 100, 2, "eventDesc", "iconUrl"));

        OLD_USER_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (3, "订阅活动", "订阅活动", "", "", 200, 200, 1, "eventDesc", "iconUrl"));
        OLD_USER_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (4, "房间内上麦", "房间内上麦", "", "", 200, 200, 1, "eventDesc", "iconUrl"));

        COMMON_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (5, "YouTube Video时长", "YouTube Video时长", "", "", 50, 25, 2, "eventDesc", "iconUrl"));
        COMMON_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (6, "创建投票", "创建投票", "", "", 50, 25, 2, "eventDesc", "iconUrl"));
        COMMON_TASK_LIST.add(new ActivityCommonConfig.QueenConfig
                (7, "玩Domino", "玩Domino", "", "", 50, 25, 2, "eventDesc", "iconUrl"));

    }

    private final Comparator<GameEventOrganizerVO.RoomItemVO> eventStartTimeAsc = Comparator.comparing(GameEventOrganizerVO.RoomItemVO::getRoomEventStartTime);

    private final Comparator<GameEventOrganizerVO.RoomItemVO> eventSubCountDesc = Comparator.comparing(GameEventOrganizerVO.RoomItemVO::getRoomEventSubCount).reversed();


    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private RoomEventSubDao roomEventSubDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private QuestionDao questionDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            GID = "0121";
        }
    }

    public GameEventOrganizerVO challengeInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String totalInfoKey = getHashTotalKey(activityId);
        String roomId = RoomUtils.formatRoomId(uid);
        GameEventOrganizerVO.ChallengeInfo challengeInfo = cacheDataService.
                getGameEventOrganizerVOChallengeInfo(totalInfoKey, roomId);
        GameEventOrganizerVO vo = new GameEventOrganizerVO();
        GameEventOrganizerVO.ChallengeInfoVO challengeInfoVO = new GameEventOrganizerVO.ChallengeInfoVO();
        List<GameEventOrganizerVO.LevelVO> levelVOList = new ArrayList<>();
        int point = challengeInfo.getPoint();
        Map<String, Integer> levelMapCollect = challengeInfo.getLevelMapCollect();
        Map<String, List<String>> levelMapAidList = challengeInfo.getLevelMapAidList();
        String tnId = actorDao.getActorData(uid).getTn_id();

        int nowIndex = getIndexLevel(point);
        int leftLevel = nowIndex;
        int needPoint = 0;
        int process = 100;
        if (nowIndex >= SCORE_LEVEL_LIST.size() - 1) {
            leftLevel = SCORE_LEVEL_LIST.size() - 1;
        } else {
            needPoint = SCORE_LEVEL_LIST.get(leftLevel + 1) - point;
            process = (point - SCORE_LEVEL_LIST.get(leftLevel)) * 100
                    / (SCORE_LEVEL_LIST.get(leftLevel + 1) - SCORE_LEVEL_LIST.get(leftLevel));
        }
        // 1-4
        for (int i = 1; i < SCORE_LEVEL_LIST.size(); i++) {
            GameEventOrganizerVO.LevelVO levelVO = new GameEventOrganizerVO.LevelVO();
            String strLevel = String.valueOf(i);
            levelVO.setLevel(i);
            levelVO.setLevelScore(SCORE_LEVEL_LIST.get(i));
            int state = levelMapCollect.getOrDefault(strLevel, 0);
            levelVO.setState(state);
            List<String> aidList = levelMapAidList.get(strLevel);
            String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId, strLevel), tnId);
            levelVO.setRoomOwnerState(!StringUtils.isEmpty(bindAid) ? 2 : state);
            if (CollectionUtils.isEmpty(aidList)) {
                levelVO.setRoomAdminState(state >= 1 ? 1 : 0);
                levelVO.setAdminGetList(Collections.emptyList());
            } else {
                List<String> aidHeadList = aidList.stream()
                        .map(aid -> ImageUrlGenerator.generateRoomUserUrl(actorDao.getActorDataFromCache(aid).getHead()))
                        .collect(Collectors.toList());
                levelVO.setRoomAdminState(aidList.size() == MAX_ADMIN_LIMIT ? 2 : 1);
                levelVO.setAdminGetList(aidHeadList);
            }
            levelVOList.add(levelVO);
        }
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        Map<Integer, ActivityCommonConfig.QueenConfig> configMap = COMMON_TASK_LIST.stream()
                .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
        challengeInfoVO.setVideoPoint(challengeInfo.getVideoPoint());
        challengeInfoVO.setVoteCount(challengeInfo.getVoteCount() * configMap.get(6).getNumPerPoint());
        challengeInfoVO.setGameCount(challengeInfo.getGameCount() * configMap.get(7).getNumPerPoint());
        challengeInfoVO.setMicCount(challengeInfo.getMicCount());
        challengeInfoVO.setPoint(challengeInfo.getPoint());
        challengeInfoVO.setSubCount(challengeInfo.getSubCount());
        challengeInfoVO.setNeedPoint(needPoint);
        challengeInfoVO.setProcess(process);
        challengeInfoVO.setLeftLevel(leftLevel);
        challengeInfoVO.setLevelVOList(levelVOList);
        vo.setChallengeInfoVO(challengeInfoVO);
        return vo;
    }

    public GameEventOrganizerVO adminList(String activityId, String uid, int level) {
        String totalInfoKey = getHashTotalKey(activityId);
        String roomId = RoomUtils.formatRoomId(uid);
        GameEventOrganizerVO.ChallengeInfo challengeInfo = cacheDataService.
                getGameEventOrganizerVOChallengeInfo(totalInfoKey, roomId);
        GameEventOrganizerVO vo = new GameEventOrganizerVO();
        List<GameEventOrganizerVO.AdminLogVO> adminList = new ArrayList<>();
        Map<String, List<String>> levelMapAidList = challengeInfo.getLevelMapAidList();
        List<String> aidList = levelMapAidList.getOrDefault(String.valueOf(level), Collections.emptyList());
        List<RoomMemberData> adminMemberList = roomMemberDao.findAdminList(roomId);
        if (!CollectionUtils.isEmpty(adminMemberList)) {
            for (RoomMemberData memberData : adminMemberList) {
                String aid = memberData.getAid();
                GameEventOrganizerVO.AdminLogVO adminLogVO = new GameEventOrganizerVO.AdminLogVO();
                adminLogVO.setAid(aid);
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                adminLogVO.setName(actorData.getName());
                adminLogVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                adminLogVO.setState(aidList.contains(aid) ? 1 : 0);
                adminList.add(adminLogVO);
            }
        }
        vo.setAdminList(adminList);
        return vo;
    }

    public void collect(String activityId, String uid, String aid, int cmd, Integer level) {
        checkActivityTime(activityId);
        String totalInfoKey = getHashTotalKey(activityId);
        String roomId = RoomUtils.formatRoomId(uid);
        String tnId = actorDao.getActorData(uid).getTn_id();
        if (StringUtils.isEmpty(tnId)) {
            logger.info("tnId is empty");
            throw new CommonH5Exception(ActivityHttpCode.AUTH_ERROR);
        }
        if (cmd == 1) {
            // 领取等级奖励
            if (level == null || 0 == level) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            String strLevel = String.valueOf(level);
            GameEventOrganizerVO.ChallengeInfo challengeInfo = cacheDataService.
                    getGameEventOrganizerVOChallengeInfo(totalInfoKey, roomId);
            Map<String, Integer> levelMapCollect = challengeInfo.getLevelMapCollect();
            String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId, strLevel), tnId);
            if (levelMapCollect.getOrDefault(String.valueOf(level), 0) == 1
                    && StringUtils.isEmpty(bindAid)) {
                synchronized (stringPool.intern(getLocalEventRoomKey(roomId))) {
                    levelMapCollect.put(String.valueOf(level), 2);
                    activityCommonRedis.setCommonHashData(totalInfoKey, roomId, JSONObject.toJSONString(challengeInfo));
                    activityCommonRedis.setCommonHashData(getHashBindKey(activityId, strLevel), tnId, uid);
                }
                handleRes(uid, LEVEL_RES_KEY_MAP.get(level).get(0), level);
            } else {
                throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_GET_ALREADY);
            }

        } else if (cmd == 2) {
            // 给admin发等级奖励
            if (StringUtils.isEmpty(aid) || level == null || 0 == level) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            if (roomMemberDao.getRoleContainMember(roomId, aid) != RoomRoleType.MANAGER) {
                throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_ONLY_TO_ADMIN);
            }

            GameEventOrganizerVO.ChallengeInfo challengeInfo = cacheDataService.
                    getGameEventOrganizerVOChallengeInfo(totalInfoKey, roomId);
            Map<String, Integer> levelMapCollect = challengeInfo.getLevelMapCollect();
            if (levelMapCollect.getOrDefault(String.valueOf(level), 0) >= 1) {
                synchronized (stringPool.intern(getLocalEventRoomKey(roomId))) {
                    Map<String, List<String>> levelMapAidList = challengeInfo.getLevelMapAidList();
                    List<String> levelAidList = levelMapAidList.getOrDefault(String.valueOf(level), new ArrayList<>());
                    if (levelAidList.size() < MAX_ADMIN_LIMIT && !levelAidList.contains(aid)) {
                        levelAidList.add(aid);
                        levelMapAidList.put(String.valueOf(level), levelAidList);
                        activityCommonRedis.setCommonHashData(totalInfoKey, roomId, JSONObject.toJSONString(challengeInfo));
                        handleRes(aid, LEVEL_RES_KEY_MAP.get(level).get(1), 10 + level);
                    } else {
                        if (levelAidList.size() >= MAX_ADMIN_LIMIT) {
                            throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_GET_MAX);
                        } else {
                            throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_GET_ALREADY);
                        }
                    }
                }
                sendMsg(uid, aid);
            } else {
                throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_NOT_UNLOCK);
            }

        } else if (cmd == 3) {
            // 领取用户订阅奖励
            String now = getDay(uid);
            String detailUserKey = getUserHashDetailKey(null, now);
            GameEventOrganizerVO.MyGiftDetailInfo myGiftDetailInfo = cacheDataService.
                    getGameEventOrganizerVOMyGiftDetailInfo(detailUserKey, now, uid);
            String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId, now + USER_SUB_TYPE), tnId);
            if (myGiftDetailInfo.getSubState() == 1 && StringUtils.isEmpty(bindAid)) {
                synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                    myGiftDetailInfo.setSubState(2);
                    activityCommonRedis.setCommonHashData(detailUserKey, uid, JSONObject.toJSONString(myGiftDetailInfo));
                }
                activityCommonRedis.setCommonHashData(getHashBindKey(activityId, now + USER_SUB_TYPE), tnId, uid);
                handleRes(uid, USER_RES_KEY_MAP.get(SUB_TYPE), 100);
            } else if (myGiftDetailInfo.getSubState() == 2) {
                throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_GET_ALREADY);
            } else {
                throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_NOT_UNLOCK);
            }

        } else if (cmd == 4) {
            // 领取用户上麦奖励
            String now = getDay(uid);
            String detailUserKey = getUserHashDetailKey(null, now);
            GameEventOrganizerVO.MyGiftDetailInfo myGiftDetailInfo = cacheDataService.
                    getGameEventOrganizerVOMyGiftDetailInfo(detailUserKey, now, uid);
            String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId, now + USER_MIC_TYPE), tnId);
            if (myGiftDetailInfo.getMicState() == 1 && StringUtils.isEmpty(bindAid)) {
                synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                    myGiftDetailInfo.setMicState(2);
                    activityCommonRedis.setCommonHashData(detailUserKey, uid, JSONObject.toJSONString(myGiftDetailInfo));
                }
                activityCommonRedis.setCommonHashData(getHashBindKey(activityId, now + USER_MIC_TYPE), tnId, uid);
                handleRes(uid, USER_RES_KEY_MAP.get(MIC_TYPE), 101);
            } else if (myGiftDetailInfo.getMicState() == 2) {
                throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_GET_ALREADY);
            } else {
                throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_NOT_UNLOCK);
            }
        }
    }

    private void sendMsg(String uid, String aid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        SendChatMsgDTO msgTextDto = new SendChatMsgDTO();
        msgTextDto.setUid(uid);
        msgTextDto.setAid(aid);
        msgTextDto.setMsgType(MsgType.TEXT);
        msgTextDto.setOs(actorData.getIntOs());
        msgTextDto.setMsgBody("يسعدني القتال معك. لقد تم إرسال المكافأة إلى حقيبة الظهر!");
        msgTextDto.setSlang(actorData.getSlang());
        JSONObject jsonTextObject = new JSONObject();
        msgTextDto.setMsgInfo(jsonTextObject);
        msgTextDto.setVersioncode(actorData.getVersion_code());
        msgTextDto.setNew_versioncode(5);
        iMsgService.sendMsg(msgTextDto);
    }

    public GameEventOrganizerVO reportAndGiftInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        String tnId = actorDao.getActorData(uid).getTn_id();
        String now = getDay(uid);
        String detailUserKey = getUserHashDetailKey(activityId, now);
        String roomId = RoomUtils.formatRoomId(uid);

        GameEventOrganizerVO.MyGiftDetailInfo myGiftDetailInfo = cacheDataService.
                getGameEventOrganizerVOMyGiftDetailInfo(detailUserKey, now, uid);
        GameEventOrganizerVO vo = new GameEventOrganizerVO();
        GameEventOrganizerVO.MyGiftVO myGiftVO = new GameEventOrganizerVO.MyGiftVO();
        String bindSubAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId, now + USER_SUB_TYPE), tnId);
        String bindMicAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId, now + USER_MIC_TYPE), tnId);
        myGiftVO.setSubCount(myGiftDetailInfo.getSubEventIds().size());
        myGiftVO.setSubState(!StringUtils.isEmpty(bindSubAid) ? 2 : myGiftDetailInfo.getSubState());
        myGiftVO.setMicCount(myGiftDetailInfo.getMicEventIds().size());
        myGiftVO.setMicState(!StringUtils.isEmpty(bindMicAid) ? 2 : myGiftDetailInfo.getMicState());


        GameEventOrganizerVO.ReportVO totalReportVO = new GameEventOrganizerVO.ReportVO();
        List<GameEventOrganizerVO.ReportVO> dayReportVOList = new ArrayList<>();
        int endTime = Math.min(activityData.getEndTime() - 10, DateHelper.getNowSeconds()); // 偏移10秒为活动的最后一天
        endTime = Math.max(endTime, DateHelper.ARABIAN.stringDateToStampSecond(now));
        String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
        String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));

        int totalNum = 0;
        if (DateHelper.getNowSeconds() > activityData.getStartTime()) {
            List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
            for (DayTimeData item : dayTimeDataList) {
                String day = item.getDate();
                String detailRoomKey = getRoomHashDetailKey(activityId, day);
                GameEventOrganizerVO.DetailInfo detailInfo = cacheDataService.getGameEventOrganizerVOInfo(detailRoomKey, day, roomId);
                GameEventOrganizerVO.ReportVO itemReportVO = new GameEventOrganizerVO.ReportVO();
                itemReportVO.setDay(day);
                itemReportVO.setCreateEventCount(detailInfo.getEventCount());
                itemReportVO.setSubCount(detailInfo.getNewSubCount() + detailInfo.getOldSubCount());
                itemReportVO.setMicCount(detailInfo.getNewMicCount() + detailInfo.getOldMicCount());
                totalNum += detailInfo.getEventCount();
                dayReportVOList.add(itemReportVO);
            }
        }
        totalReportVO.setCreateEventCount(totalNum);
        vo.setMyGiftVO(myGiftVO);
        vo.setTotalReportVO(totalReportVO);
        vo.setDayReportVOList(dayReportVOList);
        return vo;
    }

    private GameEventOrganizerVO.RoomItemVO fillRoomItemVO(RoomEventData item, int now
            , Map<String, String> commonFlagConfigMap, String uid, int slang) {
        String roomId = item.getRoomId();
        String hostUid = RoomUtils.getRoomHostId(roomId);
        GameEventOrganizerVO.RoomItemVO roomItemVO = new GameEventOrganizerVO.RoomItemVO();
        int startTime = item.getStartTime();
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        if (null == roomData) {
            return null;
        }
        roomItemVO.setRoomId(roomId);
        roomItemVO.setRoomEventCover(item.getEventCoverUrl());
        roomItemVO.setRoomEventState(now > startTime ? 1 : 0);
        roomItemVO.setRoomEventStartTime(startTime);
        roomItemVO.setRoomEventName(item.getName());
        roomItemVO.setRoomEventSubCount(item.getSubNum());
        String countryCode = ActorUtils.getCountryCode(roomData.getCountry());
        roomItemVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
        List<String> stringList = EVENT_TYPE_MAP.get(item.getType());
        if (stringList != null) {
            roomItemVO.setRoomTypeIcon(stringList.get(2));
            roomItemVO.setRoomTypeName(slang == SLangType.ARABIC ? stringList.get(1) : stringList.get(0));
        }
        if (!hostUid.equals(uid)) {
            RoomEventSubData subData = roomEventSubDao.selectOne(item.getId(), uid);
            roomItemVO.setRoomEventSubState(subData == null ? 0 : 1);
        } else {
            roomItemVO.setRoomEventSubState(1);
        }
        roomItemVO.setRoomEventId(item.getId());
        return roomItemVO;
    }

    public GameEventOrganizerVO eventList(String activityId, String uid, String searchRid, int slang) {
        GameEventOrganizerVO vo = new GameEventOrganizerVO();
        int now = DateHelper.getNowSeconds();
        Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
        if (!StringUtils.isEmpty(searchRid)) {
            List<GameEventOrganizerVO.RoomItemVO> searchList = new ArrayList<>(); // event-search房间活动
            ActorData actorData = actorDao.getActorByStrRid(searchRid);
            if (actorData == null) {
                throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
            }
            String searchRoomId = RoomUtils.formatRoomId(actorData.getUid());
            List<RoomEventData> roomEventDataList = roomEventDao.getRoomNotEndEventByAsc(searchRoomId, now);
            int pos = 0;
            for (RoomEventData item : roomEventDataList) {
                GameEventOrganizerVO.RoomItemVO roomItemVO = fillRoomItemVO(item, now, commonFlagConfigMap, uid, slang);
                if (roomItemVO == null) {
                    continue;
                }
                pos++;
                if (pos > 20) {
                    break;
                }
                searchList.add(roomItemVO);
            }
            vo.setSearchEventList(searchList);
            return vo;
        }

        List<GameEventOrganizerVO.RoomItemVO> newShiningList = new ArrayList<>(); // event-new房间活动
        List<GameEventOrganizerVO.RoomItemVO> hotEventList = new ArrayList<>(); // event-popular房间活动

        // new 榜单
        List<RoomEventData> roomEventDataList = roomEventDao.getRoomEventList(now);
        int pos = 0;
        for (RoomEventData item : roomEventDataList) {
            GameEventOrganizerVO.RoomItemVO roomItemVO = fillRoomItemVO(item, now, commonFlagConfigMap, uid, slang);
            if (roomItemVO == null) {
                continue;
            }
            pos++;
            if (pos > 20) {
                break;
            }
            newShiningList.add(roomItemVO);
        }
//        List<GameEventOrganizerVO.RoomItemVO> retNewList = newShiningList.stream()
//                .sorted(eventStartTimeAsc.thenComparing(eventSubCountDesc))
//                .limit(50).collect(Collectors.toList());

        // hot 榜单
        String typeEventIdKey = getZetEventIdTypeKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(typeEventIdKey, 50);
        int eventRank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String eventId = entry.getKey();
            Integer point = entry.getValue();
            RoomEventData roomEventData = cacheDataService.roomEventByEventId(Integer.parseInt(eventId));
            if (roomEventData == null) {
                continue;
            }
            String roomId = roomEventData.getRoomId();
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            if (null == roomData) {
                continue;
            }
            String countryCode = ActorUtils.getCountryCode(roomData.getCountry());
            GameEventOrganizerVO.RoomItemVO roomItemVO = new GameEventOrganizerVO.RoomItemVO();
            roomItemVO.setRoomId(roomId);
            roomItemVO.setRoomName(roomData.getName());
            roomItemVO.setRoomCover(ImageUrlGenerator.generateRoomUrl(roomData.getHead()));
            roomItemVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
            roomItemVO.setRoomEventName(roomEventData.getName());
            roomItemVO.setPoints(point);
            roomItemVO.setRank(eventRank);
            hotEventList.add(roomItemVO);
            eventRank++;
        }
        vo.setNewShiningList(newShiningList);
        vo.setHotEventList(hotEventList);
        return vo;
    }

    public GameEventOrganizerVO rankList(String activityId, String uid) {
        GameEventOrganizerVO vo = new GameEventOrganizerVO();
        Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
        // 房间榜单
        List<GameEventOrganizerVO.RankVO> totalRankList = new ArrayList<>(); // rank 总榜
        String typeIdKey = getZetTypeKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(typeIdKey, 50);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String roomId = entry.getKey();
            Integer point = entry.getValue();
            String hostUid = RoomUtils.getRoomHostId(roomId);
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            if (null == roomData) {
                continue;
            }
            ActorData actorData = actorDao.getActorDataFromCache(hostUid);
            String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
            GameEventOrganizerVO.RankVO rankVO = new GameEventOrganizerVO.RankVO();
            rankVO.setRoomId(roomId);
            rankVO.setHostName(actorData.getName());
            rankVO.setHostHead(ImageUrlGenerator.generateRoomUrl(actorData.getHead()));
            rankVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
            rankVO.setPoints(point);
            rankVO.setRank(rank);
            totalRankList.add(rankVO);
            rank++;
        }
        vo.setTotalRankList(totalRankList);
        return vo;
    }

    public GameEventOrganizerVO historyRecordList(String activityId, int type, String uid, int page, int slang) {
        if (!Q_ALL_LIST.contains(type) || page <= 0) {
            logger.info("not support type:{} page:{}", type, page);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String roomId = RoomUtils.formatRoomId(uid);
        List<GameEventOrganizerVO.HistoryRedisData> historyRedisDataList = getHistoryListPageRecord(activityId, roomId, type, page, slang);
        GameEventOrganizerVO vo = new GameEventOrganizerVO();
        vo.setHistoryRedisDataList(historyRedisDataList);
        return vo;
    }

    /**
     * 获取题目列表
     */
    public QuizQuestionVO getQuestionList(String uid, String activityId) {
        OtherRankingActivityData activityData = checkActivityTime(activityId);
        // 用户答题状态
        String now = getDay(uid);
        String detailUserKey = getUserHashDetailKey(activityId, now);
        GameEventOrganizerVO.MyGiftDetailInfo myGiftDetailInfo = cacheDataService.
                getGameEventOrganizerVOMyGiftDetailInfo(detailUserKey, now, uid);
        if (myGiftDetailInfo.getQuizState() == 1) {
            logger.info("uid:{} quizState:{} ", uid, myGiftDetailInfo.getQuizState());
            throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_QUESTION_ALREADY);
        }

        QuizQuestionVO vo = new QuizQuestionVO();
        List<QuizQuestionVO.QuestionVO> list = new ArrayList<>();
        int startTime = activityData.getStartTime();
        int testAddDays = activityCommonRedis.getCommonHashValue(getHashTestDayKey(activityId), uid);
        int page = (int) ((DateHelper.getNowSeconds() - startTime) / TimeUnit.DAYS.toSeconds(1) + 1 + testAddDays);
        List<QuestionData> questionList = questionDao.selectListPage(GID, page, 3);
        if (!CollectionUtils.isEmpty(questionList)) {
            for (QuestionData item : questionList) {
                QuizQuestionVO.QuestionVO questionVO = new QuizQuestionVO.QuestionVO();
                questionVO.setId(item.getId());
                questionVO.setPictureUrl(item.getPictureUrl());
                questionVO.setContent(item.getContent());
                Map<String, String> optionContentMap;
                String optionContent = item.getOptionContent();
                if (!StringUtils.isEmpty(optionContent)) {
                    optionContentMap = JSON.parseObject(optionContent, new TypeReference<HashMap<String, String>>() {
                    });
                } else {
                    optionContentMap = new HashMap<>(4);
                }
                questionVO.setOptionContent(optionContentMap);
                questionVO.setCorrectOption(item.getCorrectOption());
                list.add(questionVO);
            }
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 提交答题回答
     */
    public GameEventOrganizerVO submitAnswer(String activityId, String uid, QuizActivityDTO dto) {
        checkActivityTime(activityId);
        Map<Integer, String> quizAnswer = dto.getQuizAnswer();
        List<QuestionData> questionList = questionDao.selectList(GID);
        // 用户答题状态
        String now = getDay(uid);
        dto.setUid(uid);
        String detailUserKey = getUserHashDetailKey(activityId, now);
        GameEventOrganizerVO.MyGiftDetailInfo myGiftDetailInfo = cacheDataService.
                getGameEventOrganizerVOMyGiftDetailInfo(detailUserKey, now, uid);
        if (myGiftDetailInfo.getQuizState() == 1) {
            logger.info("uid:{} quizState:{} ", dto.getUid(), myGiftDetailInfo.getQuizState());
            throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_QUESTION_ALREADY);
        }
        Map<Integer, String> correctOptionMap;
        if (!CollectionUtils.isEmpty(questionList)) {
            correctOptionMap = questionList.stream().collect(Collectors.toMap(QuestionData::getId, QuestionData::getCorrectOption));
        } else {
            correctOptionMap = new HashMap<>(4);
        }
        if (CollectionUtils.isEmpty(quizAnswer)) {
            quizAnswer = new HashMap<>(4);
        }
        int correctCount = 0;
        for (Map.Entry<Integer, String> entry : quizAnswer.entrySet()) {
            if (!correctOptionMap.containsKey(entry.getKey())) {
                continue;
            }
            String correctOption = correctOptionMap.get(entry.getKey());
            if (Objects.equals(entry.getValue(), correctOption)) {
                correctCount++;
            }
        }
        GameEventOrganizerVO vo = new GameEventOrganizerVO();
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            myGiftDetailInfo.setQuizState(1);
            activityCommonRedis.setCommonHashData(detailUserKey, uid, JSONObject.toJSONString(myGiftDetailInfo));
        }
        if (correctCount == 3) {
            Set<String> keySet = new HashSet<>(Arrays.asList(CORRECT_ALL_RES_KEY));
            vo.setResourceKeyDataList(resourceKeyConfigDao.findListByKeys(new HashSet<>(keySet)));
            handleRes(dto.getUid(), CORRECT_ALL_RES_KEY, 201);
            return vo;
        } else {
            logger.info("correctCount lt 3  uid:{} correctCount:{}", dto.getUid(), correctCount);
            throw new CommonH5Exception(ActivityHttpCode.GAME_EVENT_QUESTION_FAILED);
        }
    }

    public OtherRankConfigVO testUidDay(int cmd, String uid, int addDays, int addValue) {
        if (ServerConfig.isProduct()) {
            throw new CommonH5Exception(ActivityHttpCode.AUTH_ERROR);
        }
        OtherRankConfigVO otherRankConfigVO = new OtherRankConfigVO();
        if (cmd == 1) {
            // 设置偏移天数
            activityCommonRedis.setCommonHashData(getHashTestDayKey(null), uid, String.valueOf(addDays));
            otherRankConfigVO.setScore(addDays);
        } else if (cmd == 2) {
            // 查询偏移天数
            int add = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
            otherRankConfigVO.setScore(add);
        } else if (cmd == 3) {
            //  执行当天的定时任务
        } else if (cmd == 4) {
            CommonMqTopicData mqData = new CommonMqTopicData();
            mqData.setRoomId(RoomUtils.formatRoomId(uid));
            mqData.setUid(uid);
            mqData.setValue(addValue);
            mqData.setItem("game_event_test");
            syncAddHandle(mqData.getRoomId(), -1, mqData);
        }
        return otherRankConfigVO;
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        if (RoomUtils.isGameRoom(roomId)) {
            return;
        }
        if (item.equals(CommonMqTaskConstant.ON_MIC_TIME)) {
            syncAddHandle(roomId, MIC_TYPE, data);
        } else if (item.equals(CommonMqTaskConstant.SUB_ROOM_EVENT)) {
            syncAddHandle(roomId, SUB_TYPE, data);
        } else if (item.equals(CommonMqTaskConstant.CREATE_ROOM_EVENT)) {
            syncAddHandle(roomId, CREATE_EVENT_TYPE, data);
        } else if (item.equals(CommonMqTaskConstant.WATCH_VIDEO_TIME)) {
            syncAddHandle(roomId, WATCH_VIDEO_TYPE, data);
        } else if (item.equals(CommonMqTaskConstant.CREATE_VOTE)) {
            syncAddHandle(roomId, VOTE_TYPE, data);
        } else if (item.equals(CommonMqTaskConstant.PLAY_DOMINO)) {
            syncAddHandle(roomId, PLAY_DOMINO_TYPE, data);
        }
    }


    private String checkAc(String roomId, int type, CommonMqTopicData mqData) {
        String eventId = null;
        if ((type == MIC_TYPE || type == SUB_TYPE) && RoomUtils.isHomeowner(mqData.getUid(), roomId)) {
//            logger.info("user is room home owner roomId:{} type:{}", roomId, type);
            // 在自己房间上麦或者订阅自己房间的活动不处理
            return null;
        }
        if (mqData != null && (CommonMqTaskConstant.WATCH_VIDEO_TIME.equals(mqData.getItem()))) {
            if (!RoomUtils.isHomeowner(mqData.getUid(), roomId)) {
                // 非房主不统计
                return null;
            }
            int countPerFive = mqData.getValue() / WATCH_VIDEO_MIN_TIME;
            if (countPerFive == 0) {
                // 小于5分钟不统计
                return null;
            }
            mqData.setValue(countPerFive);
        }
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return null;
            }
        }


        if (mqData != null && (CommonMqTaskConstant.SUB_ROOM_EVENT.equals(mqData.getItem())
                || CommonMqTaskConstant.CREATE_ROOM_EVENT.equals(mqData.getItem()))) {
            // 订阅不需要再房间活动期间
            eventId = mqData.getHandleId();
        } else {
            eventId = cacheDataService.isRunRoomEvent(roomId);
            if (eventId == null) {
                // 房间没有进行中的房间活动
                return null;
            }
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return null;
        }
        return eventId;
    }

    private void syncAddHandle(String roomId, int type, CommonMqTopicData mqData) {
        String eventId = checkAc(roomId, type, mqData);
        if (eventId == null) {
            return;
        }
        String now = getDay(RoomUtils.getRoomHostId(roomId));
        String detailKey = getRoomHashDetailKey(null, now);
        String totalInfoKey = getHashTotalKey(null);
        String typeKey = getZetTypeKey(null);
        String detailUserKey = getUserHashDetailKey(null, now);
        String typeEventIdKey = getZetEventIdTypeKey(null);
        synchronized (stringPool.intern(getLocalEventRoomKey(roomId))) {
            GameEventOrganizerVO.DetailInfo detailInfo = cacheDataService.getGameEventOrganizerVOInfo(detailKey, now, roomId);
            GameEventOrganizerVO.ChallengeInfo challengeInfo = cacheDataService.getGameEventOrganizerVOChallengeInfo(totalInfoKey, roomId);
            int addScore = 0;
            int maxLimit = 0;
            int nowNum = 0;
            int taskAddScore = 0;// 指定任务增加积分
            ActivityCommonConfig.QueenConfig queenConfig;
            boolean isNew = false;
            String uid;
            String itemKey = "";
            if (type == GAME_TYPE) {
                // 游戏
                ActivityCommonConfig.QueenConfig config = getConfig(type, isNew);
                String gameId = mqData.getHandleId();
                maxLimit = config.getMaxNum();
                nowNum = detailInfo.getGameCount();
                if (!StringUtils.isEmpty(gameId) && !gameId.equals(detailInfo.getPreStartGameId()) && nowNum < maxLimit) {
                    addScore = config.getNumPerPoint();
                    taskAddScore = addScore;
                    detailInfo.setPreStartGameId(gameId);
                    detailInfo.setGameCount(nowNum + 1);
                    challengeInfo.setGameCount(challengeInfo.getGameCount() + 1);
                    itemKey = mqData.getItem();
                }

            } else if (type == MIC_TYPE) {
                // 上麦
                uid = mqData.getUid();
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                String tnId = actorData.getTn_id();
                if (StringUtils.isEmpty(tnId)) {
                    logger.info("tnId is empty mqData:{}", JSON.toJSONString(mqData));
                    return;
                }
                Set<String> micDevices = detailInfo.getMicDevices();
                if (!micDevices.contains(tnId)) {
                    isNew = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
                    nowNum = isNew ? detailInfo.getNewMicCount() : detailInfo.getOldMicCount();
                    ActivityCommonConfig.QueenConfig config = getConfig(type, isNew);
                    maxLimit = config.getMaxNum();
                    if (nowNum < maxLimit) {
                        addScore = config.getNumPerPoint();
                        micDevices.add(tnId);
                        if (isNew) {
                            detailInfo.setNewMicCount(nowNum + 1);
                        } else {
                            detailInfo.setOldMicCount(nowNum + 1);
                        }
                        challengeInfo.setMicCount(challengeInfo.getMicCount() + 1);
                        itemKey = uid;
                        syncUserHandle(uid, type, eventId, detailUserKey, now);
                    }
                }
            } else if (type == SUB_TYPE) {
                //订阅
                uid = mqData.getUid();
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                String tnId = actorData.getTn_id();
                if (StringUtils.isEmpty(tnId)) {
                    logger.info("tnId is empty mqData:{}", JSON.toJSONString(mqData));
                    return;
                }
                Set<String> subDevices = detailInfo.getSubDevices();
                if (!subDevices.contains(tnId)) {
                    isNew = ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId());
                    nowNum = isNew ? detailInfo.getNewSubCount() : detailInfo.getOldSubCount();
                    ActivityCommonConfig.QueenConfig config = getConfig(type, isNew);
                    maxLimit = config.getMaxNum();
                    if (nowNum < maxLimit) {
                        addScore = config.getNumPerPoint();
                        subDevices.add(tnId);
                        if (isNew) {
                            detailInfo.setNewSubCount(nowNum + 1);
                        } else {
                            detailInfo.setOldSubCount(nowNum + 1);
                        }
                        challengeInfo.setSubCount(challengeInfo.getSubCount() + 1);
                        itemKey = uid;
                        syncUserHandle(uid, type, eventId, detailUserKey, now);
                    }
                }

            } else if (type == CREATE_EVENT_TYPE) {
                //创建房间活动
                detailInfo.setEventCount(detailInfo.getEventCount() + 1);
                activityCommonRedis.setCommonHashData(detailKey, roomId, JSONObject.toJSONString(detailInfo));
            } else if (type == WATCH_VIDEO_TYPE) {
                ActivityCommonConfig.QueenConfig config = getConfig(type, isNew);
                nowNum = detailInfo.getVideoPoints();
                int add = mqData.getValue() * config.getNumPerPoint();// value已转换为每5分钟为1
                maxLimit = config.getMaxPoint();
                if (nowNum < maxLimit) {
                    addScore = Math.min(add, maxLimit - nowNum);
                    detailInfo.setVideoPoints(nowNum + addScore);
                    challengeInfo.setVideoPoint(challengeInfo.getVideoPoint() + addScore);
                }

            } else if (type == VOTE_TYPE) {
                ActivityCommonConfig.QueenConfig config = getConfig(type, isNew);
                maxLimit = config.getMaxNum();
                nowNum = detailInfo.getVoteCount();
                if (nowNum < maxLimit) {
                    addScore = config.getNumPerPoint();
                    taskAddScore = addScore;
                    detailInfo.setVoteCount(nowNum + 1);
                    challengeInfo.setVoteCount(challengeInfo.getVoteCount() + 1);
                    itemKey = mqData.getItem();
                }
            } else if (type == PLAY_DOMINO_TYPE) {
                // 多米乐
                ActivityCommonConfig.QueenConfig config = getConfig(type, isNew);
                String gameId = mqData.getHandleId();
                maxLimit = config.getMaxNum();
                nowNum = detailInfo.getDominoCount();
                if (!StringUtils.isEmpty(gameId) && !gameId.equals(detailInfo.getPreStartGameId()) && nowNum < maxLimit) {
                    addScore = config.getNumPerPoint();
                    taskAddScore = addScore;
                    detailInfo.setPreStartGameId(gameId);
                    detailInfo.setDominoCount(nowNum + 1);
                    challengeInfo.setGameCount(challengeInfo.getGameCount() + 1);
                    itemKey = mqData.getItem();
                }

            } else if (mqData.getItem().equals("game_event_test")) {
                //加积分
                addScore = mqData.getValue();
            }

            if (addScore > 0) {
                detailInfo.setDayPoint(detailInfo.getDayPoint() + addScore);
                challengeInfo.setPoint(challengeInfo.getPoint() + addScore);

                int oldScore = activityCommonRedis.getCommonZSetRankingScore(typeKey, roomId);
                int nowScore = activityCommonRedis.incrCommonZSetRankingScoreSimple(typeKey, roomId, addScore);

                int oldIndex = getIndexLevel(oldScore);
                int afterIndex = getIndexLevel(nowScore);

                if (afterIndex > oldIndex) {
                    Map<String, Integer> levelMap = challengeInfo.getLevelMapCollect();
                    for (int i = oldIndex + 1; i <= afterIndex; i++) {
                        String level = String.valueOf(i);
                        int state = levelMap.getOrDefault(level, 0);
                        if (state == 0) {
                            levelMap.put(level, 1);
                        }
                    }
                    logger.info("update level success add roomId:{} eventId:{} type:{} addScore:{} oldIndex:{} afterIndex:{} levelMap:{}",
                            roomId, eventId, type, addScore, oldIndex, afterIndex, levelMap);
                }

                activityCommonRedis.setCommonHashData(detailKey, roomId, JSONObject.toJSONString(detailInfo));
                activityCommonRedis.setCommonHashData(totalInfoKey, roomId, JSONObject.toJSONString(challengeInfo));
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeEventIdKey, eventId, addScore);
                leftPushAllHistoryList(roomId, type, addScore, itemKey);
                logger.info("success add roomId:{} eventId:{} type:{} addScore:{} itemKey:{}",
                        roomId, eventId, type, addScore, itemKey);
            }
        }
    }

    private void syncUserHandle(String uid, int type, String eventId, String detailUserKey, String now) {
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            GameEventOrganizerVO.MyGiftDetailInfo myGiftDetailInfo = cacheDataService.
                    getGameEventOrganizerVOMyGiftDetailInfo(detailUserKey, now, uid);
            boolean isAdd = false;
            if (type == MIC_TYPE) {
                Set<String> micEventIds = myGiftDetailInfo.getMicEventIds();
                if (!micEventIds.contains(eventId) && micEventIds.size() < USER_MIC_LIMIT) {
                    micEventIds.add(eventId);
                    if (micEventIds.size() == USER_MIC_LIMIT) {
                        myGiftDetailInfo.setMicState(1);
                    }
                    isAdd = true;
                }
            } else if (type == SUB_TYPE) {
                Set<String> subEventIds = myGiftDetailInfo.getSubEventIds();
                if (!subEventIds.contains(eventId) && subEventIds.size() < USER_SUB_LIMIT) {
                    subEventIds.add(eventId);
                    if (subEventIds.size() == USER_SUB_LIMIT) {
                        myGiftDetailInfo.setSubState(1);
                    }
                    isAdd = true;
                }
            }
            if (isAdd) {
                activityCommonRedis.setCommonHashData(detailUserKey, uid, JSONObject.toJSONString(myGiftDetailInfo));
            }
        }
    }

    private ActivityCommonConfig.QueenConfig getConfig(int type, boolean isNew) {
        ActivityCommonConfig.QueenConfig config;
        if (type == MIC_TYPE) {
            if (isNew) {
                Map<Integer, ActivityCommonConfig.QueenConfig> configMap = NEW_USER_TASK_LIST.stream()
                        .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
                config = configMap.get(2);
            } else {
                Map<Integer, ActivityCommonConfig.QueenConfig> configMap = OLD_USER_TASK_LIST.stream()
                        .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
                config = configMap.get(4);
            }
        } else if (type == SUB_TYPE) {
            if (isNew) {
                Map<Integer, ActivityCommonConfig.QueenConfig> configMap = NEW_USER_TASK_LIST.stream()
                        .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
                config = configMap.get(1);
            } else {
                Map<Integer, ActivityCommonConfig.QueenConfig> configMap = OLD_USER_TASK_LIST.stream()
                        .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
                config = configMap.get(3);
            }
        } else if (type == WATCH_VIDEO_TYPE) {
            Map<Integer, ActivityCommonConfig.QueenConfig> configMap = COMMON_TASK_LIST.stream()
                    .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
            config = configMap.get(5);
        } else if (type == VOTE_TYPE) {
            Map<Integer, ActivityCommonConfig.QueenConfig> configMap = COMMON_TASK_LIST.stream()
                    .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
            config = configMap.get(6);
        } else if (type == PLAY_DOMINO_TYPE) {
            Map<Integer, ActivityCommonConfig.QueenConfig> configMap = COMMON_TASK_LIST.stream()
                    .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
            config = configMap.get(7);
        } else {
            Map<Integer, ActivityCommonConfig.QueenConfig> configMap = GAME_TASK_LIST.stream()
                    .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
            config = configMap.get(0);
        }
        return config;
    }

    private void sendOfficialMsg(List<String> titleList, List<String> bodyList, String item) {
        ActorData actorData = actorDao.getActorDataFromCache(item);
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleList.get(1) : titleList.get(0);
        String body = slang == SLangType.ARABIC ? bodyList.get(1) : bodyList.get(0);
        commonOfficialMsg(item, "", 0, 0, "",
                title, body, ACTIVITY_URL);
    }


    private void asyncHandleGameEventRes(String aid, String level, boolean isMain) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                List<String> resList = LEVEL_RES_KEY_MAP.get(level);
                String resKey = isMain ? resList.get(0) : resList.get(1);
                if (StringUtils.isEmpty(resKey)) {
                    logger.info("resKey is empty asyncHandleGameEventRes fail aid:{} level:{}", aid, level);
                    return;
                }
                ActorData actorData = actorDao.getActorData(aid);
                String tnId = actorData.getTn_id();
                if (StringUtils.hasLength(tnId)) {
                    String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(null, level), tnId);
                    if (StringUtils.isEmpty(bindAid) || aid.equals(bindAid)) {
                        String eventTitle = ACTIVITY_TITLE_EN;
                        resourceKeyHandlerService.sendResourceData(aid, resKey,
                                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                        activityCommonRedis.setCommonHashData(getHashBindKey(null, level), tnId, aid);
                        logger.info("handleGameEventRes success aid:{} resKey:{} level:{} tnId:{}", aid, resKey, level, tnId);
                    } else {
                        logger.info("handleGameEventRes fail aid:{} resKey:{} level:{} tnId:{} bindAid:{}", aid, resKey, level, tnId, bindAid);
                    }
                } else {
                    resourceKeyHandlerService.sendResourceData(aid, resKey,
                            ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);

                    logger.info("handleGameEventRes success tnId is empty aid:{} resKey:{} level:{} tnId:{}", aid, resKey, level, tnId);
                }
            }
        });
    }


    // 下发榜单奖励
    public void distributionRanking() {
        try {
            int length = 10;
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getZetTypeKey(null), length);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = RoomUtils.getRoomHostId(entry.getKey());
                String resKey = RANK_KEY_LIST.get(rank - 1);
                handleRes(aid, resKey, 102);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, int atype) {
        String eventTitle = EVENT_TITLE_MAP.getOrDefault(atype, "");
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
    }

    public void leftPushAllHistoryList(String roomId, int type, int change, String itemKey) {
        String key = getHistoryListKey(null, roomId, type);
        GameEventOrganizerVO.HistoryRedisData historyRedisData = new GameEventOrganizerVO.HistoryRedisData();
        historyRedisData.setChange(change);
        historyRedisData.setKey(itemKey);
        historyRedisData.setCtime(DateHelper.getNowSeconds());
        List<String> strList = new ArrayList<>();
        strList.add(JSONObject.toJSONString(historyRedisData));
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(key, strList, HISTORY_MAX_SIZE);
    }


    public List<GameEventOrganizerVO.HistoryRedisData> getHistoryListPageRecord(String activityId, String roomId,
                                                                                int type, int page, int slang) {
        try {
            int start = (page - 1) * HISTORY_PAGE_SIZE;
            int end = page * HISTORY_PAGE_SIZE;
            String key = getHistoryListKey(activityId, roomId, type);
            List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
            List<GameEventOrganizerVO.HistoryRedisData> resultList = new ArrayList<>();
            for (String json : jsonList) {
                GameEventOrganizerVO.HistoryRedisData rewardData = JSON.parseObject(json, GameEventOrganizerVO.HistoryRedisData.class);
                fillHistoryRedisData(rewardData, type, slang);
                resultList.add(rewardData);
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getHistoryListPageRecord error", e);
            return Collections.emptyList();
        }
    }

    private void fillHistoryRedisData(GameEventOrganizerVO.HistoryRedisData rewardData, int type, int slang) {
        String toKey = "";
        if (type == GAME_TYPE) {
            List<String> srcList = HISTORY_GAME_MAP.get(rewardData.getKey());
            toKey = slang == SLangType.ENGLISH ? srcList.get(0) : srcList.get(1);
        } else if (type == MIC_TYPE || type == SUB_TYPE) {
            toKey = actorDao.getActorDataFromCache(rewardData.getKey()).getName();
        } else {

        }
        rewardData.setKey(toKey);
    }


    private void doJoinReportEvent(String uid) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(ACTIVITY_TITLE_EN);
        eventReport.track(new EventDTO(event));
    }

    private void doDoneQueenEvent(String uid, int type) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(type);
        eventReport.track(new EventDTO(event));
    }

    private void doReportDetailEvent(String uid, int type, int changed, String changedDesc) {
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(21);
        event.setScore_changed_detail(type);
        event.setScore_changed_desc(changed > 0 ? changedDesc : "daily_reward");
        eventReport.track(new EventDTO(event));
    }

    private String getDay(String uid) {
        return getDay(uid, true);
    }

    private String getDay(String uid, boolean baseIsToday) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isNotProduct() || activityData.getAcNameEn().startsWith("test")) {
            return getTestDays(uid, baseIsToday);
        }
        return baseIsToday ? DateHelper.ARABIAN.formatDateInDay()
                : DateHelper.ARABIAN.getYesterdayStr(new Date());
    }


    private String getTestDays(String uid, boolean baseIsToday) {
        int addDays = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
        logger.info("test add uid:{} days:{}", uid, addDays);
        addDays = baseIsToday ? addDays : addDays - 1;
        return todayMinusDays(addDays);
    }

    /**
     * @param days
     * @return
     */
    private String todayMinusDays(int days) {
        days = -days; // -1为明天  1为昨天
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }

    private int getIndexLevel(int score) {
        List<Integer> tempLevelNumList = new ArrayList<>(SCORE_LEVEL_LIST);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

    private String getLocalEventRoomKey(String roomId) {
        return "lock:game_event:room:" + roomId;
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:game_event:user:" + uid;
    }

    private String getRoomHashDetailKey(String activityId, String dayStr) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_event:room:detail:" + dayStr;
    }

    private String getUserHashDetailKey(String activityId, String dayStr) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_event:user:detail:" + dayStr;
    }

    private String getHashTotalKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_event:total";
    }

    private String getZetTypeKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_event";
    }

    private String getZetEventIdTypeKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":game_event:id";
    }


    private String getHistoryListKey(String activityId, String roomId, int type) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return String.format("game_event:history:%s:%s:%s", aId, roomId, type);
    }


    /**
     * type 场景 1-4 为房间level奖励 ,99为用户订阅3场奖励，100为用户参与3场奖励
     * 奖励
     * 设备绑定账号
     *
     * @return
     */
    private String getHashBindKey(String activityId, String type) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":device:bind:level:" + type;
    }

    private String getHashTestDayKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":test:uid:day";
    }


}
