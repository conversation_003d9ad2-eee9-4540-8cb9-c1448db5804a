package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GetActivitySpecialItemsRecordEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.CarromGameVO;
import com.quhong.data.vo.LlluminateYouStarVO;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.SudGameConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.RoomUtils;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 克罗姆游戏活动
 */
@Service
public class CarromGameService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(CarromGameService.class);
    private static final String ACTIVITY_TITLE_EN = "Jackaroo game launched";
    private static final String ACTIVITY_TITLE_AR = "إطلاق لعبة جاكارو";
    private static final String ACTIVITY_ID = "6791f5ee71512ca818c1ede9";

    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/carrom_game/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/carrom_game/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/op_1721374572_shanyao_FJRK_a.png";
    private static final int MAX_COLLECT_DAY = 7;
    private static final String CARROM_TOTAL = "CarromTotal";
    private static final int EXTRA_TIME = 7 * 86400;

    public static final Map<String, List<String>> DAY_RES_KEY_MAP = new HashMap<String, List<String>>() {
        {
            put("CarromDay1", Arrays.asList("CarromDay1Main", "CarromDay1Other"));
            put("CarromDay2", Arrays.asList("CarromDay2Main", "CarromDay2Other"));
            put("CarromDay3", Arrays.asList("CarromDay3Main", "CarromDay3Other"));
            put("CarromDay4", Arrays.asList("CarromDay4Main", "CarromDay4Other"));
            put("CarromDay5", Arrays.asList("CarromDay5Main", "CarromDay5Other"));
            put("CarromDay6", Arrays.asList("CarromDay6Main", "CarromDay6Other"));
            put("CarromDay7", Arrays.asList("CarromDay7Main", "CarromDay7Other"));
            put(CARROM_TOTAL, Arrays.asList("CarromTotalMain", "CarromTotalOther"));
        }
    };
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private EventReport eventReport;

    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getCurrentTimeKey(String activityId) {
        return String.format("currentTime:%s", activityId);
    }

    private String getCurrentDay(String activityId) {
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }

    private int getCurrentTime(String activityId) {
        return DateHelper.getNowSeconds();
        // return activityCommonRedis.getCommonStrScore(getCurrentTimeKey(activityId));
    }


    public CarromGameVO carromGameInfo(String uid) {
//        checkActivityTime(ACTIVITY_ID);
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.info("not find uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        CarromGameVO vo = new CarromGameVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        int endTime = activityData.getEndTime() - 10; // 偏移10秒为活动的最后一天
        String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
        String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
        String now = getCurrentDay(ACTIVITY_ID);
        int nowTime = getCurrentTime(ACTIVITY_ID);
        logger.info("acStart:{} now:{} nowTime:{} acEnd:{}", acStart, now, nowTime, acEnd);
        List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
        List<CarromGameVO.DetailVO> detailListVO = new ArrayList<>();
        CarromGameVO.RedisDetail redisDetail =
                cacheDataService.getCarromGameVORedisDetail(getHashDetailKey(), uid);
        int dayNum = 0;
        for (DayTimeData item : dayTimeDataList) {
            dayNum++;
            String day = item.getDate();
            int dayStartTime = item.getTime();
            CarromGameVO.DetailVO detailVO = new CarromGameVO.DetailVO();
            detailVO.setDay(day);
            detailVO.setDayNum(dayNum);
            if (nowTime >= dayStartTime) {
                detailVO.setStatus(redisDetail.getDayMapCollect().getOrDefault(day, 0));
                detailVO.setPlayCount(redisDetail.getDayMapPlayCount().getOrDefault(day, 0));
            } else {
                detailVO.setStatus(-1);
                detailVO.setPlayCount(0);
            }
            detailListVO.add(detailVO);
        }
        vo.setDetailListVO(detailListVO);
        vo.setTotalStatus(redisDetail.getTotalStatus());
        return vo;
    }

    public CarromGameVO carromGameCollect(String uid, int dayNum) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        // int curTime = DateHelper.getNowSeconds();
        int curTime = getCurrentTime(ACTIVITY_ID);
        if (curTime < startTime || curTime > endTime + EXTRA_TIME) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
//        checkActivityTime(ACTIVITY_ID);
        if (dayNum < 0 || dayNum > 7) {
            logger.info("collect invalid uid:{} dayNum:{}", uid, dayNum);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.info("not find uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        String dayKey = getDayKey(dayNum);
        String tnId = actorData.getTn_id();
        String dayStr = getDayStrByDayNum(dayNum);
        String detailKey = getHashDetailKey();
        String nowDayStr = getCurrentDay(ACTIVITY_ID);
        CarromGameVO vo = new CarromGameVO();

        CarromGameVO.RedisDetail redisDetail =
                cacheDataService.getCarromGameVORedisDetail(detailKey, uid);
        if (dayNum == 0) {
            if (redisDetail.getDayMapCollect().size() < MAX_COLLECT_DAY) {
                logger.info("任务未完成无法领取 uid:{}", uid);
                throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
            } else {
                if (redisDetail.getTotalStatus() != 1) {
                    redisDetail.setTotalStatus(1);
                    activityCommonRedis.setCommonHashData(detailKey, uid, JSONObject.toJSONString(redisDetail));
                    fillRetReward(uid, tnId, vo, dayKey);
                } else {
                    logger.info("任务已经领取 uid:{}", uid);
                    throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
                }
            }
        } else {
            int playCount = redisDetail.getDayMapPlayCount().getOrDefault(dayStr, 0);
            int status = redisDetail.getDayMapCollect().getOrDefault(dayStr, 0);
            if (playCount >= 3 && status == 1) {
                redisDetail.getDayMapCollect().put(dayStr, 2);
                activityCommonRedis.setCommonHashData(detailKey, uid, JSONObject.toJSONString(redisDetail));
                fillRetReward(uid, tnId, vo, dayKey);
            } else {
                if (status == 0 && !dayStr.equals(nowDayStr)) {
                    logger.info("任务已结束 uid:{} dayStr:{} nowDayStr:{}", uid, dayStr, nowDayStr);
                    throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
                }
                if (status == 2) {
                    logger.info("任务已经领取 uid:{} dayStr:{}", uid, dayStr);
                } else {
                    logger.info("任务未完成 uid:{} dayStr:{}", uid, dayStr);
                }
                throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
            }
        }

        logger.info("collect success dayKey:{} uid:{} dayNum:{} tnId:{} dayStr:{}",
                dayKey, uid, dayNum, tnId, dayStr);

        return vo;
    }


    private void fillRetReward(String uid, String tnId, CarromGameVO vo, String dayKey) {
        int tnCount = StringUtils.isEmpty(tnId) ? 0 :
                activityCommonRedis.incCommonHashNum(getHashDeviceAccountCount(dayKey), tnId, 1);
        String resourceKey;
        int maxNum = CARROM_TOTAL.equals(dayKey) ? 1 : 3;
        if (tnCount <= maxNum) {
            resourceKey = DAY_RES_KEY_MAP.get(dayKey).get(0);
        } else {
            resourceKey = DAY_RES_KEY_MAP.get(dayKey).get(1);
        }
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
        if (handleRes(uid, resourceKey)) {
            List<LlluminateYouStarVO.ResourceDataVO> resultVOList = new ArrayList<>();
            resourceKeyConfigData.getResourceMetaList().forEach(item -> {
                LlluminateYouStarVO.ResourceDataVO itemVO = new LlluminateYouStarVO.ResourceDataVO();
                BeanUtils.copyProperties(item, itemVO);
                resultVOList.add(itemVO);
            });
            vo.setRetList(resultVOList);
        }
    }

    public void handleUserScore(CommonMqTopicData mqData) {
        if (null == otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID)) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        if (mqData.getValue() != SudGameConstant.JACKAROO_GAME) {
            return;
        }
        String uid = mqData.getUid();

        // if(!whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)){
        //     return;
        // }

        String now = getCurrentDay(ACTIVITY_ID);
        String detailKey = getHashDetailKey();
        synchronized (stringPool.intern("lock:carrom_game:" + uid)) {
            CarromGameVO.RedisDetail detailVO = cacheDataService.getCarromGameVORedisDetail(detailKey, uid);
            Map<String, Integer> dayMapPlayCount = detailVO.getDayMapPlayCount();
            if (!dayMapPlayCount.containsKey(now)) {
                dayMapPlayCount.put(now, 1);
            } else {
                dayMapPlayCount.put(now, dayMapPlayCount.getOrDefault(now, 0) + 1);
            }
            int playCount = dayMapPlayCount.getOrDefault(now, 0);
            if (playCount <= 3) {
                if (playCount == 3) {
                    Map<String, Integer> dayMapCollect = detailVO.getDayMapCollect();
                    dayMapCollect.put(now, 1);
                    if (dayMapCollect.size() == MAX_COLLECT_DAY) {
                        detailVO.setTotalStatus(2);
                    }
                }
                activityCommonRedis.setCommonHashData(detailKey, uid, JSONObject.toJSONString(detailVO));
            } else {
                dayMapPlayCount.put(now, dayMapPlayCount.getOrDefault(now, 0) - 1);
            }
            logger.info("success play carrom game uid:{} playCount:{}", uid, playCount);
        }
    }


    private boolean handleRes(String uid, String resKey) {
        logger.info("uid:{} resKey:{}", uid, resKey);
        return resourceKeyHandlerService.sendResourceData(uid, resKey,
                ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_TITLE_EN, ACTIVITY_URL, "");
    }


    private void doAddScoreEvent(String roomId, String aid, int score, int actionType) {
        String hostUid = RoomUtils.getRoomHostId(roomId);
        GetActivitySpecialItemsRecordEvent event = new GetActivitySpecialItemsRecordEvent();
        event.setUid(hostUid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setRoomid(roomId);
        event.setActivity_special_items_resource(String.valueOf(actionType));
        event.setGet_activity_special_items_nums(score);
        event.setFrom_uid(aid);
        eventReport.track(new EventDTO(event));
    }


    private String getDayStrByDayNum(int dayNum) {
        if (dayNum == 0) {
            return "totalDay";
        }
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
        int endTime = activityData.getEndTime() - 10; // 偏移10秒为活动的最后一天
        String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
        String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
        List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
        String date = dayTimeDataList.get(dayNum - 1).getDate();
        logger.info("dayNum:{} date:{} dayTimeDataList.size:{}", dayNum, date, dayTimeDataList.size());
        return date;
    }

    private String getHashDetailKey() {
        return ACTIVITY_ID + ":detail";
    }

    /**
     * 领取设备账号统计
     *
     * @param dayKey 取值 CarromDay1-CarromDay7以及CarromTotal
     * @return
     */
    private String getHashDeviceAccountCount(String dayKey) {
        return ACTIVITY_ID + "account:day:" + dayKey;
    }

    private String getDayKey(int dayNum) {
        return dayNum == 0 ? CARROM_TOTAL : "CarromDay" + dayNum;
    }
}
