package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityApplicationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigV2VO;
import com.quhong.data.vo.TalentHostVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.MsgType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.handler.ActivityHandler;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TalentHostService extends OtherActivityService implements ActivityHandler {


    private static final Logger logger = LoggerFactory.getLogger(TalentHostService.class);
    private static final String SIGN_ACTIVITY_ID = "673591771347cbdd5c0e0e84";
    public static final String ACTIVITY_ID = "673591771347cbdd5c0e0e85";
    private static final String TALENT_HOST_TITLE_EN = "theme room competition";
    private static final String FIRST_ENTRY = "firstEntry";
    private static final Integer ORDER_GIFT_ID = ServerConfig.isProduct() ? 1015 : 110;
    private static final String TALENT_SELECT = "talentSelect";   // 0: 未选择 1:诗歌 2:话题
    private static final Integer RECORD_PAGE_SIZE = 30;
    private static final List<Integer> TALENT_SELECT_LIST = Arrays.asList(1, 2);
    private static final List<String> TALENT_RANK_KEY_LIST = Arrays.asList("talentHostPoemPopular", "talentHostPoemCharm", "talentHostTopicPopular", "talentHostTopicCharm", "talentHostDefend");
    private static final List<String> POEM_POPULAR_RES_LIST = Arrays.asList("talentHostPoemPopularTop1-3", "talentHostPoemPopularTop4-10", "talentHostPoemPopularTop11-20");
    private static final List<String> POEM_CHARM_RES_LIST = Arrays.asList("talentHostPoemCharmTop1-3", "talentHostPoemCharmTop4-10", "talentHostPoemCharmTop11-20");
    private static final List<String> TOPIC_POPULAR_RES_LIST = Arrays.asList("talentHostTopicPopularTop1-3", "talentHostTopicPopularTop4-10", "talentHostTopicPopularTop11-20");
    private static final List<String> TOPIC_CHARM_RES_LIST = Arrays.asList("talentHostTopicCharmTop1-3", "talentHostTopicCharmTop4-10", "talentHostTopicCharmTop11-20");
    private static final List<String> DEFEND_RES_LIST = Arrays.asList("talentHostDefendTop1-3", "talentHostDefendTop4-10", "talentHostDefendTop11-20");
    private static final List<TaskConfigVO> POPULAR_TASK_LIST = new ArrayList<>();  // 人气任务
    private static final List<TaskConfigVO> HOST_TASK_LIST = new ArrayList<>();  // 房主任务
    private static final List<TaskConfigVO> DEFEND_TASK_LIST = new ArrayList<>();
    private static final String RECEIVE_GIFT_ITEM = "receive_gift";
    private static final String DEFEND_SIGN_TASK = "sign_task";
    private static final String DEFEND_SEND_GIFT = "send_gift";
    private static final List<String> TASK_ITEM_LIST = Arrays.asList(CommonMqTaskConstant.ON_MIC_TIME, CommonMqTaskConstant.INVITE_USER_ACTION,
            CommonMqTaskConstant.FOLLOW_ROOM, CommonMqTaskConstant.ADD_FRIEND, CommonMqTaskConstant.ENTER_ROOM, RECEIVE_GIFT_ITEM, DEFEND_SIGN_TASK, DEFEND_SEND_GIFT);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        POPULAR_TASK_LIST.add(new TaskConfigVO(100, 0, "talentHostPopularLevel1", "", "", "", null, null, null, null, "talentHostPopularLevel1"));
        POPULAR_TASK_LIST.add(new TaskConfigVO(300, 0, "talentHostPopularLevel2", "", "", "", null, null, null, null, "talentHostPopularLevel2"));
        POPULAR_TASK_LIST.add(new TaskConfigVO(600, 0, "talentHostPopularLevel3", "", "", "", null, null, null, null, "talentHostPopularLevel3"));
        POPULAR_TASK_LIST.add(new TaskConfigVO(1000, 0, "talentHostPopularLevel4", "", "", "", null, null, null, null, "talentHostPopularLevel4"));
        POPULAR_TASK_LIST.add(new TaskConfigVO(1500, 0, "talentHostPopularLevel5", "", "", "", null, null, null, null, "talentHostPopularLevel5"));
        POPULAR_TASK_LIST.add(new TaskConfigVO(2500, 0, "talentHostPopularLevel6", "", "", "", null, null, null, null, "talentHostPopularLevel6"));
        POPULAR_TASK_LIST.add(new TaskConfigVO(4000, 0, "talentHostPopularLevel7", "", "", "", null, null, null, null, "talentHostPopularLevel7"));

        HOST_TASK_LIST.add(new TaskConfigVO(60, 0, "on_mic_time", "", "", "ابدأ البث باستخدام علامات الشعر أو المواضيع وكن على الميكروفون لمدة 60 دقيقة", null, null, null, null, "talentHostTaskReward1"));
        HOST_TASK_LIST.add(new TaskConfigVO(3, 0, "invite_user_action", "", "", "إرسال تطبيقات دعوة الميكروفون إلى 3 مستخدمًا جديدًا في غرفة الصوت الشخصية \n (لا يتم تضمين المستخدمين الجدد الذين يقومون بتسجيل الدخول إلى الميكروفون في الحساب)", null, null, null, null, "talentHostTaskReward2"));
        HOST_TASK_LIST.add(new TaskConfigVO(3, 0, "follow_room", "", "", " إضافة 3 من المستخدمين المتابعين الجدد إلى غرفة الصوت الشخصية", null, null, null, null, "talentHostTaskReward3"));
        HOST_TASK_LIST.add(new TaskConfigVO(500, 0, "receive_gift", "", "", "هدية شخصية من الماسات 500", null, null, null, null, "talentHostTaskReward4"));

        DEFEND_TASK_LIST.add(new TaskConfigVO(1, 0, "sign_task", "", "", "تسجيل الدخول للنشاط", null, null, null, null, "talentHostDefendTaskReward1"));
        DEFEND_TASK_LIST.add(new TaskConfigVO(5, 0, "enter_room", "", "", "أدخل 5 غرف المواهب", null, null, null, null, "talentHostDefendTaskReward2"));
        DEFEND_TASK_LIST.add(new TaskConfigVO(5, 0, "defend_follow_room", "", "", "اتبع 5 غرف المواهب", null, null, null, null, "talentHostDefendTaskReward3"));
        DEFEND_TASK_LIST.add(new TaskConfigVO(3, 0, "add_friend", "", "", "كن صديقًا لـ 3 من مضيف الموهوبين \n (الموافقة المتبادلة على إضافة الأصدقاء)", null, null, null, null, "talentHostDefendTaskReward4"));
        DEFEND_TASK_LIST.add(new TaskConfigVO(60, 0, "send_gift", "", "", "أرسل هدية مخصصة [مذهلة] *60 إلى صاحب غرفة المواهب \n (يجب على مضيف دعم التسجيل ليتم احتسابه)", null, null, null, null, "talentHostDefendTaskReward5"));
    }

    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private EventReport eventReport;

    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("talentHost:%s:%s", activityId, uid);
    }

    private String getDailyHashActivityId(String activityId, String uid, String dateStr) {
        return String.format("talentHostDaily:%s:%s:%s", activityId, uid, dateStr);
    }

    private String getDailyInviteSetKey(String activityId, String uid, String dateStr) {
        return String.format("talentHostDailyInvite:%s:%s:%s", activityId, uid, dateStr);
    }

    private String getDeviceSelectSetKey(String activityId) {
        return String.format("deviceSelect:%s", activityId);
    }

    // 任务领取状态
    private String getTaskStatus(String taskKey) {
        return String.format("status:%s", taskKey);
    }

    private String getTaskInnerNumZSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("taskInnerNum:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    // 用户帮助完成用户数
    private String getUserHelpSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("userHelpSet:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    private String getTaskUserSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("taskUserSet:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }


    private String getCurrentDay(String activityId) {
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }

    public TalentHostVO talentHostConfig(String activityId, String uid) {
        TalentHostVO vo = new TalentHostVO();

        String currentDay = this.getCurrentDay(activityId);
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        OtherRankingActivityData signActivity = otherActivityService.getOtherRankingActivity(SIGN_ACTIVITY_ID);
        vo.setSignStartTime(signActivity.getStartTime());
        vo.setSignEndTime(signActivity.getEndTime());
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setUserName(actorData.getName());
        vo.setUserId(actorData.getStrRid());

        // 签到任务
        this.signTaskProcess(activityId, uid);

        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        // 首次进入判断
        vo.setFirstEntry(userDataMap.getOrDefault(FIRST_ENTRY, 1));
        if (vo.getFirstEntry() == 1) {
            activityCommonRedis.setCommonHashNum(hashActivityId, FIRST_ENTRY, 0);
        }

        // 赛道选择
        int talentSelect = userDataMap.getOrDefault(TALENT_SELECT, 0);
        // 自动选择赛道
        if (talentSelect == 0 && inActivityTime(activityId) && userLevelDao.getUserLevel(uid) >= 10) {
            talentSelect = talentHostAutoSelect(activityId, hashActivityId, uid);
        }

        vo.setTalentSelect(talentSelect);
        vo.setPopularNum(0);
        String popularRankKey = "";
        if (talentSelect > 0) {
            popularRankKey = talentSelect == 1 ? TALENT_RANK_KEY_LIST.get(0) : TALENT_RANK_KEY_LIST.get(2);
        }


        // 榜单配置
        List<TalentHostVO.TalentHostRankConfig> rankConfigList = new ArrayList<>();  // 排行榜
        for (String rankKey : TALENT_RANK_KEY_LIST) {
            List<OtherRankingListVO> rankList = new ArrayList<>();
            OtherRankingListVO myRank = new OtherRankingListVO();
            makeOtherRankingData(rankList, myRank, rankKey, uid, 50);
            TalentHostVO.TalentHostRankConfig talentHostRankConfig = new TalentHostVO.TalentHostRankConfig();
            talentHostRankConfig.setRankKey(rankKey);
            talentHostRankConfig.setRankList(rankList);
            talentHostRankConfig.setMyRank(myRank);
            rankConfigList.add(talentHostRankConfig);

            if (rankKey.equals(popularRankKey)) {
                vo.setPopularNum(myRank.getScore());
            }
        }
        vo.setRankConfigList(rankConfigList);

        // 设置房间人气值及钻石等级状态
        int popularNum = vo.getPopularNum();
        List<TaskConfigVO> popularTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : POPULAR_TASK_LIST) {
            int totalProcess = taskConfig.getTotalProcess();
            taskConfig.setCurrentProcess(Math.min(popularNum, totalProcess));
            int taskStatus = userDataMap.getOrDefault(this.getTaskStatus(taskConfig.getTaskKey()), 0);
            taskConfig.setStatus(taskStatus > 0 ? taskStatus : (popularNum >= totalProcess ? 1 : 0));
            popularTaskList.add(taskConfig);
        }
        vo.setPopularTaskList(popularTaskList);

        // 任务设置
        String dailyHashActivityId = getDailyHashActivityId(activityId, uid, currentDay);
        Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        List<TaskConfigVO> hostTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : HOST_TASK_LIST) {
            int currentProcess = userDailyMap.getOrDefault(taskConfig.getTaskKey(), 0);
            int totalProcess = taskConfig.getTotalProcess();
            taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
            int taskStatus = userDailyMap.getOrDefault(this.getTaskStatus(taskConfig.getTaskKey()), 0);
            taskConfig.setStatus(taskStatus > 0 ? taskStatus : (currentProcess >= totalProcess ? 1 : 0));
            hostTaskList.add(taskConfig);
        }
        vo.setHostTaskList(hostTaskList);

        List<TaskConfigVO> defendTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : DEFEND_TASK_LIST) {
            int currentProcess = userDailyMap.getOrDefault(taskConfig.getTaskKey(), 0);
            int totalProcess = taskConfig.getTotalProcess();
            taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
            int taskStatus = userDailyMap.getOrDefault(this.getTaskStatus(taskConfig.getTaskKey()), 0);
            taskConfig.setStatus(taskStatus > 0 ? taskStatus : (currentProcess >= totalProcess ? 1 : 0));
            defendTaskList.add(taskConfig);
        }
        vo.setDefendTaskList(defendTaskList);


        return vo;
    }

    public void signTaskProcess(String activityId, String uid) {
        // 进入活动页任务
        CommonMqTopicData data = new CommonMqTopicData();
        data.setItem(DEFEND_SIGN_TASK);
        data.setValue(1);
        data.setUid(uid);
        data.setAid(uid);
        this.taskMsgProcess(data);
    }


    // 赛道选择
    public void talentHostSelect(String activityId, String uid, int talentSelect) {
        if (!TALENT_SELECT_LIST.contains(talentSelect)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        checkActivityTime(SIGN_ACTIVITY_ID);

        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        int talentSelected = userDataMap.getOrDefault(TALENT_SELECT, 0);
        if (talentSelected > 0) {
            throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        String deviceSignSetKey = getDeviceSelectSetKey(activityId);
        int deviceStatus = activityCommonRedis.isCommonSetData(deviceSignSetKey, tnId);
        if (deviceStatus > 0) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        activityCommonRedis.setCommonHashNum(hashActivityId, TALENT_SELECT, talentSelect);
        activityCommonRedis.addCommonSetData(deviceSignSetKey, tnId);
        this.doJoinReportEvent(uid, "0");
    }

    // 随机赛道选择
    public int talentHostAutoSelect(String activityId, String hashActivityId, String uid) {

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String tnId = actorData.getTn_id();
        String deviceSignSetKey = getDeviceSelectSetKey(activityId);
        int deviceStatus = activityCommonRedis.isCommonSetData(deviceSignSetKey, tnId);
        if (deviceStatus > 0) {
            return 0;
        }

        Random random = new Random();
        int n = random.nextInt(TALENT_SELECT_LIST.size());
        int talentSelect = TALENT_SELECT_LIST.get(n);
        activityCommonRedis.setCommonHashNum(hashActivityId, TALENT_SELECT, talentSelect);
        activityCommonRedis.addCommonSetData(deviceSignSetKey, tnId);
        this.doJoinReportEvent(uid, "1");
        return talentSelect;
    }

    private void doJoinReportEvent(String uid, String joinType) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(TALENT_HOST_TITLE_EN);
        event.setScene_desc(joinType);
        eventReport.track(new EventDTO(event));
    }

    public TalentHostVO talentHostNewUserList(String activityId, String uid, int page) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int currentTime = DateHelper.getNowSeconds();
        int startTime = currentTime - 7 * 86400;
        List<MongoActorData> mongoActorDataList = actorDao.getNewUserActorList(startTime * 1000L, currentTime * 1000L, "", start, RECORD_PAGE_SIZE);
        TalentHostVO vo = new TalentHostVO();
        List<PrizeConfigV2VO> newUserList = new ArrayList<>();

        String currentDay = getCurrentDay(activityId);
        String inviteSetKey = getDailyInviteSetKey(activityId, uid, currentDay);
        for (MongoActorData mongoActorData : mongoActorDataList) {
            String aid = mongoActorData.get_id().toString();
            PrizeConfigV2VO prizeConfigV2VO = new PrizeConfigV2VO();
            prizeConfigV2VO.setUid(aid);
            prizeConfigV2VO.setUserName(mongoActorData.getName());
            prizeConfigV2VO.setUserHead(ImageUrlGenerator.generateRoomUserUrl(mongoActorData.getHead()));
            prizeConfigV2VO.setUserGender(mongoActorData.getFb_gender());
            prizeConfigV2VO.setStatus(activityCommonRedis.isCommonSetData(inviteSetKey, aid));
            prizeConfigV2VO.setCtime(ActorUtils.getRegDays(aid));
            newUserList.add(prizeConfigV2VO);
        }
        vo.setNewUserList(newUserList);
        if (mongoActorDataList.size() < RECORD_PAGE_SIZE) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    public void talentHostSendMsg(String activityId, String uid, String aid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String roomId = RoomUtils.formatRoomId(uid);

        MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
        if (mongoRoomData == null) {
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST.getCode(), "لم تقم بإنشاء غرفة");
        }

        if (uid.equals(aid)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لا أستطيع المشاركة مع نفسي");
        }

        String currentDay = getCurrentDay(activityId);
        String inviteSetKey = getDailyInviteSetKey(activityId, uid, currentDay);

        if (activityCommonRedis.isCommonSetData(inviteSetKey, aid) > 0) {
            return;
        }
        activityCommonRedis.addCommonSetData(inviteSetKey, aid);

        SendChatMsgDTO msgDto = new SendChatMsgDTO();
        msgDto.setUid(uid);
        msgDto.setAid(aid);
        msgDto.setMsgType(MsgType.SHARE_ROOM);
        msgDto.setOs(actorData.getIntOs());
        msgDto.setMsgBody("");
        msgDto.setSlang(actorData.getSlang());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("icon", ImageUrlGenerator.generateNormalUrl(mongoRoomData.getHead()));
        jsonObject.put("action", roomId);
        jsonObject.put("ridInfo", JSONObject.toJSONString(actorData.getRidData()));
        jsonObject.put("title", mongoRoomData.getName());
        jsonObject.put("type", 5);
        jsonObject.put("content", actorData.getStrRid());
        msgDto.setMsgInfo(jsonObject);
        msgDto.setVersioncode(actorData.getVersion_code());
        msgDto.setNew_versioncode(5);
        iMsgService.sendMsg(msgDto);
    }

    public void talentPopularTaskReward(String activityId, String uid, String taskKey) {
        checkActivityTime(activityId);
        synchronized (stringPool.intern("popularTaskReward" + uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
            int talentSelect = userDataMap.getOrDefault(TALENT_SELECT, 0);
            if (talentSelect <= 0) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            Map<String, TaskConfigVO> rewardConfigMap = POPULAR_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = rewardConfigMap.get(taskKey);
            if (taskConfig == null) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String statusKey = getTaskStatus(taskKey);
            int taskStatus = userDataMap.getOrDefault(statusKey, 0);
            if (taskStatus > 0) {
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            String popularRankKey = talentSelect == 1 ? TALENT_RANK_KEY_LIST.get(0) : TALENT_RANK_KEY_LIST.get(2);
            int currentProcess = activityCommonRedis.getCommonZSetRankingScore(popularRankKey, uid);
            int totalProcess = taskConfig.getTotalProcess();
            if (currentProcess < totalProcess) {
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), TALENT_HOST_TITLE_EN, TALENT_HOST_TITLE_EN, TALENT_HOST_TITLE_EN, "", "");
            activityCommonRedis.setCommonHashNum(hashActivityId, statusKey, 2);
        }
    }


    public void talentHostTaskReward(String activityId, String uid, int taskType, String taskKey) {
        checkActivityTime(activityId);
        List<TaskConfigVO> taskConfigList = taskType == 1 ? HOST_TASK_LIST : DEFEND_TASK_LIST;
        synchronized (stringPool.intern("taskReward" + uid)) {
            String currentDay = this.getCurrentDay(activityId);
            String hashDailyActivityId = getDailyHashActivityId(activityId, uid, currentDay);
            Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);

            Map<String, TaskConfigVO> rewardConfigMap = taskConfigList.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = rewardConfigMap.get(taskKey);
            if (taskConfig == null) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String statusKey = getTaskStatus(taskKey);
            int taskStatus = userDailyDataMap.getOrDefault(statusKey, 0);
            if (taskStatus > 0) {
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            int currentProcess = userDailyDataMap.getOrDefault(taskKey, 0);
            int totalProcess = taskConfig.getTotalProcess();
            if (currentProcess < totalProcess) {
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }
            String resKey = taskConfig.getResourceKey();
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), resKey, resKey, resKey, "", "");
            activityCommonRedis.setCommonHashNum(hashDailyActivityId, statusKey, 2);
        }
    }

    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        int giftPrice = giftData.getNumber() * giftData.getPrice();
        int totalPrice = giftPrice * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();
        String sendRankKey = TALENT_RANK_KEY_LIST.get(4);
        activityCommonRedis.incrCommonZSetRankingScore(sendRankKey, fromUid, totalPrice);

        for (String aid : giftData.getAid_list()) {
            String hashAidActivityId = getHashActivityId(activityId, aid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashAidActivityId);
            int talentSelect = userDataMap.getOrDefault(TALENT_SELECT, 0);
            if (talentSelect == 0 && userLevelDao.getUserLevel(aid) < 10) {
                continue;
            }

            talentSelect = talentSelect > 0 ? talentSelect : talentHostAutoSelect(activityId, hashAidActivityId, aid);
            if (talentSelect <= 0) {
                continue;
            }

            String rankKey = talentSelect == 1 ? TALENT_RANK_KEY_LIST.get(1) : TALENT_RANK_KEY_LIST.get(3);
            activityCommonRedis.incrCommonZSetRankingScore(rankKey, aid, giftPrice);

            CommonMqTopicData data = new CommonMqTopicData();
            data.setItem(RECEIVE_GIFT_ITEM);
            data.setValue(giftPrice);
            data.setUid(fromUid);
            data.setAid(aid);
            this.taskMsgProcess(data);
        }

    }

    // 统一加积分方法
    private void incActionUserPoint(TaskConfigVO taskConfig, int taskInnerNum, String uid, String aid, int incPoint, String dateStr, boolean setFlag, int limitNum, boolean newUserFlag) {
        String taskKey = taskConfig.getTaskKey();
        int maxNum = taskConfig.getTotalProcess();
        logger.info("incActionUserPoint uid:{}, aid:{}, taskKey:{}", uid, aid, taskKey);
        synchronized (stringPool.intern("incPoint" + uid)) {

            String dailyHashActivityId = this.getDailyHashActivityId(ACTIVITY_ID, uid, dateStr);
            Map<String, Integer> dailyUserDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int dailyGetPoint = dailyUserDataMap.getOrDefault(taskKey, 0);
            if (dailyGetPoint >= maxNum) {
                return;
            }

            // 新用户限制
            if (newUserFlag) {
                boolean newRegisterFlag = ActorUtils.isNewRegisterActor(aid, 7);
                if (!newRegisterFlag) {
                    return;
                }

                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (StringUtils.isEmpty(actorData.getFirstTnId())) {
                    return;
                }
            }

            // 任务要达到某一额度才算完成taskInnerNum
            if (taskInnerNum > 0) {
                String taskInnerNumKey = getTaskInnerNumZSetKey(ACTIVITY_ID, uid, taskKey, dateStr);
                int beforeNum = activityCommonRedis.getCommonZSetRankingScore(taskInnerNumKey, aid);
                if (beforeNum >= taskInnerNum) {
                    return;
                }
                int afterScore = activityCommonRedis.incrCommonZSetRankingScoreSimple(taskInnerNumKey, aid, incPoint);
                if (afterScore < taskInnerNum) {
                    return;
                }
            }

            // 帮助完成限制
            if (limitNum > 0) {
                String userHelpSetKey = getUserHelpSetKey(ACTIVITY_ID, aid, taskKey, dateStr);
                int finishNum = activityCommonRedis.getCommonSetNum(userHelpSetKey);
                if (finishNum >= limitNum) {
                    return;
                }
                activityCommonRedis.addCommonSetData(userHelpSetKey, uid);
            }

            // 同一个用户操作限制
            if (setFlag) {
                String taskUserSetKey = getTaskUserSetKey(ACTIVITY_ID, uid, taskKey, dateStr);
                int isSet = activityCommonRedis.isCommonSetData(taskUserSetKey, aid);
                if (isSet > 0) {
                    return;
                }
                activityCommonRedis.addCommonSetData(taskUserSetKey, aid);
            }


            int leftIncPoint = maxNum - dailyGetPoint;
            incPoint = Math.min(incPoint, leftIncPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskKey, incPoint);
        }
    }

    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }

        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }

        // 守卫者任务
        this.defendTaskProcess(data);

        String hostUid = "";
        switch (item) {
            case CommonMqTaskConstant.ON_MIC_TIME:
            case CommonMqTaskConstant.INVITE_USER_ACTION:
                hostUid = uid;
                break;
            case CommonMqTaskConstant.FOLLOW_ROOM:
                hostUid = RoomUtils.getRoomHostId(roomId);
                break;
            case RECEIVE_GIFT_ITEM:
                hostUid = aid;
                break;
        }

        String currentDay = getCurrentDay(ACTIVITY_ID);
        String hashActivityId = getHashActivityId(ACTIVITY_ID, hostUid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        // 赛道选择
        int talentSelect = userDataMap.getOrDefault(TALENT_SELECT, 0);

        // 自动选择赛道
        if (talentSelect <= 0 && userLevelDao.getUserLevel(hostUid) >= 10) {
            talentSelect = talentHostAutoSelect(ACTIVITY_ID, hashActivityId, hostUid);
        }
        if (talentSelect <= 0) {
            return;
        }

        switch (item) {
            case CommonMqTaskConstant.ON_MIC_TIME:
                if (!hostUid.equals(RoomUtils.getRoomHostId(roomId))) {
                    break;
                }
                MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
                int roomTag = mongoRoomData.getTag();
                if (roomTag != 12 && roomTag != 13) {
                    break;
                }
                TaskConfigVO taskConfig = HOST_TASK_LIST.get(0);
                incActionUserPoint(taskConfig, 0, hostUid, uid, 1, currentDay, false, 0, false);
                break;
            case CommonMqTaskConstant.INVITE_USER_ACTION:
                incActionUserPoint(HOST_TASK_LIST.get(1), 0, hostUid, aid, 1, currentDay, true, 5, true);
                break;
            case CommonMqTaskConstant.FOLLOW_ROOM:
                incActionUserPoint(HOST_TASK_LIST.get(2), 0, hostUid, uid, 1, currentDay, true, 0, false);
                break;
            case RECEIVE_GIFT_ITEM:
                int incPoint = data.getValue();
                incActionUserPoint(HOST_TASK_LIST.get(3), 0, hostUid, uid, incPoint, currentDay, false, 0, false);
                break;
        }
    }


    public void defendTaskProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        String defendUid = "";
        switch (item) {
            case DEFEND_SEND_GIFT:
            case DEFEND_SIGN_TASK:
            case CommonMqTaskConstant.ADD_FRIEND:
            case CommonMqTaskConstant.FOLLOW_ROOM:
            case CommonMqTaskConstant.ENTER_ROOM:
                defendUid = uid;
                break;
        }

        String currentDay = getCurrentDay(ACTIVITY_ID);
        switch (item) {
            case DEFEND_SIGN_TASK:
                incActionUserPoint(DEFEND_TASK_LIST.get(0), 0, defendUid, "", 1, currentDay, false, 0, false);
                break;
            case CommonMqTaskConstant.ENTER_ROOM:
                String enterRoomHostUid = RoomUtils.getRoomHostId(roomId);
                String enterRoomHostActivityId = getHashActivityId(ACTIVITY_ID, enterRoomHostUid);
                Map<String, Integer> enterRoomHostDataMap = activityCommonRedis.getCommonHashAll(enterRoomHostActivityId);
                int enterRoomHostSelect = enterRoomHostDataMap.getOrDefault(TALENT_SELECT, 0);
                if (enterRoomHostSelect <= 0) {
                    break;
                }
                incActionUserPoint(DEFEND_TASK_LIST.get(1), 0, defendUid, roomId, 1, currentDay, true, 0, false);
                break;
            case CommonMqTaskConstant.FOLLOW_ROOM:
                String roomHostUid = RoomUtils.getRoomHostId(roomId);
                String roomHostActivityId = getHashActivityId(ACTIVITY_ID, roomHostUid);
                Map<String, Integer> roomHostDataMap = activityCommonRedis.getCommonHashAll(roomHostActivityId);
                int roomHostSelect = roomHostDataMap.getOrDefault(TALENT_SELECT, 0);
                if (roomHostSelect <= 0) {
                    break;
                }
                incActionUserPoint(DEFEND_TASK_LIST.get(2), 0, defendUid, roomId, 1, currentDay, true, 0, false);
                break;
            case CommonMqTaskConstant.ADD_FRIEND:
                String aidActivityId = getHashActivityId(ACTIVITY_ID, aid);
                Map<String, Integer> aidDataMap = activityCommonRedis.getCommonHashAll(aidActivityId);
                int aidSelect = aidDataMap.getOrDefault(TALENT_SELECT, 0);
                if (aidSelect <= 0) {
                    break;
                }
                incActionUserPoint(DEFEND_TASK_LIST.get(3), 0, defendUid, aid, 1, currentDay, true, 0, false);
                break;
            case DEFEND_SEND_GIFT:
                int incValue = data.getValue();
                incActionUserPoint(DEFEND_TASK_LIST.get(4), 0, defendUid, aid, incValue, currentDay, false, 0, false);
                break;
        }
    }

    @Override
    public void process(SendGiftData giftData) {
        int giftId = giftData.getGid();
        if (ORDER_GIFT_ID != giftId) {
            return;
        }

        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        String fromUid = giftData.getFrom_uid();
        int giftNum = giftData.getNumber();
        for (String aid : giftData.getAid_list()) {
            String hashAidActivityId = getHashActivityId(ACTIVITY_ID, aid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashAidActivityId);
            int talentSelect = userDataMap.getOrDefault(TALENT_SELECT, 0);
            if (talentSelect == 0 && userLevelDao.getUserLevel(aid) < 10) {
                continue;
            }

            talentSelect = talentSelect > 0 ? talentSelect : talentHostAutoSelect(ACTIVITY_ID, hashAidActivityId, aid);
            if (talentSelect <= 0) {
                continue;
            }

            String rankKey = talentSelect == 1 ? TALENT_RANK_KEY_LIST.get(0) : TALENT_RANK_KEY_LIST.get(2);
            activityCommonRedis.incrCommonZSetRankingScore(rankKey, aid, giftNum);

            CommonMqTopicData data = new CommonMqTopicData();
            data.setItem(DEFEND_SEND_GIFT);
            data.setValue(giftNum);
            data.setUid(fromUid);
            data.setAid(aid);
            this.taskMsgProcess(data);
        }
    }

    private String getResourceKeyByRank(String rankKey, int rank) {
        List<String> resList = null;
        if (TALENT_RANK_KEY_LIST.get(0).equals(rankKey)) {
            resList = POEM_POPULAR_RES_LIST;
        } else if (TALENT_RANK_KEY_LIST.get(1).equals(rankKey)) {
            resList = POEM_CHARM_RES_LIST;
        } else if (TALENT_RANK_KEY_LIST.get(2).equals(rankKey)) {
            resList = TOPIC_POPULAR_RES_LIST;
        } else if (TALENT_RANK_KEY_LIST.get(3).equals(rankKey)) {
            resList = TOPIC_CHARM_RES_LIST;
        } else if (TALENT_RANK_KEY_LIST.get(4).equals(rankKey)) {
            resList = DEFEND_RES_LIST;
        }
        if (resList == null) {
            return "";
        }

        if (rank >= 11) {
            return resList.get(2);
        } else if (rank >= 4) {
            return resList.get(1);
        } else {
            return resList.get(0);
        }

    }


    public void distributionTotalRanking(String activityId) {
        try {
            for (String rankKey : TALENT_RANK_KEY_LIST) {

                Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankKey, 20);
                int rank = 1;
                for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                    String rankUid = entry.getKey();
                    String resourceKey = getResourceKeyByRank(rankKey, rank);
                    resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, TALENT_HOST_TITLE_EN, TALENT_HOST_TITLE_EN, TALENT_HOST_TITLE_EN, "", "");
                    rank += 1;
                }
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }
}
