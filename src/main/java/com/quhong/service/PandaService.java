package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.PandaDrawVO;
import com.quhong.data.vo.PandaVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.PandaRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 大航海家
 */
@Service
public class PandaService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(PandaService.class);
    private static final List<Integer> PANDA_LEVEL_LIST = Arrays.asList(2000000, 6000000, 9000000);
    private static final List<String> PANDA_SIGN_LIST = new ArrayList<>();
    private static final List<String> PANDA_SIGN_LIST1 = new ArrayList<>();
    private static final List<String> PANDA_SIGN_LIST2 = new ArrayList<>();
    private static final String ACTIVITY_NAME = "Panda";
    private static final String ACTIVITY_TITLE_EN = "Panda pet";
    private static final String ACTIVITY_TITLE_AR = "حيوان الباندا الأليف";
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final int LIMIT_INIT_POOL = 10;
    private static final int ZERO_INIT_POOL = 0;
    private static Integer PANDA_HONOR_TITLE_ID = null;
    static {
        if(ServerConfig.isProduct()){
            PANDA_SIGN_LIST.addAll(Arrays.asList("2023-09-03", "2023-09-04", "2023-09-05", "2023-09-06", "2023-09-07", "2023-09-08", "2023-09-09", "2023-09-10"));
            PANDA_SIGN_LIST1.addAll(Arrays.asList("2023-09-03", "2023-09-04", "2023-09-05", "2023-09-06"));
            PANDA_SIGN_LIST2.addAll(Arrays.asList("2023-09-07", "2023-09-08", "2023-09-09", "2023-09-10"));
            PANDA_HONOR_TITLE_ID = 15;
        }else {
            PANDA_SIGN_LIST.addAll(Arrays.asList("2023-08-25", "2023-08-26", "2023-08-27", "2023-08-28", "2023-08-29", "2023-08-30", "2023-08-31", "2023-09-01"));
            PANDA_SIGN_LIST1.addAll(Arrays.asList("2023-08-25", "2023-08-26", "2023-08-27", "2023-08-28"));
            PANDA_SIGN_LIST2.addAll(Arrays.asList("2023-08-29", "2023-08-30", "2023-08-31", "2023-09-01"));
            PANDA_HONOR_TITLE_ID = 78;
        }
    }


    @Resource
    private PandaRedis pandaRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private ResourceDistributionService distributionService;

    public PandaVO pandaConfig(String uid, String activityId, int slang) {

        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivity(activityId);
        PandaVO vo = new PandaVO();
        vo.setStartTime(otherRankingActivityData.getStartTime());
        vo.setEndTime(otherRankingActivityData.getEndTime());

        vo.setTotalFeed(pandaRedis.getTotalFeedScore(activityId));
        vo.setDrawNum(pandaRedis.getDrawAwardValue(activityId, uid));

        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        vo.setCurrentDay(currentDay);
        vo.setShare(pandaRedis.getSharePandaValue(uid, currentDay));

        // 设置每个阶段top1
        List<PandaVO.SendTop> sendTopList = new ArrayList<>();
        for (Integer stage: PANDA_LEVEL_LIST) {
            if(vo.getTotalFeed() < stage){
                continue;
            }

            List<String> userDevoteRankingList =  pandaRedis.getUserDevoteRankingList(activityId, stage, 1);
            if(!userDevoteRankingList.isEmpty()){
                String rankUid = userDevoteRankingList.get(0);
                ActorData actorData = actorDao.getActorDataFromCache(rankUid);
                PandaVO.SendTop sendTop = new PandaVO.SendTop();
                sendTop.setStage(stage);
                sendTop.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                sendTop.setUid(rankUid);
                sendTopList.add(sendTop);
            }
        }

        // 设置签到日期
        List<PandaVO.SignInfo> signList1 = new ArrayList<>();
        for (String signDay : PANDA_SIGN_LIST1) {
            PandaVO.SignInfo signInfo = new PandaVO.SignInfo();
            signInfo.setSignDay(signDay);
            signInfo.setSign(pandaRedis.getSignDayValue(activityId, uid, signDay));
            signList1.add(signInfo);
        }
        vo.setSignList1(signList1);

        List<PandaVO.SignInfo> signList2 = new ArrayList<>();
        for (String signDay : PANDA_SIGN_LIST2) {
            PandaVO.SignInfo signInfo = new PandaVO.SignInfo();
            signInfo.setSignDay(signDay);
            signInfo.setSign(pandaRedis.getSignDayValue(activityId, uid, signDay));
            signList2.add(signInfo);
        }
        vo.setSignList2(signList2);

        // 设置补签次数及当日分享状态
        vo.setReFeed(pandaRedis.getReFeedValue(activityId, uid));
        vo.setShare(pandaRedis.getSharePandaValue(uid, currentDay));
        vo.setSendTopList(sendTopList);

        // 设置转盘奖励
        vo.setTurntableList(activityCommonConfig.getPandaConfigList());

        // 分享查询
        if(otherActivityService.inActivityTime(activityId) && vo.getShare() <= 0){
            int startTime = (int) (DateHelper.ARABIAN.getTodayStartTime() / 1000);
            int endTime = startTime + 86400;
            String title = slang == 1 ? ACTIVITY_TITLE_EN : ACTIVITY_TITLE_AR;
            MomentActivityData momentActivityData = momentActivityDao.findMomentOneByTitleTime(uid, title, startTime, endTime);
            if(momentActivityData != null){
                pandaRedis.setSharePandaValue(uid, currentDay);
                pandaRedis.incReFeedValue(activityId, uid, 1);
            }
        }
        return vo;
    }



    public void pandaSign(String uid, String activityId, String signDay) {
        otherActivityService.checkActivityTime(activityId);
        if(!PANDA_SIGN_LIST.contains(signDay)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(ACTIVITY_NAME + uid)) {
            int sign = pandaRedis.getSignDayValue(activityId, uid, signDay);
            if(sign > 0){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }


            String currentDay = DateHelper.ARABIAN.formatDateInDay();
            if(currentDay.compareTo(signDay) < 0){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            if(!currentDay.equals(signDay)){
                int reFeedNum = pandaRedis.getReFeedValue(activityId, uid);
                if(reFeedNum <= 0){
                    throw new CommonH5Exception(ActivityHttpCode.PANDA_CONTRACT_SIGN);
                }
                pandaRedis.incReFeedValue(activityId, uid, -1);
            }
            pandaRedis.setSignDayValue(activityId, uid, signDay);

            if(PANDA_SIGN_LIST1.contains(signDay)){
                int total = 0;
                for (String signItem : PANDA_SIGN_LIST1) {
                    total += pandaRedis.getSignDayValue(activityId, uid, signItem);
                }

                if(total >= PANDA_SIGN_LIST1.size()){
                    pandaRedis.incDrawAwardValue(activityId, uid, 1);
                }
            }

            if(PANDA_SIGN_LIST2.contains(signDay)){
                int total = 0;
                for (String signItem : PANDA_SIGN_LIST2) {
                    total += pandaRedis.getSignDayValue(activityId, uid, signItem);
                }

                if(total >= PANDA_SIGN_LIST2.size()){
                    pandaRedis.incDrawAwardValue(activityId, uid, 1);
                }
            }
        }
    }


    public void needDrawPool(List<ActivityCommonConfig.CommonAwardConfig> pandaConfigList){

        List<String> poolList = new ArrayList<>();
        for(ActivityCommonConfig.CommonAwardConfig config: pandaConfigList){
            for (int i=0; i < config.getRateNum(); i++){
                poolList.add(config.getDrawType());
            }
        }
        Collections.shuffle(poolList);
        pandaRedis.initPoolSize(poolList);
    }

    public void initDrawPool(List<ActivityCommonConfig.CommonAwardConfig> pandaConfigList){

        int poolSize = pandaRedis.getPoolSize();
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    needDrawPool(pandaConfigList);
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            needDrawPool(pandaConfigList);
        }
    }

    public String drawCard(List<ActivityCommonConfig.CommonAwardConfig> pandaConfigList){
        initDrawPool(pandaConfigList);
        String cardKey = pandaRedis.drawCardKey();

        if (StringUtils.isEmpty(cardKey)) {
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return cardKey;
    }

    public PandaDrawVO pandaDraw(String uid, String activityId) {
        otherActivityService.checkActivityTime(activityId);
        int drawNum = pandaRedis.getDrawAwardValue(activityId, uid);
        if(drawNum <= 0){
            throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
        }
        PandaDrawVO vo = new PandaDrawVO();
        synchronized (stringPool.intern("pandaDraw" + uid)) {
            pandaRedis.incDrawAwardValue(activityId, uid, -1);
            List<ActivityCommonConfig.CommonAwardConfig> pandaConfigList = activityCommonConfig.getPandaConfigList();
            Map<String, ActivityCommonConfig.CommonAwardConfig> pandaConfigMap= activityCommonConfig.getPandaConfigList().stream().collect(Collectors.toMap(ActivityCommonConfig.CommonAwardConfig::getDrawType, Function.identity()));
            String drawKey = drawCard(pandaConfigList);
            ActivityCommonConfig.CommonAwardConfig config = pandaConfigMap.get(drawKey);
            if(config != null){
                BeanUtils.copyProperties(config, vo);
                distributionService.sendRewardResource(uid, config.getSourceId(), ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), config.getRewardTime(), config.getRewardNum(), ACTIVITY_NAME, ACTIVITY_NAME, 0);
            }else {
                throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
            }
        }
        return vo;
    }


    public void handleSendGift(SendGiftData giftData, String activityId) {

        int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size(); // 剩余钻石数
        String fromUid = giftData.getFrom_uid();
        int currentTotalFeed = pandaRedis.getTotalFeedScore(activityId);

        for (Integer stage: PANDA_LEVEL_LIST) {
            if(currentTotalFeed < stage && totalBeans > 0){
                int needUpBeans = stage - currentTotalFeed;   // 需要多少钻石升级该等级
                if(totalBeans >= needUpBeans){
                    totalBeans = totalBeans - needUpBeans;    // 剩余钻石数
                    pandaRedis.incrTotalFeedScore(activityId, needUpBeans);
                    pandaRedis.incrUserDevoteScore(activityId, stage, fromUid,needUpBeans);
                    String rankUid=  pandaRedis.getUserDevoteRankingList(activityId, stage, 1).get(0);
                    distributionService.sendRewardResource(rankUid, PANDA_HONOR_TITLE_ID, ActivityRewardTypeEnum.getEnumByName("honor_title"), 7, 0, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN,0);
                }else {
                    pandaRedis.incrTotalFeedScore(activityId, totalBeans);
                    pandaRedis.incrUserDevoteScore(activityId, stage, fromUid, totalBeans);
                    totalBeans = 0;
                }
            }
        }

        // 达到最大值还有剩余则加总数量
        if(totalBeans > 0){
            pandaRedis.incrTotalFeedScore(activityId, totalBeans);
        }
    }
}
