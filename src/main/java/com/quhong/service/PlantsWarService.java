package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class PlantsWarService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(PlantsWarService.class);
    private static final Integer PLANT_WAR_DEFAULT_NUM = 5;
    private static final String ACTIVITY_TITLE = "PlantsWar Activity";
    private static final String PLANT_WAR_PEA_KEY = "plantsNum";
    private static final String PLANT_WAR_INIT_KEY = "initNum";
    private static final String PLANT_WAR_EAT_BRAIN_KEY = "eatBrainNum";
    private static final String PLANT_WAR_DEAD_BRAIN_KEY = "deadBrainNum";
    private static final String PLANT_WAR_ATTACK_KEY = "attackNum";
    private static final String PLANT_WAR_STATUS_KEY = "status";  // 0: 正常  1: 死亡
    public static final String PLANT_WAR_ACTIVITY_ID = "65ad9767f7349cd56587f635";
    public static final List<String> ACTIVITY_LIST = Arrays.asList("65af93bf7e12b692d255db9c", "65af93bf7e12b692d255db9b");
    public static final String PEA_ACTIVITY_ID = ACTIVITY_LIST.get(0);
    public static final String ZOMBIE_ACTIVITY_ID = ACTIVITY_LIST.get(1);
    private static final Integer PEA_GIFT_ID;
    private static final Integer ZOMBIE_GIFT_ID;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        if(ServerConfig.isProduct()){
            PEA_GIFT_ID = 686;
            ZOMBIE_GIFT_ID = 685;
        }else {
            PEA_GIFT_ID = 828;
            ZOMBIE_GIFT_ID = 829;
        }
    }

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;

    public String getHashActivityId(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }

    public String getAttackListKey(String activityId, String uid){
        return String.format("attackList:%s:%s", activityId, uid);
    }

    public String getBrainRankingKey(String activityId){
        return String.format("BrainRanking:%s", activityId);
    }

    public String getAttackSetKey(String activityId){
        return String.format("attackSet:%s", activityId);
    }

    // 统计相关
    // 复活用户数
    public String getPlantWarAliveSetKey(String activityId){
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        return String.format("plantWarAliveSet:%s:%s", activityId, dateStr);
    }

    // 复活次数
    public String getAliveCountStrKey(String activityId){
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        return String.format("plantWarAliveCount:%s:%s", activityId, dateStr);
    }

    // 吃掉脑子总数
    public String getEatBrainCountStrKey(String activityId){
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        return String.format("eatBrainCount:%s:%s", activityId, dateStr);
    }


    private void fillEatBrainRankingVO(PlantsWarVO vo, String uid, ActorData actorData){
        List<BrainRankingListVO> brainRankingList = new ArrayList<>();
        BrainRankingListVO myBrainRank = new BrainRankingListVO();
        String brainRankingKey = getBrainRankingKey(PLANT_WAR_ACTIVITY_ID);
        Map<String, Integer> brainRankingMap = activityCommonRedis.getCommonRankingMap(brainRankingKey, 20);
        for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
            BrainRankingListVO brainRankingVO = new BrainRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            brainRankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            brainRankingVO.setName(rankActor.getName());
            brainRankingVO.setUid(aid);
            brainRankingVO.setScore(entry.getValue());
            brainRankingVO.setZombieScore(activityOtherRedis.getOtherRankingScore(ZOMBIE_ACTIVITY_ID, aid, 1, 0));
            brainRankingList.add(brainRankingVO);
        }

        Comparator<BrainRankingListVO> scoreDesc = Comparator.comparing(BrainRankingListVO::getScore).reversed();
        Comparator<BrainRankingListVO> zombieScoreDesc = Comparator.comparing(BrainRankingListVO::getZombieScore).reversed();
        brainRankingList.sort(scoreDesc.thenComparing(zombieScoreDesc));

        List<BrainRankingListVO> sortRankingList = new ArrayList<>();
        int brainRank = 1;
        for (BrainRankingListVO brainVO: brainRankingList) {
            brainVO.setRank(brainRank);

            sortRankingList.add(brainVO);
            if(uid.equals(brainVO.getUid())){
                BeanUtils.copyProperties(brainVO, myBrainRank);
            }
            brainRank += 1;
            if(brainRank > 10){
                break;
            }
        }

        vo.setBrainRankingList(sortRankingList);
        if(myBrainRank.getRank() == null || myBrainRank.getRank() == 0){
            myBrainRank.setName(actorData.getName());
            myBrainRank.setUid(uid);
            myBrainRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myBrainRank.setScore(activityCommonRedis.getCommonZSetRankingScore(brainRankingKey, uid));
            myBrainRank.setZombieScore(activityOtherRedis.getOtherRankingScore(ZOMBIE_ACTIVITY_ID, uid, 1, 0));
            myBrainRank.setRank(-1);
        }
        vo.setMyBrainRank(myBrainRank);
    }

    public PlantsWarVO plantsWarConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(PEA_ACTIVITY_ID);
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> taskNumMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        PlantsWarVO vo = JSON.parseObject(JSON.toJSONString(taskNumMap), PlantsWarVO.class);
        vo.setInitNum(taskNumMap.getOrDefault(PLANT_WAR_INIT_KEY, PLANT_WAR_DEFAULT_NUM));
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);

        // 设置攻击者列表
        if(inActivityTime(PEA_ACTIVITY_ID)){
            List<PlantsWarVO.AttackInfo> attackList = new ArrayList<>();
            String attackListKey = getAttackListKey(activityId, uid);
            List<String> attackDataList = activityCommonRedis.getCommonListRecord(attackListKey, 10);
            for (String attackUid : attackDataList) {
                PlantsWarVO.AttackInfo attackInfo = new PlantsWarVO.AttackInfo();
                ActorData attackActor = actorDao.getActorDataFromCache(attackUid);
                attackInfo.setAttackTime(10);
                attackInfo.setRid(attackActor.getRid());
                attackInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(attackActor.getHead()));
                attackList.add(attackInfo);
            }
            vo.setAttackList(attackList);
        }else {
            vo.setAttackList(Collections.emptyList());
        }

        // 设置吃掉的脑子数排行榜
        fillEatBrainRankingVO(vo, uid, actorData);

        // 设置豌豆射手礼物排行榜
        List<OtherRankingListVO> peaRankingList = new ArrayList<>();
        OtherRankingListVO myPeaRank = new OtherRankingListVO();
        Map<String, Integer> peaRankingMap = activityOtherRedis.getOtherRankingMap(PEA_ACTIVITY_ID, 1, 10, 0);
        int peaRank = 1;
        for (Map.Entry<String, Integer> entry : peaRankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingListVO.setName(rankActor.getName());
            rankingListVO.setScore(entry.getValue());
            rankingListVO.setRank(peaRank);
            rankingListVO.setUid(aid);
            if(uid.equals(entry.getKey())){
                BeanUtils.copyProperties(rankingListVO, myPeaRank);
            }
            peaRankingList.add(rankingListVO);
            peaRank += 1;
        }
        vo.setPeaRankingList(peaRankingList);
        if(myPeaRank.getRank() == null || myPeaRank.getRank() == 0){
            myPeaRank.setName(actorData.getName());
            myPeaRank.setUid(uid);
            myPeaRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myPeaRank.setScore(activityOtherRedis.getOtherRankingScore(PEA_ACTIVITY_ID, uid, 1, 0));
            myPeaRank.setRank(-1);
        }
        vo.setMyPeaRank(myPeaRank);

        // 设置等级勋章钻石数
        vo.setPeaBadgeSend(activityOtherRedis.getOtherReachingScore(PEA_ACTIVITY_ID, uid, 1, 0));
        vo.setZombieBadgeSend(activityOtherRedis.getOtherReachingScore(ZOMBIE_ACTIVITY_ID, uid, 1, 0));
        return vo;
    }

    public PlantsWarSearchVO plantsWarSearch(String activityId, String searchId) {
        int searchRid;
        try {
            searchId = searchId.trim();
            if(searchId.length() > 10){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            searchRid = Integer.parseInt(searchId);
        }catch (Exception e){
            logger.error("plantsWarSearch searchId={} error: {}", searchId, e.getMessage(), e);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if(searchRid <= 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ActorData actorData = actorDao.getActorByRid(searchRid);
        if(actorData == null){
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_FIND_EXIST);
        }
        PlantsWarSearchVO vo = new PlantsWarSearchVO();
        String searchUid = actorData.getUid();
        String hashActivityId = getHashActivityId(activityId, searchUid);
        Map<String, Integer> taskMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        vo.setPlantsNum(taskMap.getOrDefault(PLANT_WAR_PEA_KEY, 0));
        vo.setInitNum(taskMap.getOrDefault(PLANT_WAR_INIT_KEY, PLANT_WAR_DEFAULT_NUM));
        vo.setAttackNum(taskMap.getOrDefault(PLANT_WAR_ATTACK_KEY, 0));
        vo.setStatus(taskMap.getOrDefault(PLANT_WAR_STATUS_KEY, 0));
        vo.setRid(actorData.getRid());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    public void distributeBrainRanking(){
        try {
            if(!ServerConfig.isProduct()){
                return;
            }

            List<BrainRankingListVO> brainRankingList = new ArrayList<>();
            String brainRankingKey = getBrainRankingKey(PLANT_WAR_ACTIVITY_ID);
            Map<String, Integer> brainRankingMap = activityCommonRedis.getCommonRankingMap(brainRankingKey, 20);
            for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
                BrainRankingListVO brainRankingVO = new BrainRankingListVO();
                String aid = entry.getKey();
                ActorData rankActor = actorDao.getActorDataFromCache(aid);
                brainRankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                brainRankingVO.setName(rankActor.getName());
                brainRankingVO.setUid(aid);
                brainRankingVO.setScore(entry.getValue());
                brainRankingVO.setZombieScore(activityOtherRedis.getOtherRankingScore(ZOMBIE_ACTIVITY_ID, aid, 1, 0));
                brainRankingList.add(brainRankingVO);
            }

            Comparator<BrainRankingListVO> scoreDesc = Comparator.comparing(BrainRankingListVO::getScore).reversed();
            Comparator<BrainRankingListVO> zombieScoreDesc = Comparator.comparing(BrainRankingListVO::getZombieScore).reversed();
            brainRankingList.sort(scoreDesc.thenComparing(zombieScoreDesc));

            int brainRank = 1;
            for (BrainRankingListVO brainVO: brainRankingList) {
                String aid = brainVO.getUid();
                if(brainRank == 1){
                    distributionService.sendRewardResource(aid, 2454, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 540, ActivityRewardTypeEnum.getEnumByName("mic"), 15, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 195, ActivityRewardTypeEnum.getEnumByName("buddle"), 15, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 179, ActivityRewardTypeEnum.getEnumByName("ride"), 15, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                } else if (brainRank == 2) {
                    distributionService.sendRewardResource(aid, 2455, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 540, ActivityRewardTypeEnum.getEnumByName("mic"), 7, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 195, ActivityRewardTypeEnum.getEnumByName("buddle"), 7, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 179, ActivityRewardTypeEnum.getEnumByName("ride"), 7, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                } else if (brainRank == 3) {
                    distributionService.sendRewardResource(aid, 2456, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 540, ActivityRewardTypeEnum.getEnumByName("mic"), 3, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                    distributionService.sendRewardResource(aid, 195, ActivityRewardTypeEnum.getEnumByName("buddle"), 3, 0, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
                }
                brainRank += 1;

                if(brainRank > 3){
                    break;
                }
            }
        }catch (Exception e){
            logger.error("distributeBrainRanking error:{}", e.getMessage(), e);
        }


    }


    public void handleSendGiftData(SendGiftData giftData) {
        int sendNumber = giftData.getNumber();
        int totalNum = sendNumber * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();
        int giftId = giftData.getGid();
        Set<String> aidSet = giftData.getAid_list();

        String hashActivityId = getHashActivityId(PLANT_WAR_ACTIVITY_ID, fromUid);
        Map<String, Integer> taskNumMap =  activityCommonRedis.getCommonHashAll(hashActivityId);

        if(PEA_GIFT_ID == giftId){
            // 发送豌豆礼物, 发送者正常状态，增加豌豆数量, 处于死亡状态, 不增加豌豆数量
            // 接收者, 接收者正常状态，无需处理, 处于死亡状态, 增加复活豌豆数量, 达到5个就复活
            int sendUserStatus = taskNumMap.getOrDefault(PLANT_WAR_STATUS_KEY, 0);
            if(sendUserStatus == 0){
                activityCommonRedis.incCommonHashNum(hashActivityId, PLANT_WAR_PEA_KEY, totalNum);
            }

            for (String aid : aidSet) {
                String hashAidActivityId = getHashActivityId(PLANT_WAR_ACTIVITY_ID, aid);
                Map<String, Integer> aidTaskNumMap =  activityCommonRedis.getCommonHashAll(hashAidActivityId);

                int receiveUserStatus = aidTaskNumMap.getOrDefault(PLANT_WAR_STATUS_KEY, 0);
                if(receiveUserStatus >= 1){
                    int afterAidNumber = activityCommonRedis.incCommonHashNum(hashAidActivityId, PLANT_WAR_INIT_KEY, sendNumber);
                    if(afterAidNumber >= 5){
                        activityCommonRedis.setCommonHashNum(hashAidActivityId, PLANT_WAR_INIT_KEY, 5);
                        activityCommonRedis.setCommonHashNum(hashAidActivityId, PLANT_WAR_STATUS_KEY, 0);
                        activityCommonRedis.addCommonSetData(getPlantWarAliveSetKey(PLANT_WAR_ACTIVITY_ID), aid);
                        activityCommonRedis.incCommonStrScore(getAliveCountStrKey(PLANT_WAR_ACTIVITY_ID), 1);

                    }
                }
            }
        }else {
            // 发送僵尸礼物, 无需处理
            // 接收者, 如果接收者正常状态，增加发送者对接收者的攻击数, 处于死亡状态, 无需处理
            String attackSetKey = getAttackSetKey(PLANT_WAR_ACTIVITY_ID);
            int currentTime = DateHelper.getNowSeconds();
            for (String aid : aidSet) {
                synchronized (stringPool.intern(ACTIVITY_TITLE + aid)) {
                    String hashAidActivityId = getHashActivityId(PLANT_WAR_ACTIVITY_ID, aid);
                    Map<String, Integer> aidTaskNumMap =  activityCommonRedis.getCommonHashAll(hashAidActivityId);

                    int receiveStatus = aidTaskNumMap.getOrDefault(PLANT_WAR_STATUS_KEY, 0);
                    if(receiveStatus == 0){
                        logger.info("handleSendGiftData aid:{} currentTime: {}", aid, currentTime);
                        String attackListKey = getAttackListKey(PLANT_WAR_ACTIVITY_ID, aid);
                        List<String> attackDataList = IntStream.range(0, sendNumber).mapToObj(i -> fromUid).collect(Collectors.toList());
                        activityCommonRedis.rightPushAllCommonList(attackListKey, attackDataList);
                        activityCommonRedis.incCommonHashNum(hashAidActivityId, PLANT_WAR_ATTACK_KEY, sendNumber);
                        activityCommonRedis.addCommonSetData(attackSetKey, aid);
                    }
                }
            }
        }
    }

    public void handlePlantsWarData() {
        try {

            if(!inActivityTime(PEA_ACTIVITY_ID)){
                return;
            }

            // 定时处理被攻击的用户
            String attackSetKey = getAttackSetKey(PLANT_WAR_ACTIVITY_ID);
            Set<String> aidSet = activityCommonRedis.getCommonSetMember(attackSetKey);
            for (String aid : aidSet) {
                synchronized (stringPool.intern(ACTIVITY_TITLE + aid)) {
                    String hashActivityId = getHashActivityId(PLANT_WAR_ACTIVITY_ID, aid);
                    Map<String, Integer> taskNumMap =  activityCommonRedis.getCommonHashAll(hashActivityId);

                    int plantWarStatus = taskNumMap.getOrDefault(PLANT_WAR_STATUS_KEY, 0);
                    int peaNum = taskNumMap.getOrDefault(PLANT_WAR_PEA_KEY, 0);
                    int initNum = taskNumMap.getOrDefault(PLANT_WAR_INIT_KEY, PLANT_WAR_DEFAULT_NUM);

                    if(plantWarStatus >= 1){
                        activityCommonRedis.removeCommonSetData(attackSetKey, aid);
                    }else {
                        String attackListKey = getAttackListKey(PLANT_WAR_ACTIVITY_ID, aid);
                        String attackFromUid = activityCommonRedis.leftPopCommonListKey(attackListKey);
                        if(!StringUtils.isEmpty(attackFromUid)){
                            activityCommonRedis.incCommonHashNum(hashActivityId, PLANT_WAR_ATTACK_KEY, -1);
                            if(initNum > 0){
                                initNum -= 1;
                                activityCommonRedis.setCommonHashNum(hashActivityId, PLANT_WAR_INIT_KEY, initNum);
                            }else if (peaNum > 0){
                                peaNum -= 1;
                                activityCommonRedis.setCommonHashNum(hashActivityId, PLANT_WAR_PEA_KEY, peaNum);
                            }

                            if(initNum + peaNum <= 0){
                                logger.info("handlePlantsWarData fromUid : {} attack aid: {} success", attackFromUid, aid);

                                // 吃掉脑子
                                // 设置发送者排行榜数据、 吃掉脑子数量
                                String brainRankingKey = getBrainRankingKey(PLANT_WAR_ACTIVITY_ID);
                                activityCommonRedis.incrCommonZSetRankingScore(brainRankingKey, attackFromUid, 1);

                                String hashFromActivityId = getHashActivityId(PLANT_WAR_ACTIVITY_ID, attackFromUid);
                                activityCommonRedis.incCommonHashNum(hashFromActivityId, PLANT_WAR_EAT_BRAIN_KEY, 1);

                                // 被吃掉脑子的用户：
                                // 1、清空攻击列表、设置攻击数量为0、移除定时set、增加被吃掉脑子数量、设置状态为死亡
                                activityCommonRedis.deleteCommonListData(attackListKey);
                                activityCommonRedis.removeCommonSetData(attackSetKey, aid);
                                activityCommonRedis.incCommonHashNum(hashActivityId, PLANT_WAR_DEAD_BRAIN_KEY, 1);
                                activityCommonRedis.setCommonHashNum(hashActivityId, PLANT_WAR_ATTACK_KEY, 0);
                                activityCommonRedis.setCommonHashNum(hashActivityId, PLANT_WAR_STATUS_KEY, 1);

                                // 统计吃掉脑子次数
                                activityCommonRedis.incCommonStrScore(getEatBrainCountStrKey(PLANT_WAR_ACTIVITY_ID), 1);


                            }
                        }else {
                            int attackListSize = activityCommonRedis.getCommonListSize(attackListKey);
                            if(attackListSize <= 0){
                                activityCommonRedis.removeCommonSetData(attackSetKey, aid);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("handlePlantsWarData error:{}", e.getMessage(), e);
        }

    }


}
