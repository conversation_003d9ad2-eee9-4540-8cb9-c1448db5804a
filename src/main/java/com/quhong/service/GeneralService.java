package com.quhong.service;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.vo.GiftFruitPartyVO;
import com.quhong.mongo.dao.GiftNumDao;
import com.quhong.mongo.data.GiftNum;
import com.quhong.mongo.data.RoomBadgeData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class GeneralService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(GeneralService.class);
    private static final String ACTIVITY_TITLE_EN = "Fruit Party Activity";
    private static final String ACTIVITY_TITLE_AR = "Fruit Party Activity";
    private static final String ACTIVITY_DESC = "Fruit Party Activity desc";
    private static final String ACTIVITY_ID = "aa";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/gift_collector/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/gift_collector/?activityId=%s", ACTIVITY_ID);
    private static List<Integer> AC_GIFT_LIST = Arrays.asList(1107, 1106, 1108, 1109, 1110, 1111, 1113, 1112);

    @Resource
    private GiftNumDao giftNumDao;


    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            AC_GIFT_LIST = Arrays.asList(68, 117);
        }
    }

    // 水果互动礼物
    public GiftFruitPartyVO fruitPartyConfig(String activityId, String uid) {
        GiftFruitPartyVO vo = new GiftFruitPartyVO();
        List<GiftFruitPartyVO.GiftNumVO> giftNumVOList = new ArrayList<>();
        List<GiftNum> giftNumList = giftNumDao.getByGiftIdSet(uid, AC_GIFT_LIST);
        if (!CollectionUtils.isEmpty(giftNumList)) {
            Map<Integer, GiftNum> giftIdMapBeans = giftNumList.stream().collect
                    (Collectors.toMap(GiftNum::getGiftId, Function.identity()));
            for (Integer giftId : AC_GIFT_LIST) {
                GiftFruitPartyVO.GiftNumVO itemVO = new GiftFruitPartyVO.GiftNumVO();
                GiftNum item = giftIdMapBeans.get(giftId);
                if (item != null) {
                    BeanUtils.copyProperties(item, itemVO);
                } else {
                    itemVO.setGiftId(giftId);
                    itemVO.setGiftNum(0);
                }
                giftNumVOList.add(itemVO);
            }
        } else {
            for (Integer giftId : AC_GIFT_LIST) {
                GiftFruitPartyVO.GiftNumVO itemVO = new GiftFruitPartyVO.GiftNumVO();
                itemVO.setGiftId(giftId);
                itemVO.setGiftNum(0);
                giftNumVOList.add(itemVO);
            }
        }
        vo.setGiftNumVOList(giftNumVOList);
        return vo;
    }

}
