package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.LuckyDrawEvent;
import com.quhong.constant.LuckyDrawHttpCode;
import com.quhong.constant.LuckyDrawType;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.DrawLuckyLotteryVO;
import com.quhong.data.vo.DrawLuckyScrollVO;
import com.quhong.data.vo.LuckyLotteryActivityVO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.LuckyLotteryActivityDao;
import com.quhong.mongo.data.LuckyLotteryActivity;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mq.MqSenderService;
import com.quhong.mq.ResourceDeliveryService;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.LuckyDrawActivityDao;
import com.quhong.mysql.data.LuckyDrawActivityData;
import com.quhong.redis.LuckyLotteryRedis;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class LuckyLotteryService {
    private static final Logger logger = LoggerFactory.getLogger(LuckyLotteryService.class);

    private static final int LUCKY_MONEY_TYPE = 912;
    private static final int LIMIT_INIT_POOL = 10;
    private static final int ZERO_INIT_POOL = 0;
    private static final String RECORD_TITLE = "LuckyDraw";

    @Resource
    private LuckyLotteryActivityDao luckyLotteryActivityDao;
    @Resource
    private LuckyLotteryRedis luckyLotteryRedis;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private ResourceDeliveryService resourceDeliveryService;
    @Resource
    protected DataCenterService dataCenterService;
    @Resource
    private LuckyLotteryService luckyLotteryService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private LuckyDrawActivityDao luckyDrawActivityDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private ActivityUtilService activityUtilService;

    // 抽奖配置接口
    // @Cacheable(value = "luckyLottery", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public LuckyLotteryActivity getLuckyLottery(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        LuckyLotteryActivity data = luckyLotteryActivityDao.findData(activityId);
        if (null == data) {
            logger.error("cannot find ranking activity activityId={}", activityId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }
    // 获取参加抽奖人数
    private int getJoinUserNums(String activityId, int showJoin) {
        if (showJoin == 1) {
            return luckyLotteryRedis.getJoinUserNums(activityId);
        } else {
            return 0;
        }
    }

    // 获取剩余抽奖数量
    private int getRemainNums(String activityId, String uid, int partakeType, int startTime,
                                    int endTime, int freeNums) {
        if (partakeType == LuckyDrawType.FREE_DRAW) {
            int nowSeconds = DateHelper.getNowSeconds();
            if (startTime > nowSeconds || endTime < nowSeconds) {
                return 0;
            } else {
                int remainNums = luckyLotteryRedis.getUserFreeNums(activityId, uid);
                return remainNums == -1 ? freeNums : remainNums;
            }
        } else {
            MongoActorData actor = actorDao.findActorDataFromDB(uid);
            return partakeType == LuckyDrawType.HEART_DRAW ? actor.getHeartGot() : actor.getBeans();
        }
    }

    // 初始化奖池
    public void needDrawPool(String activityId, int poolConfigSize,
                             Map<String, LuckyLotteryActivity.RewardConfigDetail> rewardConfigMap){
        List<String> poolList = new ArrayList<>();
        for(String rewardKey: rewardConfigMap.keySet()){
            double rewardPercent = rewardConfigMap.get(rewardKey).getRewardPercent() * 0.01;
            int rewardSize = (int) (poolConfigSize * rewardPercent);
            for (int i=0; i < rewardSize; i++){
                poolList.add(rewardKey);
            }
        }
        Collections.shuffle(poolList);
        luckyLotteryRedis.initPoolSize(activityId, poolList);
    }

    public void initDrawPool(String activityId, int poolConfigSize,
                             Map<String, LuckyLotteryActivity.RewardConfigDetail> rewardConfigMap){
        int poolSize = luckyLotteryRedis.getPoolSize(activityId);
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    needDrawPool(activityId, poolConfigSize, rewardConfigMap);
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            needDrawPool(activityId, poolConfigSize, rewardConfigMap);
        }
    }

    // api-初始化数据
    public void initLuckyDrawData(String activityId, String uid, LuckyLotteryActivityVO vo){
        LuckyLotteryActivity luckyActivity = luckyLotteryService.getLuckyLottery(activityId);
        // 设置参与抽奖的人数
        int showJoin = luckyActivity.getActivityConfig().getShowJoin();
        vo.setJoinNums(getJoinUserNums(activityId, showJoin));
        // 设置剩余数量钻石、心心或免费次数
        LuckyLotteryActivity.PartakeConfig partakeConfig = luckyActivity.getPartakeConfig();
        int partakeType = partakeConfig.getPartakeType();
        int startTime = luckyActivity.getStartTime();
        int endTime = luckyActivity.getEndTime();
        int freeNums = partakeConfig.getRoundFreeNums();
        vo.setRemainNums(getRemainNums(activityId, uid, partakeType,startTime, endTime, freeNums));
        // 初始化奖池
        int poolConfigSize = partakeConfig.getPoolSize();
        Map<String, LuckyLotteryActivity.RewardConfigDetail> rewardConfigMap =
                luckyActivity.getRewardConfigList().stream().collect(Collectors.toMap(k -> k.getRewardType() + k.getRewardIndex(), Function.identity()));
        initDrawPool(activityId, poolConfigSize, rewardConfigMap);

    }

    // 抽奖
    private void luckyDrawDiamondCost(String uid, int changed, String activityName) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(LUCKY_MONEY_TYPE);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(activityName);
        moneyDetailReq.setDesc(activityName);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_BEANS_OUT);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 异步打钻
     */
    private void luckyDrawChargeDiamonds(String uid, int changed, String title, String detailDesc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(905);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        // moneyDetailReq.setTitle("LuckyDraw Award");
        // moneyDetailReq.setDesc(String.format("%d LuckyDraw Award", changed));
        moneyDetailReq.setDesc(detailDesc);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    private void onceAgainReturn(String activityId, String uid, int partakeType, int unitPrice, String acNameEn){
        if(partakeType == LuckyDrawType.DIAMOND_DRAW){
            luckyDrawChargeDiamonds(uid, unitPrice, RECORD_TITLE, acNameEn + "Once Again Return");
        }else if(partakeType == LuckyDrawType.HEART_DRAW){
            heartRecordDao.changeHeart(uid, unitPrice, RECORD_TITLE, acNameEn + "Once Again Return");

        }else{
            luckyLotteryRedis.incUserFreeNums(activityId, uid, unitPrice);
        }

    }


    public void rewardDistribute(String activityId, String uid, int partakeType, int unitPrice,
                                 LuckyLotteryActivity.RewardConfigDetail rewardConfig, String acNameEn){
        String rewardType = rewardConfig.getRewardType();
        int rewardNum = rewardConfig.getRewardNum() != null?rewardConfig.getRewardNum():0;
        switch (rewardType){
            case ResourceConstant.THANKS:
                break;
            case ResourceConstant.ONCE_AGAIN:
                onceAgainReturn(activityId, uid, partakeType, unitPrice, acNameEn);
                break;
            case ResourceConstant.HEART:
                heartRecordDao.changeHeart(uid, rewardNum, RECORD_TITLE, acNameEn + "reward");
                break;
            case ResourceConstant.DIAMOND:
                luckyDrawChargeDiamonds(uid, rewardNum, RECORD_TITLE, acNameEn + "reward");
                break;
            default:
                // 使用py下发资源
                activityUtilService.handleResources(new GiftsMqVo(uid,
                        rewardConfig.getRewardType(),
                        rewardConfig.getSourceId(),
                        rewardConfig.getRewardTime(),
                        null == rewardConfig.getRewardNum() ? 1 : rewardConfig.getRewardNum()));
        }

        int curTime = DateHelper.getNowSeconds();
        ActorData actor = actorDao.getActorDataFromCache(uid);

        LuckyDrawActivityData luckyDrawLog = new LuckyDrawActivityData();
        luckyDrawLog.setActivityId(activityId);
        luckyDrawLog.setUid(uid);
        luckyDrawLog.setRewardType(rewardConfig.getRewardType()!=null?rewardConfig.getRewardType():"");
        luckyDrawLog.setRewardIndex(rewardConfig.getRewardIndex()!=null?rewardConfig.getRewardIndex():"");

        luckyDrawLog.setRewardIcon(rewardConfig.getRewardIcon()!=null?rewardConfig.getRewardIcon():"");
        luckyDrawLog.setRewardNameEn(rewardConfig.getRewardNameEn()!=null?rewardConfig.getRewardNameEn():"");
        luckyDrawLog.setRewardNameAr(rewardConfig.getRewardNameAr()!=null?rewardConfig.getRewardNameAr():"");

        luckyDrawLog.setRewardNum(rewardConfig.getRewardNum()!=null?rewardConfig.getRewardNum():0);
        luckyDrawLog.setRewardTime(rewardConfig.getRewardTime()!=null?rewardConfig.getRewardTime():0);
        luckyDrawLog.setSourceId(rewardConfig.getSourceId()!=null?rewardConfig.getSourceId():0);
        luckyDrawLog.setCtime(curTime);
        luckyDrawActivityDao.insert(luckyDrawLog);


        // 上报数数
        LuckyDrawEvent luckyDrawEvent = new LuckyDrawEvent();
        luckyDrawEvent.setActive_id(activityId);
        luckyDrawEvent.setUid(uid);
        luckyDrawEvent.setChannel(actor.getAppPackageName());
        luckyDrawEvent.setCost_type(partakeType);
        luckyDrawEvent.setCost_number(unitPrice);
        luckyDrawEvent.setReward_name(rewardConfig.getRewardNameEn()!=null?rewardConfig.getRewardNameEn():"");
        luckyDrawEvent.setReward_number(rewardConfig.getRewardNum()!=null?rewardConfig.getRewardNum():0);
        luckyDrawEvent.setReward_time(rewardConfig.getRewardTime()!=null?rewardConfig.getRewardTime():0);
        luckyDrawEvent.setCtime(curTime);
        logger.info("luckyDrawEvent data: {}", luckyDrawEvent);
        eventReport.track(new EventDTO(luckyDrawEvent));


    }

    public void limitUserTotalDrawNums(String activityId, String uid, int drawLimit, int drawNums) {
        if (drawLimit == 1 && drawNums > 0) {
            // 获取用户总抽奖次数
            int userDrawNums = luckyLotteryRedis.getUserTotalDrawNums(activityId, uid);
            if (userDrawNums >= drawNums) {
                logger.info("LuckyLottery activity userDrawNums Out uid={} activityId={} userDrawNums={}, drawNums={}",
                        uid, activityId, userDrawNums, drawNums);
                throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_TOTAL_NUM_OUT);
            }
        }
    }

    public void limitUserDailyDrawNums(String activityId, String uid, int dailyLimit, int dailyNums) {
        if (dailyLimit == 1 && dailyNums > 0) {
            // 获取用户总抽奖次数
            int userDailyNums = luckyLotteryRedis.getUserDailyDrawNums(activityId, uid);
            if (userDailyNums >= dailyNums) {
                logger.info("LuckyLottery activity userDailyNums Out uid={} activityId={} userDailyNums={}, dailyNums={}",
                        uid, activityId, userDailyNums, dailyNums);
                throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_DAILY_NUM_OUT);
            }
        }
    }

    // 扣取额度
    public void deductUserQuota(String activityId, String uid, int partakeType, int unitPrice, String acNameEn) {
        if (partakeType == LuckyDrawType.FREE_DRAW) {
            int curNum = luckyLotteryRedis.getUserFreeNums(activityId, uid);
            int leftNum = curNum - unitPrice;
            if (leftNum < 0) {
                throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_BEANS_OUT);
            }
            luckyLotteryRedis.setUserFreeNums(activityId, uid, leftNum);
        } else if (partakeType == LuckyDrawType.DIAMOND_DRAW) {
            luckyDrawDiamondCost(uid, unitPrice, acNameEn);
        } else {
            boolean heartFlag = heartRecordDao.changeHeart(uid, -unitPrice, acNameEn, acNameEn);
            if (!heartFlag) {
                throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_BEANS_OUT);
            }
        }
    }

    public DrawLuckyLotteryVO drawLuckyLottery(String activityId, String uid){
        int nowSeconds = DateHelper.getNowSeconds();
        LuckyLotteryActivity luckyActivity = luckyLotteryService.getLuckyLottery(activityId);
        int startTime = luckyActivity.getStartTime();
        int endTime = luckyActivity.getEndTime();
        if (startTime > nowSeconds || endTime < nowSeconds) {
            logger.error("LuckyLottery activity has finish uid={} activityId={}", uid, activityId);
            throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_TIME_OUT);
        }
        // 是否限制每人抽奖总次数
        int drawLimit = luckyActivity.getPartakeConfig().getDrawLimit();
        int drawNums = luckyActivity.getPartakeConfig().getDrawNums();
        limitUserTotalDrawNums(activityId, uid, drawLimit, drawNums);
        // 是否限制每人每天抽奖次数
        int dailyLimit = luckyActivity.getPartakeConfig().getDailyLimit();
        int dailyNums = luckyActivity.getPartakeConfig().getDailyNums();
        limitUserDailyDrawNums(activityId, uid, dailyLimit, dailyNums);
        // 扣免费数量、钻、心心
        int partakeType = luckyActivity.getPartakeConfig().getPartakeType();
        int unitPrice = luckyActivity.getPartakeConfig().getUnitPrice();
        int freeNums = luckyActivity.getPartakeConfig().getRoundFreeNums();
        String acNameEn = luckyActivity.getAcNameEn();
        deductUserQuota(activityId, uid, partakeType, unitPrice, acNameEn);
        // 抽奖
        DrawLuckyLotteryVO vo = new DrawLuckyLotteryVO();
        int poolConfigSize = luckyActivity.getPartakeConfig().getPoolSize();
        Map<String, LuckyLotteryActivity.RewardConfigDetail> rewardConfigMap =
                luckyActivity.getRewardConfigList().stream().collect(Collectors.toMap(k -> k.getRewardType() + k.getRewardIndex(), Function.identity()));
        initDrawPool(activityId, poolConfigSize, rewardConfigMap);
        String drawLuckyKey = luckyLotteryRedis.drawLuckyKey(activityId, uid);
        LuckyLotteryActivity.RewardConfigDetail rewardConfig = rewardConfigMap.get(drawLuckyKey);
        logger.info("drawLuckyKey activityId={}, uid={}, key={}, rewardConfig={}", activityId, uid, drawLuckyKey, rewardConfig);
        vo.setRewardConfig(rewardConfig);
        int showJoin = luckyActivity.getActivityConfig().getShowJoin();
        vo.setJoinNums(getJoinUserNums(activityId, showJoin));
        vo.setRemainNums(getRemainNums(activityId, uid, partakeType, startTime, endTime, freeNums));

        // 奖励下发及下发记录(异步)
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                rewardDistribute(activityId, uid, partakeType, unitPrice, rewardConfig, acNameEn);
            }
        });

        // 记录总抽奖次数或当日抽奖次数
        if (drawLimit == 1 && drawNums > 0){
            luckyLotteryRedis.incUserTotalDrawNums(activityId, uid);
        }

        if (dailyLimit == 1 && dailyNums > 0){
            luckyLotteryRedis.incUserDailyDrawNums(activityId, uid);
        }

        // 参与抽奖统计
        if (luckyActivity.getActivityConfig().getShowJoin() == 1){
            luckyLotteryRedis.setJoinUserNums(activityId, uid);
        }

        return vo;

    }

    // 个人历史记录
    public DrawLuckyScrollVO drawLuckyHistory(String activityId, String uid){
        DrawLuckyScrollVO luckyScrollVO = new DrawLuckyScrollVO();
        List<DrawLuckyScrollVO.RewardConfig> scrollList = new ArrayList<>();
        List<LuckyDrawActivityData> scrollDataList = luckyDrawActivityDao.selectScrollList(activityId);
        if(scrollDataList != null){
            for(LuckyDrawActivityData luckyDraw:scrollDataList){
                ActorData actor = actorDao.getActorData(luckyDraw.getUid());
                DrawLuckyScrollVO.RewardConfig rewardConfig = new DrawLuckyScrollVO.RewardConfig();
                rewardConfig.setName(actor.getName()!=null?actor.getName():"");
                rewardConfig.setRewardNameEn(luckyDraw.getRewardNameEn());
                rewardConfig.setRewardNameAr(luckyDraw.getRewardNameAr());
                scrollList.add(rewardConfig);
            }
        }
        luckyScrollVO.setScrollList(scrollList);
        List<DrawLuckyScrollVO.MeRewardConfig> meRewardList = new ArrayList<>();
        List<LuckyDrawActivityData> meDataList = luckyDrawActivityDao.selectList(activityId, uid);
        if(meDataList != null){
            for(LuckyDrawActivityData meLuckyDraw:meDataList){
                DrawLuckyScrollVO.MeRewardConfig meRewardConfig = new DrawLuckyScrollVO.MeRewardConfig();
                meRewardConfig.setRewardType(meLuckyDraw.getRewardType()!=null?meLuckyDraw.getRewardType():"");
                meRewardConfig.setRewardNum(meLuckyDraw.getRewardNum());
                meRewardConfig.setRewardTime(meLuckyDraw.getRewardTime());
                meRewardConfig.setRewardIcon(meLuckyDraw.getRewardIcon());
                meRewardConfig.setRewardNameEn(meLuckyDraw.getRewardNameEn());
                meRewardConfig.setRewardNameAr(meLuckyDraw.getRewardNameAr());
                meRewardList.add(meRewardConfig);
            }
        }
        luckyScrollVO.setMeRewardList(meRewardList);
        return luckyScrollVO;
    }
}
