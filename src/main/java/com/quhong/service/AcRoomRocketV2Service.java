package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GetActivitySpecialItemsRecordEvent;
import com.quhong.analysis.RoomRocketLogEvent;
import com.quhong.constant.MatchConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.vo.RoomReturnBonusVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.SLangType;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.msg.room.RocketV2LaunchPlatformMsg;
import com.quhong.msg.room.RocketV2RewardMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.RoomRocketV2Redis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 第二版房间火箭
 */
@Service
public class AcRoomRocketV2Service {


    private static final Logger logger = LoggerFactory.getLogger(AcRoomRocketV2Service.class);
    private static final List<Integer> SCORE_LEVEL_LIST = Arrays.asList(0, 100, 300, 1000, 3000, 5000, 10000, 20000, 50000);
    private static final List<Integer> DSCORE_LEVEL_LIST = Arrays.asList(100, 200, 700, 2000, 2000, 5000, 10000, 30000, 0);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? "https://static.youstar.live/RoomRocketV2/" : "https://test2.qmovies.tv/RoomRocketV2/";

    private static final int A_TYPE = 930;
    private static final String TITLE = "Room Rocket RewardV2";
    private static final String DESC = "Room Rocket RewardV2";
    private static final Map<Integer, String> LEVEL_TITLE_MAP = new HashMap<Integer, String>() {
        {
            put(1, "Room Rocket RewardV2-lv1");
            put(2, "Room Rocket RewardV2-lv2");
            put(3, "Room Rocket RewardV2-lv3");
            put(4, "Room Rocket RewardV2-lv4");
            put(5, "Room Rocket RewardV2-lv5");
            put(6, "Room Rocket RewardV2-lv6");
            put(7, "Room Rocket RewardV2-lv7");
            put(8, "Room Rocket RewardV2-lv8");
        }
    };

    private static final Map<Integer, String> LEVEL_SCREEN_ICON_MAP = new HashMap<Integer, String>() {
        {
            put(1, "https://cdn3.qmovies.tv/youstar/op_1729242559_12x.png");
            put(2, "https://cdn3.qmovies.tv/youstar/op_1729242559_22x.png");
            put(3, "https://cdn3.qmovies.tv/youstar/op_1729242559_32x.png");
            put(4, "https://cdn3.qmovies.tv/youstar/op_1729242559_42x.png");
            put(5, "https://cdn3.qmovies.tv/youstar/op_1729242559_52x.png");
            put(6, "https://cdn3.qmovies.tv/youstar/op_1729242559_62x.png");
            put(7, "https://cdn3.qmovies.tv/youstar/op_1729242559_72x.png");
            put(8, "https://cdn3.qmovies.tv/youstar/op_1729242559_82x.png");
        }
    };

    private static final String GET_COINS_REWARD_DESC = "\u202B %s \u202C got %s coins in the rocket.";
    private static final String GET_COINS_REWARD_DESC_AR = "%s حصلت على%s كوينزات في الصاروخ.";
    private static final String GET_COINS_REWARD_DESC_SUB = "%s coins";
    private static final String GET_COINS_REWARD_DESC_AR_SUB = "%s كوينزات";

    private static final String GET_DIAMONDS_REWARD_DESC = "\u202B %s \u202C got %s diamonds in rocket.";
    private static final String GET_DIAMONDS_REWARD_DESC_AR = "%s حصلت على %s الماس في الصاروخ.";
    private static final String GET_DIAMONDS_REWARD_DESC_SUB = "%s diamonds";
    private static final String GET_DIAMONDS_REWARD_DESC_AR_SUB = "%s الماس ";


    private static final String GET_GIFTS_REWARD_DESC = "\u202B %s \u202C got %s gift in Rocket";
    private static final String GET_GIFTS_REWARD_DESC_AR = "%s حصلت على %s هدية في روكيت";
    private static final String GET_GIFTS_REWARD_DESC_SUB = "%s gift";
    private static final String GET_GIFTS_REWARD_DESC_AR_SUB = "%s هدية";


    private static final String GET_PROPS_REWARD_DESC = "\u202B %s \u202C got %s %s days in the rocket";
    // private static final String GET_PROPS_REWARD_DESC_AR = "%s حصلت على %s %s أيام في الصاروخ";
    private static final String GET_PROPS_REWARD_DESC_AR = "%s حصلت على %s %s الايام في الصاروخ";
    private static final String GET_PROPS_REWARD_DESC_SUB = "%s %s days";
    private static final String GET_PROPS_REWARD_DESC_AR_SUB = "%s %s الايام";


    private static final String ROCKET_NOTICE_TITLE = "Rocket Upgrade Rewards!";
    private static final String ROCKET_NOTICE_TITLE_AR = "مكافآت ترقية الصاروخ!";

    private static final String ROCKET_NOTICE_DESC = "火箭升级奖励";
    private static final String ROCKET_NOTICE_DESC_AR = "火箭升级奖励-阿语";

    private static final String ROCKET_NOTICE_COINS_DESC = "Congratulations! You have received %s Room Rocket Upgrade Reward %s Coins";
    private static final String ROCKET_NOTICE_COINS_DESC_AR = "تهانينا! لقد حصلت على %sمكافأة ترقية صاروخ الغرفة%s كوينزات.";

    private static final String ROCKET_NOTICE_DIAMONDS_DESC = "Congratulations! You have received the rocket upgrade reward of %s diamonds in the %s room.";
    private static final String ROCKET_NOTICE_DIAMONDS_DESC_AR = "تهانينا! لقد حصلت على مكافأة ترقية الصاروخ قيمتها %s ماسات في غرفة %s.";

    private static final String ROCKET_NOTICE_GIFTS_DESC = "Congratulations! You have received %s bonus gifts for the rocket upgrade in %s room ";
    private static final String ROCKET_NOTICE_GIFTS_DESC_AR = "تهانينا! لقد حصلت على %s هدايا لترقية الصاروخ في %s الغرفة";

    private static final String ROCKET_NOTICE_PROPS_DESC = "Congratulations! You have received the rocket upgrade reward in the %s room, %s %s days.";
    private static final String ROCKET_NOTICE_PROPS_DESC_AR = "تهانينا! لقد حصلت على مكافأة ترقية الصاروخ في الغرفة %s، ل %s %s  من الأيام.";

    private final static String COMMON_TEXT_EN = "The rocket was launched in the #username# room. Enter the room to claim your reward! GO>";
    private final static String COMMON_TEXT_AR = "تم إطلاق الصاروخ في غرفة #username#. أدخل الغرفة لتحصل على مكافأتك! اذهب>";
    private final static String COMMON_VIEW_EN = "GO>";
    private final static String COMMON_VIEW_AR = "اذهب>";
    private final static String GAME_TYPE_ROCKET_UPDATE_LEVEL = "screen_rocket_update_level";


    // 斋灯相关推送
    private static final String FANOOS_NOTICE_TITLE = "Light up the fanoos reward";
    private static final String FANOOS_NOTICE_TITLE_AR = "جائزة إضاءة الفانوس";

    private static final String GET_FANOOS_COINS_REWARD_DESC = "\u202B %s \u202C got %s coins in the fanoos.";
    private static final String GET_FANOOS_COINS_REWARD_DESC_AR = "%s حصلت على%s كوينزات في الفانوس.";
    private static final String GET_FANOOS_COINS_REWARD_DESC_SUB = "%s coins";
    private static final String GET_FANOOS_COINS_REWARD_DESC_AR_SUB = "%s كوينزات";

    private static final String GET_FANOOS_DIAMONDS_REWARD_DESC = "\u202B %s \u202C got %s diamonds in fanoos.";
    private static final String GET_FANOOS_DIAMONDS_REWARD_DESC_AR = "%s حصلت على %s الماس في الفانوس.";
    private static final String GET_FANOOS_DIAMONDS_REWARD_DESC_SUB = "%s diamonds";
    private static final String GET_FANOOS_DIAMONDS_REWARD_DESC_AR_SUB = "%s الماس ";

    private static final String GET_FANOOS_GIFTS_REWARD_DESC = "\u202B %s \u202C got %s gift in fanoos";
    private static final String GET_FANOOS_GIFTS_REWARD_DESC_AR = "%s حصلت على %s هدية في روكيت";
    private static final String GET_FANOOS_GIFTS_REWARD_DESC_SUB = "%s gift";
    private static final String GET_FANOOS_GIFTS_REWARD_DESC_AR_SUB = "%s هدية";

    private static final String GET_FANOOS_PROPS_REWARD_DESC = "\u202B %s \u202C got %s %s days in the fanoos";
    private static final String GET_FANOOS_PROPS_REWARD_DESC_AR = "%s حصلت على %s %s الايام في الفانوس";
    private static final String GET_FANOOS_PROPS_REWARD_DESC_SUB = "%s %s days";
    private static final String GET_FANOOS_PROPS_REWARD_DESC_AR_SUB = "%s %s الايام";


    private static final String FANOOS_NOTICE_COINS_DESC = "Congratulations! You have received %s Room fanoos Upgrade Reward %s Coins";
    private static final String FANOOS_NOTICE_COINS_DESC_AR = "تهانينا! لقد حصلت على %sمكافأة ترقية فانوس الغرفة%s كوينزات.";

    private static final String FANOOS_NOTICE_DIAMONDS_DESC = "Congratulations! You have received the fanoos upgrade reward of %s diamonds in the %s room.";
    private static final String FANOOS_NOTICE_DIAMONDS_DESC_AR = "تهانينا! لقد حصلت على مكافأة ترقية الفانوس قيمتها %s ماسات في غرفة %s.";

    private static final String FANOOS_NOTICE_GIFTS_DESC = "Congratulations! You have received %s bonus gifts for the fanoos upgrade in %s room ";
    private static final String FANOOS_NOTICE_GIFTS_DESC_AR = "تهانينا! لقد حصلت على %s هدايا لترقية الفانوس في %s الغرفة";

    private static final String FANOOS_NOTICE_PROPS_DESC = "Congratulations! You have received the fanoos upgrade reward in the %s room, %s %s days.";
    private static final String FANOOS_NOTICE_PROPS_DESC_AR = "تهانينا! لقد حصلت على مكافأة ترقية الفانوس في الغرفة %s، ل %s %s  من الأيام.";

    /**
     * 选项价值降序
     */
    private static final Comparator<RocketRewardConfigData.ResourceMeta> RESOURCE_PRICE_DESC = Comparator.comparing(RocketRewardConfigData.ResourceMeta::getResourcePrice).reversed();

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private EventReport eventReport;
    @Resource
    protected OfficialDao officialDao;
    @Resource
    protected NoticeNewDao noticeNewDao;
    @Autowired
    protected RoomWebSender roomWebSender;
    @Resource
    private RoomRocketV2Redis roomRocketV2Redis;
    @Resource
    private RocketRewardConfigDao rocketRewardConfigDao;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private SysConfigDao sysConfigDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
        }
    }


    public void handleUserScore(SendGiftData data, CommonMqTopicData mqData) {
        int rocketSwitch = sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_V2_KEY);
        if (rocketSwitch == 0) {
            return;
        }
//        if (DateHelper.getNowSeconds() < MatchConstant.ROOM_ALLOWANCE_START) {
//            return;
//        }
        String roomId;
        if (data != null) {
            roomId = data.getRoomId();
        } else {
            roomId = mqData.getRoomId();
        }
//        if (ServerConfig.isProduct()) {
//            boolean isWhiteTestRoom = whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID);
//            if (!isWhiteTestRoom) {
//                // 灰度测试,只统计测试房间的
//                return;
//            }
////            if (isWhiteTestRoom) {
////                // 正式开始，排除测试房间
////                logger.info("return testRoom:{}", roomId);
////                return;
////            }
//        }

        String hostUid = RoomUtils.getRoomHostId(roomId); // 房主uid
        String aid; // 上麦用户或者发礼物用户
        boolean newUserFlag = false;
        int score;
        long totalPrice = 0;
        int actionType = 0; // 1 发礼物  2有效上麦 3加入房间会员

        if (data != null) {
            actionType = 1;
            aid = data.getFrom_uid();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            newUserFlag = ActorUtils.isNewDeviceAccount(aid, actorData.getFirstTnId());
            totalPrice = ((long) data.getPrice() * data.getNumber() * data.getAid_list().size());
            score = (int) totalPrice / 3;
//            score = newUserFlag ? (int) totalPrice / 3 * 2 : (int) totalPrice / 3;
        } else {
            actionType = CommonMqTaskConstant.ON_MIC_TIME.equals(mqData.getItem()) ? 2 : 3;
            aid = mqData.getUid();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            newUserFlag = ActorUtils.isNewDeviceAccount(aid, actorData.getFirstTnId());
            if (actionType == 2) {
                score = newUserFlag ? 10 : 3;
            } else {
                score = newUserFlag ? 5 : 2;
            }
        }
        if (score <= 0 || StringUtils.isEmpty(aid)) {
            logger.info("score:{} lte 0 or aid is empty", score);
            return;
        }
        String nowDay = DateHelper.ARABIAN.formatDateInDay();
        int nowTime = DateHelper.getNowSeconds();
        String detailKey = roomRocketV2Redis.getHashDetailKey(nowDay);
        synchronized (stringPool.intern("lock:room_rocket_v2:" + roomId)) {
            RoomReturnBonusVO.DetailVO detailVO = cacheDataService.getRocketV2Detail(detailKey, roomId);
            if (actionType == 2 || actionType == 3) {
                Set<String> disUsers = detailVO.getMicUsers();
                int maxLimit = 0;
                int nowNum = 0;
                if (actionType == 2) {
                    if (newUserFlag) {
                        maxLimit = 4; // 40/10
                        nowNum = detailVO.getNewUserMic();
                    } else {
                        maxLimit = 10;//  30/3
                        nowNum = detailVO.getOldUserMic();
                    }
                    if (disUsers.contains(aid)) {
//                    logger.info("return actionType:{} newUserFlag:{} roomId:{} dis user list contains aid:{} or nowNum:{} > maxLimit:{}",
//                            actionType, newUserFlag, roomId, aid, nowNum, maxLimit);
                        return;
                    }
                } else {
                    if (newUserFlag) {
                        maxLimit = 4; // 20/5
                        nowNum = detailVO.getNewMemberNum();
                    } else {
                        maxLimit = 10; // 20/2
                        nowNum = detailVO.getOldMemberNum();
                    }
                }
                if (nowNum >= maxLimit) {
//                    logger.info("return actionType:{} newUserFlag:{} roomId:{} dis user list contains aid:{} or nowNum:{} > maxLimit:{}",
//                            actionType, newUserFlag, roomId, aid, nowNum, maxLimit);
                    return;
                }
                if (actionType == 2) {
                    if (newUserFlag) {
                        detailVO.setNewUserMic(nowNum + 1);
                    } else {
                        detailVO.setOldUserMic(nowNum + 1);
                    }
                    detailVO.setUserMicPoint(detailVO.getUserMicPoint() + score);
                    disUsers.add(aid);
                } else {
                    if (newUserFlag) {
                        detailVO.setNewMemberNum(nowNum + 1);
                    } else {
                        detailVO.setOldMemberNum(nowNum + 1);
                    }
                    detailVO.setMemberPoint(detailVO.getMemberPoint() + score);
                }

            } else {
                detailVO.setSendGiftPoint(detailVO.getSendGiftPoint() + score);
            }
            int beforeNum = detailVO.getDayPoint();
            int afterNum = beforeNum + score;
            detailVO.setDayPoint(afterNum);
            roomRocketV2Redis.setCommonHashData(detailKey, roomId, JSONObject.toJSONString(detailVO));
            updateRoomRocketRedis(nowDay, nowTime, roomId, aid, score);
            logger.info("ac room rocket success add roomId:{} aid:{} actionType:{} totalBeforeNum:{} addScore:{} totalAfterNum:{}",
                    roomId, aid, actionType, beforeNum, score, afterNum);
        }
    }


    private void updateRoomRocketRedis(String nowDay, int nowTime, String roomId, String aid, int addScore) {
        int maxIndex = 8; // 目前取值为8
        RocketLevelV2Data oldRocketLevelV2Data = roomRocketV2Redis.getRocketLevelV2Data(nowDay, roomId);
        //  oldIndex 为已完成的等级, 0为还没完成任何级别 取值 0-8
        int oldIndex = oldRocketLevelV2Data.getLevel();
        if (oldIndex >= maxIndex) {
            logger.info("oldIndex:{} gte maxIndex:{} is to max return", oldIndex, maxIndex);
            return;
        }
        List<RocketRewardConfigData> rocketRewardConfigDataList = rocketRewardConfigDao.findV2AllCache();
        List<Integer> rocketLaunchLimitList = new ArrayList<>();
        int total = 0;
        for (RocketRewardConfigData item : rocketRewardConfigDataList) {
            int rocketLevel = item.getRocketLevel();
            if (rocketLevel > oldIndex && rocketLevel <= maxIndex) {
                total += item.getRocketLaunchLimit();
                rocketLaunchLimitList.add(total);
            }
        }

        if (CollectionUtils.isEmpty(rocketLaunchLimitList)) {
            logger.info("rocketLaunchLimitList is empty return");
            return;
        }
        rocketLaunchLimitList.add(0, 0);
        Map<Integer, RocketRewardConfigData> rocketLevelMap = rocketRewardConfigDataList.stream().collect(Collectors.toMap(RocketRewardConfigData::getRocketLevel, Function.identity()));

        int addOld = oldRocketLevelV2Data.getNextLevelAdd();
        int totalAdd = addOld + addScore;
        int calcIndex = MatchUtils.getIndexByScore(totalAdd, rocketLaunchLimitList);
        int afterIndex = calcIndex + oldIndex;
//        logger.info("rocketLaunchLimitList:{} roomId:{} aid:{} addScore:{} oldIndex:{} calcIndex:{} " +
//                        "afterIndex:{} totalAdd:{} oldRocketLevelV2Data:{}",
//                rocketLaunchLimitList, roomId, aid, addScore, oldIndex, calcIndex,
//                afterIndex, totalAdd, JSONObject.toJSONString(oldRocketLevelV2Data));

        if (afterIndex == oldIndex) {
            int level = afterIndex + 1;
            RocketRewardConfigData destRocketRewardConfigData = rocketLevelMap.get(level);
            int levelTotalNew = destRocketRewardConfigData.getRocketLaunchLimit();
            int levelTotalOld = oldRocketLevelV2Data.getNextLevelTotal() == 0 ? levelTotalNew : oldRocketLevelV2Data.getNextLevelTotal();

            // 当日房间每个等级的用户榜
            int levelOld = roomRocketV2Redis.getRocketRankingScore(nowDay, roomId, level, aid);
            roomRocketV2Redis.setRocketRank(nowDay, roomId, level, aid, levelOld + addScore, nowTime);

            roomRocketV2Redis.setRocketLevelV2Data(nowDay, roomId,
                    new RocketLevelV2Data(afterIndex, totalAdd, levelTotalNew));

            BigDecimal newRate = ArithmeticUtils.div(totalAdd, levelTotalNew);
            BigDecimal oldRate = ArithmeticUtils.div(addOld, levelTotalOld);
            if (!newRate.equals(oldRate)) {

                // 发送火箭能量进度改变消息
                asynSendRocketV2(new RocketV2MsgData(nowDay, roomId, level, 0,
                        newRate.multiply(new BigDecimal(100)).intValue(), null));
            }
            // 当日房间用户总榜
            roomRocketV2Redis.incrCommonZSetRankingScore(roomRocketV2Redis.getZSetTotalKey(nowDay, roomId), aid, addScore);
//            logger.info("roomId:{} aid:{} level:{} levelAddNew:{} levelTotalNew:{} newRate:{} " +
//                            "levelAddOld:{} levelTotalOld:{} oldRate:{}",
//                    roomId, aid, level, totalAdd, levelTotalNew, newRate,
//                    addOld, levelTotalOld, oldRate);
        } else if (afterIndex > oldIndex) {
            List<RocketV2MsgData> rocketV2MsgDataList = new ArrayList<>();
            // 更新房间火箭等级
            for (int i = oldIndex + 1; i <= afterIndex; i++) {
                // 满足第i级的火箭发射条件
                RocketRewardConfigData destRocketRewardConfigData = rocketLevelMap.get(i);
                int levelTotalNew = destRocketRewardConfigData.getRocketLaunchLimit();
                int start = 0;
                if (i == oldIndex + 1) {
                    start = oldRocketLevelV2Data.getNextLevelAdd();
                }
                int levelAdd = levelTotalNew - start;

                // 当日房间每个等级的用户榜
                int levelOld = roomRocketV2Redis.getRocketRankingScore(nowDay, roomId, i, aid);
                roomRocketV2Redis.setRocketRank(nowDay, roomId, i, aid, levelOld + levelAdd, nowTime);

                // 当日房间用户总榜
                roomRocketV2Redis.incrCommonZSetRankingScore(roomRocketV2Redis.getZSetTotalKey(nowDay, roomId), aid, levelAdd);

                rocketV2MsgDataList.add(new RocketV2MsgData(nowDay, roomId, i, 1,
                        100, rocketLevelMap.get(i)));
                logger.info("update level roomId:{} aid:{} level:{} levelAdd:{} levelTotalNew:{} newRate:{}",
                        roomId, aid, i, levelAdd, levelTotalNew, 100);
            }

            int nextLevel = afterIndex + 1;
            if (nextLevel <= maxIndex) {
                // 多出来的积分累积到下一等级
                RocketRewardConfigData destRocketRewardConfigData = rocketLevelMap.get(nextLevel);
                int levelTotalNew = destRocketRewardConfigData.getRocketLaunchLimit();
                int levelLeftLimit = rocketLaunchLimitList.get(calcIndex);
                int levelAddNew = totalAdd - levelLeftLimit;

                // 当日房间每个等级的用户榜
                int levelOld = roomRocketV2Redis.getRocketRankingScore(nowDay, roomId, nextLevel, aid);
                roomRocketV2Redis.setRocketRank(nowDay, roomId, nextLevel, aid, levelOld + levelAddNew, nowTime);

                BigDecimal newRate = ArithmeticUtils.div(levelAddNew, levelTotalNew);
                // 发送火箭能量进度改变消息
                rocketV2MsgDataList.add(new RocketV2MsgData(nowDay, roomId, nextLevel, 0,
                        newRate.multiply(new BigDecimal(100)).intValue(), null));

                // 取值 1-7 ,值为已完成的等级值
                roomRocketV2Redis.setRocketLevelV2Data(nowDay, roomId,
                        new RocketLevelV2Data(afterIndex, levelAddNew, levelTotalNew));

                // 当日房间用户总榜
                roomRocketV2Redis.incrCommonZSetRankingScore(roomRocketV2Redis.getZSetTotalKey(nowDay, roomId), aid, levelAddNew);

                logger.info("roomId:{} aid:{} level:{} levelAddNew:{} levelTotalNew:{} newRate:{} " +
                                "levelLeftLimit:{} addOld:{} addScore:{} totalAdd:{} calcIndex:{}",
                        roomId, aid, nextLevel, levelAddNew, levelTotalNew, newRate,
                        levelLeftLimit, addOld, addScore, totalAdd, calcIndex);
            } else {
                // 取值8,值为已完成的等级值
                roomRocketV2Redis.setRocketLevelV2Data(nowDay, roomId,
                        new RocketLevelV2Data(maxIndex, 0, 0));
            }
            asynSendRocketV2List(rocketV2MsgDataList);

        } else {
            // 可能配置更新了
            logger.error("error config refeash not add roomId:{} aid:{} addScore:{} oldIndex:{}  > afterIndex:{}",
                    roomId, aid, addScore, oldIndex, afterIndex);
        }

    }

    private void asynSendRocketV2List(List<RocketV2MsgData> rocketV2MsgDataList) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                for (RocketV2MsgData item : rocketV2MsgDataList) {
                    sendRocketV2LaunchPlatformMsg(item.getNowDay(), item.getRoomId(), item.getRocketLevel(),
                            item.getUpdateLevel(), item.getEnergyRate(), item.getConfigData());
                }
            }
        });
    }

    private void asynSendRocketV2(RocketV2MsgData item) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                sendRocketV2LaunchPlatformMsg(item.getNowDay(), item.getRoomId(), item.getRocketLevel(),
                        item.getUpdateLevel(), item.getEnergyRate(), item.getConfigData());
            }
        });
    }

    /**
     * @param nowDay
     * @param roomId
     * @param rocketLevel 当前等级为新开启的未完成
     * @param updateLevel
     * @param energyRate
     * @param configData
     */
    private void sendRocketV2LaunchPlatformMsg(String nowDay, String roomId, int rocketLevel,
                                               int updateLevel, int energyRate, RocketRewardConfigData configData) {
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        RocketV2LaunchPlatformMsg msg = new RocketV2LaunchPlatformMsg();
        msg.setFromRoomId(roomId);
        msg.setRocketLevel(rocketLevel);
        msg.setEnergyRate(energyRate);
        msg.setUpdateLevel(updateLevel);
        String broadcastRoomId = roomId;
        List<String> rocketRankingList = Collections.emptyList();
        Set<String> oldRoomActors = new HashSet<>();

        if (updateLevel > 0) {
            msg.setRoomName(roomData.getName());
            msg.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            rocketRankingList = roomRocketV2Redis.getRocketRankingList(nowDay, roomId,
                    rocketLevel, 3);
            String top1Uid = !CollectionUtils.isEmpty(rocketRankingList) ? rocketRankingList.get(0) : null;

            RoomActorDetailData actorData = roomActorCache.getData(roomId, top1Uid, false);
            UserInfoObject userInfoObject = new UserInfoObject();
            userInfoObject.setUid(top1Uid);
            userInfoObject.setName(actorData.getName());
            userInfoObject.setHead(actorData.getHead());
            userInfoObject.setViceHost(actorData.getViceHost());
            msg.setTop1User(userInfoObject);
            if (configData.getAllRoomBroadcast() > 0) {
                msg.setAllRoomBroadcast(1);
                msg.setAllRoomMessage(1);
                broadcastRoomId = RoomWebSender.ALL_ROOM + "_" + roomId; // 8591版本会有公屏消息
//                pushCommonLevelUpdate(roomId, roomData, rocketLevel); // 8591版本后或者之前的会有公屏消息
            } else {
                msg.setAllRoomBroadcast(0);
                msg.setAllRoomMessage(0);
//                broadcastRoomId = RoomWebSender.ALL_ROOM + "_" + roomId;
                broadcastRoomId = roomId;
            }

//            pushCommonLevelUpdate(roomId,roomData);
        }

        // 发奖励
        if (updateLevel > 0) {
            // 其他用户延迟10s
            TimerService.getService().addDelay(new DelayTask(10000) {
                @Override
                protected void execute() {
                    handleUpdateLevelReward(roomData, configData, nowDay, rocketLevel);
                }
            });
            rocketLaunchEventReport(roomId, rocketLevel, configData.getRocketLaunchLimit());
        }
        // 在奖励写入redis之后再发广播
        roomWebSender.sendRoomWebMsg(broadcastRoomId, "", msg, false);

        logger.info("sendRocketV2LaunchPlatformMsg={} updateLevel={}", JSONObject.toJSONString(msg), updateLevel);
    }

    // 处理升级奖励下发
    private void handleUpdateLevelReward(MongoRoomData roomData, RocketRewardConfigData configData,
                                         String nowDay, int rocketLevel) {

        String roomId = roomData.getRid();
        List<RocketRewardConfigData.ResourceMeta> roomOwnerMetaList = configData.getRoomOwnerMetaList();
        List<RocketRewardConfigData.ResourceMeta> roomTop3MetaList = configData.getRoomTop3MetaList();
        List<RocketRewardConfigData.ResourceMeta> superMetaList = configData.getSuperMetaList();
        Set<String> alreadyGetUser = new HashSet<>();
        Map<String, List<RocketRewardConfigData.ResourceMeta>> userMapRewardList = new HashMap<>();
        logger.info("roomId:{} rocketLevel:{} roomOwnerMetaList={} roomTop3MetaList={} superMetaList={}",
                roomId, rocketLevel, roomOwnerMetaList.size(), roomTop3MetaList.size(), superMetaList.size());
        // 房主
        if (!CollectionUtils.isEmpty(roomOwnerMetaList)) {
            String roomHostUid = RoomUtils.getRoomHostId(roomId);
            for (RocketRewardConfigData.ResourceMeta item : roomOwnerMetaList) {
                addToTemplateList(nowDay, roomId, roomHostUid, rocketLevel, item, userMapRewardList);
            }
        }

        // top3
        if (!CollectionUtils.isEmpty(roomTop3MetaList)) {
            List<String> rocketRankingList = roomRocketV2Redis.getRocketRankingList(nowDay, roomId, rocketLevel, 3);
            int index = 0;
            int topCount = rocketRankingList.size();
            for (RocketRewardConfigData.ResourceMeta item : roomTop3MetaList) {
                String topUid = index < topCount ? rocketRankingList.get(index) : null;
                if (!StringUtils.isEmpty(topUid)) {
                    addToTemplateList(nowDay, roomId, topUid, rocketLevel, item, userMapRewardList);
                    alreadyGetUser.add(topUid);
                }
                index++;
            }
        }

        Set<String> oldRoomActors = roomPlayerRedis.getRoomActors(roomId);
        // 普通用户
        if (!CollectionUtils.isEmpty(superMetaList)) {
            int superMaxCount = configData.getSuperMaxCount() != null ? configData.getSuperMaxCount() : 10;
            Set<String> roomActors = new HashSet<>(oldRoomActors);
            roomActors.removeAll(alreadyGetUser);
            List<String> roomActorList = new ArrayList<>(roomActors);
            Collections.shuffle(roomActorList);
            logger.info("online count:{} alreadyGetUser count:{}", roomActorList.size(), alreadyGetUser.size());

            int poolSize = superMetaList.size();
            int rewardCount = 0;
            int index = 0;
            Map<String, RocketRewardConfigData.ResourceMeta> superMetaMap = superMetaList.stream().collect(Collectors.toMap(RocketRewardConfigData.ResourceMeta::getMetaId, Function.identity()));
            for (String aid : roomActorList) {
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (actorData.getRobot() == 1) {
                    continue;
                }
                if (rewardCount >= superMaxCount) {
                    break;
                }

                Map<String, Integer> calcMap = this.getCalcMap(superMetaList);
                String drawMetaId = this.getMetaIdByProbability(calcMap);
                if (drawMetaId == null) {
                    continue;
                }
                RocketRewardConfigData.ResourceMeta item = superMetaMap.get(drawMetaId);
                addToTemplateList(nowDay, roomId, aid, rocketLevel, item, userMapRewardList);
                rewardCount++;
                index++;
            }
            logger.info("roomId:{} rocketLevel:{} roomActorsSize:{} poolSize:{} actorIndex:{} rewardCount:{} ", roomId, rocketLevel, roomActorList.size(), poolSize, index, rewardCount);
        }

        if (!CollectionUtils.isEmpty(userMapRewardList)) {
            Set<String> finalOldRoomActors = oldRoomActors;
            int lightSwitch = sysConfigDao.getIntValue(SysConfigDao.ROCKET_CONFIG, SysConfigDao.ROOM_ROCKET_SWITCH_V2_LIGHT_KEY);
            userMapRewardList.forEach((k, v) -> {
                ActorData actorData = actorDao.getActorDataFromCache(k);

                // 发送奖励消息，收到这个消息后再拉接口弹窗
                RocketV2RewardMsg rocketV2RewardMsg = new RocketV2RewardMsg();
                rocketV2RewardMsg.setUid(k);
                rocketV2RewardMsg.setRoomId(roomId);
                rocketV2RewardMsg.setRocketLevel(rocketLevel);
                roomWebSender.sendPlayerWebMsg(roomId, k, k, rocketV2RewardMsg, false);

                v.sort(RESOURCE_PRICE_DESC);
                for (RocketRewardConfigData.ResourceMeta res : v) {
                    if (finalOldRoomActors.contains(k)) {
                        // 在房间，发奖励公屏消息
                        if (lightSwitch == 1) {
                            for (String showUid : finalOldRoomActors) {
                                ActorData showActorData = actorDao.getActorDataFromCache(showUid);
                                boolean isShowSubFanoos = AppVersionUtils.versionCheck(8611, showActorData.getVersion_code(), showActorData.getIntOs());
                                screenMsg(roomId, actorData, res, isShowSubFanoos, showUid);
                            }
                        } else {
                            screenMsg(roomId, actorData, res, false, null);
                        }
                    } else {
                        // 不在房间，发官方消息
                        boolean isShowFanoos = lightSwitch == 1
                                && AppVersionUtils.versionCheck(8611, actorData.getVersion_code(), actorData.getIntOs());
                        commonOfficialMsg(actorData, roomData.getName(), res, isShowFanoos);
                    }
                    // 发奖励
                    ResourceKeyConfigData.ResourceMeta resourceMeta = new ResourceKeyConfigData.ResourceMeta();
                    BeanUtils.copyProperties(res, resourceMeta);
                    resourceKeyHandlerService.sendOneResourceDataNoBroadcast(actorData, resourceMeta, A_TYPE,
                            LEVEL_TITLE_MAP.getOrDefault(rocketLevel, TITLE),
                            LEVEL_TITLE_MAP.getOrDefault(rocketLevel, DESC), 6);
                }
            });
            logger.info("roomId:{} rocketLevel:{} userMapRewardList.size:{}", roomId, rocketLevel, userMapRewardList.size());
        }
    }


    private void addToTemplateList(String day, String roomId, String aid, int level,
                                   RocketRewardConfigData.ResourceMeta srcItem,
                                   Map<String, List<RocketRewardConfigData.ResourceMeta>> poolMap) {
        List<RocketRewardConfigData.ResourceMeta> templateList = poolMap.computeIfAbsent(aid, k -> new ArrayList<>());
        RocketRewardConfigData.ResourceMeta toItem = new RocketRewardConfigData.ResourceMeta();
        BeanUtils.copyProperties(srcItem, toItem);
        templateList.add(toItem);
        // 奖励写入redis
        roomRocketV2Redis.saveRewardList(templateList, day, roomId, aid, level);
        logger.info("addToTemplateList to redis success day:{} roomId:{} aid:{} rocketLevel:{} templateList.size:{} poolMap.size:{}",
                day, roomId, aid, level, templateList.size(), poolMap.size());
    }


    public void screenMsg(String roomId, ActorData actor, RocketRewardConfigData.ResourceMeta res, boolean isShowFanoos,
                          String singleShowUid) {
        // 推送公屏消息
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(actor.getName());
        object.setHighlightColor("#FFE200");
        list.add(object);
        object = new HighlightTextObject();
        object.setText(getDescSub(res, 1, isShowFanoos));
        object.setHighlightColor("#FFE200");
        list.add(object);
        object = new HighlightTextObject();
        object.setText(getDescSub(res, 2, isShowFanoos));
        object.setHighlightColor("#FFE200");
        list.add(object);

        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(actor.getUid());
        msg.setUser_name(actor.getName());
        msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        msg.setText(getDesc(res, actor.getName(), 1, isShowFanoos));
        msg.setText_ar(getDesc(res, actor.getName(), 2, isShowFanoos));
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
        msg.setGame_type("rocket_reward");
        msg.setHide_head(1);

        if (!StringUtils.isEmpty(singleShowUid)) {
            // 对单个用户发公屏消息
            roomWebSender.sendPlayerWebMsg(roomId, singleShowUid, singleShowUid, msg, false);
        } else {
            // 对整个房间发公屏消息
            roomWebSender.sendRoomWebMsg(roomId, "", msg, false);
        }


    }

    public void commonOfficialMsg(ActorData actor, String roomName, RocketRewardConfigData.ResourceMeta resourceMeta, boolean isShowFanoos) {
        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setValid(1);
        officialData.setTo_uid(actor.getUid());
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setNews_type(0);
        officialData.setAtype(0);
        officialData.setTitle(getNoticeDesc(null, roomName, actor.getSlang(), isShowFanoos));
        officialData.setBody(getNoticeDesc(resourceMeta, roomName, actor.getSlang(), isShowFanoos));
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(actor.getUid(), officialData.get_id().toString()));
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg("", actor.getUid(), actor.getUid(), msg, false);
        }
    }

    /**
     * 是否满足获奖条件
     */
    private boolean meetTheAwardConditions(int costBeans, RocketRewardConfigData.ResourceMeta detail) {
        if (detail.getLowerLimit() == null) {
            return true;
        }
        if (detail.getLowerLimit().equals(detail.getUpperLimit())) {
            return costBeans == detail.getLowerLimit();
        } else {
            if (costBeans >= detail.getLowerLimit()) {
                if (detail.getUpperLimit() == null) {
                    return true;
                }
                return costBeans < detail.getUpperLimit();
            }
        }
        return false;
    }

    private Map<String, Integer> getCalcMap(List<RocketRewardConfigData.ResourceMeta> resourceMetaList) {
        Map<String, Integer> calcMap = new HashMap<>();
        for (RocketRewardConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            try {
                int rate = (int) (Float.parseFloat(resourceMeta.getRateNumber()) * 1000);
                if (rate >= 0) {
                    calcMap.put(resourceMeta.getMetaId(), rate);
                }
            } catch (Exception e) {
                logger.info("resourceMeta:{} error:{}", JSONObject.toJSONString(resourceMeta), e.getMessage(), e);
                return null;
            }
        }
        return calcMap;
    }

    public String getMetaIdByProbability(Map<String, Integer> sourceMap) {
        try {
            if (CollectionUtils.isEmpty(sourceMap)) {
                logger.info("getAwardIdByProbability sourceMap is empty");
                return null;
            }
            int total = 0;
            Map<Integer, String> mapRatio = new HashMap<>();
            List<Integer> ratioList = new ArrayList<>(); // 这样大于等于0，小于1000取第一个（key为1000） 、大于等于1000，小于2000取第二个 （key为2000）
            for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
                String awardId = entry.getKey();
                int value = entry.getValue();
                if (value <= 0) {
                    continue;
                }
                total += value;
                mapRatio.put(total, awardId);
                ratioList.add(total);
            }
            if (total == 0) {
                logger.info("getAwardIdByProbability total is zero sourceMap={} ", sourceMap);
                return null;
            }
            int ratio = ThreadLocalRandom.current().nextInt(0, total);//0-(total-1)
            if (!ratioList.contains(ratio)) {
                ratioList.add(ratio);
                Collections.sort(ratioList);
            }

            int index = ratioList.indexOf(ratio);
            int destNum = ratioList.get(index + 1);
            return mapRatio.get(destNum);
        } catch (Exception e) {
            logger.error("getAwardIdByProbability error sourceMap:{} msg:{}", sourceMap, e.getMessage(), e);
            return null;
        }
    }

    public String getMetaIdByProbability2(Map<String, Integer> sourceMap) {
        if (CollectionUtils.isEmpty(sourceMap)) {
            return null;
        }
        
        TreeMap<Integer, String> weightMap = new TreeMap<>();
        int totalWeight = 0;
        
        for (Map.Entry<String, Integer> entry : sourceMap.entrySet()) {
            if (entry.getValue() > 0) {
                totalWeight += entry.getValue();
                weightMap.put(totalWeight, entry.getKey());
            }
        }
        
        if (totalWeight == 0) {
            return null;
        }
        
        int randomWeight = ThreadLocalRandom.current().nextInt(totalWeight);
        return weightMap.higherEntry(randomWeight).getValue();
    }

    private String getDescSub(RocketRewardConfigData.ResourceMeta resourceMeta, Integer slang, boolean isShowFanoos) {
        // -3金币 -2: 钻石 4：礼物
        if (resourceMeta.getResourceType() == -2) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_DIAMONDS_REWARD_DESC_AR_SUB : GET_DIAMONDS_REWARD_DESC_AR_SUB, resourceMeta.getResourceNumber())
                    : String.format(isShowFanoos ? GET_FANOOS_DIAMONDS_REWARD_DESC_SUB : GET_DIAMONDS_REWARD_DESC_SUB, resourceMeta.getResourceNumber());
        } else if (resourceMeta.getResourceType() == -3) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_COINS_REWARD_DESC_AR_SUB : GET_COINS_REWARD_DESC_AR_SUB, resourceMeta.getResourceNumber())
                    : String.format(isShowFanoos ? GET_FANOOS_COINS_REWARD_DESC_SUB : GET_COINS_REWARD_DESC_SUB, resourceMeta.getResourceNumber());
        } else if (resourceMeta.getResourceType() == 4) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_GIFTS_REWARD_DESC_AR_SUB : GET_GIFTS_REWARD_DESC_AR_SUB, resourceMeta.getResourceNumber())
                    : String.format(isShowFanoos ? GET_FANOOS_GIFTS_REWARD_DESC_SUB : GET_GIFTS_REWARD_DESC_SUB, resourceMeta.getResourceNumber());
        } else {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_PROPS_REWARD_DESC_AR_SUB : GET_PROPS_REWARD_DESC_AR_SUB, getResName(resourceMeta.getResourceType(), SLangType.ARABIC), resourceMeta.getResourceTime()) :
                    String.format(isShowFanoos ? GET_FANOOS_PROPS_REWARD_DESC_SUB : GET_PROPS_REWARD_DESC_SUB, getResName(resourceMeta.getResourceType(), SLangType.ENGLISH), resourceMeta.getResourceTime());
        }
    }

    private String getDesc(RocketRewardConfigData.ResourceMeta resourceMeta, String userName, Integer slang, boolean isShowFanoos) {
        // -3金币 -2: 钻石 4：礼物
        if (resourceMeta.getResourceType() == -2) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_DIAMONDS_REWARD_DESC_AR : GET_DIAMONDS_REWARD_DESC_AR, userName, resourceMeta.getResourceNumber())
                    : String.format(isShowFanoos ? GET_FANOOS_DIAMONDS_REWARD_DESC : GET_DIAMONDS_REWARD_DESC, userName, resourceMeta.getResourceNumber());
        } else if (resourceMeta.getResourceType() == -3) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_COINS_REWARD_DESC_AR : GET_COINS_REWARD_DESC_AR, userName, resourceMeta.getResourceNumber())
                    : String.format(isShowFanoos ? GET_FANOOS_COINS_REWARD_DESC : GET_COINS_REWARD_DESC, userName, resourceMeta.getResourceNumber());
        } else if (resourceMeta.getResourceType() == 4) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_GIFTS_REWARD_DESC_AR : GET_GIFTS_REWARD_DESC_AR, userName, resourceMeta.getResourceNumber())
                    : String.format(isShowFanoos ? GET_FANOOS_GIFTS_REWARD_DESC : GET_GIFTS_REWARD_DESC, userName, resourceMeta.getResourceNumber());
        } else {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? GET_FANOOS_PROPS_REWARD_DESC_AR : GET_PROPS_REWARD_DESC_AR, userName, getResName(resourceMeta.getResourceType(), SLangType.ARABIC), resourceMeta.getResourceTime())
                    : String.format(isShowFanoos ? GET_FANOOS_PROPS_REWARD_DESC : GET_PROPS_REWARD_DESC, userName, getResName(resourceMeta.getResourceType(), SLangType.ENGLISH), resourceMeta.getResourceTime());
        }
    }

    private String getNoticeDesc(RocketRewardConfigData.ResourceMeta resourceMeta, String roomName, Integer slang, boolean isShowFanoos) {

        // -3金币 -2: 钻石 4：礼物
        if (resourceMeta == null) {
            return slang == SLangType.ARABIC ? isShowFanoos ? FANOOS_NOTICE_TITLE_AR : ROCKET_NOTICE_TITLE_AR
                    : isShowFanoos ? FANOOS_NOTICE_TITLE : ROCKET_NOTICE_TITLE;
        } else if (resourceMeta.getResourceType() == -2) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? FANOOS_NOTICE_DIAMONDS_DESC_AR : ROCKET_NOTICE_DIAMONDS_DESC_AR, resourceMeta.getResourceNumber(), roomName)
                    : String.format(isShowFanoos ? FANOOS_NOTICE_DIAMONDS_DESC : ROCKET_NOTICE_DIAMONDS_DESC, resourceMeta.getResourceNumber(), roomName);
        } else if (resourceMeta.getResourceType() == -3) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? FANOOS_NOTICE_COINS_DESC_AR : ROCKET_NOTICE_COINS_DESC_AR, roomName, resourceMeta.getResourceNumber())
                    : String.format(isShowFanoos ? FANOOS_NOTICE_COINS_DESC : ROCKET_NOTICE_COINS_DESC, roomName, resourceMeta.getResourceNumber());
        } else if (resourceMeta.getResourceType() == 4) {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? FANOOS_NOTICE_GIFTS_DESC_AR : ROCKET_NOTICE_GIFTS_DESC_AR, resourceMeta.getResourceNumber(), roomName)
                    : String.format(isShowFanoos ? FANOOS_NOTICE_GIFTS_DESC : ROCKET_NOTICE_GIFTS_DESC, resourceMeta.getResourceNumber(), roomName);
        } else {
            return slang == SLangType.ARABIC ? String.format(isShowFanoos ? FANOOS_NOTICE_PROPS_DESC_AR : ROCKET_NOTICE_PROPS_DESC_AR, roomName, getResName(resourceMeta.getResourceType(), SLangType.ARABIC), resourceMeta.getResourceTime())
                    : String.format(isShowFanoos ? FANOOS_NOTICE_PROPS_DESC : ROCKET_NOTICE_PROPS_DESC, roomName, getResName(resourceMeta.getResourceType(), SLangType.ENGLISH), resourceMeta.getResourceTime());
        }
    }

    private void doAddScoreEvent(String roomId, String aid, int score, int actionType) {
        GetActivitySpecialItemsRecordEvent event = new GetActivitySpecialItemsRecordEvent();
        eventReport.track(new EventDTO(event));
    }

    /**
     * @param level          当前已完成最大等级，取值0-8
     * @param nextLevelAdd   level+1级的当前分数
     * @param nextLevelTotal level+1级的总分数
     * @return
     */
    private String getLevelAddValue(int level, int nextLevelAdd, int nextLevelTotal) {
        return String.format("%s-%s-%s", level, nextLevelAdd, nextLevelTotal);
    }

    private String getResName(int resType, int sLang) {
        String nameEn = "resource";
        String nameAr = "الموارد";
        switch (resType) {
            case BaseDataResourcesConstant.TYPE_MIC:
                nameEn = "Mic Frame";
                nameAr = "اطارات المايك";
                break;
            case BaseDataResourcesConstant.TYPE_RIDE:
                nameEn = "Ride";
                nameAr = "المركبات";
                break;
            case BaseDataResourcesConstant.TYPE_BUDDLE:
                nameEn = "Bubble";
                nameAr = "فقاعات الدردشة";
                break;
            case BaseDataResourcesConstant.TYPE_RIPPLE:
                nameEn = "Voice Wave";
                nameAr = "الموجات الصوتية";
                break;
            case BaseDataResourcesConstant.TYPE_FLOAT_SCREEN:
                nameEn = "Floating Profiles";
                nameAr = "زينة البروفايل";
                break;
            case BaseDataResourcesConstant.TYPE_ENTRY_EFFECT:
                nameEn = "Entry effect";
                nameAr = "تأثير الدخول";
                break;
            case BaseDataResourcesConstant.TYPE_MINE_BACKGROUND:
                nameEn = "Room background";
                nameAr = "خلفيات الغرفة";
                break;
            default:
                logger.info("not support res_type={} sLang={}", resType, sLang);
                break;
        }
        if (sLang == SLangType.ARABIC) {
            return nameAr;
        } else {
            return nameEn;
        }
    }

    private void rocketLaunchEventReport(String roomId, int showRocketLevel, int rocketLaunchLimit) {
        RoomRocketLogEvent event = new RoomRocketLogEvent();
        event.setFunction_ver(2);
        event.setUid(RoomUtils.getRoomHostId(roomId));
        event.setRoom_id(roomId);
        event.setRoom_rocket_level(showRocketLevel);
        event.setRoom_rocket_cost(rocketLaunchLimit);
        event.setDate(DateHelper.ARABIAN.formatDateInDay());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }


    public void pushCommonLevelUpdate(String roomId, MongoRoomData roomData, int rocketLevel) {
        String toRoomId = RoomWebSender.ALL_ROOM + "_" + roomId;
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(RoomUtils.getRoomHostId(roomId));
        msg.setUser_name("");
        msg.setUser_head(LEVEL_SCREEN_ICON_MAP.getOrDefault(rocketLevel, ""));
        msg.setText(COMMON_TEXT_EN.replace("#username#", roomData.getName()));
        msg.setText_ar(COMMON_TEXT_AR.replace("#username#", roomData.getName()));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(roomData.getName());
        object.setHighlightColor("#FFE200");
        list.add(object);
        HighlightTextObject viewObject = new HighlightTextObject();
        viewObject.setText(COMMON_VIEW_EN);
        viewObject.setHighlightColor("#37B5FF");
        list.add(viewObject);
        HighlightTextObject viewArObject = new HighlightTextObject();
        viewArObject.setText(COMMON_VIEW_AR);
        viewArObject.setHighlightColor("#37B5FF");
        list.add(viewArObject);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
//        msg.setWeb_type(webType);
//        msg.setWeb_url(webUrl);
        msg.setGame_type(GAME_TYPE_ROCKET_UPDATE_LEVEL);
        roomWebSender.sendRoomWebMsg(toRoomId, null, msg, false, 8592);
    }
}
