package com.quhong.service;

import com.quhong.config.QueenAwardConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.QueenDiscountVO;
import com.quhong.data.vo.QueenExpressListVO;
import com.quhong.data.vo.QueenHallVO;
import com.quhong.data.vo.QueenLikeVO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.QueenDiscountDao;
import com.quhong.mysql.dao.QueenHallDao;
import com.quhong.mysql.dao.QueenHallExpressDao;
import com.quhong.mysql.data.QueenDiscountData;
import com.quhong.mysql.data.QueenHallData;
import com.quhong.mysql.data.QueenHallExpressData;
import com.quhong.redis.ActivityOtherRedis;
import com.quhong.redis.QueenHallRedis;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class QueenHallService {
    private static final Logger logger = LoggerFactory.getLogger(QueenHallService.class);
    private static final String LOCK_KEY = "queen_hall_service_";
    private static final int QUEEN_MONEY_TYPE = 913;
    private static final int QUEEN_MONEY = -500;
    private static final String QUEEN_MONEY_TITLE = "Express Queen";
    private static final String QUEEN_MONEY_DESC = "Express Queen of Queen Hall";
    private static final int CONTENT_LENGTH = 300;
    private static final int QUEEN_PAGE_SIZE = 10;
    private static final Map<String, Integer> DISCOUNT_MAP = new HashMap<String, Integer>();
    static {
        DISCOUNT_MAP.put("100", 100000);
        DISCOUNT_MAP.put("200", 200000);
        DISCOUNT_MAP.put("300", 300000);
        DISCOUNT_MAP.put("500", 500000);
    }

    @Resource
    private QueenHallExpressDao queenHallExpressDao;
    @Resource
    private QueenHallDao queenHallDao;
    @Resource
    private QueenDiscountDao queenDiscountDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private QueenHallRedis queenHallRedis;
    @Resource
    private ActivityOtherRedis activityOtherRedis;
    @Resource
    protected DataCenterService dataCenterService;
    @Resource
    protected QueenAwardConfig queenAwardConfig;


    /**
     * 获取折扣领取状态
     */
    public void setDiscountStatus(QueenHallVO queenHallVO, String uid){
        Map<String, Integer> discountStatus = new HashMap<>();
        discountStatus.put("status100", 0);
        discountStatus.put("status200", 0);
        discountStatus.put("status300", 0);
        discountStatus.put("status500", 0);

        List<QueenHallData> dataList = queenHallDao.getQueenHallDataFromCache();
        Map<String, QueenHallData> keyMap = CollectionUtil.listToKeyMap(dataList, QueenHallData::getUid);
        if(keyMap.get(uid) != null){
            int uidReceiveNum = getReceiveNum(uid);
            for(String mapKey:DISCOUNT_MAP.keySet()){
                int receiveNum = DISCOUNT_MAP.get(mapKey);
                String statusKey = "status" + mapKey;
                if(uidReceiveNum > receiveNum){
                    discountStatus.put(statusKey, 1);
                }
            }
            List<QueenDiscountData> discountList = queenDiscountDao.selectListByUid(uid);
            for(QueenDiscountData discountData:discountList){
                String statusKey = "status" + discountData.getDiscountNum();
                discountStatus.put(statusKey, 0);
            }
        }
        queenHallVO.setDiscountStatus(discountStatus);
    }

    private int getReceiveNum(String uid){
        String activityName = queenAwardConfig.getActivityName();
        return activityOtherRedis.getOtherReachingScore(activityName, uid, ActivityConstant.RECEIVE_RANK, 0);
    }

    private int getSendingNum(String uid){
        String activityName = queenAwardConfig.getActivityName();
        return activityOtherRedis.getOtherReachingScore(activityName, uid, ActivityConstant.SEND_RANK, 0);
    }

    /**
     * 女王名人堂配置
     * @param uid 用户id
     * @return 女王列表、评论列表
     */
    public QueenHallVO getQueenHall(String uid) {
        QueenHallVO queenHallVO = new QueenHallVO();
        queenHallVO.setStartTime(queenAwardConfig.getStartTime());
        queenHallVO.setEndTime(queenAwardConfig.getEndTime());
        queenHallVO.setSendingNum(getSendingNum(uid));
        queenHallVO.setReceiveNum(getReceiveNum(uid));
        setDiscountStatus(queenHallVO, uid);
        ActorData uidActor = actorDao.getActorDataFromCache(uid);
        queenHallVO.setHead(ImageUrlGenerator.generateRoomUserUrl(uidActor.getHead()));

        List<QueenHallData> dataList = queenHallDao.getQueenHallDataFromCache();
        List<QueenHallVO.QueenHallConfig> queenHallList = new ArrayList<>();
        for (QueenHallData queenData : dataList) {
            ActorData actor = actorDao.getActorDataFromCache(queenData.getUid());
            QueenHallVO.QueenHallConfig queenHallConfig = new QueenHallVO.QueenHallConfig();
            BeanUtils.copyProperties(queenData, queenHallConfig);
            queenHallConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
            queenHallConfig.setName(actor.getName());
            queenHallConfig.setAid(actor.getUid());
            queenHallConfig.setIsLike(queenHallRedis.isLikeQueen(uid, queenData.getUid()));
            queenHallConfig.setLikes(queenHallRedis.getLikeQueenSize(queenData.getUid()));
            queenHallList.add(queenHallConfig);
        }
        queenHallVO.setQueenHallList(queenHallList);
        return queenHallVO;
    }

    private void expressQueenDiamondCost(String uid) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(QUEEN_MONEY_TYPE);
        moneyDetailReq.setChanged(QUEEN_MONEY);
        moneyDetailReq.setTitle(QUEEN_MONEY_TITLE);
        moneyDetailReq.setDesc(QUEEN_MONEY_DESC);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_NOT_DIAMONDS);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }
    }

    /**
     * 判断是否女王
     * @param aid 用户id
     */
    private void isQueenUser(String aid){
        List<QueenHallData> dataList = queenHallDao.getQueenHallDataFromCache();
        Map<String, QueenHallData> keyMap = CollectionUtil.listToKeyMap(dataList, QueenHallData::getUid);
        if (keyMap.get(aid) == null) {
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_NOT_QUEEN);
        }
    }

    private void onActivityTime(){
        int curTime = DateHelper.getNowSeconds();
        if(curTime < queenAwardConfig.getStartTime() || curTime > queenAwardConfig.getEndTime()){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }

    /**
     * 表白女王
     * @param uid 用户id
     * @param aid 被评论用户id
     * @param content 评论内容
     * @return 成功或者失败
     */
    public String expressQueen(String uid, String aid, String content) {
        if(content == null || content.length() > CONTENT_LENGTH || content.trim().length() == 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        onActivityTime();
        isQueenUser(aid);
        expressQueenDiamondCost(uid);
        QueenHallExpressData expressData = new QueenHallExpressData();
        expressData.setLikes(0);
        expressData.setUid(uid);
        expressData.setAid(aid);
        expressData.setContent(content);
        expressData.setCtime(DateHelper.getNowSeconds());
        queenHallExpressDao.insertOne(expressData);
        return "success";
    }

    public QueenExpressListVO queenExpressList(String uid, int page) {
        QueenExpressListVO queenExpressListVO = new QueenExpressListVO();
        int userFirstId = -1;
        List<QueenExpressListVO.QueenExpressConfig> queenExpressList = new ArrayList<>();
        QueenHallExpressData userExpressData = queenHallExpressDao.selectOne(uid);
        if(userExpressData != null){
            userFirstId = userExpressData.getId();
            if(page == 1){
                QueenExpressListVO.QueenExpressConfig expressConfig = new QueenExpressListVO.QueenExpressConfig();
                ActorData uidActor = actorDao.getActorDataFromCache(uid);
                ActorData aidActor = actorDao.getActorDataFromCache(userExpressData.getAid());
                BeanUtils.copyProperties(userExpressData, expressConfig);
                expressConfig.setUidHead(ImageUrlGenerator.generateRoomUserUrl(uidActor.getHead()));
                expressConfig.setUidName(uidActor.getName());
                expressConfig.setExpressRid(uidActor.getRid());
                expressConfig.setAidHead(ImageUrlGenerator.generateRoomUserUrl(aidActor.getHead()));
                expressConfig.setAidName(aidActor.getName());
                expressConfig.setIsLike(queenHallRedis.isLikeExpress(uid, userExpressData.getId()));
                expressConfig.setLikes(queenHallRedis.getLikeExpressSize(userExpressData.getId()));
                queenExpressList.add(expressConfig);
            }
        }

        List<QueenHallExpressData> expressDataList = queenHallExpressDao.selectPageList(page, QUEEN_PAGE_SIZE);
        for (QueenHallExpressData expressData : expressDataList) {
            if(userFirstId == expressData.getId()){
                continue;
            }
            ActorData uidActor = actorDao.getActorDataFromCache(expressData.getUid());
            ActorData aidActor = actorDao.getActorDataFromCache(expressData.getAid());
            QueenExpressListVO.QueenExpressConfig expressConfig = new QueenExpressListVO.QueenExpressConfig();
            BeanUtils.copyProperties(expressData, expressConfig);
            expressConfig.setUidHead(ImageUrlGenerator.generateRoomUserUrl(uidActor.getHead()));
            expressConfig.setUidName(uidActor.getName());
            expressConfig.setExpressRid(uidActor.getRid());
            expressConfig.setAidHead(ImageUrlGenerator.generateRoomUserUrl(aidActor.getHead()));
            expressConfig.setAidName(aidActor.getName());
            expressConfig.setIsLike(queenHallRedis.isLikeExpress(uid, expressData.getId()));
            expressConfig.setLikes(queenHallRedis.getLikeExpressSize(expressData.getId()));
            queenExpressList.add(expressConfig);
        }
        queenExpressListVO.setExpressList(queenExpressList);
        queenExpressListVO.setNextUrl(expressDataList.size() < QUEEN_PAGE_SIZE?0:page+1);
        return queenExpressListVO;
    }

    /**
     * 点赞女王
     * @param uid 用户
     * @param aid 女王id
     * @return 成功或者失败
     */
    public QueenLikeVO likeQueen(String uid, String aid) {
        QueenLikeVO queenLikeVO = new QueenLikeVO();
        onActivityTime();
        isQueenUser(aid);
        int isLike = queenHallRedis.isLikeQueen(uid, aid);
        if(isLike == 1){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE);
        }
        queenLikeVO.setIsLike(queenHallRedis.addLikeQueen(uid, aid));
        queenLikeVO.setLikes(queenHallRedis.getLikeQueenSize(aid));
        return queenLikeVO;
    }

    /**
     * 点赞表白记录
     * @param uid 用户id
     * @param likeId  评论id
     * @return 点赞数量
     */
    public QueenLikeVO likeExpress(String uid, int likeId) {
        QueenLikeVO queenLikeVO = new QueenLikeVO();
        onActivityTime();
        int isLike = queenHallRedis.isLikeExpress(uid, likeId);
        if(isLike == 1){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE);
        }
        DistributeLock lock = new DistributeLock(LOCK_KEY + uid + likeId);
        try {
            lock.lock();
            QueenHallExpressData expressData = queenHallExpressDao.selectOne(likeId);
            if(expressData == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            int isSuccess = queenHallRedis.addLikeExpress(uid, likeId);
            if(isSuccess == 0){
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE);
            }
            int likes = queenHallRedis.getLikeExpressSize(likeId);
            queenHallExpressDao.updateOne(likeId, likes);
            queenLikeVO.setLikes(likes);
            queenLikeVO.setIsLike(1);
        } catch (Exception e) {
            logger.error("likeExpress error. uid={} likeId={} error={}", uid, likeId, e);
        } finally {
            lock.unlock();
        }
        return queenLikeVO;
    }


    /**
     * 生成订单id
     * @return 订单id
     */
    public static String getOrderNo() {
        String orderNo = "";
        String transactionNo = String.valueOf((Math.random() * 9 + 1) * 1000000);
        String sdf = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        orderNo = transactionNo.substring(0, 6);
        orderNo = sdf + orderNo;
        return orderNo;
    }

    /**
     * 获取女王折扣记录信息
     * @param uid 用户
     * @return 成功或者失败
     */
    public QueenDiscountVO queenDiscountList(String uid) {
        QueenDiscountVO queenDiscountVO = new QueenDiscountVO();
        List<QueenDiscountVO.QueenDiscountConfig> discountList = new ArrayList<>();
        List<QueenDiscountData> queenDiscountDataList = queenDiscountDao.selectListByUid(uid);
        for (QueenDiscountData discountData : queenDiscountDataList) {
            // ActorData uidActor = actorDao.getActorDataFromCache(discountData.getUid());
            QueenDiscountVO.QueenDiscountConfig discountConfig = new QueenDiscountVO.QueenDiscountConfig();
            BeanUtils.copyProperties(discountData, discountConfig);
            String toUid = discountData.getToUid();
            if(!StringUtils.isEmpty(toUid)){
                ActorData aidActor = actorDao.getActorDataFromCache(toUid);
                discountConfig.setGiveRid(aidActor.getRid());
            }
            discountList.add(discountConfig);
        }
        queenDiscountVO.setDiscountList(discountList);
        return queenDiscountVO;
    }

    /**
     * 获取女王折扣
     * @param uid 用户
     * @return 成功或者失败
     */
    public int getQueenDiscount(String uid, String discountNum) {
        isQueenUser(uid);
        onActivityTime();
        Integer receiveNum = DISCOUNT_MAP.get(discountNum);
        if(receiveNum == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        int uidReceiveNum = getReceiveNum(uid);
        if(uidReceiveNum < receiveNum){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_NUMS_ENOUGH);
        }
        QueenDiscountData discountData = queenDiscountDao.selectOne(uid, discountNum);
        if(discountData == null){
            QueenDiscountData newDiscountData = new QueenDiscountData();
            newDiscountData.setDiscountId(getOrderNo());
            newDiscountData.setUid(uid);
            newDiscountData.setDiscountNum(discountNum);
            newDiscountData.setWriteOff(0);
            newDiscountData.setMtime(DateHelper.getNowSeconds());
            newDiscountData.setCtime(DateHelper.getNowSeconds());
            return queenDiscountDao.insertOne(newDiscountData);
        }else{
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_GET);
        }
    }


    /**
     * 赠送女王折扣
     * @param uid 用户
     * @return 成功或者失败
     */
    public int giveQueenDiscount(String uid, String recordId, int giveRid) {
        isQueenUser(uid);
        onActivityTime();
        if(giveRid == 0){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData aidActor = actorDao.getActorByRid(giveRid);
        if(aidActor == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if(aidActor.getUid().equals(uid)){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_CANNOT_SELF);
        }
        QueenDiscountData discountData = queenDiscountDao.selectOneById(uid, recordId);
        if(discountData == null || !StringUtils.isEmpty(discountData.getToUid()) || discountData.getWriteOff() == 1){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_GIVE);
        }
        discountData.setToUid(aidActor.getUid());
        discountData.setMtime(DateHelper.getNowSeconds());
        return queenDiscountDao.updateOne(discountData);
    }
}
