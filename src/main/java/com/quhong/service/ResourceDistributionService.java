package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 活动奖励资源下发
 *
 * <AUTHOR>
 * @date 2022/9/19
 */
@Service
public class ResourceDistributionService {

    private static final Logger logger = LoggerFactory.getLogger(ResourceDistributionService.class);

    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private HeartRecordDao heartRecordDao;

    /**
     * 下发活动礼物
     *
     * @param uid   获取礼物的用户uid
     * @param sourceId  资源id
     * @param typeEnum  资源类型
     * @param days   礼物有效时间 /天
     * @param num  礼物数量
     * @param title  标题
     * @param desc  描述
     */
    public void sendRewardResource(String uid, Integer sourceId, ActivityRewardTypeEnum typeEnum, Integer days, Integer num, String title, String desc, int officialMsg) {
        if (typeEnum == null) {
            return;
        }
        if (typeEnum == ActivityRewardTypeEnum.DIAMOND) {
            logger.info("distribute diamonds to users. toUid={}, num={}", uid, num);
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(905);
            moneyDetailReq.setChanged(num);
            moneyDetailReq.setTitle(title);
            moneyDetailReq.setDesc(desc);
            moneyDetailReq.setMtime(DateHelper.getNowSeconds());
            mqSenderService.asyncChargeDiamonds(moneyDetailReq);
        } else if (typeEnum == ActivityRewardTypeEnum.COIN) {
            logger.info("send coin reward. uid={}, num={}, title={}", uid, num, title);
            heartRecordDao.changeHeart(uid, num, title, desc);
        } else {
            logger.info("send gift to mq. toUid={}, sourceId={}, rewardType={} time={}", uid, sourceId, typeEnum.getName(), days);
            ResourcesDTO resourcesDTO = new ResourcesDTO();
            resourcesDTO.setUid(uid);
            if (typeEnum == ActivityRewardTypeEnum.BACKGROUND) {
                resourcesDTO.setRoomId(RoomUtils.formatRoomId(uid));
            }
            resourcesDTO.setResId(String.valueOf(sourceId));
            resourcesDTO.setResType(typeEnum.getCode());
            resourcesDTO.setItemsSourceDetail(desc);
            resourcesDTO.setDesc(desc);
            if (typeEnum == ActivityRewardTypeEnum.HONOR_TITLE){
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
            }else {
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
            }
            resourcesDTO.setOfficialMsg(officialMsg);
            resourcesDTO.setmTime(DateHelper.getNowSeconds());
            resourcesDTO.setNum(num != 0 ? num : 1);
            resourcesDTO.setDays(days != 0 ? days : -1);
            mqSenderService.asyncHandleResources(resourcesDTO);
        }
    }
}
