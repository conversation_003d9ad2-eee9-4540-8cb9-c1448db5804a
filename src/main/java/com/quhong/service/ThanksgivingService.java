package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.ThankGivingVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class ThanksgivingService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(ThanksgivingService.class);
    private static final Integer START_TIME = 1700686800;
    private static final Integer END_TIME = 1701032400;
    private static final Integer GIVE_GIFT_ID = ServerConfig.isProduct() ? 665 : 90;
    private static final Integer GIVE_MIC_ID = ServerConfig.isProduct() ? 516 : 559;
    private static final String ACTIVITY_ID = "thanksGiving";
    private static final String ACTIVITY_ID_DEVICE = "thanksGivingDevice";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ActorDao actorDao;


    public ThankGivingVO thankGivingConfig(String activityId, String uid) {

        ThankGivingVO vo = new ThankGivingVO();

        if(!ACTIVITY_ID.equals(activityId)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if(actorData == null || StringUtils.isEmpty(actorData.getTn_id())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        vo.setGetAward(activityCommonRedis.isCommonSetData(ACTIVITY_ID, uid));
        vo.setDeviceGet(activityCommonRedis.isCommonSetData(ACTIVITY_ID_DEVICE, actorData.getTn_id()));
        vo.setStartTime(START_TIME);
        vo.setEndTime(END_TIME);
        return vo;
    }

    public void thankGivingGetAward(String activityId, String uid) {

        int currentTime = DateHelper.getNowSeconds();

        if(currentTime< START_TIME || currentTime > END_TIME){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if(actorData == null || StringUtils.isEmpty(actorData.getTn_id())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if(!ACTIVITY_ID.equals(activityId)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(ACTIVITY_ID + uid)) {

            int getStatus = activityCommonRedis.isCommonSetData(ACTIVITY_ID, uid);
            if(getStatus >= 1) {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_GET);
            }

            int deviceStatus = activityCommonRedis.isCommonSetData(ACTIVITY_ID_DEVICE, actorData.getTn_id());
            if(deviceStatus >= 1){
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_GET);
            }

            distributionService.sendRewardResource(uid, GIVE_GIFT_ID, ActivityRewardTypeEnum.getEnumByName("gift"), 3, 3, ACTIVITY_ID, ACTIVITY_ID, 0);
            distributionService.sendRewardResource(uid, GIVE_MIC_ID, ActivityRewardTypeEnum.getEnumByName("mic"), 3, 0, ACTIVITY_ID, ACTIVITY_ID, 0);
            activityCommonRedis.addCommonSetData(ACTIVITY_ID, uid);
            activityCommonRedis.addCommonSetData(ACTIVITY_ID_DEVICE, actorData.getTn_id());
        }

    }

}
