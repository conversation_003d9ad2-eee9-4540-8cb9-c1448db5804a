package com.quhong.service;

import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.LimitedRechargeVO;
import com.quhong.data.vo.LuckyGlovesVO;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.enums.SLangType;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.GiftSendNumDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class LuckyGlovesService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(LuckyGlovesService.class);
    private static final String ACTIVITY_TITLE_EN = "Lucky Gloves Activity";
    public static final String ACTIVITY_ID = "686cf36b38cbb65e074edc30";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/lucky_gloves2025/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/lucky_gloves2025/?activityId=%s", ACTIVITY_ID);
    private static final List<Integer> GIFT_LEVEL_LIST = Arrays.asList(0, 500, 1000, 3000, 7000);
    private static final List<String> RES_LEVEL_LIST = Arrays.asList("", "LuckyGem500", "LuckyGem1000", "LuckyGem3000", "LuckyGem7000");
    private static final List<String> EVENT_LEVEL_LIST = Arrays.asList("", "Lucky gloves-gem500 reward", "Lucky gloves-gem1000 reward", "Lucky gloves-gem3000 reward", "Lucky gloves-gem7000 reward");
    // 消息文本常量 - 奖励发放
    private static final String GIFT_REWARD_DISTRIBUTED_TITLE_EN = "Lucky Gloves";
    private static final String GIFT_REWARD_DISTRIBUTED_TITLE_AR = "قفازات الحظ";
    private static final String GIFT_REWARD_DISTRIBUTED_BODY_EN = "Congratulations, you have completed today's task and successfully unlocked the gem! The prize \uD83C\uDF81 in the lucky gemstone has been issued to you, go check it out~";
    private static final String GIFT_REWARD_DISTRIBUTED_BODY_AR = "تهانينا، لقد أكملت مهمة اليوم وفتحت الجواهر بنجاح! لقد تم إرسال الجوائز\uD83C\uDF81 في جواهر الحظ لك، اذهب وتحقق منها~!";

    private static final List<Integer> GIFT_NEW_LEVEL_LIST = Arrays.asList(0, 10, 30, 70, 150);
    private static final List<String> RES_NEW_LEVEL_LIST = Arrays.asList("", "LuckyGem10", "LuckyGem30", "LuckyGem70", "LuckyGem150");

    private static String START_NEW_LEVEL_TIME = "2025-07-16";
    @Resource
    private GiftSendNumDao giftSendNumDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            START_NEW_LEVEL_TIME = "2025-07-14";
        }
    }

    // 记录每日拳套礼物的发送数
    private String getZSetDailyGiftActivityId(String activityId, String dateStr) {
        return String.format("dailyGlovesGift:%s:%s", activityId, dateStr);
    }

    public LuckyGlovesVO luckyGlovesConfig(String activityId, String uid) {
        LuckyGlovesVO vo = new LuckyGlovesVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        String currentDay = getDayByBase(activityId, uid);
        String key = getZSetDailyGiftActivityId(activityId, currentDay);
        int sendNum = activityCommonRedis.getCommonZSetRankingScore(key, uid);
        vo.setDayEndTime((int) (DateHelper.ARABIAN.getTodayStartTime() / 1000) +
                (int) TimeUnit.DAYS.toSeconds(1));
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        OtherMyRankVO myInfo = new OtherMyRankVO();
        myInfo.setUid(uid);
        myInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        myInfo.setName(actorData.getName());
        myInfo.setVipLevel(vipInfoDao.getIntVipLevel(uid));
        myInfo.setBadgeList(badgeDao.getBadgeList(uid));
        myInfo.setScore(sendNum);
        vo.setMyInfo(myInfo);
        vo.setNewConfigStartTime(DateHelper.DEFAULT.stringDateToStampSecond(START_NEW_LEVEL_TIME));
        return vo;
    }


    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String uid = giftData.getFrom_uid();
        int sendNum = giftData.getNumber() * giftData.getAid_list().size();
        String currentDay = getDayByBase(activityId, uid);
        String key = getZSetDailyGiftActivityId(activityId, currentDay);
        int oldSendNum = activityCommonRedis.getCommonZSetRankingScore(key, uid);
        int newSendNum = activityCommonRedis.incrCommonZSetRankingScoreSimple(key, uid, sendNum);
        int oldLevel = 0;
        int newLevel = 0;

        int nowTime = DateHelper.getNowSeconds();
        List<String> resKeyList;
        if (nowTime >= DateHelper.DEFAULT.stringDateToStampSecond(START_NEW_LEVEL_TIME)) {
            oldLevel = getNewBaseIndexLevel(oldSendNum, GIFT_NEW_LEVEL_LIST);
            newLevel = getNewBaseIndexLevel(newSendNum, GIFT_NEW_LEVEL_LIST);
            resKeyList = RES_NEW_LEVEL_LIST;
        } else {
            oldLevel = getNewBaseIndexLevel(oldSendNum, GIFT_LEVEL_LIST);
            newLevel = getNewBaseIndexLevel(newSendNum, GIFT_LEVEL_LIST);
            resKeyList = RES_LEVEL_LIST;
        }

        // 如果等级提升，下发奖励
        if (newLevel > oldLevel) {
            // 跨级的，需要把之前等级的key都下发
            for (int level = oldLevel + 1; level <= newLevel; level++) {
                if (level < resKeyList.size() && StringUtils.hasLength(resKeyList.get(level))) {
                    String resKey = resKeyList.get(level);
                    String eventTitle = EVENT_LEVEL_LIST.get(level);
                    resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
                    doActivityParticipationEvent(activityId, uid, level);
                    logger.info("sendGiftHandle reward sent to uid: {}, level: {}, resKey: {}", uid, level, resKey);
                }
            }
            // 发送奖励已发放的系统消息
            sendOfficialMsg(uid, GIFT_REWARD_DISTRIBUTED_TITLE_EN, GIFT_REWARD_DISTRIBUTED_TITLE_AR,
                    GIFT_REWARD_DISTRIBUTED_BODY_EN, GIFT_REWARD_DISTRIBUTED_BODY_AR, ACTIVITY_URL);
            logger.info("sendGiftHandle reward distribution message sent to uid: {}", uid);
        }
    }

    public void fixAllToNewLevel() {
        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        String key = getZSetDailyGiftActivityId(ACTIVITY_ID, currentDay);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(key, 20000);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String uid = entry.getKey();
            int sendNum = entry.getValue();
            int oldLevel = getNewBaseIndexLevel(sendNum, GIFT_LEVEL_LIST);
            int newLevel = getNewBaseIndexLevel(sendNum, GIFT_NEW_LEVEL_LIST);
            if (newLevel > oldLevel) {
                for (int level = oldLevel + 1; level <= newLevel; level++) {
                    if (level < RES_NEW_LEVEL_LIST.size() && StringUtils.hasLength(RES_NEW_LEVEL_LIST.get(level))) {
                        String resKey = RES_NEW_LEVEL_LIST.get(level);
                        String eventTitle = EVENT_LEVEL_LIST.get(level);
                        resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
                        doActivityParticipationEvent(ACTIVITY_ID, uid, level);
                        logger.info("fixAllToNewLevel reward sent to uid: {}, level: {}, resKey: {}", uid, level, resKey);
                    }
                }
            }
        }
    }

    /**
     * 发送系统消息
     */
    private void sendOfficialMsg(String uid, String titleEn, String titleAr, String bodyEn, String bodyAr, String url) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("sendOfficialMsg actor not found for uid: {}", uid);
            return;
        }
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleAr : titleEn;
        String body = slang == SLangType.ARABIC ? bodyAr : bodyEn;
        String actText = slang == SLangType.ARABIC ? "شاهد" : "View";
        commonOfficialMsg(uid, "", 0, 0, actText, title, body, url);
    }

    private void doActivityParticipationEvent(String activityId, String uid, int step) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name("Lucky gloves");
        event.setActive_id(activityId);
        event.setActivity_stage(step);
        event.setActivity_stage_desc("");
        eventReport.track(new EventDTO(event));
    }
}
