package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.dto.TimeCapsuleDTO;
import com.quhong.data.vo.TimeCapsuleVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.TimeCapsuleDao;
import com.quhong.mongo.dao.UserCapsuleDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.TimeCapsuleData;
import com.quhong.mongo.data.UserCapsuleData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class TimeCapsuleService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(TimeCapsuleService.class);
    private static final Integer AWARD_GIFT = ServerConfig.isProduct() ? 682 : 823;
    private static final String ACTIVITY_TITLE = "Time Capsule";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private TimeCapsuleDao timeCapsuleDao;
    @Resource
    private TimeCapsuleService timeCapsuleService;
    @Resource
    private UserCapsuleDao userCapsuleDao;

    @Cacheable(value = "getWishDataList", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<TimeCapsuleData> getWishDataList(){
        return timeCapsuleDao.findAll();
    }


    public String getWishActivityId(String activityId, String docId){
        return String.format("%s:%s", activityId, docId);
    }

    public String getJoinWishKey(String activityId){
        return String.format("joinWish:%s", activityId);
    }

    public String getWriteWishKey(String activityId){
        return String.format("writeWish:%s", activityId);
    }

    public String getWriteWishTnKey(String activityId){
        return String.format("writeWishTn:%s", activityId);
    }

    public TimeCapsuleVO timeCapsuleConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        TimeCapsuleVO vo = new TimeCapsuleVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if(actorData == null || StringUtils.isEmpty(actorData.getTn_id())){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        // 用户是否已填写
        String writeWishKey = getWriteWishKey(activityId);
        vo.setUserWrite(activityCommonRedis.isCommonSetData(writeWishKey, uid));

        String writeWishTnKey = getWriteWishTnKey(activityId);
        String tnId = actorData.getTn_id();
        vo.setUserWrite(activityCommonRedis.isCommonSetData(writeWishTnKey, tnId));


        String joinWishKey = getJoinWishKey(activityId);
        vo.setJoinNum(activityCommonRedis.getCommonSetNum(joinWishKey) * 3);

        List<TimeCapsuleVO.WishInfo> wishInfoList = new ArrayList<>();
        List<TimeCapsuleData> wishDataList = timeCapsuleService.getWishDataList();
        for (TimeCapsuleData timeCapsuleData: wishDataList) {
            TimeCapsuleVO.WishInfo wishInfo = new TimeCapsuleVO.WishInfo();
            String docId = timeCapsuleData.get_id().toString();
            wishInfo.setDocId(docId);
            wishInfo.setWishEn(timeCapsuleData.getWishEn());
            wishInfo.setWishAr(timeCapsuleData.getWishAr());

            String wishActivityId = getWishActivityId(activityId, docId);
            wishInfo.setLikeNum(activityCommonRedis.getCommonSetNum(wishActivityId) * 3);
            wishInfo.setLikeStatus(activityCommonRedis.isCommonSetData(wishActivityId, uid));
            wishInfoList.add(wishInfo);
        }

        vo.setWishInfoList(wishInfoList);
        return vo;
    }

    public void timeCapsuleJoinWish(String activityId, String uid) {

        checkActivityTime(activityId);
        String joinWishKey = getJoinWishKey(activityId);
        int isMember = activityCommonRedis.isCommonSetData(joinWishKey, uid);
        if(isMember == 0){
            activityCommonRedis.addCommonSetData(joinWishKey, uid);
        }
    }

    public void timeCapsuleLike(String activityId, String uid, String docId) {

        checkActivityTime(activityId);
        String wishActivityId = getWishActivityId(activityId, docId);
        activityCommonRedis.addCommonSetData(wishActivityId, uid);
    }

    public void timeCapsuleWriteWish(TimeCapsuleDTO dto) {
        String activityId = dto.getActivityId();
        if(StringUtils.isEmpty(activityId)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);

        String uid = dto.getUid();
        if(StringUtils.isEmpty(uid)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        String wishInfo = dto.getWishInfo();

        if(StringUtils.isEmpty(wishInfo) || wishInfo.length() == 0){
            logger.error("not find wishInfo {}", uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        if(wishInfo.length() > 400){
            logger.error("too long wishInfo {}", uid);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }


        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if(actorData == null || StringUtils.isEmpty(actorData.getTn_id())){
            logger.error("user tnId is error {}", uid);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        // 设备检测
        String tnId = actorData.getTn_id();
        String writeWishTnKey = getWriteWishTnKey(activityId);
        int isWriteTn = activityCommonRedis.isCommonSetData(writeWishTnKey, tnId);
        if(isWriteTn > 0){
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        // 用户检测
        String writeWishKey = getWriteWishKey(activityId);
        int isWrite = activityCommonRedis.isCommonSetData(writeWishKey, uid);
        if(isWrite > 0){
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        synchronized (stringPool.intern(ACTIVITY_TITLE + uid)) {
            UserCapsuleData userCapsuleData = userCapsuleDao.findData(uid, 1);
            if(userCapsuleData != null){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            userCapsuleData = new UserCapsuleData();
            userCapsuleData.setUid(uid);
            userCapsuleData.setWishInfo(wishInfo);
            userCapsuleDao.save(userCapsuleData);

            activityCommonRedis.addCommonSetData(writeWishTnKey, tnId);
            activityCommonRedis.addCommonSetData(writeWishKey, uid);
            distributionService.sendRewardResource(uid, AWARD_GIFT, ActivityRewardTypeEnum.getEnumByName("gift"), 7, 10, ACTIVITY_TITLE, ACTIVITY_TITLE, 0);
        }
    }

}
