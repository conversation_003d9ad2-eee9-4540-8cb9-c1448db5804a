package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GetActivityTicketsEvent;
import com.quhong.analysis.StarBeatGameLogEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.data.vo.ShootRecordVO;
import com.quhong.data.vo.ShootVO;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.FriendsData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.msg.obj.LuckyGiftRewardObject;
import com.quhong.msg.room.RoomLuckGiftRewardMsg;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Component
public class ShootActivityService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(ShootActivityService.class);
    private static final String ACTIVITY_ID = "67064b67d2c43eda8c149dd2";
    private static final String ACTIVITY_TITLE_EN = "Shot & Surprise!";
    private static final String ACTIVITY_TITLE_EN_PAY = "Shot & Surprise!-reward1";
    private static final String ACTIVITY_TITLE_EN_NO_PAY = "Shot & Surprise!-reward2";
    private static final String ACTIVITY_TITLE_EN_SPECIAL = "Shot & Surprise!-reward3";
    private static final String ACTIVITY_TITLE_AR = "التصويب و المفاجأة !";
    private static final String ACTIVITY_DESC = "Shot & Surprise Rewards";
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/op_1727086329_icon.png";
    private static final Integer ACTIVITY_SHARE_ID = ServerConfig.isProduct() ? 28 : 13;
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ?
            String.format("https://static.youstar.live/shot_jackpot202410/?activityId=%s&shareId=%s", ACTIVITY_ID, ACTIVITY_SHARE_ID) :
            String.format("https://test2.qmovies.tv/shot_jackpot202410/?activityId=%s&shareId=%s", ACTIVITY_ID, ACTIVITY_SHARE_ID);
    // 卡种类
    private static final String BEFORE_CARD_KEY = "before";
    private static final String CENTER_CARD_KEY = "center";
    private static final String AFTER_CARD_KEY = "after";
    private static final String GUARD_CARD_KEY = "guard";
    private static final String FAILED_CARD_KEY = "failed";
    private static final String CARD_PAGE_CONFIG_KEY = "Shotsignindraw";   // 打开页面抽卡片
    private static final String CARD_GIFT_CONFIG_KEY = "Shotsenddraw";   // 发送礼物卡片
    private static final String CARD_FRIEND_CONFIG_KEY = "Shotgiverdraw";   // 赠送好友
    private static final List<String> CARD_TYPES = Arrays.asList(BEFORE_CARD_KEY, CENTER_CARD_KEY, AFTER_CARD_KEY);
    private static final int LIMIT_INIT_POOL = 10;
    private static final Integer RECORD_PAGE_SIZE = 10;

    private static final String POOL_SIZE_PAY_KEY = "shotSurprisepay";   // 付费用户key
    private static final String POOL_SIZE_NO_PAY_KEY = "shotSurprisenopay";   // 非付费用户key
    private static final String POOL_SIZE_SPECIAL_KEY = "shotSurprisespecial";   // 特殊用户key

    private static final List<String> DRAW_USER_LIST = Arrays.asList("620bd31e464de81263b9c0ef", "616f2433541b4e39f7dabca4", "62a45b81c8dad4488d482810", "5c88a0f166dc630038467c4e", "5ab6ac5c1bad4814cbd56daa", "62a3fb110460bd3470eb8c76", "5e3521cfb271b6040a95e13a", "5d82dd40abb01a009560e3a4", "6606588cbe90384a14cd5279");
    private static final List<Integer> DRAW_NUM_LIST = Arrays.asList(1, 10);
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final Map<String, Integer> CARD_TYPE_MAP = new HashMap<>();
    static {
        CARD_TYPE_MAP.put(BEFORE_CARD_KEY, 1);
        CARD_TYPE_MAP.put(CENTER_CARD_KEY, 2);
        CARD_TYPE_MAP.put(AFTER_CARD_KEY, 3);
        CARD_TYPE_MAP.put(GUARD_CARD_KEY, 4);
    }

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;
    @Resource
    private IMsgService iMsgService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private FriendsDao friendsDao;



    private String getHashActivityId(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }

    // 抽奖品Key
    private String getListDrawPrizeKey(String activityId, String resKey){
        return String.format("drawPrize:%s:%s", activityId, resKey);
    }

    private String getShotSendCardKey(String activityId, String uid){
        return String.format("shotSendCard:%s:%s", activityId, uid);
    }

    // 抽卡片Key
    private String getListDrawCardKey(String activityId, String origin){
        return String.format("drawCard:%s:%s", activityId, origin);
    }

    private String getDeviceLimitKey(String activityId){
        return String.format("deviceLimit:%s:%s", activityId, DateHelper.ARABIAN.formatDateInDay());
    }

    // 滚动记录
    private String getScrollKey(String activityId){
        return String.format("scroll:%s", activityId);
    }

    // 历史记录key
    private String getHistoryRecordListKey(String activityId, String uid){
        return String.format("historyRecord:%s:%s", activityId, uid);
    }


    public ShootVO shootConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        // 活动配置相关
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> cardMapData = activityCommonRedis.getCommonHashAll(hashActivityId);
        ShootVO vo = new ShootVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setFirstEntry(cardMapData.getOrDefault("firstEntry", 0));
        if(vo.getFirstEntry() == 0){
            activityCommonRedis.setCommonHashNum(hashActivityId, "firstEntry", 1);
        }

        // 当日抽卡牌
        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        int currentDayDraw = cardMapData.getOrDefault(currentDay, 0);
        if(currentDayDraw <= 0 && inActivityTime(activityId)){
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            String tnId = actorData.getTn_id();
            boolean flag = !StringUtils.isEmpty(tnId);

            String deviceLimitKey = getDeviceLimitKey(activityId);
            if (activityCommonRedis.getCommonZSetRankingScore(deviceLimitKey, tnId) >= 1){
                flag = false;
            }

            if (flag){
                String cardKey = this.drawCard(activityId, uid, CARD_PAGE_CONFIG_KEY);
                if(!FAILED_CARD_KEY.equals(cardKey)){
                    int cardNum = cardMapData.getOrDefault(cardKey, 0);
                    cardMapData.put(cardKey, cardNum+1);
                    vo.setDrawCardVO(this.getOpenDrawCard(cardKey));
                }
                this.doCardReportEvent(uid, cardKey, "1");
                activityCommonRedis.setCommonHashNum(hashActivityId, currentDay, 1);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(deviceLimitKey, tnId, 1);
            }
        }

        vo.setBefore(cardMapData.getOrDefault(BEFORE_CARD_KEY, 0));
        vo.setCenter(cardMapData.getOrDefault(CENTER_CARD_KEY, 0));
        vo.setAfter(cardMapData.getOrDefault(AFTER_CARD_KEY, 0));
        vo.setGuard(cardMapData.getOrDefault(GUARD_CARD_KEY, 0));


        // 射门抽奖滚屏记录
        List<PrizeConfigVO> drawRecordList = new ArrayList<>();
        List<String> scrollList = activityCommonRedis.getCommonListRecord(getScrollKey(activityId));
        for (String item : scrollList) {
            PrizeConfigVO drawRecord = JSON.parseObject(item, PrizeConfigVO.class);
            ActorData actor = actorDao.getActorDataFromCache(drawRecord.getUid());
            drawRecord.setUserName(actor.getName());
            drawRecordList.add(drawRecord);
        }
        vo.setDrawRecordList(drawRecordList);
        return vo;
    }

    private void doCardReportEvent(String uid, String cardType, String cardOrigin) {
        GetActivityTicketsEvent event = new GetActivityTicketsEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_tickets_resource(cardOrigin);
        event.setActivity_tickets_type(CARD_TYPE_MAP.getOrDefault(cardType, 0));
        event.setGet_activity_tickets(1);
        eventReport.track(new EventDTO(event));
    }

    public String drawCard(String activityId, String uid, String origin){
        synchronized (stringPool.intern(uid)) {
            String poolDrawKey = this.getListDrawCardKey(activityId, origin);
            this.initDrawPool(origin, poolDrawKey);
            String cardKey = activityCommonRedis.leftPopCommonListKey(poolDrawKey);
            if (StringUtils.isEmpty(cardKey)) {
                throw new CommonH5Exception(HttpCode.SERVER_ERROR);
            }
            if (!FAILED_CARD_KEY.equals(cardKey)){
                activityCommonRedis.incCommonHashNum(getHashActivityId(activityId, uid), cardKey, 1);
            }
            return cardKey;
        }
    }

    // 初始化奖池
    public void initDrawPool(String origin, String poolDrawKey){
        int poolSize = activityCommonRedis.getCommonListSize(poolDrawKey);
        if(poolSize <= LIMIT_INIT_POOL){
            List<String> poolList = new ArrayList<>();
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(origin);
            if(resourceKeyConfigData != null) {
                List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();
                for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceMetaList) {
                    int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                    poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
                }
                Collections.shuffle(poolList);
                activityCommonRedis.rightPushAllCommonList(poolDrawKey, poolList);
            }
        }
    }

    private PrizeConfigVO getOpenDrawCard(String cardKey){
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(CARD_PAGE_CONFIG_KEY);
        if(resourceKeyConfigData != null) {
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));;
            ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(cardKey);
            PrizeConfigVO drawCardVO = new PrizeConfigVO();
            drawCardVO.setIconEn(resourceMeta.getResourceIcon());
            drawCardVO.setNameEn(resourceMeta.getResourceNameEn());
            drawCardVO.setNameAr(resourceMeta.getResourceNameAr());
            return drawCardVO;

        }
        return null;
    }

    // 发送礼物抽奖
    public void shootGiftHandle(SendGiftData giftData, String activityId){
        int totalNum = giftData.getNumber() * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();
        Map<String, Integer> rewardMap = new HashMap<>();
        for (int i=0; i < totalNum; i++){
            String cardKey = this.drawCard(activityId, fromUid, CARD_GIFT_CONFIG_KEY);
            this.doCardReportEvent(fromUid, cardKey, "2");

            if (!FAILED_CARD_KEY.equals(cardKey)){
                rewardMap.compute(cardKey, (k, v) -> {
                    if (null == v) {
                        v = 1;
                    }else {
                        v += 1;
                    }
                    return v;
                });
            }
        }

        if (!rewardMap.isEmpty()){
            RoomLuckGiftRewardMsg rewardMsg = new RoomLuckGiftRewardMsg();
            List<LuckyGiftRewardObject> luckyGiftRewardList = new ArrayList<>();
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(CARD_GIFT_CONFIG_KEY);
            if(resourceKeyConfigData == null) {
                return;
            }
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            for (String key : rewardMap.keySet()) {
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(key);
                LuckyGiftRewardObject rewardObject = new LuckyGiftRewardObject();
                rewardObject.setIcon(resourceMeta.getResourceIcon());
                rewardObject.setName("");
                rewardObject.setType(2);
                rewardObject.setValue(rewardMap.get(key));
                luckyGiftRewardList.add(rewardObject);
            }

            rewardMsg.setAid(fromUid);
            rewardMsg.setA_type(0);
            rewardMsg.setType(2);
            rewardMsg.setLucky_gift_reward(luckyGiftRewardList);
            marsMsgActivityService.asyncSendPlayerMsg(roomId, fromUid, fromUid, rewardMsg, false);
        }
    }


    // 赠送守卫卡
    public ShootVO sendCard(String activityId, String uid, String giveRid){
        checkActivityTime(activityId);
        ActorData aidActor = actorDao.getActorByStrRid(giveRid);
        if(aidActor == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if(aidActor.getUid().equals(uid)){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_CANNOT_SELF);
        }
        logger.info("sendCard giveRid:{}, aidActor:{}", giveRid, JSONObject.toJSONString(aidActor));
        int currentTime = DateHelper.getNowSeconds();
        ShootVO vo = new ShootVO();
        synchronized (stringPool.intern(uid)) {
            int guardNum = activityCommonRedis.getCommonHashValue(getHashActivityId(activityId, uid), GUARD_CARD_KEY);
            if(guardNum <= 0){
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }

            activityCommonRedis.incCommonHashNum(getHashActivityId(activityId, uid), GUARD_CARD_KEY, -1);

            String aid = aidActor.getUid();
            String cardKey = this.drawCard(activityId, uid, CARD_FRIEND_CONFIG_KEY);
            this.doCardReportEvent(aid, cardKey, "3");
            activityCommonRedis.incCommonHashNum(getHashActivityId(activityId, aid), cardKey, 1);
            ShootRecordVO.ShootRecord uidShootRecord = new ShootRecordVO.ShootRecord();
            uidShootRecord.setFriendRid(aid);
            uidShootRecord.setStatus(1);
            uidShootRecord.setFriendCard(cardKey);
            uidShootRecord.setCtime(currentTime);


            ShootRecordVO.ShootRecord aidShootRecord = new ShootRecordVO.ShootRecord();
            aidShootRecord.setFriendRid(uid);
            aidShootRecord.setStatus(2);
            aidShootRecord.setFriendCard(cardKey);
            aidShootRecord.setCtime(currentTime);
            activityCommonRedis.addCommonListData(getShotSendCardKey(activityId, uid), JSONObject.toJSONString(uidShootRecord));
            activityCommonRedis.addCommonListData(getShotSendCardKey(activityId, aid), JSONObject.toJSONString(aidShootRecord));
            sendPrivateMsg(uid, aidActor.getUid());

            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(CARD_FRIEND_CONFIG_KEY);
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(cardKey);
            PrizeConfigVO drawCardVO = new PrizeConfigVO();
            drawCardVO.setIconEn(resourceMeta.getResourceIcon());
            drawCardVO.setNameEn(resourceMeta.getResourceNameEn());
            drawCardVO.setNameAr(resourceMeta.getResourceNameAr());
            vo.setDrawCardVO(drawCardVO);
        }
        return vo;
    }

    public void sendPrivateMsg(String uid, String aid){
        try {
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            SendChatMsgDTO msgDto = new SendChatMsgDTO();
            msgDto.setUid(uid);
            msgDto.setAid(aid);
            msgDto.setMsgType(MsgType.SHARE_ACTIVITY);
            msgDto.setOs(actorData.getIntOs());
            msgDto.setMsgBody("");
            msgDto.setSlang(actorData.getSlang());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("shareId", ACTIVITY_SHARE_ID);
            msgDto.setMsgInfo(jsonObject);
            msgDto.setVersioncode(actorData.getVersion_code());
            msgDto.setNew_versioncode(5);
            HttpResult<Object> ret = iMsgService.sendMsg(msgDto);
            logger.info("inner msg success msgDto={} return--> code={} msg={}", msgDto, ret.getCode(), ret.getMsg());
        }catch (Exception e){
            logger.error("sendPrivateMsg error:{}", e.getMessage(), e);
        }

    }

    // 赠送/接收记录
    public ShootRecordVO shootRecord(String activityId, String uid, int page) {
        int pageSize = 10;
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        String shotSendCardKey = getShotSendCardKey(activityId, uid);
        List<String> shotSendCardList = activityCommonRedis.getCommonListPageRecord(shotSendCardKey, start, end);
        ShootRecordVO shootRecordVO = new ShootRecordVO();
        List<ShootRecordVO.ShootRecord> shootRecordList = new ArrayList<>();

        for (String record : shotSendCardList) {
            ShootRecordVO.ShootRecord shootRecord =  JSONObject.parseObject(record, ShootRecordVO.ShootRecord.class);
            ActorData friendActor = actorDao.getActorDataFromCache(shootRecord.getFriendRid());
            shootRecord.setFriendRid(friendActor.getStrRid());
            shootRecordList.add(shootRecord);
        }
        shootRecordVO.setShootRecordList(shootRecordList);
        if(shootRecordList.size() < pageSize){
            shootRecordVO.setNextUrl(-1);
        }else {
            shootRecordVO.setNextUrl(page + 1);
        }
        return shootRecordVO;
    }

    /**
     * 使用卡片抽奖
     */
    private void initDrawPrizePool(Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String drawPrizeKey){
        int poolSize = activityCommonRedis.getCommonListSize(drawPrizeKey);
        if(poolSize <= LIMIT_INIT_POOL){
            List<String> poolList = new ArrayList<>();
            for(String prizeKey: resourceMetaMap.keySet()){
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(prizeKey);
                int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
            }
            Collections.shuffle(poolList);
            activityCommonRedis.rightPushAllCommonList(drawPrizeKey, poolList);
        }
    }

    // 抽奖
    private String shootDraw(String activityId, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String resKey){
        String drawPrizeKey = getListDrawPrizeKey(activityId, resKey);
        this.initDrawPrizePool(resourceMetaMap, drawPrizeKey);
        String awardKey = activityCommonRedis.leftPopCommonListKey(drawPrizeKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return awardKey;
    }

    // 获取抽奖配置
    private ResourceKeyConfigData getResourceKeyConfig(String uid){
        if(DRAW_USER_LIST.contains(uid)){
            return  resourceKeyConfigDao.findByKey(POOL_SIZE_SPECIAL_KEY);
        }
        int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
        if(rechargeMoney >= 5){
            return  resourceKeyConfigDao.findByKey(POOL_SIZE_PAY_KEY);
        }else {
            return  resourceKeyConfigDao.findByKey(POOL_SIZE_NO_PAY_KEY);
        }
    }

    private String getResTitleByKey(String resKey){
        if (POOL_SIZE_SPECIAL_KEY.equals(resKey)){
            return ACTIVITY_TITLE_EN_SPECIAL;
        }

        if (POOL_SIZE_PAY_KEY.equals(resKey)){
            return ACTIVITY_TITLE_EN_PAY;
        }
        return ACTIVITY_TITLE_EN_NO_PAY;
    }

    public ShootVO shootCardDraw(String activityId, String uid, int zone, String cardType, int amount) {
        checkActivityTime(activityId);
        if(!CARD_TYPES.contains(cardType)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if(!DRAW_NUM_LIST.contains(amount)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        synchronized (stringPool.intern("draw" + uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> cardNumMap =  activityCommonRedis.getCommonHashAll(hashActivityId);
            int cardNum = cardNumMap.getOrDefault(cardType, 0);
            if(cardNum <= 0 || cardNum < amount){
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            int afterNum = activityCommonRedis.incCommonHashNum(hashActivityId, cardType, -amount);
            cardNumMap.put(cardType, afterNum);

            int currentTime = DateHelper.getNowSeconds();
            List<PrizeConfigVO> drawRecordList = new ArrayList<>();
            String rewardTitle = "";
            ShootVO vo = new ShootVO();
            vo.setBefore(cardNumMap.getOrDefault(BEFORE_CARD_KEY, 0));
            vo.setCenter(cardNumMap.getOrDefault(CENTER_CARD_KEY, 0));
            vo.setAfter(cardNumMap.getOrDefault(AFTER_CARD_KEY, 0));
            vo.setGuard(cardNumMap.getOrDefault(GUARD_CARD_KEY, 0));

            if (zone == 0) {   // 不在区域内没得抽奖
                logger.info("shootCardDraw not in zone activityId:{}, uid:{}, cardType:{}, amount:{}", activityId, uid, cardType, amount);
            }else{
                ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig(uid);
                if(resourceKeyConfigData == null){
                    throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
                }
                String resKey = resourceKeyConfigData.getKey();
                rewardTitle = this.getResTitleByKey(resKey);
                Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
                for (int i = 0; i < amount; i++) {
                    String awardKey = this.shootDraw(activityId, resourceMetaMap, resKey);
                    ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(awardKey);
                    if(resourceMeta == null){
                        continue;
                    }
                    resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, rewardTitle, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
                    PrizeConfigVO drawRecord = new PrizeConfigVO();
                    drawRecord.setUid(uid);
                    drawRecord.setDrawType(awardKey);
                    drawRecord.setRewardType(String.valueOf(resourceMeta.getResourceType()));
                    drawRecord.setNameEn(resourceMeta.getResourceNameEn());
                    drawRecord.setNameAr(resourceMeta.getResourceNameAr());
                    drawRecord.setIconEn(resourceMeta.getResourceIcon());
                    drawRecord.setRewardTime(resourceMeta.getResourceTime());
                    drawRecord.setRewardNum(resourceMeta.getResourceNumber());
                    drawRecord.setRewardPrice(resourceMeta.getResourcePrice());
                    drawRecord.setCtime(currentTime);
                    drawRecordList.add(drawRecord);

                    String jsonRecord = JSON.toJSONString(drawRecord);
                    activityCommonRedis.addCommonListData(getScrollKey(activityId), jsonRecord);
                    activityCommonRedis.addCommonListRecord(getHistoryRecordListKey(activityId, uid), jsonRecord);
                }
            }
            this.doDrawReportEvent(uid, cardType, amount, rewardTitle, drawRecordList);
            vo.setDrawRecordList(drawRecordList);
            return vo;
        }
    }


    private void doDrawReportEvent(String uid, String cardType, int amount, String rewardTitle, List<PrizeConfigVO> drawRecordList) {
        Map<String, Integer> drawRecordMap = drawRecordList.stream().collect(Collectors.groupingBy(PrizeConfigVO::getNameEn, Collectors.summingInt(PrizeConfigVO::getRewardNum)));
        StarBeatGameLogEvent event = new StarBeatGameLogEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE_EN);
        event.setTicket_type(CARD_TYPE_MAP.getOrDefault(cardType, 0));
        event.setCost_ticket(amount);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(drawRecordList.size());
        event.setDraw_detail(rewardTitle);
        event.setDraw_result(JSON.toJSONString(drawRecordMap));
        eventReport.track(new EventDTO(event));
    }

    public ShootVO shootDrawRecord(String activityId, String uid, int page) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;

        String recordListKey = getHistoryRecordListKey(activityId, uid);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        ShootVO drawRecordVO = new  ShootVO();
        List<PrizeConfigVO> drawRecordList = new ArrayList<>();
        for (String item : prizeKeyTimeList) {
            PrizeConfigVO drawRecord = JSON.parseObject(item, PrizeConfigVO.class);
            drawRecordList.add(drawRecord);
        }
        drawRecordVO.setDrawRecordList(drawRecordList);
        if(drawRecordList.size() < RECORD_PAGE_SIZE){
            drawRecordVO.setNextUrl(-1);
        }else {
            drawRecordVO.setNextUrl(page + 1);
        }
        return drawRecordVO;
    }


    public ShootVO shootFriendList(String activityId, String uid, int page){
        int start = (page - 1) * RECORD_PAGE_SIZE;
        ShootVO vo = new ShootVO();

        List<FriendsData> dataList = friendsDao.findDataList(uid, start, RECORD_PAGE_SIZE);
        List<PrizeConfigVO> friendList = new ArrayList<>();
        for (FriendsData friendsData : dataList) {
            String friendAid = Objects.equals(uid, friendsData.getUidFirst()) ? friendsData.getUidSecond() : friendsData.getUidFirst();
            ActorData actorData = actorDao.getActorDataFromCache(friendAid);
            PrizeConfigVO prizeConfigVO = new PrizeConfigVO();
            prizeConfigVO.setUid(friendAid);
            prizeConfigVO.setUserRid(actorData.getStrRid());
            prizeConfigVO.setUserName(actorData.getName());
            prizeConfigVO.setIconEn(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            friendList.add(prizeConfigVO);
        }
        vo.setFriendList(friendList);
        vo.setNextUrl(dataList.size() < RECORD_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

}
