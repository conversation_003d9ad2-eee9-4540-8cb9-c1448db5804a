package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mysql.dao.InviteFissionDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.InviteBackData;
import com.quhong.mysql.data.InviteFissionData;
import com.quhong.redis.BackUserStateRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.ArithmeticUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 拉新裂变活动收齐
 */
@Service
public class InviteFissionCollectService extends OtherActivityService implements TaskMsgHandler {

    public static final String ACTIVITY_ID = "inviteFission";
    private static final Logger logger = LoggerFactory.getLogger(InviteFissionCollectService.class);

    private static final String DAU_2 = "dau2";
    private static final String DAU_3 = "dau3";
    private static final String DAU_5 = "dau5";
    private static final String DAU_7 = "dau7";

    private static final List<Integer> GIFT_BEANS_LIST = Arrays.asList(0, 500, 1000, 2000);

    private static final List<String> GIFT_KEY_LIST = Arrays.asList("gift1", "gift2", "gift3");

    private static final List<Double> MONEY_LEVEL_LIST = Arrays.asList(0.0, 0.99, 19.99, 49.99);

    private static final List<String> MONEY_KEY_LIST = Arrays.asList("charge1", "charge2", "charge3");

    private static final List<String> DAU_KEY_LIST = Arrays.asList(DAU_2, DAU_3, DAU_5, DAU_7);

    private static final List<String> PAY_ITEM_LIST = Arrays.asList("google", "apple", "huawei");

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    public static final Map<Integer, String> DAU_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(2, DAU_2); //
            put(3, DAU_3); //
            put(7, DAU_7); //
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_CHARGE_PRODUCT_MAP = new HashMap<String, String>() {
        {
            put("product_coins_diamonds1", "back_ac_reward_099_key"); //
            put("product_coins_diamonds3", "back_ac_reward_1999_key"); //
            put("product_coins_diamonds4", "back_ac_reward_4999_key"); //
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_DAU_MAP = new HashMap<String, String>() {
        {
            put(DAU_2, "back_ac_reward_day2_key");
            put(DAU_3, "back_ac_reward_day3_key");
            put(DAU_5, "back_ac_reward_day5_key");
            put(DAU_7, "back_ac_reward_day7_key");
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_CHARGE_MAP = new HashMap<String, String>() {
        {
            put("charge1", "back_ac_reward_099_key"); // product_coins_diamonds1
            put("charge2", "back_ac_reward_1999_key"); // product_coins_diamonds3
            put("charge3", "back_ac_reward_4999_key"); // product_coins_diamonds4
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_GIFT_MAP = new HashMap<String, String>() {
        {
            put("gift1", "back_ac_reward_gift1_key");
            put("gift2", "back_ac_reward_gift2_key");
            put("gift3", "back_ac_reward_gift3_key");
            put("gift4", "back_ac_reward_gift4_key");
        }
    };

    public static final Map<String, String> TYPE_BACK_CONFIG_FRIEND_MAP = new HashMap<String, String>() {
        {
            put("friend1", "back_ac_reward_friend1_key");
            put("friend2", "back_ac_reward_friend2_key");
            put("friend3", "back_ac_reward_friend3_key");
            put("friend4", "back_ac_reward_friend4_key");
        }
    };

    private static final int INVITE_ALIVE_DAY = 30;

    private static final List<String> TASK_ALL_LIST = Arrays.asList(
            CommonMqTaskConstant.USER_UP_MIC //
    );


    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private BackUserStateRedis backUserStateRedis;
    @Resource
    private FriendsDao friendsDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private InviteFissionDao inviteFissionDao;

    @PostConstruct
    public void init() {
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ALL_LIST.contains(item)) {
            return;
        }
        handleUserScore(null, data, null, null);
    }

    public void handleUserScore(SendGiftData data, CommonMqTopicData mqData, RechargeInfo rechargeInfo, RechargeInfo refundRechargeInfo) {

        String uid; // 被邀请者
        if (data != null) {
            uid = data.getFrom_uid();
        } else if (mqData != null) {
            uid = mqData.getUid();
        } else if (rechargeInfo != null) {
            uid = rechargeInfo.getUid();
        } else {
            uid = refundRechargeInfo.getUid();
        }

//        if (ServerConfig.isProduct()) {
//            boolean isWhiteTestUser = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
//            if (!isWhiteTestUser) {
//                // 灰度测试,只统计测试用户
//                return;
//            }
//        }

        boolean isReg = ActorUtils.isNewRegisterActor(uid, INVITE_ALIVE_DAY);
        if (!isReg) {
            return;
        }
        InviteFissionData inviteFissionData = inviteFissionDao.getOneByAid(uid);
        if (inviteFissionData == null) {
            return;
        }

        Integer aliveDay = inviteFissionData.getAliveDay();
        String yaoQingUid = inviteFissionData.getUid(); // 邀请者
        BackUserStateData invitedUserData = backUserStateRedis.getInvitedUserData(uid); // 被邀请者领取数据
//        if (invitedUserData == null) {
//            return;
//        }

        InviterUserStateData yaoQingUserdata = backUserStateRedis.getInvitedUserData(yaoQingUid, uid); // 邀请者领取数据
        String now = getDayByBase(ACTIVITY_ID, uid);
        synchronized (stringPool.intern("lock:invite:user:collect:" + uid)) {
            if (mqData != null && CommonMqTaskConstant.USER_UP_MIC.equals(mqData.getItem())) {
                if (aliveDay != null && aliveDay >= 7) {
                    //  已经领取过7天奖励了，这里兼容下老的（按starpage接口计入日活的方式）已经领取完的
                } else {
                    Set<String> daySet = invitedUserData.getDayNewActiveSet();
                    if (!daySet.contains(now)) {
                        daySet.add(now);
                        int totalDayCount = daySet.size();
                        if (DAU_KEY_MAP.containsKey(totalDayCount)) {
                            // 更新被邀请方状态
                            invitedUserData.setDau2State(changeInviteUserState(uid, invitedUserData.getDau2State(), totalDayCount, 2, DAU_2));
                            invitedUserData.setDau3State(changeInviteUserState(uid, invitedUserData.getDau3State(), totalDayCount, 3, DAU_3));
                            invitedUserData.setDau7State(changeInviteUserState(uid, invitedUserData.getDau7State(), totalDayCount, 7, DAU_7));

                            // 更新邀请方状态
                            Map<String, Integer> dayMap = yaoQingUserdata.getDayMapStatus();
                            String level = DAU_KEY_MAP.getOrDefault(totalDayCount, "");
                            int state = dayMap.getOrDefault(level, 0);
                            if (state == 0) {
                                dayMap.put(level, 1);
                            }
                        }
                        backUserStateRedis.saveInvitedUserState(invitedUserData, uid);
                        backUserStateRedis.saveInviterUserState(yaoQingUserdata, yaoQingUid, uid);
                        logger.info("success add sign day uid:{} totalDayCount:{} daySet:{}", uid, totalDayCount, daySet);
                    }
                }
            }

            if (data != null) {
                // 1 发礼物
                int giftBeans = invitedUserData.getGiftBeans();
                Map<String, Integer> giftMapStatus = invitedUserData.getGiftMapStatus();
                Map<String, Integer> yqGiftMapStatus = yaoQingUserdata.getGiftMapStatus();
                long totalPrice = ((long) data.getPrice() * data.getNumber() * data.getAid_list().size());
                giftBeans += (int) totalPrice;
                int giftIndex = getIndexLevel(giftBeans, GIFT_BEANS_LIST);
                if (giftIndex > 0) {
                    invitedUserData.setGiftMapStatus(fillMap(giftMapStatus, GIFT_KEY_LIST.subList(0, giftIndex)));
                    yaoQingUserdata.setGiftMapStatus(fillMap(yqGiftMapStatus, GIFT_KEY_LIST.subList(0, giftIndex)));
                }
                invitedUserData.setGiftBeans(giftBeans);
                backUserStateRedis.saveInvitedUserState(invitedUserData, uid);
                backUserStateRedis.saveInviterUserState(yaoQingUserdata, yaoQingUid, uid);
            } else if (rechargeInfo != null) {
                // 充值
                String rechargeItem = rechargeInfo.getRechargeItem();
                double add = rechargeInfo.getRechargeMoney() == null ? 0 : rechargeInfo.getRechargeMoney();
                if (add > 0 && PAY_ITEM_LIST.contains(rechargeItem)) {
                    double money = invitedUserData.getTotalDollars();
                    money += add;
                    double nowMoney = ArithmeticUtils.round(money, 2);
                    int moneyIndex = getIndexLevel(nowMoney, MONEY_LEVEL_LIST);
                    Map<String, Integer> dollarsMapStatus = invitedUserData.getDollarsMapStatus();
                    if (moneyIndex > 0) {
                        invitedUserData.setDollarsMapStatus(fillMap(dollarsMapStatus, MONEY_KEY_LIST.subList(0, moneyIndex)));
                    }
                    invitedUserData.setTotalDollars(nowMoney);
                    backUserStateRedis.saveInvitedUserState(invitedUserData, uid);
                }

                int rechargeDiamond = rechargeInfo.getRechargeDiamond() == null ? 0 : rechargeInfo.getRechargeDiamond();
                if (rechargeDiamond > 0) {
                    List<Integer> weekTimeList = yaoQingUserdata.getWeekTimeList();
                    List<Integer> weekBeansList = yaoQingUserdata.getWeekBeansList();
                    List<Integer> weekStatusList = yaoQingUserdata.getWeekStatusList();
                    if (CollectionUtils.isEmpty(weekTimeList)) {
                        int start = inviteFissionData.getCtime();
                        weekTimeList.add(start); // 第一周的开始时间
                        if (ServerConfig.isNotProduct()) {
                            weekTimeList.add(start + (int) TimeUnit.HOURS.toSeconds(1)); // 第二周的开始时间
                            weekTimeList.add(start + (int) TimeUnit.HOURS.toSeconds(2));// 第三周的开始时间
                        } else {
                            weekTimeList.add(start + (int) TimeUnit.DAYS.toSeconds(7)); // 第二周的开始时间
                            weekTimeList.add(start + (int) TimeUnit.DAYS.toSeconds(14));// 第三周的开始时间
                        }
                        weekBeansList.addAll(Arrays.asList(0, 0, 0));
                        weekStatusList.addAll(Arrays.asList(0, 0, 0));
                    }

                    int nowTime = DateHelper.getNowSeconds();
                    List<Integer> tempLevelNumList = new ArrayList<>(weekTimeList);
                    tempLevelNumList.add(0, 0);
                    int weekIndex = getIndexLevel(nowTime, tempLevelNumList);

                    int addD = 0;
                    if (weekIndex == 1) {
                        addD = (int) (rechargeDiamond * 0.12f);
                    } else if (weekIndex == 2) {
                        addD = (int) (rechargeDiamond * 0.1f);
                    } else if (weekIndex == 3) {
                        addD = (int) (rechargeDiamond * 0.08f);
                    }
                    if (addD > 0 && weekStatusList.get(weekIndex - 1) != 2) {
                        int nowD = weekBeansList.get(weekIndex - 1);
                        weekBeansList.set(weekIndex - 1, Math.min((nowD + addD), 2000));
                    }
                    backUserStateRedis.saveInviterUserState(yaoQingUserdata, yaoQingUid, uid);
                }

            } else {
                // 退单
                List<Integer> weekBeansList = yaoQingUserdata.getWeekBeansList();
                if (!CollectionUtils.isEmpty(weekBeansList)) {
                    weekBeansList.clear();
                    weekBeansList.addAll(Arrays.asList(0, 0, 0));
                    backUserStateRedis.saveInviterUserState(yaoQingUserdata, yaoQingUid, uid);
                }
            }
        }
    }

    private int changeInviteUserState(String aid, int oldState, int nowDau, int destDau, String dauStr) {
        if (oldState == 0) {
            if (nowDau >= destDau) {
                return 1;
            }
            return 0;
        } else {
            return oldState;
        }
    }

    private Map<String, Integer> fillMap(Map<String, Integer> itemMapStatus, List<String> srcKeyList) {
        if (itemMapStatus == null) {
            itemMapStatus = new HashMap<>();
        }
        for (String itemKey : srcKeyList) {
            //  0 待完成 1 待领取 2 已完成
            int oldStatus = itemMapStatus.getOrDefault(itemKey, 0);
            if (oldStatus == 0) {
                itemMapStatus.put(itemKey, 1);
            }
        }
        return itemMapStatus;
    }


    private int getIndexLevel(int score, List<Integer> srcList) {
        List<Integer> tempLevelNumList = new ArrayList<>(srcList);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

    private int getIndexLevel(double score, List<Double> srcList) {
        List<Double> tempLevelNumList = new ArrayList<>(srcList);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Double::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

}
