package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.ActivityDailyRankingEvent;
import com.quhong.analysis.DrawPrizeRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.LotteryPrizeData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 分享大奖活动
 *
 * <AUTHOR>
 * @date 2024/8/21
 */
@Service
public class ShareJackpotActivityService extends OtherActivityService implements DailyTaskHandler{

    private static final Logger logger = LoggerFactory.getLogger(ShareJackpotActivityService.class);

    private static final String ACTIVITY_ID = ServerConfig.isProduct() ? "66e0124c4abc20b7f4accbb2" : "66c84267cd4f00008500326e";
    // private static final String ACTIVITY_ID = ServerConfig.isProduct() ? "66e012644abc20b7f4accbb3" : "66c84267cd4f00008500326e";

    private static final String DAILY_RANK_REWARD_TITLE = "Share Jackpot-Daily Rank Reward";
    private static final String PAID_DRAW_REWARD_TITLE = "Share Jackpot-Paid Prize Pool Draw Reward";
    private static final String UNPAID_DRAW_REWARD_TITLE = "Share Jackpot-Unpaid Prize Pool Draw Reward";

    private static final List<Integer> LUCKY_GIFT_ID_LIST = ServerConfig.isProduct() ? Arrays.asList(901, 902, 903) : Arrays.asList(902, 903, 904);
    private static final List<Float> REWARD_RATE_LIST = Arrays.asList(0.15f, 0.1150f, 0.09f, 0.06f, 0.04f, 0.02f, 0.01f, 0.005f);
    private static final int PRIZE_POOL_BASE_VALUE = 10000;
    private static final String LOCK_KEY = "activityDraw_";
    private static final String DRAW_POINT_KEY = "drawPoint";     // 抽奖剩余积分数
    private static final String DRAW_COUNT_KEY = "drawCount";     // 累计抽奖次数
    private static final String DRAW_TASK_LEVEL_KEY = "drawTaskLevel";     // 抽奖任务等级
    private static final List<Integer> DRAW_TASK_LIST = Arrays.asList(10, 30, 50);
    private static final List<String> DRAW_TASK_REWARD_KEY_LIST = Arrays.asList("shareTheJackpotDraw10", "shareTheJackpotDraw30", "shareTheJackpotDraw50");

    private static final Map<String, LotteryPrizeData> LOTTERY_PRIZE_MAP = new HashMap<>(8);

    private static final int LIMIT_INIT_POOL = 1000;
    private static final int INIT_POOL_SIZE = 1000;

    private static final int REWARD_LIMIT = 10000; // 日榜奖励门槛

    @PostConstruct
    public void init() {
        LOTTERY_PRIZE_MAP.put("1", new LotteryPrizeData(ResTypeEnum.DIAMONDS.getType(), 0, "https://test2.qmovies.tv/share_jackpot/assets/9999-48a6f401.png", 999, 0, 0.01, 0.02));
        LOTTERY_PRIZE_MAP.put("2", new LotteryPrizeData(ResTypeEnum.DIAMONDS.getType(), 0, "https://test2.qmovies.tv/share_jackpot/assets/999-f1d3ed5a.png", 99, 0, 0.04, 0.08));
        LOTTERY_PRIZE_MAP.put("3", new LotteryPrizeData(ResTypeEnum.BAG_GIFT.getType(), 902, "https://cdn3.qmovies.tv/gift/op_1725934218_icon.png", 1, -1, 0.05, 0.11));
        LOTTERY_PRIZE_MAP.put("4", new LotteryPrizeData(ResTypeEnum.MIC.getType(), 245, "https://cdn3.qmovies.tv/mic/op_1725937003_op_sys_1617011275_city_treasure_06.png", 1, 1, 0.18, 0.13));
        LOTTERY_PRIZE_MAP.put("5", new LotteryPrizeData(ResTypeEnum.BUBBLE.getType(), 267, "https://cdn3.qmovies.tv/buddle/op_1725937854_11.png", 1, 1, 0.20, 0.15));
        LOTTERY_PRIZE_MAP.put("6", new LotteryPrizeData(ResTypeEnum.RIPPLE.getType(), 40, "https://cdn3.qmovies.tv/ripple/op_1725937724_png.png", 1, 1, 0.18, 0.18));
        LOTTERY_PRIZE_MAP.put("7", new LotteryPrizeData(ResTypeEnum.RIDE.getType(), 198, "https://cdn3.qmovies.tv/join/op_1725883032_op_1715929176_icon.png", 1, 1, 0.16, 0.15));
        LOTTERY_PRIZE_MAP.put("8", new LotteryPrizeData(ResTypeEnum.FLOAT_SCREEN.getType(), 107, "https://cdn3.qmovies.tv/screen/op_1725937634_gift.png", 1, 1, 0.18, 0.18));
    }

    @Resource
    private EventReport eventReport;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ShareJackpotActivityService shareJackpotActivityService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;

    public PrizePoolSharingVO pageInfo(String activityId, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        PrizePoolSharingVO vo = new PrizePoolSharingVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        int prizePoolBeans = getPrizePoolBeans(activityId, DateHelper.ARABIAN.formatDateInDay());
        vo.setPrizePoolBeans(prizePoolBeans);
        vo.setRewardBeanNumList(getRewardBeanNumList(prizePoolBeans));
        vo.setDailyDataList(getDailyDataList(activityId, uid, 1, actorData, 50));
        vo.getDailyDataList().forEach(k -> k.setPrizePoolBeans(getPrizePoolBeans(activityId, k.getKey())));
        String userDataKey = getHashUserKey(activityId, uid);
        Map<String, Integer> userHashData = activityCommonRedis.getCommonHashAll(userDataKey);
        vo.setPointBalance(userHashData.getOrDefault(DRAW_POINT_KEY, 0));
        vo.setDrawCount(userHashData.getOrDefault(DRAW_COUNT_KEY, 0));
        vo.setRewardNotifyList(getRewardNotifyList(activityId));
        return vo;
    }

    public DrawResultVO draw(String activityId, String uid, int drawNum, int slang, String roomId) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (drawNum != 1 && drawNum != 10) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
        int drawCost = drawNum * 100;
        int pointBalance;
        List<LotteryPrizeData> rewardList;
        boolean isPayUser = shareJackpotActivityService.isPayUser(uid);
        try (DistributeLock lock = new DistributeLock(LOCK_KEY + uid)) {
            lock.lock();
            // 扣除积分
            pointBalance = costPoint(activityId, uid, drawCost);
            // 检测奖池
            checkPrizePool(activityId, isPayUser);
            // 抽奖
            rewardList = drawReward(activityId, isPayUser, uid, drawNum);
            // 发送奖励
            sendReward(activityId, uid, rewardList, actorData, isPayUser);
        }
        DrawResultVO vo = new DrawResultVO();
        vo.setPointBalance(pointBalance);
        List<DrawResultVO.Reward> list = new ArrayList<>();
        for (LotteryPrizeData prizeData : rewardList) {
            ResTypeEnum typeEnum = ResTypeEnum.getByType(prizeData.getResType());
            if (typeEnum == null) {
                continue;
            }
            DrawResultVO.Reward reward = new DrawResultVO.Reward();
            reward.setIcon(prizeData.getIcon());
            reward.setName(typeEnum.getNameBySlang(slang));
            if (typeEnum == ResTypeEnum.COIN || typeEnum == ResTypeEnum.DIAMONDS) {
                reward.setTag(SLangType.ENGLISH == slang ? String.format(typeEnum.getTagEn(), prizeData.getNum()) : String.format(typeEnum.getTagAr(), prizeData.getNum()));
            } else {
                reward.setTag(SLangType.ENGLISH == slang ? String.format(typeEnum.getTagEn(), prizeData.getDays()) : String.format(typeEnum.getTagAr(), prizeData.getDays()));
            }
            list.add(reward);
        }
        saveDrawRecord(activityId, uid, list);
        doReportEvent(uid, roomId, drawNum, drawCost, isPayUser);
        vo.setRewardList(list);
        vo.setHasReward(1);
        return vo;
    }

    public PageVO<DrawHistoryVO> getDrawRecord(String activityId, String uid, int page) {
        int pageSize = 10;
        int start = (page - 1) * pageSize;
        int end = page * pageSize;
        List<DrawHistoryVO> drawRecords = activityCommonRedis.getDrawRecords(activityId, uid, start, end, DrawHistoryVO.class);
        return new PageVO<>(drawRecords, drawRecords.size() >= pageSize ? String.valueOf(page + 1) : "");
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public boolean isPayUser(String uid) {
        double rechargeAmount = rechargeDailyInfoDao.selectUserRechargeAmount(uid, DateHelper.getNowSeconds() - (int) TimeUnit.DAYS.toSeconds(30));
        return rechargeAmount >= 1;
    }

    private void doReportEvent(String uid, String roomId, int drawNum, int drawCost, boolean isPayUser) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence("ShareJackpot");
        event.setSence_detail(0);
        event.setRoom_id(roomId);
        event.setCost_score(drawCost);
        event.setDraw_nums(drawNum);
        event.setDraw_detail(isPayUser ? "Paid Prize Pool" : "Unpaid Prize Pool");
        event.setDraw_success_nums(drawNum);
        eventReport.track(new EventDTO(event));
    }

    private List<PrizePoolSharingVO.RewardNotify> getRewardNotifyList(String activityId) {
        List<PrizePoolSharingVO.RewardNotify> list = new ArrayList<>();
        List<String> strValueList = activityCommonRedis.getAwardNotifyList(activityId, 0);
        if (!CollectionUtils.isEmpty(strValueList)) {
            for (String strValue : strValueList) {
                list.add(JSONObject.parseObject(strValue, PrizePoolSharingVO.RewardNotify.class));
            }
        }
        return list;
    }

    private void saveDrawRecord(String activityId, String uid, List<DrawResultVO.Reward> rewardList) {
        DrawHistoryVO historyVO = new DrawHistoryVO();
        List<DrawHistoryVO.Reward> list = new ArrayList<>();
        rewardList.forEach(k -> {
            DrawHistoryVO.Reward reward = new DrawHistoryVO.Reward();
            BeanUtils.copyProperties(k, reward);
            list.add(reward);
        });
        historyVO.setRewardList(list);
        historyVO.setCtime(DateHelper.getNowSeconds());
        activityCommonRedis.saveDrawRecord(activityId, uid, historyVO);
    }

    private List<LotteryPrizeData> drawReward(String activityId, boolean isPayUser, String uid, int drawNum) {
        List<LotteryPrizeData> list = new ArrayList<>();
        for (int i = 0; i < drawNum; i++) {
            String prizeId = activityCommonRedis.leftPopCommonListKey(getPrizePoolKey(activityId, isPayUser));
            LotteryPrizeData prizeData = LOTTERY_PRIZE_MAP.get(prizeId);
            if (prizeData != null) {
                list.add(prizeData);
            }
        }
        int drawCount = activityCommonRedis.incCommonHashNum(getHashUserKey(activityId, uid), DRAW_COUNT_KEY, drawNum);
        drawCountTaskReward(activityId, uid, drawCount);
        return list;
    }

    private void drawCountTaskReward(String activityId, String uid, int drawCount) {
        if (drawCount < DRAW_TASK_LIST.get(0)) {
            return;
        }
        int taskLevel = activityCommonRedis.getCommonHashValue(getHashUserKey(activityId, uid), DRAW_TASK_LEVEL_KEY);
        int newTaskLevel = 0;
        for (int i = 1; i <= DRAW_TASK_LIST.size(); i++) {
            if (drawCount >= DRAW_TASK_LIST.get(i - 1) && i > taskLevel) {
                resourceKeyHandlerService.sendResourceData(uid, DRAW_TASK_REWARD_KEY_LIST.get(i - 1), "", "", "", "", "");
                newTaskLevel = i;
            }
        }
        if (newTaskLevel != 0) {
            activityCommonRedis.setCommonHashData(getHashUserKey(activityId, uid), DRAW_TASK_LEVEL_KEY, newTaskLevel + "");
        }
    }

    private void checkPrizePool(String activityId, boolean isPayUser) {
        int poolSize = activityCommonRedis.getCommonListSize(getPrizePoolKey(activityId, isPayUser));
        if (poolSize <= 0) {
            expandPrizePool(activityId, isPayUser);
        } else if (poolSize <= LIMIT_INIT_POOL) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    expandPrizePool(activityId, isPayUser);
                }
            });
        }
    }

    private String getPrizePoolKey(String activityId, boolean isPayUser) {
        return activityId + "_" + (isPayUser ? 1 : 0);
    }

    private void expandPrizePool(String activityId, boolean isPayUser) {
        List<String> prizeList = new ArrayList<>(INIT_POOL_SIZE);
        for (Map.Entry<String, LotteryPrizeData> entry : LOTTERY_PRIZE_MAP.entrySet()) {
            // 添加元素到奖池
            double prob = isPayUser ? entry.getValue().getPayUserProb() : entry.getValue().getProb();
            int num = (int) (prob * INIT_POOL_SIZE);
            prizeList.addAll(Collections.nCopies(num, entry.getKey()));
        }
        // 打乱奖池顺序
        Collections.shuffle(prizeList);
        activityCommonRedis.rightPushAllCommonList(getPrizePoolKey(activityId, isPayUser), prizeList);
    }

    private void sendReward(String activityId, String uid, List<LotteryPrizeData> rewardList, ActorData actorData, boolean isPayUser) {
        for (LotteryPrizeData prizeData : rewardList) {
            ResTypeEnum typeEnum = ResTypeEnum.getByType(prizeData.getResType());
            if (typeEnum == null) {
                continue;
            }
            String title = isPayUser ? PAID_DRAW_REWARD_TITLE : UNPAID_DRAW_REWARD_TITLE;
            sendActivityReward(uid, prizeData.getResType(), prizeData.getResId(), prizeData.getNum(), prizeData.getDays(),905, title, title, 1);
            PrizePoolSharingVO.RewardNotify rewardNotify;
            if (typeEnum == ResTypeEnum.COIN || typeEnum == ResTypeEnum.DIAMONDS || typeEnum == ResTypeEnum.BAG_GIFT) {
                rewardNotify = new PrizePoolSharingVO.RewardNotify(actorData.getName(), prizeData.getIcon(), prizeData.getNum());
            } else {
                rewardNotify = new PrizePoolSharingVO.RewardNotify(actorData.getName(), prizeData.getIcon(), prizeData.getDays());
            }
            activityCommonRedis.addAwardNotify(activityId, 0, JSONObject.toJSONString(rewardNotify));
        }
    }

    private int costPoint(String activityId, String uid, int costPoints) {
        String userDataKey = getHashUserKey(activityId, uid);
        Map<String, Integer> userHashData = activityCommonRedis.getCommonHashAll(userDataKey);
        int currentPoints = userHashData.getOrDefault(DRAW_POINT_KEY, 0);
        int leftPoints = currentPoints - costPoints;
        if (leftPoints < 0) {
            throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
        }
        return activityCommonRedis.incCommonHashNum(userDataKey, DRAW_POINT_KEY, -costPoints);

    }

    @Override
    public void dailyTaskRun(String dateStr) {
        sendDailyRankReward();
        dailyRankingEventReport();
    }

    /**
     * 日榜奖励
     */
    public void sendDailyRankReward() {
        try {
            OtherRankingActivityData activityData = otherActivityService.getActivityData(ACTIVITY_ID);
            if (activityData == null) {
                logger.info("activityData is null. activityId={}", ACTIVITY_ID);
                return;
            }
            String yesterdayStr = DateSupport.yyyyMMdd(DateSupport.ARABIAN.getYesterday());
            Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingDailyMap(ACTIVITY_ID, 1, 50, yesterdayStr, 0);
            if (CollectionUtils.isEmpty(rankingMap)) {
                logger.info("rankingMap is empty. activityId={}", ACTIVITY_ID);
                return;
            }
            int totalBeans = getPrizePoolBeans(ACTIVITY_ID, DateHelper.ARABIAN.getYesterdayStr(new Date()));
            int rank = 1;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String aid = entry.getKey();
                int rewardDiamonds = calculateRewardDiamonds(rank, totalBeans);
                logger.info("sendDailyRankReward. aid={} rank={} totalBeans={} rewardDiamonds={}", aid, rank, totalBeans, rewardDiamonds);
                chargeDiamonds(aid, rewardDiamonds, DAILY_RANK_REWARD_TITLE, DAILY_RANK_REWARD_TITLE);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("sendDailyRankReward error: {}", e.getMessage(), e);
        }
    }

    public void dailyRankingEventReport() {
        try {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(ACTIVITY_ID);
            if (activityData == null) {
                logger.info("dailyRankingEventReport param error, can not find activity data. activityId={}", ACTIVITY_ID);
                return;
            }
            String yesterdayStr = DateSupport.yyyyMMdd(DateSupport.ARABIAN.getYesterday());
            Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingDailyMap(ACTIVITY_ID, 1, 0, yesterdayStr, 0);
            if (CollectionUtils.isEmpty(rankingMap)) {
                logger.info("dailyRankingEventReport rankingMap is empty. activityId={} strDate={}", ACTIVITY_ID, yesterdayStr);
                return;
            }
            int rank = 1;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                ActivityDailyRankingEvent event = new ActivityDailyRankingEvent();
                String aid = entry.getKey();
                ActorData rankActor = actorDao.getActorDataFromCache(aid);
                event.setUid(aid);
                event.setRid(rankActor != null ? rankActor.getRid() : 0);
                event.setDate(yesterdayStr);
                event.setRank(rank);
                event.setRank_value(entry.getValue());
                event.setActivity_name("ShareJackpot");
                event.setActive_id(ACTIVITY_ID);
                event.setRank_detail(getRankDetail(rank));
                eventReport.track(new EventDTO(event));
                if (entry.getValue() >= REWARD_LIMIT) {
                    resourceKeyHandlerService.sendResourceData(aid, "shareTheJackpotLimitReward", "", "", "", "", "");
                }
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("sendDailyRankReward error: {}", e.getMessage(), e);
        }
    }

    private String getRankDetail(int rank) {
        return BigDecimal.valueOf(getRewardRate(rank) * 100).setScale(2, RoundingMode.HALF_UP) + "%";
    }

    private int calculateRewardDiamonds(int rank, int totalBeans) {
        return (int) (getRewardRate(rank) * totalBeans);
    }

    private float getRewardRate(int rank) {
        if (rank == 1) {
            return REWARD_RATE_LIST.get(0);
        }
        if (rank == 2) {
            return REWARD_RATE_LIST.get(1);
        }
        if (rank == 3) {
            return REWARD_RATE_LIST.get(2);
        }
        if (rank >= 4 && rank <= 5) {
            return REWARD_RATE_LIST.get(3);
        }
        if (rank >= 6 && rank <= 10) {
            return REWARD_RATE_LIST.get(4);
        }
        if (rank >= 11 && rank <= 15) {
            return REWARD_RATE_LIST.get(5);
        }
        if (rank >= 16 && rank <= 25) {
            return REWARD_RATE_LIST.get(6);
        }
        if (rank >= 26 && rank <= 50) {
            return REWARD_RATE_LIST.get(7);
        }
        return 0;
    }

    private List<Integer> getRewardBeanNumList(int prizePoolBeans) {
        return REWARD_RATE_LIST.stream().map(k -> (int) (k * prizePoolBeans)).collect(Collectors.toList());
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        if (!LUCKY_GIFT_ID_LIST.contains(giftData.getGid())) {
            return;
        }
        int totalBeans = giftData.getPrice() * giftData.getNumber() * giftData.getAid_list().size();
        int beans = (int) (totalBeans * 0.1);
        incPrizePoolBeans(activityId, beans);
        activityCommonRedis.incCommonHashNum(getHashUserKey(activityId, giftData.getFrom_uid()), DRAW_POINT_KEY, totalBeans);
    }

    public List<OtherRankConfigVO.DailyData> getDailyDataList(String activityId, String uid, int rankType, ActorData actorData, int rankLength) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        List<OtherRankConfigVO.DailyData> list = new ArrayList<>();
        LocalDate startDate = DateSupport.ARABIAN.getLocalDate(activity.getStartTime() * 1000L);
        LocalDate endDate = DateSupport.ARABIAN.getLocalDate(activity.getEndTime() * 1000L);
        LocalDate today = DateSupport.ARABIAN.getToday();
        int roundNum = activity.getRoundNum();
        while (!startDate.isAfter(endDate)) {
            String strDate = DateSupport.yyyyMMdd(startDate);
            String key = DateSupport.format(startDate);
            String showDate = DateSupport.ARABIAN.dm(startDate);
            if (startDate.isAfter(today)) {
                OtherMyRankVO myRankVO = new OtherMyRankVO();
                myRankVO.setSendingScore(0);
                myRankVO.setRank(-1);
                myRankVO.setName(actorData.getName());
                myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                list.add(new OtherRankConfigVO.DailyData(key, showDate, 0, "", Collections.emptyList(), myRankVO));
            } else {
                int status = startDate.isEqual(today) ? 1 : 2;
                Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingDailyMap(activityId, rankType, rankLength, strDate, roundNum);
                List<OtherRankingListVO> rankingList = getRankingList(rankingMap);
                String top1Head = rankingList.size() >= 1 ? rankingList.get(0).getHead() : "";
                list.add(new OtherRankConfigVO.DailyData(key, showDate, status, top1Head, rankingList, getMyRanking(activityId, uid, rankType, strDate, roundNum, actorData)));
            }
            startDate = startDate.plusDays(1);
        }
        return list;
    }

    private List<OtherRankingListVO> getRankingList(Map<String, Integer> rankingMap) {
        List<OtherRankingListVO> list = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO vo = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            vo.setUid(aid);
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            vo.setName(rankActor.getName());
            vo.setScore(entry.getValue());
            vo.setRank(rank);
            list.add(vo);
            rank += 1;
        }
        return list;
    }

    private OtherMyRankVO getMyRanking(String activityId, String uid, int rankType, String strDate, int roundNum, ActorData actorData) {
        OtherMyRankVO vo = new OtherMyRankVO();
        vo.setUid(uid);
        vo.setSendingScore(activityOtherRedis.getOtherRankingDailyScore(activityId, uid, rankType, strDate, roundNum));
        int rank = activityOtherRedis.getOtherDailyRank(activityId, uid, rankType, strDate, roundNum);
        vo.setRank(rank == 0 || rank > 99 ? -1 : rank);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    public void incPrizePoolBeans(String activityId, int beans) {
        String today = DateHelper.ARABIAN.formatDateInDay();
        activityCommonRedis.incCommonHashNum(activityId, today, beans);
    }

    public int getPrizePoolBeans(String activityId, String strDate) {
        return activityCommonRedis.getCommonHashValue(activityId, strDate) + PRIZE_POOL_BASE_VALUE;
    }

    private String getHashUserKey(String activityId, String uid) {
        return String.format("userData:%s:%s", activityId, uid);
    }
}
