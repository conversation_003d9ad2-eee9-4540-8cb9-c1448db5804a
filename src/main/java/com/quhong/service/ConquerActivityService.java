package com.quhong.service;

import com.quhong.core.utils.DateHelper;
import com.quhong.deliver.OtherRankActivityDeliver;
import com.quhong.exception.CommonException;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.MongoThemeDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.ConquerActivity;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.MongoThemeData;
import com.quhong.msg.room.RoomConquerMsg;
import com.quhong.msg.room.RoomInfoChangeMsg;
import com.quhong.mysql.dao.MineBackgroundDao;
import com.quhong.mysql.dao.UploadBackgroundDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.MineBackgroundData;
import com.quhong.mysql.data.UploadBackgroundData;
import com.quhong.redis.ConquerRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 征服活动
 */
@Component
public class ConquerActivityService {
    private final static Logger logger = LoggerFactory.getLogger(ConquerActivityService.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    private ConquerRedis conquerRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private RankActivityService rankActivityService;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private UploadBackgroundDao uploadBackgroundDao;
    @Resource
    private MongoThemeDao themeDao;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MineBackgroundDao mineBackgroundDao;


    private String getRoomThemeUrl(int themeId, String roomId){
        MongoThemeData themeData = null;
        if (themeId >= 1000) {
            UploadBackgroundData data = uploadBackgroundDao.selectOne(themeId);
            if (data != null) {
                return data.getBgUrl();
            }
        } else {

            themeData = themeDao.findThemeData(themeId);
            if(themeData != null){
                return themeData.getBgurl();
            }
            // int userLevel = userLevelDao.getUserLevel(RoomUtils.getRoomHostId(roomId));
            // // 使用vip主题，如果vip等级小于2且用户等级小于20，设置成默认主题
            // if (themeData.getType() == 1 && vipLevel < 2 && userLevel < 20) {
            //     logger.info("vip2 expired. uid={} roomId={} theme={}", uid, roomId, themeId);
            //     throw new CommonException(RoomHttpCode.VIP2_EXPIRED);
            // }
            // // 购买和下发的主题过期后，设置成默认主题
            // if (themeData.getType() != 0 && themeData.getType() != 1) {
            //     MineBackgroundData data = mineBackgroundDao.selectOne(roomId, themeId);
            //     if (data == null || DateHelper.getNowSeconds() >= data.getEndTime()) {
            //         logger.info("background expired. uid={} roomId={} theme={}", uid, roomId, themeId);
            //         throw new CommonException(RoomHttpCode.BACKGROUND_EXPIRED);
            //     }
            // }
        }
        return "";
    }

    public void cleanConquer() {
        ConquerActivity conquerActivity = rankActivityService.getConquerCache();
        if (conquerActivity == null) {
            return;
        }
        int currentTime = DateHelper.getNowSeconds();
        if (conquerActivity.getStartTime() > currentTime || currentTime > conquerActivity.getEndTime() + 10800) { // 延迟3个小时结束
            return;
        }
        String conquerId = conquerActivity.get_id().toString();
        Map<Integer, ConquerActivity.ConfigDetail> keyMap = CollectionUtil.listToKeyMap(conquerActivity.getConfigList(), ConquerActivity.ConfigDetail::getLevel);
        Map<String, Integer> rankingMap = conquerRedis.getRankingMap(conquerId);

        for (String roomId : rankingMap.keySet()) {
            logger.info("ConquerActivityTask: conquerId: {}, roomId: {}", conquerId, roomId);

            conquerRedis.deleteRoomConquerLevel(conquerId, roomId); // 删除当前被征服等级
            conquerRedis.delCountdownRoomList(conquerId, roomId);  // 删除当前房间倒计时
            conquerRedis.deleteRoomConquerRankKey(conquerId, roomId); // 删除用户对房间贡献排行数据
            conquerRedis.removeConquerTempNum(conquerId, roomId); // 删除当前房间临时礼物总发送数量
            conquerRedis.delRoomConquerSumRank(conquerId, roomId);
            RoomConquerMsg msg = new RoomConquerMsg();
            msg.setRoomid(roomId);
            msg.setHead("");
            msg.setUrl(conquerActivity.getJoinUrl());
            msg.setEnd_time(0);
            msg.setCurrent_value(0);
            msg.setLevel(0);
            ConquerActivity.ConfigDetail configDetail = keyMap.get(1);
            msg.setTheme("");
            msg.setTotal_value(configDetail.getConquerNum());
            roomWebSender.sendRoomWebMsg(roomId, null, msg, false);

            MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            RoomInfoChangeMsg roomChangeMsg = new RoomInfoChangeMsg();
            roomChangeMsg.setRid(roomId);
            roomChangeMsg.setRoomName(roomData.getName());
            roomChangeMsg.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));

            int themeId = roomData.getTheme();
            roomChangeMsg.setThemeUrl(getRoomThemeUrl(themeId, roomId));
            roomWebSender.sendRoomWebMsg(roomId, "", roomChangeMsg, true);

        }
    }
}
