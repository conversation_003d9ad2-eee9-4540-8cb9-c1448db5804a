package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.VoyagerVO;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 大航海家
 */
@Service
public class VoyagerService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(VoyagerService.class);
    private static final List<Integer> ROOM_LEVEL_LIST = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9);
    private static final List<String> ROOM_OWNER_RESOURCE_KEY_LIST = Arrays.asList("voyagerOwnerLevel100", "voyagerOwnerLevel500", "voyagerOwnerLevel1700", "voyagerOwnerLevel3000",
            "voyagerOwnerLevel10000", "voyagerOwnerLevel20000", "voyagerOwnerLevel50000", "voyagerOwnerLevel100000");
    private static final List<String> ROOM_MEMBER_RESOURCE_KEY_LIST = Arrays.asList("voyagerMemberLevel100", "voyagerMemberLevel500", "voyagerMemberLevel1700", "voyagerMemberLevel3000",
            "voyagerMemberLevel10000", "voyagerMemberLevel20000", "voyagerMemberLevel50000", "voyagerMemberLevel100000");
    private static final Map<Integer, Integer> LEVEL_LENGTH_MAP = new HashMap<>();   // 等级与船员数量对应关系
    private static final Map<Integer, Integer> LEVEL_UP_CONDITION_MAP = new HashMap<>();   // 等级升级条件
    private static final Map<Integer, Integer> LEVEL_METER_MAP = new HashMap<>();    // 等级与航速对应关系
    private static final Integer BASE_METER_BEAN = 1000;
    private static final String ACTIVITY_TITLE_EN = "Big Voyager";
    private static final String ACTIVITY_DAILY_TITLE_EN = "Daily Big Voyager";
    private static final String ACTIVITY_TITLE_AR = "مسافر كبير";
    private static final String ACTIVITY_DESC = "voyager activity rewards";
    private static final String ACTIVITY_ID = "66ab8547fcaf2ec79a10fd92";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/big_voyager/?activityId=%s", ACTIVITY_ID): String.format("https://test2.qmovies.tv/big_voyager/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";
    private static final String VOYAGER_ROOM_LEVEL_KEY = "voyager_room_level";
    private static final String VOYAGER_ROOM_NUM_KEY = "voyager_room_num";
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    static {
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(0), 0);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(1), 1);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(2), 2);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(3), 3);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(4), 4);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(5), 4);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(6), 5);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(7), 5);
        LEVEL_LENGTH_MAP.put(ROOM_LEVEL_LIST.get(8), 6);

        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(0), 100);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(1), 400);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(2), 1200);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(3), 3000);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(4), 8000);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(5), 18000);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(6), 40000);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(7), 100000);
        LEVEL_UP_CONDITION_MAP.put(ROOM_LEVEL_LIST.get(8), -1);

        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(0), 1);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(1), 1);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(2), 2);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(3), 2);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(4), 3);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(5), 3);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(6), 4);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(7), 4);
        LEVEL_METER_MAP.put(ROOM_LEVEL_LIST.get(8), 5);

    }

    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Autowired(required = false)
    private EventReport eventReport;

    private String getHashActivityId(String activityId, String roomId, String dateStr){
        return String.format("%s:%s:%s", activityId, roomId, dateStr);
    }

    private String getHashBoatActivityId(String activityId){
        return String.format("boat:%s:", activityId);
    }

    private String getDateStrKey(String activityId){
        return String.format("dateStr:%s", activityId);
    }

    // 日里程key
    private String dailyMileageZSetKey(String activityId, String dateStr) {
        return String.format("dailyMileage:%s:%s", activityId, dateStr);
    }

    // 用户对房间的贡献key
    private String dailyUserDevoteRoomZSetKey(String activityId, String roomId, int level, String dateStr) {
        return String.format("dailyUserDevoteRoom:%s:%s:%s:%s", activityId, roomId, level, dateStr);
    }

    // 升级记录
    private String getDailyUplevelKey(String activityId){
        return String.format("dailyUplevel:%s", activityId);
    }

    public VoyagerVO voyagerConfig(String uid, String activityId, String dateStr) {

        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivity(activityId);
        VoyagerVO vo = new VoyagerVO();
        vo.setStartTime(otherRankingActivityData.getStartTime());
        vo.setEndTime(otherRankingActivityData.getEndTime());


        // 当前房间等级信息
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setCaptainName(actorData.getName());
        vo.setCaptainHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setCaptainUid(uid);


        // 滚屏信息
        List<String> recordList = activityCommonRedis.getCommonListRecord(getDailyUplevelKey(activityId));
        List<ResourceKeyConfigData> keyConfigList = resourceKeyConfigDao.findListByKeys(new HashSet<>(ROOM_OWNER_RESOURCE_KEY_LIST));
        Map<String, ResourceKeyConfigData> keyConfigMap = keyConfigList.stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));

        List<VoyagerVO.RewardVO> rewardList = new ArrayList<>();
        for (String record : recordList) {
            String[] split = record.split("-");
            String hostUid = split[0];
            String voyagerCaptainKey = split[1];

            ResourceKeyConfigData resourceKeyConfigData = keyConfigMap.get(voyagerCaptainKey);
            if(resourceKeyConfigData == null){
                continue;
            }

            ActorData recordActor = actorDao.getActorDataFromCache(hostUid);
            VoyagerVO.RewardVO rewardVO = new VoyagerVO.RewardVO();
            rewardVO.setName(recordActor.getName());
            rewardVO.setRewardImageList(resourceKeyConfigData.getResourceMetaList().stream().map(ResourceKeyConfigData.ResourceMeta::getResourceIcon).collect(Collectors.toList()));
            rewardList.add(rewardVO);
        }

        vo.setRewardList(rewardList);


        String roomId = RoomUtils.formatRoomId(uid);
        // String dateStr = activityCommonRedis.getCommonStrValue(getDateStrKey(activityId));
        if (dateStr.equals("2024-08-02")){
            dateStr = "";
        }else if (dateStr.equals("")) {
            dateStr = DateHelper.ARABIAN.formatDateInDay();
        }
        String hashActivityId = getHashActivityId(activityId, roomId, dateStr);
        Map<String, Integer> allRoomMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        int dailyRoomLevel = allRoomMap.getOrDefault(VOYAGER_ROOM_LEVEL_KEY, 1);       // 当前房间等级
        int dailyRoomLevelDevote = allRoomMap.getOrDefault(VOYAGER_ROOM_NUM_KEY, 0);   // 当前等级贡献度

        vo.setLevel(dailyRoomLevel);
        vo.setRoomDevote(dailyRoomLevelDevote);

        String boatName = activityCommonRedis.getCommonHashStrValue(getHashBoatActivityId(activityId), roomId);
        vo.setBoatName(boatName == null ? actorData.getName() : boatName);

        // 我的船信息
        List<VoyagerVO.BoatConfig> boatConfigList = new ArrayList<>();
        for (Integer level : ROOM_LEVEL_LIST) {
            VoyagerVO.BoatConfig boatConfig = new VoyagerVO.BoatConfig();
            boatConfig.setLevel(level);
            int length = LEVEL_LENGTH_MAP.getOrDefault(level, 1);
            List<VoyagerVO.CrewConfig>  crewConfigList = new ArrayList<>();
            if(length > 0){
                ActorData rankActor = null;
                Map<String, Integer>  rankingLevelMap = activityCommonRedis.getCommonRankingMap(dailyUserDevoteRoomZSetKey(activityId, roomId, level, dateStr), 10);
                int rankCrew = 1;
                for (Map.Entry<String, Integer> entry : rankingLevelMap.entrySet()) {
                    String rankAid = entry.getKey();
                    if(rankAid.equals(uid)){
                        continue;
                    }
                    VoyagerVO.CrewConfig crewConfig = new VoyagerVO.CrewConfig();
                    rankActor = actorDao.getActorDataFromCache(rankAid);
                    crewConfig.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                    crewConfig.setName(rankActor.getName());
                    crewConfig.setUid(rankActor.getUid());
                    crewConfigList.add(crewConfig);

                    if(rankCrew == length){
                        break;
                    }
                    rankCrew += 1;

                }
            }
            boatConfig.setCrewConfigList(crewConfigList);
            boatConfigList.add(boatConfig);

        }
        vo.setBoatConfigList(boatConfigList);

        // 日里程排行
        VoyagerVO.RankVO myMileageRank = new VoyagerVO.RankVO();
        List<VoyagerVO.RankVO> mileageRankList = new ArrayList<>();
        Map<String, Integer> mileageRankMap = activityCommonRedis.getCommonRankingMap(dailyMileageZSetKey(activityId, dateStr), 10);
        int mileageRank = 1;
        for (Map.Entry<String, Integer> entry : mileageRankMap.entrySet()) {

            int meterNum = entry.getValue() / BASE_METER_BEAN;
            if(meterNum <= 0){
                continue;
            }

            VoyagerVO.RankVO mileageVO = new VoyagerVO.RankVO();
            String rankRoomId = entry.getKey();
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(rankRoomId);
            mileageVO.setName(roomData.getName());
            mileageVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            mileageVO.setScore(meterNum);
            mileageVO.setRank(mileageRank);
            mileageVO.setTargetId(rankRoomId);
            mileageVO.setLevel(activityCommonRedis.getCommonHashValue(getHashActivityId(activityId, roomId, dateStr), VOYAGER_ROOM_LEVEL_KEY));

            if(roomId.equals(entry.getKey())){
                BeanUtils.copyProperties(mileageVO, myMileageRank);
            }
            mileageRankList.add(mileageVO);
            mileageRank += 1;
        }
        vo.setMileageRankList(mileageRankList);

        if(myMileageRank.getRank() == 0){
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
            myMileageRank.setName(roomData == null ? "" : roomData.getName());
            myMileageRank.setHead(roomData == null ? "" : ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            myMileageRank.setLevel(vo.getLevel());
            myMileageRank.setTargetId(roomId);
            int myMileage = activityCommonRedis.getCommonZSetRankingScore(dailyMileageZSetKey(activityId, dateStr), roomId);
            myMileageRank.setScore(myMileage / BASE_METER_BEAN);
        }
        vo.setMyMileageRank(myMileageRank);


        // 发送榜排行
        int roundNum = otherRankingActivityData.getRoundNum();
        VoyagerVO.RankVO mySendRank = new VoyagerVO.RankVO();
        List<VoyagerVO.RankVO> sendRankList = new ArrayList<>();
        Map<String, Integer> sendRankingMap = activityOtherRedis.getOtherRankingMap(activityId, ActivityConstant.SEND_RANK, 10, roundNum);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : sendRankingMap.entrySet()) {
            VoyagerVO.RankVO rankVO = new VoyagerVO.RankVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankVO.setName(rankActor.getName());
            rankVO.setScore(entry.getValue());
            rankVO.setTargetId(aid);
            rankVO.setRank(rank);
            if(uid.equals(entry.getKey())){
                BeanUtils.copyProperties(rankVO, mySendRank);
            }
            sendRankList.add(rankVO);
            rank += 1;
        }
        vo.setSendRankList(sendRankList);

        if(mySendRank.getRank() == 0){
            mySendRank.setName(actorData.getName());
            mySendRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            mySendRank.setScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RANK, roundNum));
            mySendRank.setTargetId(uid);
        }
        vo.setMySendRank(mySendRank);

        return vo;
    }

    public void distributeMileageRankingAward(String dateStr){
        try{
            if (dateStr == null){
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            }
            Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(dailyMileageZSetKey(ACTIVITY_ID, dateStr), 50);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String roomId = entry.getKey();
                String hostUid = RoomUtils.getRoomHostId(roomId);
                logger.info("distributeMileageRankingAward rank:{} hostUid: {}", rank, hostUid);

                if(rank <= 10){
                    String resourceKey = String.format("voyagerDailyTop%s", rank);
                    resourceKeyHandlerService.sendResourceData(hostUid, resourceKey, ACTIVITY_DAILY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                }

                ActivityDailyRankingEvent event = new ActivityDailyRankingEvent();
                event.setActive_id(ACTIVITY_ID);
                event.setDate(dateStr);
                event.setUid(hostUid);
                event.setActivity_name(ACTIVITY_TITLE_EN);
                event.setRank(rank);
                event.setRank_value(entry.getValue());
                event.setCtime(DateHelper.getNowSeconds());
                eventReport.track(new EventDTO(event));
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distributionLionRankingAward error: {}", e.getMessage(), e);
        }
    }

    public void updateBoatName(String uid, String activityId, String boatName){
        checkActivityTime(activityId);
        String roomId = RoomUtils.formatRoomId(uid);
        if(StringUtils.isEmpty(boatName)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        if(boatName.length() > 15){
            throw new CommonH5Exception(ActivityHttpCode.TEXT_YOU_POSTED_IS_TOO_LONG);
        }
        activityCommonRedis.setCommonHashData(getHashBoatActivityId(activityId), roomId, boatName);
    }


    private void distributeRoomDevote(String activityId, String roomId, String dateStr, int beforeLevel, int currentLevel){

        try {
            String voyagerCaptainKey = ROOM_OWNER_RESOURCE_KEY_LIST.get(beforeLevel - 1);
            String voyagerCrewKey = ROOM_MEMBER_RESOURCE_KEY_LIST.get(beforeLevel - 1);

            String hostUid = RoomUtils.getRoomHostId(roomId);
            String hostUidCaptain = hostUid + "-" + voyagerCaptainKey;
            activityCommonRedis.addCommonListRecord(getDailyUplevelKey(activityId), hostUidCaptain);
            resourceKeyHandlerService.sendResourceData(hostUid, voyagerCaptainKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);

            int rankCrew = 1;
            int length = LEVEL_LENGTH_MAP.getOrDefault(beforeLevel, 1);
            Map<String, Integer> rankingLevelMap = activityCommonRedis.getCommonRankingMap(dailyUserDevoteRoomZSetKey(activityId, roomId, beforeLevel, dateStr), 10);
            for (Map.Entry<String, Integer> entry : rankingLevelMap.entrySet()) {
                String rankAid = entry.getKey();
                if(rankAid.equals(hostUid)){
                    continue;
                }
                resourceKeyHandlerService.sendResourceData(rankAid, voyagerCrewKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                if(rankCrew == length){
                    break;
                }
                rankCrew += 1;
            }

            ActivityParticipationEvent event = new ActivityParticipationEvent();
            event.setActive_id(ACTIVITY_ID);
            event.setUid(hostUid);
            event.setActivity_name(ACTIVITY_TITLE_EN);
            event.setActivity_stage(currentLevel);
            event.setCtime(DateHelper.getNowSeconds());
            eventReport.track(new EventDTO(event));

        }catch (Exception e){
            logger.error("distributeRoomDevote2 error roomId={}, beforeLevel={} currentLevel={}", roomId, beforeLevel, currentLevel);
        }


    }

    public void handleSendGift(SendGiftData giftData, String activityId){

        int totalBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size(); // 剩余钻石数
        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();
        if(StringUtils.isEmpty(giftData.getRoomId())){
            return;
        }

        synchronized (stringPool.intern(roomId)) {
            // String dateStr = activityCommonRedis.getCommonStrValue(getDateStrKey(activityId));
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String hashActivityId = getHashActivityId(activityId, roomId, dateStr);
            Map<String, Integer> allRoomMap = activityCommonRedis.getCommonHashAll(hashActivityId);
            int dailyRoomLevel = allRoomMap.getOrDefault(VOYAGER_ROOM_LEVEL_KEY, 1);       // 当前房间等级
            int dailyRoomLevelDevote = allRoomMap.getOrDefault(VOYAGER_ROOM_NUM_KEY, 0);   // 当前等级贡献度


            // 越级统计
            for (Integer level : ROOM_LEVEL_LIST) {
                if(dailyRoomLevel <= level && totalBeans > 0){
                    int upLevelCondition = LEVEL_UP_CONDITION_MAP.getOrDefault(dailyRoomLevel, -1);    // 升级所需钻石数
                    int meterLevel = LEVEL_METER_MAP.getOrDefault(dailyRoomLevel, 1);                  // 根据等级获取航速

                    // 统计升级
                    String dailyUserDevoteRoomKey = dailyUserDevoteRoomZSetKey(activityId, roomId, dailyRoomLevel, dateStr);
                    logger.info("voyager info fromUid: {}, roomId:{}, totalBeans:{}, dailyRoomLevel:{}, upLevelCondition:{}, meterLevel:{}, dailyRoomLevelDevote:{}", fromUid, roomId, totalBeans, dailyRoomLevel, upLevelCondition, meterLevel, dailyRoomLevelDevote);

                    if(upLevelCondition > 0){
                        int needUpBeans = upLevelCondition - dailyRoomLevelDevote;   // 需要多少钻石升级该等级

                        int incMileageBeans = 0;
                        if(totalBeans >= needUpBeans){            // 升级
                            totalBeans = totalBeans - needUpBeans; // 剩余钻石数
                            incMileageBeans = needUpBeans;
                            dailyRoomLevel += 1;
                            dailyRoomLevelDevote = 0;
                            activityCommonRedis.setCommonHashNum(hashActivityId, VOYAGER_ROOM_NUM_KEY, 0);

                            logger.info("voyager upLevel  fromUid: {}, roomId:{}, level:{}, needUpBeans:{},  totalBeans:{}", fromUid, roomId, level, needUpBeans,  totalBeans);

                            // 增加上一级该用户对该房间贡献
                            activityCommonRedis.incrCommonZSetRankingScore(dailyUserDevoteRoomKey, fromUid, needUpBeans);
                            // 奖励上一级贡献的用户
                            distributeRoomDevote(activityId, roomId, dateStr, dailyRoomLevel - 1, dailyRoomLevel);

                            // 升级等级
                            activityCommonRedis.setCommonHashNum(hashActivityId, VOYAGER_ROOM_LEVEL_KEY, dailyRoomLevel);
                        }else {
                            incMileageBeans = totalBeans;
                            dailyRoomLevelDevote += totalBeans;
                            // 统计里程
                            logger.info("voyagerInc devote fromUid: {}, roomId:{}, totalBeans:{}, dailyRoomLevel:{}, upLevelCondition:{}", fromUid, roomId, totalBeans, dailyRoomLevel, upLevelCondition);
                            activityCommonRedis.incCommonHashNum(hashActivityId, VOYAGER_ROOM_NUM_KEY, totalBeans);
                            activityCommonRedis.incrCommonZSetRankingScore(dailyUserDevoteRoomKey, fromUid, totalBeans);
                            totalBeans = 0;
                        }

                        int incMeter = incMileageBeans * meterLevel;
                        activityCommonRedis.incrCommonZSetRankingScore(dailyMileageZSetKey(activityId, dateStr), roomId, incMeter);
                    }else {
                        activityCommonRedis.incrCommonZSetRankingScore(dailyUserDevoteRoomKey, fromUid, totalBeans);

                        int incMeter = totalBeans * meterLevel;
                        activityCommonRedis.incrCommonZSetRankingScore(dailyMileageZSetKey(activityId, dateStr), roomId, incMeter);
                        totalBeans = 0;
                    }
                }
            }
        }
    }
}
