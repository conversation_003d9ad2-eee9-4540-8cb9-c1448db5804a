package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.vo.CatchFishVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigV2VO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.room.redis.RoomPlayerRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 捕鱼王游戏
 */
@Service
public class CatchFishService2025 extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(CatchFishService2025.class);
    private static final String ACTIVITY_TITLE = "Fishing King";
    private static final String ACTIVITY_ID = "68551ee2d1766602fee28ed5";
    private static final String ACTIVITY_BROADCAST_ICON = "";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/fishing_king2025/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/fishing_king2025/?activityId=%s", ACTIVITY_ID);
    private static final String CATCH_FISH_TIMES = "catchTimes";
    private static final String CATCH_FISH_LEFT_BEANS = "leftBeans";
    private static final String CATCH_FISH_EARN_BEAN = "earnBeans";
    private static final String CATCH_FISH_SEA_METER = "seaMeter";    // 当前达到的深度

    // key匹配
    private static final String CATCH_FAIL_MATCH = "fail";
    private static final String CATCH_FISH_MATCH = "fish";
    private static final String CATCH_BOX_MATCH = "box";
    private static final String CATCH_SWIM_MATCH = "swim";

    // 依次为 休闲区，深海区，神秘区
    private static final List<String> CATCH_FISH_SEA_LIST = Arrays.asList("FishingKingRecreationArea", "FishingKingDeepSeaArea", "FishingKingMysteriousArea");

    // 休闲区 鱼类抽奖key，依次为付费用户，非付费用户，特殊用户(目前没有区分用户)
    private static final List<String> CATCH_FISH_1_DRAW_LIST = Arrays.asList("FishingKingRecreationAreapayfish", "FishingKingRecreationAreapayfish", "FishingKingRecreationAreapayfish");
    private static final List<String> CATCH_FISH_2_DRAW_LIST = Arrays.asList("FishingKingDeepSeaAreapayfish", "FishingKingDeepSeaAreapayfish", "FishingKingDeepSeaAreapayfish");
    private static final List<String> CATCH_FISH_3_DRAW_LIST = Arrays.asList("FishingKingMysteriousAreapayfish", "FishingKingMysteriousAreapayfish", "FishingKingMysteriousAreapayfish");

    // 休闲区 宝箱抽奖key，依次为付费用户，非付费用户，特殊用户(目前没有区分用户)
    private static final List<String> CATCH_BOX_1_DRAW_LIST = Arrays.asList("FishingKingRecreationAreapaytreasure", "FishingKingRecreationAreapaytreasure", "FishingKingRecreationAreapaytreasure");
    private static final List<String> CATCH_BOX_2_DRAW_LIST = Arrays.asList("FishingKingDeepSeaAreapaytreasure", "FishingKingDeepSeaAreapaytreasure", "FishingKingDeepSeaAreapaytreasure");
    private static final List<String> CATCH_BOX_3_DRAW_LIST = Arrays.asList("FishingKingMysteriousAreapaytreasure", "FishingKingMysteriousAreapaytreasure", "FishingKingMysteriousAreapaytreasure");

    private static final List<String> DRAW_USER_LIST = Arrays.asList("616f2433541b4e39f7dabca4", "62a45b81c8dad4488d482810", "5c88a0f166dc630038467c4e", "5ab6ac5c1bad4814cbd56daa", "62a3fb110460bd3470eb8c76", "5e3521cfb271b6040a95e13a", "5d82dd40abb01a009560e3a4", "6606588cbe90384a14cd5279", "5cc819f966dc630025bf64fa", "5c0ffd0c66dc6300781eaccc");
    private static final List<Integer> CATCH_FISH_TIMES_LIST = Arrays.asList(1, 10);
    private static final Map<String, List<Integer>> CATCH_FISH_COST_MAP = new HashMap<>();
    private static final Map<String, String> CATCH_FISH_TITLE_MAP = new HashMap<>();
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final int LIMIT_INIT_POOL = 30;
    private static final Integer RECORD_PAGE_SIZE = 10;

    private static final Integer DAY_RANK = 1;
    private static final Integer WEEK_RANK = 2;
    private static final Integer MONTH_RANK = 3;
    private static final List<Integer> RANK_LIST = Arrays.asList(DAY_RANK, WEEK_RANK, MONTH_RANK);

    private static int START_UNLOCK_SEA_TIME = 1754272800;
    static {
        // 海的区域与消费钻石的映射
        CATCH_FISH_COST_MAP.put(CATCH_FISH_SEA_LIST.get(0), Arrays.asList(100, 1000));
        CATCH_FISH_COST_MAP.put(CATCH_FISH_SEA_LIST.get(1), Arrays.asList(200, 2000));
        CATCH_FISH_COST_MAP.put(CATCH_FISH_SEA_LIST.get(2), Arrays.asList(1000, 10000));


        CATCH_FISH_TITLE_MAP.put(CATCH_FISH_1_DRAW_LIST.get(0), "FishingKing-recreation area-Fish");
        CATCH_FISH_TITLE_MAP.put(CATCH_BOX_1_DRAW_LIST.get(0), "FishingKing-recreation area-Treasure");

        CATCH_FISH_TITLE_MAP.put(CATCH_FISH_2_DRAW_LIST.get(0), "FishingKing-deepsea area-Fish");
        CATCH_FISH_TITLE_MAP.put(CATCH_BOX_2_DRAW_LIST.get(0), "FishingKing-deepsea area-Treasure");

        CATCH_FISH_TITLE_MAP.put(CATCH_FISH_3_DRAW_LIST.get(0), "FishingKing-mysterious area-Fish");
        CATCH_FISH_TITLE_MAP.put(CATCH_BOX_3_DRAW_LIST.get(0), "FishingKing-mysterious area-Treasure");

    }


    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private CacheDataService cacheDataService;


    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            START_UNLOCK_SEA_TIME = 1753672816;
        }
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("catchFish:%s:%s", activityId, uid);
    }

    private String getHashDailyActivityId(String activityId, String uid, String dateStr) {
        return String.format("catchFishDaily:%s:%s:%s", activityId, uid, dateStr);
    }


    // 每日不同海域的人数key
    private String getSeaZoneUserKey(String activityId, String seaKey, String dateStr) {
        return String.format("seaZoneUser:%s:%s:%s", activityId, seaKey, dateStr);
    }


    // 捕鱼榜单 type 1:日榜 2:周榜 3:月榜
    private String getCatchFishRankKey(String activityId, int type, String dateStr) {
        return String.format("catchFishRank:%s:%s:%s", activityId, type, dateStr);
    }

    // 抽奖品Key-奖池redis
    private String getListDrawPrizeKey(String activityId, String resKey) {
        return String.format("drawPrize:%s:%s", activityId, resKey);
    }

    // 历史记录key
    private String getHistoryRecordListKey(String activityId, String uid) {
        return String.format("historyRecord:%s:%s", activityId, uid);
    }


    public CatchFishVO catchFishConfig(String activityId, String uid) {

//        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        CatchFishVO vo = new CatchFishVO();
//        vo.setStartTime(activity.getStartTime());
//        vo.setEndTime(activity.getEndTime());
//        String inRoomId = roomPlayerRedis.getActorRoomStatus(uid);
//        vo.setPopularRoomId(StringUtils.isEmpty(inRoomId) ? getPopularRoomId() : inRoomId);

        String currentDay = getDayByBase(activityId, uid);
        String hashDailyActivityId = getHashDailyActivityId(activityId, uid, currentDay);
        Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);
        vo.setSeaMeter(userDailyDataMap.getOrDefault(CATCH_FISH_SEA_METER, 0));

        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        vo.setEarnBeans(userDataMap.getOrDefault(CATCH_FISH_EARN_BEAN, 0));

        ActorData actorData = actorDao.getActorData(uid);
        vo.setNowBeans(actorData.getBeans());

        // 设置每个海域资源
        Map<String, ResourceKeyConfigData> resConfigDataMap = resourceKeyConfigDao.findListByKeyList(CATCH_FISH_SEA_LIST).stream().collect(Collectors.toMap(ResourceKeyConfigData::getKey, Function.identity()));

        List<ResourceKeyConfigData> resConfigDataNew = new ArrayList<>();
        List<PrizeConfigV2VO> seaUserList = new ArrayList<>();
        for (String resKey : CATCH_FISH_SEA_LIST) {
            ResourceKeyConfigData resKeyData = resConfigDataMap.get(resKey);
//            for (ResourceKeyConfigData.ResourceMeta resourceMeta : resKeyData.getResourceMetaList()) {
//                resourceMeta.setStatus(0);
//            }
            resConfigDataNew.add(resKeyData);
            String seaKey = resKeyData.getKey();
            PrizeConfigV2VO seaUserConfig = new PrizeConfigV2VO();
            seaUserConfig.setZoneType(seaKey);
            // 解锁当前区域的人数
            seaUserConfig.setRateNumber(String.valueOf(activityCommonRedis.getCommonSetNum(getSeaZoneUserKey(activityId, seaKey, currentDay))));
            seaUserList.add(seaUserConfig);
        }
        vo.setResourceKeyDataList(resConfigDataNew);
        vo.setSeaUserList(seaUserList);

        // 默认海域
        if (inActivityTime(activityId)) {
            this.addSeaZoneList(activityId, CATCH_FISH_SEA_LIST.get(0), currentDay, uid);
            // if (DateHelper.getNowSeconds() > START_UNLOCK_SEA_TIME) {
            //     this.addSeaZoneList(activityId, CATCH_FISH_SEA_LIST.get(1), currentDay, uid);
            //     this.addSeaZoneList(activityId, CATCH_FISH_SEA_LIST.get(2), currentDay, uid);
            // }
        }
        return vo;
    }

    public CatchFishVO catchFishRank(String activityId, String uid, int rankType) {
        if (!RANK_LIST.contains(rankType)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        CatchFishVO vo = new CatchFishVO();
        String currentDay = getDayByBase(activityId, uid);
        Map<Integer, String> dateMap = getDateMap(currentDay);
        String date = dateMap.get(rankType);
        // 设置排行榜
        String rankKey = getCatchFishRankKey(activityId, rankType, date);
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        makeOtherRankingData(rankingList, myRank, rankKey, uid, 10);
        vo.setCatchRankList(rankingList);
        vo.setMyCatchRank(myRank);
        return vo;
    }


    private void addSeaZoneList(String activityId, String seaType, String currentDay, String uid) {
        String key = getSeaZoneUserKey(activityId, seaType, currentDay);
        int isEnter = activityCommonRedis.isCommonSetData(key, uid);
        if (isEnter > 0) {
            return;
        }
        activityCommonRedis.addCommonSetData(key, uid);
        this.doOpenSeaZoneEvent(activityId, uid, seaType);
    }

    /**
     * 开启海域埋点
     */
    private void doOpenSeaZoneEvent(String activityId, String uid, String seaType) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE);
        event.setActive_id(activityId);
        event.setActivity_stage_desc(seaType);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 潜水道具获取事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, String seaType) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(1);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(1);
        event.setResource_desc(seaType);
        event.setChange_nums(1);
        eventReport.track(new EventDTO(event));
    }


    private void doDrawReportEvent(String uid, String seaType, int amount, int costNum, String rewardTitleDesc, List<PrizeConfigV2VO> drawRecordList, String metaId) {
        Map<String, Integer> drawRecordMap = drawRecordList.stream().collect(Collectors.groupingBy(PrizeConfigV2VO::getResourceNameEn, Collectors.summingInt(PrizeConfigV2VO::getResourceNumber)));
        StarBeatGameLogEvent event = new StarBeatGameLogEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE);
        event.setSence_detail_desc(seaType);
        event.setCost_ticket(costNum);
        event.setCost_diamonds(costNum);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(CATCH_FAIL_MATCH.equals(metaId) ? 0 : drawRecordList.size());
        event.setDraw_detail(rewardTitleDesc);
        event.setDraw_result(JSON.toJSONString(drawRecordMap));
        eventReport.track(new EventDTO(event));
    }


    // 获取抽奖配置
    private ResourceKeyConfigData getResourceKeyConfig(String uid, String seaType, String metaId) {

        List<String> resKeyList = null;
        if (CATCH_FISH_SEA_LIST.get(0).equals(seaType)) {
            resKeyList = metaId.contains(CATCH_FISH_MATCH) ? CATCH_FISH_1_DRAW_LIST : CATCH_BOX_1_DRAW_LIST;
        } else if (CATCH_FISH_SEA_LIST.get(1).equals(seaType)) {
            resKeyList = metaId.contains(CATCH_FISH_MATCH) ? CATCH_FISH_2_DRAW_LIST : CATCH_BOX_2_DRAW_LIST;
        } else {
            resKeyList = metaId.contains(CATCH_FISH_MATCH) ? CATCH_FISH_3_DRAW_LIST : CATCH_BOX_3_DRAW_LIST;
        }
        return resourceKeyConfigDao.findByKey(resKeyList.get(0));

//        if (DRAW_USER_LIST.contains(uid)) {
//            return resourceKeyConfigDao.findByKey(resKeyList.get(2));
//        }
//        int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
//        if (rechargeMoney >= 5) {
//            return resourceKeyConfigDao.findByKey(resKeyList.get(0));
//        } else {
//            return resourceKeyConfigDao.findByKey(resKeyList.get(1));
//        }
    }

    private void initDrawPrizePool(Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String drawPrizeKey) {
        int poolSize = activityCommonRedis.getCommonListSize(drawPrizeKey);
        if (poolSize <= LIMIT_INIT_POOL) {
            List<String> poolList = new ArrayList<>();
            for (String prizeKey : resourceMetaMap.keySet()) {
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(prizeKey);
                int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
            }
            Collections.shuffle(poolList);
            activityCommonRedis.rightPushAllCommonList(drawPrizeKey, poolList);
        }
    }

    // 抽奖
    private String drawInPool(String activityId, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String resKey) {
        String drawPrizeKey = getListDrawPrizeKey(activityId, resKey);
        this.initDrawPrizePool(resourceMetaMap, drawPrizeKey);
        String awardKey = activityCommonRedis.leftPopCommonListKey(drawPrizeKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return awardKey;
    }

    private String getResTitleByKey(String resKey) {
        return CATCH_FISH_TITLE_MAP.get(resKey);
    }

    public CatchFishVO catchFishDraw(String activityId, String uid, String seaType, int amount, String metaId) {
        OtherRankingActivityData otherRankingActivityData = otherActivityService.checkActivityTime(activityId);
        if (!CATCH_FISH_SEA_LIST.contains(seaType)) {  //
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (!CATCH_FISH_TIMES_LIST.contains(amount)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        List<Integer> costFishList = CATCH_FISH_COST_MAP.get(seaType);
        if (costFishList == null) {
            logger.error("catchFishDraw not find costFishList seaType:{}", seaType);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        // 查询中奖道具
        ResourceKeyConfigData seaTypeConfig = resourceKeyConfigDao.findByKey(seaType);
        if (seaTypeConfig == null) {
            logger.error("catchFishDraw not find seaTypeConfig seaType:{}", seaType);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        Map<String, ResourceKeyConfigData.ResourceMeta> seaTypeConfigMap = seaTypeConfig.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        ResourceKeyConfigData.ResourceMeta seaResMeta = seaTypeConfigMap.get(metaId);
        if (seaResMeta == null) {
            logger.error("catchFishDraw not find seaResMeta seaType:{}", seaType);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        int costNum = amount == 1 ? costFishList.get(0) : costFishList.get(1);
        ActorData actorData = actorDao.getActorData(uid);
        String currentDay = getDayByBase(activityId, uid);
        if (costNum > actorData.getBeans()) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_DIAMOND);
        }
        String titleEnPre = ServerConfig.isProduct() && otherRankingActivityData.getAcNameEn().startsWith("test") ? "test" : "";
        CatchFishVO vo = new CatchFishVO();
        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            String hashDailyActivityId = getHashDailyActivityId(activityId, uid, currentDay);
            int leftBeans = drawDiamondCost(uid, costNum);

            int currentTime = DateHelper.getNowSeconds();
            String rewardTitleDesc = "";
            List<PrizeConfigV2VO> drawRecordList = new ArrayList<>();
            vo.setNowBeans(leftBeans);
            if (CATCH_FAIL_MATCH.equals(metaId) || metaId.contains(CATCH_SWIM_MATCH)) {
                // 抽中了潜水道具或者失败
                rewardTitleDesc = metaId;
                for (int i = 0; i < amount; i++) {
                    PrizeConfigV2VO drawRecord = new PrizeConfigV2VO();
                    BeanUtils.copyProperties(seaResMeta, drawRecord);
                    drawRecord.setZoneType(seaType);
                    drawRecord.setUid(uid);
                    drawRecord.setCtime(currentTime);
                    drawRecordList.add(drawRecord);
                    activityCommonRedis.addCommonListData(getHistoryRecordListKey(activityId, uid), JSON.toJSONString(drawRecord));
                    if (metaId.contains(CATCH_SWIM_MATCH)) {
                        int afterSeaMeter = activityCommonRedis.incCommonHashNum(hashDailyActivityId, CATCH_FISH_SEA_METER, seaResMeta.getResourceNumber());

                        if (afterSeaMeter >= 25 && afterSeaMeter <= 49) {
                            this.addSeaZoneList(activityId, CATCH_FISH_SEA_LIST.get(1), currentDay, uid);
                        }

                        if (afterSeaMeter >= 50) {
                            this.addSeaZoneList(activityId, CATCH_FISH_SEA_LIST.get(2), currentDay, uid);
                        }
                        vo.setSeaMeter(afterSeaMeter);
                        doReportSpecialItemsEvent(activityId, uid, seaType);
                    }
                }
            } else {
                // 抽中了鱼或者宝箱
                ResourceKeyConfigData resKeyConfigData = this.getResourceKeyConfig(uid, seaType, metaId);
                if (resKeyConfigData == null) {
                    throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
                }
                String resKey = resKeyConfigData.getKey();
                rewardTitleDesc = this.getResTitleByKey(resKey);
                Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
                int earnBeans = 0;
                rewardTitleDesc = String.format("%s %s", titleEnPre, rewardTitleDesc);
                for (int i = 0; i < amount; i++) {
                    String awardKey = this.drawInPool(activityId, resourceMetaMap, resKey);
                    ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(awardKey);
                    if (resourceMeta == null) {
                        continue;
                    }
                    resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, MoneyTypeConstant.CATCH_FISH_TYPE,
                            rewardTitleDesc, rewardTitleDesc, rewardTitleDesc, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
                    PrizeConfigV2VO drawRecord = new PrizeConfigV2VO();
                    BeanUtils.copyProperties(resourceMeta, drawRecord);
                    drawRecord.setZoneType(seaType);
                    drawRecord.setUid(uid);
                    drawRecord.setCtime(currentTime);
                    drawRecordList.add(drawRecord);
                    activityCommonRedis.addCommonListData(getHistoryRecordListKey(activityId, uid), JSON.toJSONString(drawRecord));

                    if (resourceMeta.getResourceType() == -2) {
                        earnBeans += resourceMeta.getResourceNumber();
                    }
                }

                if (earnBeans > 0) {
                    // 统计生涯的赚取钻石
                    activityCommonRedis.incCommonHashNum(hashActivityId, CATCH_FISH_EARN_BEAN, earnBeans);
                }
            }
            vo.setDrawRecordList(drawRecordList);
            this.doDrawReportEvent(uid, seaType, amount, costNum, rewardTitleDesc, drawRecordList,metaId);
        }

        Map<Integer, String> dateMap = getDateMap(currentDay);
        for (Integer type : RANK_LIST) {
            String date = dateMap.get(type);
            if (StringUtils.isEmpty(date)) {
                continue;
            }
            String rankKey = getCatchFishRankKey(activityId, type, date);
            // 统计赚取钻石的日，周，月榜
            activityCommonRedis.incrCommonZSetRankingScoreSimple(rankKey, uid, costNum);
        }
        return vo;
    }

    private static Map<Integer, String> getDateMap(String currentDay) {
        String weekStart = DateHelper.ARABIAN.getWeekStartDate();
        String monthStart = DateHelper.ARABIAN.getTableSuffix();
        Map<Integer, String> dateMap = new HashMap<>(3);
        dateMap.put(DAY_RANK, currentDay);
        dateMap.put(WEEK_RANK, weekStart);
        dateMap.put(MONTH_RANK, monthStart);
        return dateMap;
    }


    public CatchFishVO catchFishRecord(String activityId, String uid, int page) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        String recordListKey = getHistoryRecordListKey(activityId, uid);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        CatchFishVO drawRecordVO = new CatchFishVO();
        List<PrizeConfigV2VO> drawRecordList = new ArrayList<>();
        for (String item : prizeKeyTimeList) {
            PrizeConfigV2VO drawRecord = JSON.parseObject(item, PrizeConfigV2VO.class);
            drawRecordList.add(drawRecord);
        }
        drawRecordVO.setDrawRecordList(drawRecordList);
        if (drawRecordList.size() < RECORD_PAGE_SIZE) {
            drawRecordVO.setNextUrl(-1);
        } else {
            drawRecordVO.setNextUrl(page + 1);
        }
        return drawRecordVO;
    }

    // 1、扣费
    private int drawDiamondCost(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(MoneyTypeConstant.CATCH_FISH_TYPE);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(MoneyTypeConstant.CATCH_FISH_FEE_TITLE);
        moneyDetailReq.setDesc(MoneyTypeConstant.CATCH_FISH_FEE_TITLE);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_DIAMOND);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return Integer.parseInt(result.getData());
    }
}
