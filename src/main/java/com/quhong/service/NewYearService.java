package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.NewYearExpectDTO;
import com.quhong.data.vo.NewYearHallVO;
import com.quhong.data.vo.NewYearMessageVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.NewYearRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Component
public class NewYearService extends OtherActivityService{

    private static final Logger logger = LoggerFactory.getLogger(NewYearService.class);
    private static final String NEW_YEAR_ORIGIN = "new_year_expect";
    private static final List<String> SHARE_DESC_EN = Arrays.asList(
            "#Write down your 2023 wish#\n" + "New year New expectation\n" + "Don't forget to write down your New Year's wishes",
            "#Write down your 2023 wish#\n" + "May health and happiness come to everyone",
            "#Write down your 2023 wish#\n" + "No matter what, 2023 will end up with unforgettable stories\n" + "Wishing everyone enjoy your 2023"
    );
    private static final List<String> SHARE_DESC_AR = Arrays.asList(
            "# اكتب رغبتك لعام 2023 #\n" + "عام جديد بآفاق جديدة\n" + "لا تنس كتابة رغبات السنة الجديدة الخاصة بك",
            "# اكتب رغبتك لعام 2023 #\n" + "أتمنى أن تأتي الصحة والسعادة للجميع",
            "# اكتب رغبتك لعام 2023 #\n" + "عام 2023 سينتهي به المطاف بقصص لا تُنسى\n" + "أتمنى للجميع الاستمتاع بك عام 2023"
    );

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private NewYearRedis newYearRedis;

    public NewYearMessageVO newYearMessage(String uid, String activityId) {
        OtherRankingActivityData otherActivity = getOtherRankingActivity(activityId);
        if(otherActivity == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        MomentActivityData myFuture = momentActivityDao.findNewYearMsgOne(uid, NEW_YEAR_ORIGIN);
        List<MomentActivityData> momentList = momentActivityDao.findNewYearMsgList(uid, NEW_YEAR_ORIGIN);

        NewYearMessageVO vo = new NewYearMessageVO();
        List<NewYearMessageVO.Message> momentIds = new ArrayList<>();

        if (myFuture != null) {
            NewYearMessageVO.Message myMessage = new NewYearMessageVO.Message();
            ActorData actorData = actorDao.getActorDataFromCache(uid);

            String momentId = myFuture.get_id().toString();
            myMessage.setMomentId(momentId);
            myMessage.setMessage(newYearRedis.getMomentMessage(momentId));
            myMessage.setName(actorData.getName());
            myMessage.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myMessage.setRepost(myFuture.getRepost());
            myMessage.setLikes(myFuture.getLikes() != null ? myFuture.getLikes().size() : 0);
            myMessage.setComments(myFuture.getComments());

            momentIds.add(myMessage);
            vo.setWrote(1);
        }

        for (MomentActivityData data : momentList) {
            NewYearMessageVO.Message otherMessage = new NewYearMessageVO.Message();
            String momentId = data.get_id().toString();
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            otherMessage.setMomentId(momentId);
            otherMessage.setMessage(newYearRedis.getMomentMessage(momentId));
            otherMessage.setName(actorData.getName());
            otherMessage.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            otherMessage.setRepost(data.getRepost());
            otherMessage.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
            otherMessage.setComments(data.getComments());

            momentIds.add(otherMessage);
            if (momentIds.size() >= 5) {
                break;
            }
        }

        vo.setMessageList(momentIds);
        vo.setStartTime(otherActivity.getStartTime());
        vo.setEndTime(otherActivity.getEndTime());

        return vo;

    }


    public NewYearHallVO newYearHall(int page, String momentId) {
        int size = 20;
        int start = (page - 1) * size;

        List<MomentActivityData> momentList = momentActivityDao.findMsgPageList(momentId, NEW_YEAR_ORIGIN, start, size);

        NewYearHallVO vo = new NewYearHallVO();
        List<NewYearHallVO.Message> momentIds = new ArrayList<>();

        for (MomentActivityData data : momentList) {
            NewYearHallVO.Message otherMessage = new NewYearHallVO.Message();

            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());

            otherMessage.setMessage(newYearRedis.getMomentMessage(data.get_id().toString()));
            otherMessage.setName(actorData.getName());
            otherMessage.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            otherMessage.setRepost(data.getRepost());
            otherMessage.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
            otherMessage.setComments(data.getComments());

            momentIds.add(otherMessage);
        }

        vo.setMessageList(momentIds);
        vo.setNext(momentIds.size() < size ? -1 : page + 1);
        return vo;

    }

    public void newYearExpect(NewYearExpectDTO dto) {

        String activityId = dto.getActivityId();
        String message = dto.getMessage();
        message = message.trim();

        String uid = dto.getUid();
        String img = dto.getMessageUrl();

        String messageMD5 = DigestUtils.md5DigestAsHex(message.getBytes(StandardCharsets.UTF_8));

        if(StringUtils.isEmpty(activityId) || StringUtils.isEmpty(message) || message.length() > 200){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        OtherRankingActivityData otherActivity = getOtherRankingActivity(activityId);
        if(otherActivity == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        int currentTime = DateHelper.getNowSeconds();
        if (currentTime < otherActivity.getStartTime() || currentTime > otherActivity.getEndTime()) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        synchronized (stringPool.intern(uid + messageMD5)) {

            MomentActivityData expected = momentActivityDao.findNewYearMsgOne(uid, NEW_YEAR_ORIGIN);
            if(expected != null){
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
            }

            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();

            publishMomentDTO.setUid(uid);
            List<String> momentText = dto.getSlang() == SLangType.ARABIC ? SHARE_DESC_AR : SHARE_DESC_EN;
            Collections.shuffle(momentText);
            publishMomentDTO.setText(momentText.get(0));
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(NEW_YEAR_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(img);
            imageDTO.setWidth("1080");
            imageDTO.setHeight("1920");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));
            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if(result.getCode() == 20){
                logger.info("newYearExpect level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("newYearExpect error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            MomentActivityData myFuture = momentActivityDao.findNewYearMsgOne(uid, NEW_YEAR_ORIGIN);
            if(myFuture != null){
                newYearRedis.setMomentMessage(myFuture.get_id().toString(), message);
            }else {
                throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
            }

        }
    }


    public NewYearHallVO newYearExpectRanking(String uid) {

        List<MomentActivityData> momentList = momentActivityDao.momentRanking(NEW_YEAR_ORIGIN, 0,0, 5);

        NewYearHallVO vo = new NewYearHallVO();
        List<NewYearHallVO.Message> momentIds = new ArrayList<>();

        for (MomentActivityData data : momentList) {
            NewYearHallVO.Message otherMessage = new NewYearHallVO.Message();

            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());

            otherMessage.setMessage(newYearRedis.getMomentMessage(data.get_id().toString()));
            otherMessage.setName(actorData.getName());
            otherMessage.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            otherMessage.setRepost(data.getRepost());
            otherMessage.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
            otherMessage.setComments(data.getComments());
            momentIds.add(otherMessage);
        }

        vo.setMessageList(momentIds);
        vo.setNext(-1);

        return vo;

    }
}
