package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.ConquerActivity;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.mysql.dao.GiftRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.CountData;
import com.quhong.redis.ConquerRedis;
import com.quhong.redis.RankingActivityRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RankActivityService {
    private static final Logger logger = LoggerFactory.getLogger(RankActivityService.class);
    private static final Comparator<RankingListVO> conquerRoomDesc = Comparator.comparing(RankingListVO::getConquerRoom).reversed();
    private static final Comparator<RankingListVO> scoreDesc = Comparator.comparing(RankingListVO::getScore).reversed();

    @Resource
    private RankingActivityDao rankingActivityDao;

    @Resource
    private ConquerActivityDao conquerActivityDao;

    @Resource
    private RankingActivityRedis rankingActivityRedis;
    @Resource
    private RankActivityService rankActivityService;
    @Resource
    private ConquerRedis conquerRedis;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private GiftRecordDao giftRecordDao;
    @Resource
    private CacheDataService cacheDataService;



    /**
     * 获取正在运行的冲榜活动
     */
    public List<RankingActivity> getRankingActivities() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<RankingActivity> activityList = rankActivityService.getRankingActivitiesFromCache();
        return activityList.stream().filter(a -> a.getEndTime() > nowSeconds && a.getStartTime() <= nowSeconds).collect(Collectors.toList());
    }

    @Cacheable(value = "getRankingActivitiesFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<RankingActivity> getRankingActivitiesFromCache() {
        List<RankingActivity> activityList = rankingActivityDao.getRankingActivities();
        logger.info("getRankingActivitiesFromCache size={}", activityList.size());
        return activityList;
    }

    @Cacheable(value = "rankActivity", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public RankingActivity getRankingActivity(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        RankingActivity data = rankingActivityDao.findData(activityId);
        if (null == data) {
            logger.error("cannot find ranking activity activityId={}", activityId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }

    @Cacheable(value = "getRankingActivityNull", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public RankingActivity getRankingActivityNull(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            return null;
        }
        return rankingActivityDao.findData(activityId);
    }
    @Cacheable(value = "getConquerCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public ConquerActivity getConquerCache() {
        try {
            return conquerActivityDao.findOne();
        } catch (Exception e) {
            logger.info("getConquerCache error:", e);
            return null;
        }
    }

    public RankingVO rankRanking(String uid, String activityId, int rankType, int rankGiftId, int rankGender, int gloryInfo) {
        RankingVO vo = new RankingVO();
        RankingActivity activity = rankActivityService.getRankingActivity(activityId);
        if (1 == activity.getConfig().getNoRankReward()) {
            return vo;
        }
        for (RankingActivity.RankingConfig rankingConfig : activity.getRankingConfigList()) {
            String conquerId = null;
            if (rankingConfig.getRankingAttribute() != rankType) {
                continue;
            }
            // 指定礼物的榜单
            if (rankGiftId != 0) {
                if (null == rankingConfig.getGiftId() || rankingConfig.getGiftId() != rankGiftId) {
                    continue;
                }
            }
            if (rankType == ActivityConstant.CONQUER_RANK) {
                ConquerActivity conquerActivity = rankActivityService.getConquerCache();
                if (conquerActivity == null) {
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR);
                }
                conquerId = conquerActivity.get_id().toString();
            }
            vo.setRankingList(getRankingListVO(activityId, rankType, rankGiftId, rankingConfig.isAssociateUser(), conquerId, rankGender, gloryInfo, rankingConfig.getSupportRoomUser()));
            vo.setMyRank(getMyRankVO(activityId, uid, rankType, rankGiftId, rankingConfig.isAssociateUser(), conquerId, vo.getRankingList(), rankGender, gloryInfo, rankingConfig.getSupportRoomUser()));
        }
        return vo;
    }

    /**
     * 排行榜个人数据
     */
    private MyRankVO getMyRankVO(String activityId, String uid, int rankType, int rankGiftId, boolean associateUser,
                                 String conquerId, List<RankingListVO> rankingList, int rankGender, int gloryInfo, int supportRoomUser) {
        MyRankVO myRank = new MyRankVO();
        if (ActivityConstant.ROOM_RANK == rankType) {
            MongoRoomData data = mongoRoomDao.getDataFromCache(RoomUtils.formatRoomId(uid));
            if (null == data) {
                logger.error("can not find room. uid={}", uid);
                return myRank;
            }
            myRank.setName(data.getName());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(data.getHead()));
            fillMyRank(myRank, activityId, data.getRid(), rankType, rankGiftId, associateUser, conquerId, rankingList, rankGender);
            if (supportRoomUser > 0) {
                List<String> supportUserList = rankingActivityRedis.getSupportRoomUserRankingList(activityId, RoomUtils.formatRoomId(uid), rankGiftId, rankGender, 3);
                List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
                for (String supportUid : supportUserList) {
//                        if (supportUid.equals(RoomUtils.getRoomHostId(entry.getKey()))){
//                            continue;
//                        }
                    OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                    ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                    supportUserVO.setName(supportActorData.getName());
                    supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                    supportUserVO.setUid(supportUid);
                    supportMyUserVOList.add(supportUserVO);
//                        if (supportMyUserVOList.size() >= 3){
//                            break;
//                        }
                }
                myRank.setSupportUserList(supportMyUserVOList);
            }
        } else {
            ActorData actor = actorDao.getActorDataFromCache(uid);
            if (null == actor) {
                logger.error("can not find actor. uid={}", uid);
                return myRank;
            }
            myRank.setName(actor.getName());
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
            myRank.setUid(uid);
            fillMyRank(myRank, activityId, uid, rankType, rankGiftId, associateUser, conquerId, rankingList, rankGender);

            if (gloryInfo > 0) {
                Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
                myRank.setBadgeList(badgeDao.getBadgeList(uid));
                myRank.setVipLevel(vipInfoDao.getIntVipLevel(uid));
                String countryCode = ActorUtils.getCountryCode(actor.getCountry());
                myRank.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
            }
        }
        return myRank;
    }

    /**
     * 获取征服活动排行榜
     */
    public List<String> getConquerRankingList(String activityId, int rankType, int rankGiftId, String conquerId, int rankGender) {
        List<RankingListVO> rankingList = new ArrayList<>();
        Map<String, Integer> rankingMap = rankingActivityRedis.getRankingMap(activityId, rankType, rankGiftId, 10, conquerId, rankGender);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            RankingListVO rankingListVO = new RankingListVO();
            rankingListVO.setUid(entry.getKey());
            rankingListVO.setConquerRoom(entry.getValue());
            rankingListVO.setScore(rankingActivityRedis.getScore(activityId, entry.getKey(), rankType, rankGiftId, rankGender));
            rankingList.add(rankingListVO);
        }
        rankingList.sort(conquerRoomDesc.thenComparing(scoreDesc));
        return CollectionUtil.getPropertyList(rankingList, RankingListVO::getUid, null);
    }

    /**
     * 获取排行榜
     */
    private List<RankingListVO> getRankingListVO(String activityId, int rankType, int rankGiftId, boolean associateUser, String conquerId,
                                                 int rankGender, int gloryInfo, int supportRoomUserInfo) {
        List<RankingListVO> rankingList = new ArrayList<>();

        Map<String, Integer> rankingMap = rankingActivityRedis.getRankingMap(activityId, rankType, rankGiftId, 10, conquerId, rankGender);
        Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            RankingListVO rankingListVO = new RankingListVO();
            // rankingListVO.setScore(entry.getValue());
            if (rankType == ActivityConstant.CONQUER_RANK) {
                rankingListVO.setConquerRoom(entry.getValue());
                rankingListVO.setScore(rankingActivityRedis.getScore(activityId, entry.getKey(), rankType, rankGiftId, rankGender));
                rankingListVO.setUid(entry.getKey());
            } else {
                rankingListVO.setScore(entry.getValue());
            }

            if (ActivityConstant.ROOM_RANK == rankType) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setRoomId(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                if (supportRoomUserInfo > 0) {
                    List<String> supportUserList = rankingActivityRedis.getSupportRoomUserRankingList(activityId, entry.getKey(), rankGiftId, rankGender, 3);
                    List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
                    for (String supportUid : supportUserList) {
//                        if (supportUid.equals(RoomUtils.getRoomHostId(entry.getKey()))){
//                            continue;
//                        }
                        OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                        ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                        supportUserVO.setName(supportActorData.getName());
                        supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                        supportUserVO.setUid(supportUid);
                        supportMyUserVOList.add(supportUserVO);
//                        if (supportMyUserVOList.size() >= 3){
//                            break;
//                        }
                    }
                    rankingListVO.setSupportUserList(supportMyUserVOList);
                }
            } else {

                ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
                if (null == rankActor) {
                    logger.error("can not find actor. uid={}", entry.getKey());
                    continue;
                }
                rankingListVO.setName(rankActor.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                rankingListVO.setUid(entry.getKey());
                rankingListVO.setRidData(rankActor.getRidData());

                if (gloryInfo > 0) {
                    rankingListVO.setBadgeList(badgeDao.getBadgeList(entry.getKey()));
                    rankingListVO.setVipLevel(vipInfoDao.getIntVipLevel(entry.getKey()));
                    String countryCode = ActorUtils.getCountryCode(rankActor.getCountry());
                    rankingListVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
                }
            }
            if (associateUser) {
                if (ServerConfig.isProduct()) {
                    // 正式服走1分钟的缓存

                    RankingActivityRedis.Associate associate = rankActivityService.getAssociateHead(activityId, entry.getKey(), rankType, rankGiftId);
                    if (null == associate) {
                        continue;
                    }
                    rankingListVO.setAssociateHead(associate.getHead());
                    rankingListVO.setAssociateUid(associate.getUid());
                } else {
                    RankingActivityRedis.Associate associate = getAssociateHead(activityId, entry.getKey(), rankType, rankGiftId);
                    if (null == associate) {
                        continue;
                    }
                    rankingListVO.setAssociateHead(associate.getHead());
                    rankingListVO.setAssociateUid(associate.getUid());
                }
            }
            rankingList.add(rankingListVO);
        }
        if (rankType == ActivityConstant.CONQUER_RANK) {
            rankingList.sort(conquerRoomDesc.thenComparing(scoreDesc));
        }
        return rankingList;
    }


    @Cacheable(value = "getAssociateHead", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public RankingActivityRedis.Associate getAssociateHead(String activityId, String uid, int rankType, int rankGiftId) {
        RankingActivityRedis.Associate associate = rankingActivityRedis.getAssociate(activityId, uid, rankType, rankGiftId);
        if (null != associate) {
            ActorData actorData = actorDao.getActorDataFromCache(associate.getUid());
            if (null == actorData) {
                logger.error("can not find actor. uid={}", associate.getUid());
                return null;
            }
            associate.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            return associate;
        }
        return null;
    }

    private void fillMyRank(MyRankVO myRank, String activityId, String uid, int rankType,
                            int rankGiftId, boolean associateUser, String conquerId, List<RankingListVO> rankingList, int rankGender) {
        myRank.setScore(rankingActivityRedis.getScore(activityId, uid, rankType, rankGiftId, rankGender));
        if (ActivityConstant.CONQUER_RANK == rankType) {
            myRank.setConquerRoom(conquerRedis.getScore(conquerId, uid));
            int total = 1;
            for (RankingListVO vo : rankingList) {
                if (uid.equals(vo.getUid())) {
                    myRank.setRank(total);
                }
                total += 1;
            }

        } else {
            myRank.setRank(rankingActivityRedis.getRank(activityId, uid, rankType, rankGiftId, rankGender));
        }
        if (associateUser) {
            // 关联用户信息
            RankingActivityRedis.Associate associate = rankingActivityRedis.getAssociate(activityId, uid, rankType, rankGiftId);
            if (null != associate) {
                ActorData actorData = actorDao.getActorDataFromCache(associate.getUid());
                if (null == actorData) {
                    logger.error("can not find actor. uid={}", associate.getUid());
                    return;
                }
                myRank.setAssociateHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                myRank.setAssociateUid(associate.getUid());
                myRank.setAssociateScore(associate.getScore());
            }
        }

    }

    public ReachingVO rankReaching(String uid, String activityId) {
        ReachingVO vo = new ReachingVO();
        RankingActivity activity = rankActivityService.getRankingActivity(activityId);
        if (activity.getConfig().getHasGradeReward() != 1) {
            return vo;
        }
        for (RankingActivity.ReachingConfig reachingConfig : activity.getReachingConfigList()) {
            if (reachingConfig.getReachingRewardType() == ActivityConstant.SEND_RANK) {
                vo.setSender(rankingActivityRedis.getReachingScore(activityId, uid, reachingConfig.getReachingRewardType(), reachingConfig.getCalculateMethod()));
            } else if (reachingConfig.getReachingRewardType() == ActivityConstant.RECEIVE_RANK) {
                vo.setReceiver(rankingActivityRedis.getReachingScore(activityId, uid, reachingConfig.getReachingRewardType(), reachingConfig.getCalculateMethod()));
            } else if (reachingConfig.getReachingRewardType() == ActivityConstant.CONQUER_RANK) {
                ConquerActivity conquerActivity = rankActivityService.getConquerCache();
                if (conquerActivity != null) {
                    vo.setConquerRoom(conquerRedis.getScore(conquerActivity.get_id().toString(), uid));
                }
            }
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setGender(actorData.getFb_gender());
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        return vo;
    }

    public void fixRankReaching() {
        String acId = "67b6d16ca8e9a74324fe65ba";
        long startTime = 1740085200000L;
        List<CountData> sendList = giftRecordDao.getSendBellCountTimeList(startTime);
        List<CountData> recvList = giftRecordDao.getRecvBellCountTimeList(startTime);
        for (CountData countData : sendList) {
            String fromUid = countData.getMyKey();
            int totalPrice = (int) countData.getCount();
            rankingActivityRedis.incrReachingScore(acId, fromUid, -1, totalPrice, 1, 2);
        }
        for (CountData countData : recvList) {
            String aid = countData.getMyKey();
            int totalPrice = (int) countData.getCount();
            rankingActivityRedis.incrReachingScore(acId, aid, -1, totalPrice, 2, 2);
        }
    }

}
