package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.GiftRecordDao;
import com.quhong.mysql.data.CountData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 2024植树活动
 */
@Component
public class PlantTreeV2Service extends OtherActivityService implements DailyTaskHandler {

    private static final Logger logger = LoggerFactory.getLogger(PlantTreeV2Service.class);
    private static final String ACTIVITY_ID = "6731e563edc3e32f0a2897c8";
    private static final String PLANT_TREE_LEFT_WATER = "leftWater";   //  剩余水滴数
    private static final String PLANT_TREE_LEFT_BEANS = "leftBeans";
    private static final String PLANT_TREE_USED_WATER = "usedWater";    // 已浇水滴数
    private static final String PLANT_TREE_LEVEL = "plantLevel";    // 等级
    private static final String PLANT_TREE_FINISH_PLANT = "finishPlant";    // 完成种植次数
    private static final String PLANT_TREE_REMIND = "remind";    // 通知
    private static final Integer INVITE_MAX_NUM = 5;
    private static final List<String> PLANT_TREE_DATE_LIST = Arrays.asList("2024-11-13", "2024-11-14", "2024-11-15", "2024-11-16", "2024-11-17", "2024-11-18");    // 通知
    private static final List<PlantingTaskVO> PLANT_TREE_TASK_LIST = new ArrayList<>();
    private static final String DAILY_TASK_REWARD_TITLE = "Plant-daily task reward";
    private static final String COMPLETE_PLANTING_TOP1_REWARD_KEY = "completePlantingTop1"; // 第一个完成种植的奖励
    private static final String COMPLETE_PLANTING_REWARD_KEY = "completePlantingOther"; // 完成种植的奖励
    private static final String COMPLETE_PLANTING_NUM5_REWARD_KEY = "completePlantingNum5"; // 累计完成5次种植的奖励
    private static final int ACTIVITY_SHARE_ID = ServerConfig.isProduct() ? 35 : 8;

    private static final List<Reward> NEW_REWARD_LIST = Arrays.asList(
            new Reward(1, 5, "plantingActivityLevel1"),
            new Reward(2, 25, "plantingActivityLevel2"),
            new Reward(3, 45, "plantingActivityLevel3"),
            new Reward(4, 95, "plantingActivityLevel4"),
            new Reward(5, 195, "plantingActivityLevel5"),
            new Reward(6, 845, "plantingActivityLevel6")
    );
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        PLANT_TREE_TASK_LIST.add(new PlantingTaskVO("sign", 5, 5));
        PLANT_TREE_TASK_LIST.add(new PlantingTaskVO("invite_friend", 5, 25));
        PLANT_TREE_TASK_LIST.add(new PlantingTaskVO("send_gift", 1, -1));
    }

    @Resource
    private  ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private EventReport eventReport;
    @Resource
    private GiftRecordDao giftRecordDao;


    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("plantTreeV2:%s:%s", activityId, uid);
    }

    private String getDailyHashActivityId(String activityId, String uid, String dateStr) {
        return String.format("plantTreeV2Daily:%s:%s:%s", activityId, uid, dateStr);
    }

    private String getRemindUserSetKey(String activityId) {
        return String.format("plantTreeV2Remind:%s", activityId);
    }

    // 荣誉堂-完成第一个任务
    private String getDailyFinishTaskZSetKey(String activityId, String dateStr) {
        return String.format("dailyFinishTask:%s:%s", activityId, dateStr);
    }


    // 邀请hash
    private String getHashStrDailyInviteKey(String activityId, String uid, String dateStr) {
        return String.format("dailyUserInvite:%s:%s:%s", activityId, uid, dateStr);
    }

    // 被邀请key
    private String getDailyInvitedSetKey(String activityId, String uid, String dateStr) {
        return String.format("dailyUserInvited:%s:%s:%s", activityId, uid, dateStr);
    }

    private String getInvitePositionKey(Integer position) {
        return String.format("position:%s", position);
    }

    // 完成植树滚动条消息
    private String getDailyFinishTreeRollKey(String activityId, String dateStr) {
        return String.format("dailyFinishTreeRoll:%s:%s", activityId, dateStr);
    }

    private String getCurrentDay(String activityId){
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }

    public WeeklyPlantingVO plantTreeV2Config(String activityId, String uid) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        String currentDay = this.getCurrentDay(activityId);
        WeeklyPlantingVO vo = new WeeklyPlantingVO();
        vo.setCurrentDay(currentDay);
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        // 用户相关数据
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        String hashDailyActivityId = getDailyHashActivityId(activityId, uid, currentDay);
        Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);

        vo.setWaterDropNum(userDailyDataMap.getOrDefault(PLANT_TREE_LEFT_WATER, 0));
        vo.setUsedWaterDropNum(userDailyDataMap.getOrDefault(PLANT_TREE_USED_WATER, 0));
        vo.setSignReminderStatus(userDataMap.getOrDefault(PLANT_TREE_REMIND, 0));
        vo.setCompletedPlantingNum(userDataMap.getOrDefault(PLANT_TREE_FINISH_PLANT, 0));
        vo.setHotRoomId(getPopularRoomId());

        // 荣誉榜排名
        List<PlantingSignVO> signRecords = new ArrayList<>();
        Map<String, List<OtherRankingListVO>> honorRankMap = new HashMap<>();
        for (String dateStr : PLANT_TREE_DATE_LIST){
            // 签到记录
            PlantingSignVO signVO = new PlantingSignVO();
            signVO.setStrDate(dateStr);
            signVO.setStatus(userDataMap.getOrDefault(dateStr, 0));
            signRecords.add(signVO);

            // 荣誉堂
            List<OtherRankingListVO> honorRankList = new ArrayList<>();
            OtherRankingListVO myHonorRank = new OtherRankingListVO();
            String dailyFinishTaskKey = getDailyFinishTaskZSetKey(activityId, dateStr);
            makeOtherRankingData(honorRankList, myHonorRank, dailyFinishTaskKey, uid, 16, false);
            honorRankMap.put(dateStr, honorRankList);

        }
        vo.setDailyDateList(new ArrayList<>());
        vo.setSignRecords(signRecords);
        vo.setHonorRankingMap(honorRankMap);

        // 邀请列表
        List<InviteFriendVO> inviteFriendVOList = getInviteList(activityId, uid, currentDay);
        vo.setInviteList(inviteFriendVOList);

        List<InviteFriendVO> inviteFriendFinishList = inviteFriendVOList.stream().filter(item -> item.getStatus() > 0).collect(Collectors.toList());
        // 任务列表
        List<PlantingTaskVO> taskVOList = new ArrayList<>();
        for (PlantingTaskVO task: PLANT_TREE_TASK_LIST) {
            String taskKey = task.getKey();
            if(taskKey.equals(PLANT_TREE_TASK_LIST.get(0).getKey())){
                task.setFinishValue(userDataMap.getOrDefault(currentDay, 0) * task.getOnceValue());
            }else if(taskKey.equals(PLANT_TREE_TASK_LIST.get(1).getKey())){
                task.setFinishValue(inviteFriendFinishList.size() * task.getOnceValue());
            }
            taskVOList.add(task);
        }
        vo.setTaskList(taskVOList);

        // 中奖通知
        vo.setRewardNotifyList(activityCommonRedis.getCommonListRecord(getDailyFinishTreeRollKey(activityId, currentDay)));
        return vo;
    }

    private List<InviteFriendVO> getInviteList(String activityId, String uid, String currentDay) {
        String hashInviteStrKey = getHashStrDailyInviteKey(activityId, uid, currentDay);
        Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashInviteStrKey);
        List<InviteFriendVO> list = new ArrayList<>();
        for (int i = 1; i <= INVITE_MAX_NUM; i++) {
            String  invitePositionKey = this.getInvitePositionKey(i);
            String inviteData = userStrDataMap.get(invitePositionKey);
            if (inviteData == null) {
                list.add(new InviteFriendVO("", "", i, -1));
            } else {
                InviteFriendVO inviteFriendVO = JSONObject.parseObject(inviteData, InviteFriendVO.class);
                ActorData actor = actorDao.getActorDataFromCache(inviteFriendVO.getAid());
                inviteFriendVO.setName(actor.getName());
                inviteFriendVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
                inviteFriendVO.setRid(actor.getStrRid());
                list.add(inviteFriendVO);
            }
        }
        return list;
    }

    /**
     * 浇水
     */
    public WateringVO plantTreeV2Watering(String activityId, String uid, int num) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        WateringVO vo = new WateringVO();
        if(num <= 0){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern("watering_" + uid)) {

            String currentDay = this.getCurrentDay(activityId);
            String hashDailyActivityId = getDailyHashActivityId(activityId, uid, currentDay);
            Map<String, Integer> userDailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);
            long leftNum = userDailyDataMap.getOrDefault(PLANT_TREE_LEFT_WATER, 0);
            if (num > leftNum) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            int afterLeftNum = activityCommonRedis.incCommonHashNum(hashDailyActivityId, PLANT_TREE_LEFT_WATER, -num);
            int curNum = activityCommonRedis.incCommonHashNum(hashDailyActivityId, PLANT_TREE_USED_WATER, num);

            doReportWaterChangeEvent(activityId, uid, -1, num, 4, "浇树");
            if (curNum == num) {
                // 完成每日首次浇水
                // 我被邀请用户列表 我-B
                Set<String> invitedUserSet = activityCommonRedis.getCommonSetMember(getDailyInvitedSetKey(activityId, uid, currentDay));
                int inviteIncWater = PLANT_TREE_TASK_LIST.get(1).getOnceValue();
                for (String inviteUid : invitedUserSet) {   // 这些人邀请我-B - A列表
                    String hashInviteStrKey = getHashStrDailyInviteKey(activityId, inviteUid, currentDay);
                    Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashInviteStrKey);
                    for (Map.Entry<String, String> entry : userStrDataMap.entrySet()) {
                        String invitePosition = entry.getKey();
                        String inviteData = entry.getValue();
                        InviteFriendVO inviteFriend = JSONObject.parseObject(inviteData, InviteFriendVO.class);
                        String inviteAid = inviteFriend.getAid();
                        if(!inviteAid.equals(uid)){
                            continue;
                        }

                        if(inviteFriend.getStatus() == 1){
                            continue;
                        }

                        inviteFriend.setStatus(1);
                        activityCommonRedis.setCommonHashData(hashInviteStrKey, invitePosition, JSONObject.toJSONString(inviteFriend));

                        String hashDailyInvitedActivityId = getDailyHashActivityId(activityId, inviteUid, currentDay);
                        activityCommonRedis.incCommonHashNum(hashDailyInvitedActivityId, PLANT_TREE_LEFT_WATER, inviteIncWater);
                        doReportWaterChangeEvent(activityId, uid, 1, inviteIncWater, 2, "拉人助力");
                    }
                }
            }

            String hashActivityId = getHashActivityId(activityId, uid);
            // int firstPlant = activityCommonRedis.getCommonHashValue(hashActivityId, PLANT_TREE_FIRST_PLANT);

            int curPlantingLevel = userDailyDataMap.getOrDefault(PLANT_TREE_LEVEL, 0);
            int newPlantingLevel = 0;
            List<Reward> rewardList = NEW_REWARD_LIST;
            for (Reward reward : rewardList) {
                if (curNum >= reward.getLimit() && reward.getLevel() > curPlantingLevel) {
                    // 达到奖励门槛，发送奖励
                    resourceKeyHandlerService.sendResourceData(uid, reward.getRewardKey(), DAILY_TASK_REWARD_TITLE, DAILY_TASK_REWARD_TITLE, DAILY_TASK_REWARD_TITLE, "", "");
                    doReportEvent(activityId, uid, reward.getLevel());
                    newPlantingLevel = Math.max(newPlantingLevel, reward.getLevel());
                    String dailyFinishTaskKey = getDailyFinishTaskZSetKey(activityId, currentDay);
                    activityCommonRedis.incrCommonZSetRankingScore(dailyFinishTaskKey, uid, newPlantingLevel);

                    if (curNum >= rewardList.get(rewardList.size() - 1).getLimit()) {
                        // 获奖通知
                        String dailyFinishTreeKey = getDailyFinishTreeRollKey(activityId, currentDay);
                        activityCommonRedis.addCommonListRecord(dailyFinishTreeKey, ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

                        // 累加完成种植次数
                        if (activityCommonRedis.incCommonHashNum(hashActivityId, PLANT_TREE_FINISH_PLANT, 1) == 2) {
                            resourceKeyHandlerService.sendResourceData(uid, COMPLETE_PLANTING_NUM5_REWARD_KEY, "Plant-total complete 5times", "Plant-total complete 5times", "Plant-total complete 5times", "", "");
                        }
                    }
                }
            }
            if (newPlantingLevel != 0) {
                activityCommonRedis.setCommonHashNum(hashDailyActivityId, PLANT_TREE_LEVEL, newPlantingLevel);
            }
            vo.setCurLevel(Math.max(curPlantingLevel, newPlantingLevel));
            vo.setWaterDropNum(afterLeftNum);
        }
        return vo;
    }

    public void handleChangeTimeData() {
        String currentDay = getCurrentDay(ACTIVITY_ID);
        List<CountData>  plantTreeList = giftRecordDao.getPlantTreeChangeTimeList(1731877200000L);
        for (CountData countData : plantTreeList) {
            String fromUid = countData.getMyKey();
            String dailyHashActivityId = getDailyHashActivityId(ACTIVITY_ID, fromUid, currentDay);
            int totalPrice = (int) countData.getCount();
            // 排行榜
            activityOtherRedis.incrOtherRankingScore(ACTIVITY_ID, fromUid, totalPrice, 1, 0);


            logger.info("handleChangeTimeData  fromUid:{}, totalPrice:{}", fromUid, totalPrice);
            int leftBeans = activityCommonRedis.getCommonHashValue(dailyHashActivityId, PLANT_TREE_LEFT_BEANS);
            totalPrice = totalPrice + leftBeans;


            int incNum = totalPrice / 50;
            leftBeans = totalPrice % 50;
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, PLANT_TREE_LEFT_WATER, incNum);
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, PLANT_TREE_LEFT_BEANS, leftBeans);
            doReportWaterChangeEvent(ACTIVITY_ID, fromUid, 1, incNum, 3, "送礼");
        }
    }

    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        int totalPrice = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();
        String currentDay = getCurrentDay(activityId);
        String dailyHashActivityId = getDailyHashActivityId(activityId, fromUid, currentDay);
        int leftBeans = activityCommonRedis.getCommonHashValue(dailyHashActivityId, PLANT_TREE_LEFT_BEANS);
        totalPrice = totalPrice + leftBeans;

        int incNum = totalPrice / 50;
        leftBeans = totalPrice % 50;

        activityCommonRedis.incCommonHashNum(dailyHashActivityId, PLANT_TREE_LEFT_WATER, incNum);
        activityCommonRedis.setCommonHashNum(dailyHashActivityId, PLANT_TREE_LEFT_BEANS, leftBeans);
        doReportWaterChangeEvent(activityId, fromUid, 1, incNum, 3, "送礼");

    }

    /**
     * 邀请好友
     */
    public ListVO<InviteFriendVO> inviteFriend(String activityId, String uid, String aid, int position) {
        if (!friendsDao.isFriend(uid, aid)) {
            logger.error("inviteFriend param, not is friend. uid={} aid={}", uid, aid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        String currentDay = getCurrentDay(activityId);
        synchronized (stringPool.intern("invite_" + uid)) {
            String hashInviteStrKey = getHashStrDailyInviteKey(activityId, uid, currentDay);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashInviteStrKey);
            String invitePositionKey = getInvitePositionKey(position);
            String invitePosition = userStrDataMap.get(invitePositionKey);
            if(invitePosition != null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            for (Map.Entry<String, String> entry : userStrDataMap.entrySet()) {
                String inviteData = entry.getValue();
                InviteFriendVO inviteFriend = JSONObject.parseObject(inviteData, InviteFriendVO.class);

                if(inviteFriend.getAid().equals(aid)){
                    throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
                }
            }
            String dailyHashInvitedActivityId = getDailyHashActivityId(activityId, aid, currentDay);
            int status = activityCommonRedis.getCommonHashValue(dailyHashInvitedActivityId, PLANT_TREE_USED_WATER) > 0 ? 1 : 0;
            InviteFriendVO recordData = new InviteFriendVO();
            recordData.setAid(aid);
            recordData.setPosition(position);
            recordData.setStatus(status);
            activityCommonRedis.setCommonHashData(hashInviteStrKey, invitePositionKey, JSONObject.toJSONString(recordData));
            if (status == 1) {
                // 被邀请的好友已完成浇水，双方同时获得10g
                String hashDailyActivityId = getDailyHashActivityId(activityId, uid, currentDay);
                activityCommonRedis.incCommonHashNum(hashDailyActivityId, PLANT_TREE_LEFT_WATER, 5);
                doReportWaterChangeEvent(activityId, uid, 1, 5, 2, "拉人助力");
            }

            // 记录我被谁邀请
            String dailyInvitedSetKey = getDailyInvitedSetKey(activityId, aid, currentDay);
            activityCommonRedis.addCommonSetData(dailyInvitedSetKey, uid);

        }
        // 发送活动私信给朋友
        sendInviteMsg(uid, aid);
        return new ListVO<>(getInviteList(activityId, uid, currentDay));
    }

    private void sendInviteMsg(String uid, String aid) {
        ActorData actorData = actorDao.getActorDataFromCache(aid);
        SendChatMsgDTO msgDto = new SendChatMsgDTO();
        msgDto.setUid(uid);
        msgDto.setAid(aid);
        msgDto.setMsgType(MsgType.SHARE_ACTIVITY);
        msgDto.setOs(actorData.getIntOs());
        msgDto.setMsgBody("");
        msgDto.setSlang(actorData.getSlang());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("shareId", ACTIVITY_SHARE_ID);
        msgDto.setMsgInfo(jsonObject);
        msgDto.setVersioncode(actorData.getVersion_code());
        msgDto.setNew_versioncode(5);
        iMsgService.sendMsg(msgDto);
    }

    public ListVO<InviteFriendVO> removeInviteFriend(String activityId, String uid, String aid) {
        String currentDay = getCurrentDay(activityId);
        checkActivityTime(activityId);
        synchronized (stringPool.intern("invite_" + uid)) {
            String hashInviteStrKey = getHashStrDailyInviteKey(activityId, uid, currentDay);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashInviteStrKey);
            for (Map.Entry<String, String> entry : userStrDataMap.entrySet()) {
                String inviteKey = entry.getKey();
                String inviteData = entry.getValue();
                InviteFriendVO inviteFriend = JSONObject.parseObject(inviteData, InviteFriendVO.class);

                if(inviteFriend.getAid().equals(aid) && inviteFriend.getStatus() != 1){
                    activityCommonRedis.delCommonHashData(hashInviteStrKey, inviteKey);
                }
            }
        }
        return new ListVO<>(getInviteList(activityId, uid, currentDay));
    }

    /**
     * 签到
     */
    public void plantTreeV2Sign(String activityId, String uid) {
        checkActivityTime(activityId);
        String currentDay = getCurrentDay(activityId);
        synchronized (stringPool.intern("sign_" + uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
            int signStatus = userDataMap.getOrDefault(currentDay, 0);
            if(signStatus > 0){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }
            activityCommonRedis.setCommonHashNum(hashActivityId, currentDay, 1);
            String hashDailyActivityId = getDailyHashActivityId(activityId, uid, currentDay);
            activityCommonRedis.incCommonHashNum(hashDailyActivityId, PLANT_TREE_LEFT_WATER, 5);
            doReportWaterChangeEvent(activityId, uid, 1, 5, 1, "签到");
        }
    }

    /**
     * 设置签到提醒
     */
    public void plantTreeV2SignReminder(String activityId, String uid, int status) {
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        activityCommonRedis.setCommonHashNum(hashActivityId, PLANT_TREE_REMIND, status);
        if (status == 0) {
            activityCommonRedis.removeCommonSetData(getRemindUserSetKey(activityId), uid);
        } else {
            activityCommonRedis.addCommonSetData(getRemindUserSetKey(activityId), uid);
        }
    }


    private void doReportEvent(String activityId, String uid, int stage) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setActivity_name("Plant");
        event.setActive_id(activityId);
        event.setChannel("");
        event.setActivity_stage(stage);
        event.setCost_activity_ticket(0);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void doReportWaterChangeEvent(String activityId, String uid, int action, int num, int source, String desc) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setActivity_name("Plant");
        event.setActive_id(activityId);
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActive_id("");
        event.setActivity_special_items_resource(source);
        event.setResource_desc(desc);
        event.setChange_nums(num);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }


    private static class Reward {
        private int level;
        private int limit;
        private String rewardKey;

        public Reward() {
        }

        public Reward(int level, int limit, String rewardKey) {
            this.level = level;
            this.limit = limit;
            this.rewardKey = rewardKey;
        }

        public int getLevel() {
            return level;
        }

        public void setLevel(int level) {
            this.level = level;
        }

        public int getLimit() {
            return limit;
        }

        public void setLimit(int limit) {
            this.limit = limit;
        }

        public String getRewardKey() {
            return rewardKey;
        }

        public void setRewardKey(String rewardKey) {
            this.rewardKey = rewardKey;
        }
    }

    @Override
    public void dailyTaskRun(String dateStr) {
        try {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if (activityData == null) {
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }
            if (dateStr == null) {
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            }
            logger.info("dailyTaskRun Plant Tree");
            List<String> rankingList = activityCommonRedis.getCommonRankingList(getDailyFinishTaskZSetKey(ACTIVITY_ID, dateStr), 16);
            int rank = 1;
            for (String rankUid : rankingList) {
                if (rank == 1){
                    resourceKeyHandlerService.sendResourceData(rankUid, COMPLETE_PLANTING_TOP1_REWARD_KEY, "Plant-honor reward", "Plant-honor reward",  "Plant-honor reward", "", "");
                }
                resourceKeyHandlerService.sendResourceData(rankUid, COMPLETE_PLANTING_REWARD_KEY, "Plant-honor reward", "Plant-honor reward",  "Plant-honor reward", "", "");
                rank += 1;
            }

        } catch (Exception e) {
            logger.error("distribution Catch Fish error: {}", e.getMessage(), e);
        }
    }

}
