package com.quhong.service;

import com.quhong.config.HonorNationalConfig;

import com.quhong.constant.ActivityConstant;
import com.quhong.data.ActorData;
import com.quhong.data.vo.HonorNationalVO;

import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;

import com.quhong.redis.ActivityOtherRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class HonorNationalService {
    private static final Logger logger = LoggerFactory.getLogger(HonorNationalService.class);

    @Resource
    private ActorDao actorDao;
    @Resource
    protected HonorNationalConfig honorNationalConfig;
    @Resource
    protected ActivityOtherRedis activityOtherRedis;



    /**
     *  国家荣誉
     */
    public HonorNationalVO getHonorNational(String uid) {
        HonorNationalVO vo = new HonorNationalVO();

        String activityName = honorNationalConfig.getActivityName();

        vo.setStartTime(honorNationalConfig.getStartTime());
        vo.setEndTime(honorNationalConfig.getEndTime());
        ActorData uidActor = actorDao.getActorData(uid);
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(uidActor.getHead()));

        List<HonorNationalConfig.FlagConfig> flagConfigList =  honorNationalConfig.getGiftList();

        List<HonorNationalVO.FlagConfig> flagList = new ArrayList<>();


        for (HonorNationalConfig.FlagConfig footBallConfig : flagConfigList) {
            String activityNameGiftId = activityName + footBallConfig.getGiftId();
            int sendNum = activityOtherRedis.getOtherReachingScore(activityNameGiftId, uid, ActivityConstant.SEND_RANK, 0);
            if(sendNum > 0){
                HonorNationalVO.FlagConfig flagConfigVO = new HonorNationalVO.FlagConfig();
                flagConfigVO.setName(footBallConfig.getName());
                flagConfigVO.setNameAr(footBallConfig.getNameAr());
                flagConfigVO.setIcon(footBallConfig.getIcon());
                flagConfigVO.setSendNum(sendNum);
                flagList.add(flagConfigVO);

            }
        }
        vo.setFlagList(flagList);
        return vo;
    }
}
