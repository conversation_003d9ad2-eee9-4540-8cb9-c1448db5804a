package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.OperationRoomWashVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.OtherSupportUserVO;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 分国家运营房选拔赛
 */
@Service
public class OperationRoomCountrySelectService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(OperationRoomCountrySelectService.class);
    private static final String ACTIVITY_TITLE_EN = "Activity Room Countries Select Competition";
    public static String ACTIVITY_ID = "68500a8d5e2d0c57c95d04c0"; //
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/op_room_countries_select/?activityId=%s", ACTIVITY_ID);
    private static final List<String> ALL_LOOK_USER = Arrays.asList("635e7fa60adfa9310ea6ddac","6679169633182509a8419d21","5c89f2ae66dc63003846826e"
    ,"5aafad091bad4807d134c2d4","5ade03ef1bad48aa748aa0ee","594a9d991bad4838a47d9a99","5b5defb5acc6cb0035b6cc81","5c9c029366dc63004c2cbdf9"); // 有权限看排行榜积分的用户
    // 已经是运营房的房间不参加本次活动
    List<String> OPERATION_ROOM_LIST = Arrays.asList(
            "r:6111b324830ba10ccf89fe70",
            "r:5c80e09c66dc63003dc4de58",
            "r:5c6cd10f66dc63516adcb6b3",
            "r:6063258e7a505b70e2bed1de",
            "r:5fafa436a3639186a55df9d8",
            "r:60d0a3a691965616273453bf",
            "r:5db8575dabb01a005f968a93",
            "r:5d04ba8babb01a005df4ea18",
            "r:64778030f0a7955a89912700",
            "r:5f9cbfd3abb01a00144e2ed7",
            "r:62d6c46f69dc68b21e2afca1",
            "r:60dd015bf4d233799ebac629",
            "r:622e9cc874bb1a9f4c763f4f",
            "r:63e410ba5ed641547f759cb0",
            "r:5ee27069484060c13abb07e8",
            "r:60254fa3abb01a0032b55944",
            "r:5cb2f98c66dc63003ebfe3f2",
            "r:5dd82e611b9cae1212012d7f",
            "r:5fb920c22a8c6468a363dbd8",
            "r:634454c6202c0143cdef7ba1",
            "r:5bc8a56266dc6300f162f3eb",
            "r:633cb648fbdf9b5e1f285947",
            "r:5ea7a1c3abb01a00856e5520",
            "r:5c190cd066dc63009d6527df",
            "r:5c334f6b66dc63011a81a3a5",
            "r:620bd31e464de81263b9c0ef",
            "r:600806c21b666cec90d405c0",
            "r:600c00bf32a61091e2d5f187",
            "r:5dcf5fb977cc7c7e1f8d4029",
            "r:5c65a64966dc63003ebe124d",
            "r:5c40423966dc630030b5faa3",
            "r:5cc9473a66dc630025bf7645",
            "r:5f6769ac15a6c3af461ca69e",
            "r:5aed7d7f1bad489359765888",
            "r:5eeba3ef48e4232b9867f454",
            "r:5db52b44ab4f1e4128e0fc96",
            "r:5d8b945178cf5ea933183b5b",
            "r:5c5cbfc066dc630026cb6262",
            "r:5f16d69babb01a004fdfe5d4",
            "r:5da4da74add87696168edb44"
    );

    // "沙特阿拉伯", "阿联酋", "伊拉克", "卡塔尔", "阿曼", "叙利亚", "阿尔及利亚", "摩洛哥", "约旦", "埃及", "巴林";
    private static final List<String> ALL_SELECT_COUNTRY_LIST = Arrays.asList("sa", "ae", "iq", "qa", "om", "sy", "dz", "ma", "jo", "eg", "bh");

//    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
//            CommonMqTaskConstant.PERSONAL_UPDATE_COUNTRY);


    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    @Resource
    private WhiteTestDao whiteTestDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "684a8fb1eb33845d8cfad49c";
            OPERATION_ROOM_LIST = Arrays.asList("r:684fedef978db62160ef9448", "r:684fedeb978db62160ef9447");
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/op_room_countries_select/?activityId=%s", ACTIVITY_ID);
        }
    }


    private String getNewOperationRoomWashRankKey(String activityId, String countryCode) {
        return String.format("operationRoomSelect:new:%s:%s", activityId, countryCode);
    }

    private String getNewSupportRoomUserKey(String activityId, String roomId, String countryCode) {
        return String.format("supportRoomUser:new:%s:%s:%s", activityId, roomId, countryCode);
    }

    private String getOperationRoomWashRankKey(String activityId, String countryCode) {
        return String.format("operationRoomSelect:%s:%s", activityId, countryCode);
    }

    private String getSupportRoomUserKey(String activityId, String roomId, String countryCode) {
        return String.format("supportRoomUser:%s:%s:%s", activityId, roomId, countryCode);
    }


    /**
     * 验证并获取有效的国家码 - 优先使用IP国家
     */
    private String getValidCountryCode(String countryCode, String uid) {
        try {
            // 如果传入的countryCode为空，优先获取用户的IP国家码
            if (StringUtils.isEmpty(countryCode)) {
                // 先从Redis Hash中获取已记录的IP国家
                String ipCountryHashKey = getUserIpCountryHashKey(ACTIVITY_ID);
                countryCode = activityCommonRedis.getCommonHashStrValue(ipCountryHashKey, uid);

                // 如果Redis中没有，则从ActorData中获取IP国家
                if (StringUtils.isEmpty(countryCode)) {
                    ActorData actorData = actorDao.getActorDataFromCache(uid);
                    countryCode = ActorUtils.getCountryCode(actorData.getIpCodeCountry());
                }
            }

            // 如果countryCode不在允许列表中，则使用ae作为默认值
            if (!ALL_SELECT_COUNTRY_LIST.contains(countryCode)) {
                countryCode = "ae";
            }

            return countryCode;
        } catch (Exception e) {
            logger.error("getValidCountryCode error: {}", e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
    }

    /**
     * 验证并获取有效的国家码 - 优先使用IP国家
     */
    private String getValidCountryCodeByUid(String uid, boolean isSave) {
        try {
            // 先从Redis Hash中获取已记录的IP国家
            String ipCountryHashKey = getUserIpCountryHashKey(ACTIVITY_ID);
            String countryCode = activityCommonRedis.getCommonHashStrValue(ipCountryHashKey, uid);

            // 如果Redis中没有，则从ActorData中获取IP国家
            if (StringUtils.isEmpty(countryCode)) {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                countryCode = ActorUtils.getCountryCode(actorData.getIpCodeCountry());
                if (isSave && ALL_SELECT_COUNTRY_LIST.contains(countryCode)) {
                    activityCommonRedis.setCommonHashData(ipCountryHashKey, uid,
                            countryCode);
                }
            }
            if (StringUtils.isEmpty(countryCode)) {
                countryCode = "us"; // 没有ip国家码，使用us作为默认值,非统计国家
            }
            return countryCode;
        } catch (Exception e) {
            logger.error("getValidCountryCode error: {}", e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
    }


    public OperationRoomWashVO operationRoomSelectConfig(String activityId, String uid, String countryCode) {
        try {
            // 验证并获取有效的国家码
            countryCode = getValidCountryCode(countryCode, uid);
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            String userIpCountryCode = getValidCountryCodeByUid(uid, false); // 使用IP国家

            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
            OperationRoomWashVO vo = new OperationRoomWashVO();
            vo.setStartTime(activityData.getStartTime());
            vo.setEndTime(activityData.getEndTime());

            Map<Integer, OtherRankingListVO> hiddenRankingMap = new HashMap<>();
            OtherRankingListVO myRank = new OtherRankingListVO();
            Map<String, Integer> scoreMap = new HashMap<>();
            String hostRoomId = RoomUtils.formatRoomId(uid);

            String operationRoomWashRankKey = getNewOperationRoomWashRankKey(activityId, countryCode);
            Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(operationRoomWashRankKey, 10);
            int rank = 1;
            int flag = -1; // 房间在榜单中的排名,前10有值，否则为-1

            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                OtherRankingListVO rankingListVO = new OtherRankingListVO();
                String roomId = entry.getKey();
                rankingListVO.setScoreStr(entry.getValue().toString());
                rankingListVO.setRoomId(roomId);
                rankingListVO.setRank(rank);
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
                rankingListVO.setName(roomData.getName());
                rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));

                String supportUserKey = getNewSupportRoomUserKey(activityId, roomId, countryCode);
                List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 4);
                List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
                for (String supportUid : supportUserList) {
                    if (supportUid.equals(RoomUtils.getRoomHostId(roomId))) {
                        continue;
                    }
                    OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                    ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                    supportUserVO.setName(supportActorData.getName());
                    supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                    supportUserVO.setUid(supportUid);
                    supportUserVOList.add(supportUserVO);

                    if (supportUserVOList.size() >= 3) {
                        break;
                    }
                }
                rankingListVO.setSupportUserList(supportUserVOList);
                if (roomId.equals(hostRoomId)) {
                    flag = rank;
                    BeanUtils.copyProperties(rankingListVO, myRank);
                    List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
                    for (OtherSupportUserVO srcSupportUser : supportUserVOList) {
                        OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                        BeanUtils.copyProperties(srcSupportUser, supportUserVO);
                        supportMyUserVOList.add(supportUserVO);
                    }
                    myRank.setSupportUserList(supportMyUserVOList);
                } else {
                    if (!ALL_LOOK_USER.contains(uid)) {
                        rankingListVO.setScoreStr("*****");
                    }
                }
                hiddenRankingMap.put(rank, rankingListVO);
                scoreMap.put(entry.getKey(), entry.getValue());
                rank += 1;
            }

            if (flag > 0) {
                OtherRankingListVO beforeMap = hiddenRankingMap.get(flag - 1);
                if (beforeMap != null) {
                    beforeMap.setScoreStr(scoreMap.get(beforeMap.getRoomId()).toString());
                }

                OtherRankingListVO afterMap = hiddenRankingMap.get(flag + 1);
                if (afterMap != null) {
                    afterMap.setScoreStr(scoreMap.get(afterMap.getRoomId()).toString());
                }
            } else {

                myRank.setScoreStr(String.valueOf(activityCommonRedis.getCommonZSetRankingScore(operationRoomWashRankKey, hostRoomId)));
                myRank.setRoomId(hostRoomId);
                myRank.setRank(-1); // 没有上榜
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(hostRoomId);
                if (roomData != null) {
                    myRank.setName(roomData.getName());
                    myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                } else {
                    myRank.setName(actorData.getName());
                    myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                }
                String supportUserKey = getNewSupportRoomUserKey(activityId, hostRoomId, countryCode);
                List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 4);
                List<OtherSupportUserVO> supportMyUserVOList = new ArrayList<>();
                for (String supportUid : supportUserList) {
                    if (supportUid.equals(RoomUtils.getRoomHostId(hostRoomId))) {
                        continue;
                    }
                    OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                    ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                    supportUserVO.setName(supportActorData.getName());
                    supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                    supportUserVO.setUid(supportUid);
                    supportMyUserVOList.add(supportUserVO);
                    if (supportMyUserVOList.size() >= 3) {
                        break;
                    }
                }
                myRank.setSupportUserList(supportMyUserVOList);
            }
            if (OPERATION_ROOM_LIST.contains(hostRoomId)) {
                myRank.setRank(-2); // 已经是运营房
            } else if (!ALL_SELECT_COUNTRY_LIST.contains(userIpCountryCode)) {
                myRank.setRank(-3); // 用户国家不在允许列表中
            } else if (!countryCode.equals(userIpCountryCode)) {
                myRank.setRank(-4); // 当前选择的国家不是用户实际国家
            }
            vo.setSelectedCountryCode(countryCode);
            vo.setMyRank(myRank);
            vo.setOtherRankingList(new ArrayList<>(hiddenRankingMap.values()));
            return vo;
        } catch (Exception e) {
            logger.error("operationRoomSelectConfig error: {}", e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }
    }

    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        try {
            int totalPrice = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            String roomId = giftData.getRoomId();
            String fromUid = giftData.getFrom_uid();

            if (StringUtils.isEmpty(roomId)) {
                return;
            }

            if (OPERATION_ROOM_LIST.contains(roomId) || RoomUtils.isGameRoom(roomId)) {
                return;
            }

            // 获取房主的IP国家码
            String hostUid = RoomUtils.getRoomHostId(roomId);
            String countryCode = getValidCountryCodeByUid(hostUid, true);

            // 只收集在允许列表中的国家的数据
            if (!ALL_SELECT_COUNTRY_LIST.contains(countryCode)) {
                return;
            }

            String operationRoomWashRankKey = getNewOperationRoomWashRankKey(activityId, countryCode);
            String supportRoomUserKey = getNewSupportRoomUserKey(activityId, roomId, countryCode);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(operationRoomWashRankKey, roomId, totalPrice);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(supportRoomUserKey, fromUid, totalPrice);
        } catch (Exception e) {
            logger.error("sendGiftHandle error: {}", e.getMessage(), e);
            throw new CommonH5Exception();
        }
    }


//    public void taskMsgProcess(CommonMqTopicData data) {
//        String uid = data.getUid();
//        String aid = data.getAid();
//        String roomId = data.getRoomId();
//        String item = data.getItem();
//        if (!CommonMqTaskConstant.PERSONAL_UPDATE_COUNTRY.equals(item)) {
//            return;
//        }
//        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
//            return;
//        }
//        if (!inActivityTime(ACTIVITY_ID)) {
//            return;
//        }
//
//        changeCountryHandle(uid, data);
//    }

    private void changeCountryHandle(String uid, CommonMqTopicData mqData) {
        try {
            if (!checkAc(uid, mqData)) {
                return;
            }

            String changeCountry = mqData.getAid();
            if (StringUtils.isEmpty(changeCountry) || !changeCountry.contains("-")) {
                logger.error("changeCountry format invalid: uid={} changeCountry={}", uid, changeCountry);
                return;
            }

            // 解析改之前和改之后的国家码
            String[] countryCodes = changeCountry.split("-");
            if (countryCodes.length != 2) {
                logger.error("changeCountry format invalid: uid={} changeCountry={}", uid, changeCountry);
                return;
            }

            String oldCountryCode = countryCodes[0];
            String newCountryCode = countryCodes[1];

            boolean shouldMigrate = false; // 是否迁移数据，否则清空

            actorDao.removeActorCache(uid);
            // 验证国家码有效性
            if (ALL_SELECT_COUNTRY_LIST.contains(oldCountryCode)) {
                logger.info("old country codes  in select list: uid={} oldCountryCode={} newCountryCode={}",
                        uid, oldCountryCode, newCountryCode);
                if (ALL_SELECT_COUNTRY_LIST.contains(newCountryCode)) {
                    shouldMigrate = true;
                }
            } else {
                logger.info("old country codes not in select list: uid={} oldCountryCode={} newCountryCode={}",
                        uid, oldCountryCode, newCountryCode);
                return;
            }

            int vipLevel = vipInfoDao.getIntVipLevel(uid);
            String roomId = RoomUtils.formatRoomId(uid);

            // 获取改国家次数记录key
            String changeCountryCountKey = getChangeCountryCountKey(ACTIVITY_ID);
            int changeCount = activityCommonRedis.getCommonHashValue(changeCountryCountKey, uid);

            // 判断是否允许改国家
            boolean canChange = false;

            if (shouldMigrate) {
                if (vipLevel <= 0) {
                    // vip<=0时，始终允许改国家，但清空数据
                    canChange = true;
                    shouldMigrate = false;
                } else if (vipLevel <= 4) {
                    // vip<=4时，可改国家1次
                    if (changeCount < 1) {
                        canChange = true;
                        shouldMigrate = true;
                    } else {
                        // 超过次数，清空数据
                        canChange = true;
                        shouldMigrate = false;
                    }
                } else {
                    // vip>=5时，可改国家2次
                    if (changeCount < 2) {
                        canChange = true;
                        shouldMigrate = true;
                    } else {
                        // 超过次数，清空数据
                        canChange = true;
                        shouldMigrate = false;
                    }
                }
            }

            // 获取原国家的数据
            String oldOperationRoomWashRankKey = getNewOperationRoomWashRankKey(ACTIVITY_ID, oldCountryCode);
            String oldSupportRoomUserKey = getNewSupportRoomUserKey(ACTIVITY_ID, roomId, oldCountryCode);

            // 获取原有数据用于备份和迁移
            int oldRoomScore = activityCommonRedis.getCommonZSetRankingScore(oldOperationRoomWashRankKey, roomId);
            // 只迁移前50名用户的数据
            Map<String, Integer> oldSupportUsersMap = activityCommonRedis.getCommonRankingMap(oldSupportRoomUserKey, 50);

            // 备份原有数据到redis hash
            String backupKey = getBackupKey(ACTIVITY_ID, uid, changeCount + 1);
            Map<String, String> backupData = new HashMap<>();
            backupData.put("oldCountryCode", oldCountryCode);
            backupData.put("newCountryCode", newCountryCode);
            backupData.put("roomScore", String.valueOf(oldRoomScore));
            backupData.put("changeTime", String.valueOf(DateHelper.getNowSeconds()));
            backupData.put("vipLevel", String.valueOf(vipLevel));
//            backupData.put("supportUsers", JSONObject.toJSONString(oldSupportUsersMap));
            activityCommonRedis.setCommonHashDataAll(backupKey, backupData);

            if (shouldMigrate && oldRoomScore > 0) {
                // 迁移数据到新国家
                String newOperationRoomWashRankKey = getNewOperationRoomWashRankKey(ACTIVITY_ID, newCountryCode);
                String newSupportRoomUserKey = getNewSupportRoomUserKey(ACTIVITY_ID, roomId, newCountryCode);

                // 迁移房间积分
                activityCommonRedis.incrCommonZSetRankingScoreSimple(newOperationRoomWashRankKey, roomId, oldRoomScore);

                // 迁移支持用户积分
                for (Map.Entry<String, Integer> entry : oldSupportUsersMap.entrySet()) {
                    String supportUid = entry.getKey();
                    int supportScore = entry.getValue();
                    if (supportScore > 0) {
                        activityCommonRedis.incrCommonZSetRankingScoreSimple(newSupportRoomUserKey, supportUid, supportScore);
                    }
                }

                logger.info("migrate country data success: uid={} roomId={} oldCountry={} newCountry={} roomScore={} supportUserCount={}",
                        uid, roomId, oldCountryCode, newCountryCode, oldRoomScore, oldSupportUsersMap.size());
            } else {
                logger.info("clear country data: uid={} roomId={} oldCountry={} newCountry={} roomScore={} supportUserCount={} shouldMigrate={}",
                        uid, roomId, oldCountryCode, newCountryCode, oldRoomScore, oldSupportUsersMap.size(), shouldMigrate);
            }

            // 清空原国家数据
            if (oldRoomScore > 0) {
                activityCommonRedis.removeCommonZSet(oldOperationRoomWashRankKey, roomId);
            }

            // 清空原支持用户数据
            activityCommonRedis.deleteCommonZSet(oldSupportRoomUserKey);
//            for (String supportUid : oldSupportUsersMap.keySet()) {
//                activityCommonRedis.removeCommonZSet(oldSupportRoomUserKey, supportUid);
//            }

            // 更新改国家次数
            activityCommonRedis.setCommonHashNum(changeCountryCountKey, uid, changeCount + 1);
            activityCommonRedis.setCommonHashData(changeCountryCountKey, uid + "_lastChangeTime", String.valueOf(DateHelper.getNowSeconds()));
            activityCommonRedis.setCommonHashData(changeCountryCountKey, uid + "_lastChange", changeCountry);

            logger.info("changeCountryHandle success: uid={} changeCountry={} vipLevel={} changeCount={} shouldMigrate={}",
                    uid, changeCountry, vipLevel, changeCount + 1, shouldMigrate);

        } catch (Exception e) {
            logger.error("changeCountryHandle error: uid={} mqData={} error={}", uid, mqData, e.getMessage(), e);
        }
    }

    /**
     * 获取改国家次数记录key
     */
    private String getChangeCountryCountKey(String activityId) {
        return String.format("changeCountryCount:%s", activityId);
    }

    /**
     * 获取备份数据key
     */
    private String getBackupKey(String activityId, String uid, int changeIndex) {
        return String.format("countryChangeBackup:%s:%s:%d", activityId, uid, changeIndex);
    }

    private boolean checkAc(String uid, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    // 总榜排行榜奖励
    public void distributionTotalRanking(String activityId) {
        try {
            // 为每个国家分别发放奖励
            for (String countryCode : ALL_SELECT_COUNTRY_LIST) {
                Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getNewOperationRoomWashRankKey(activityId, countryCode), 10);
                int rank = 1;
                for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                    String rankUid = RoomUtils.getRoomHostId(entry.getKey());
                    String resourceKey = null;
                    switch (rank) {
                        case 1:
                            resourceKey = "ActivityRoomCompetitionTop1";
                            break;
                        case 2:
                            resourceKey = "ActivityRoomCompetitionTop2";
                            break;
                        case 3:
                            resourceKey = "ActivityRoomCompetitionTop3";
                            break;
                        default:
                            resourceKey = "ActivityRoomCompetitionTop4-10";
                    }
                    resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_EN, "", "");
                    rank += 1;
                }
            }
        } catch (Exception e) {
            logger.error("distributionTotalRanking error: {}", e.getMessage(), e);
        }
    }

    /**
     * 账号国家改IP国家 - 转化国家函数
     * 将参与活动的所有用户账号国家转化为ip国家
     */
    public void convertAccountCountryToIpCountry() {
        try {
            logger.info("开始执行账号国家改IP国家转化，活动ID: {}", ACTIVITY_ID);

            // 获取所有参与活动的用户
            Set<String> participantUids = getAllActivityParticipants();
            logger.info("找到 {} 个参与活动的用户", participantUids.size());

            int processedCount = 0;
            int migratedCount = 0;
            int clearedCount = 0;

            for (String uid : participantUids) {
                try {
                    boolean result = convertSingleUserCountry(uid);
                    if (result) {
                        migratedCount++;
                    } else {
                        clearedCount++;
                    }
                    processedCount++;

                    // 每处理100个用户输出一次进度
                    if (processedCount % 100 == 0) {
                        logger.info("转化进度: {}/{}, 迁移: {}, 清空: {}",
                                processedCount, participantUids.size(), migratedCount, clearedCount);
                    }
                } catch (Exception e) {
                    logger.error("转化用户{}失败: {}", uid, e.getMessage(), e);
                }
            }

            logger.info("账号国家改IP国家转化完成，总处理: {}, 迁移: {}, 清空: {}",
                    processedCount, migratedCount, clearedCount);

        } catch (Exception e) {
            logger.error("convertAccountCountryToIpCountry error: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取所有参与活动的用户UID
     */
    private Set<String> getAllActivityParticipants() {
        Set<String> allUids = new HashSet<>();

        try {
            // 从各个国家的排行榜中获取参与用户
            for (String countryCode : ALL_SELECT_COUNTRY_LIST) {
                String operationRoomWashRankKey = getOperationRoomWashRankKey(ACTIVITY_ID, countryCode);
                Map<String, Integer> roomRankingMap = activityCommonRedis.getCommonRankingMap(operationRoomWashRankKey, 2000);

                for (String roomId : roomRankingMap.keySet()) {
                    // 添加房主
                    String hostUid = RoomUtils.getRoomHostId(roomId);
                    allUids.add(hostUid);

                    // 添加支持用户
//                    String supportRoomUserKey = getSupportRoomUserKey(ACTIVITY_ID, roomId, countryCode);
//                    List<String> supportUsers = activityCommonRedis.getCommonRankingList(supportRoomUserKey, 10000);
//                    allUids.addAll(supportUsers);
                }
            }

            logger.info("从活动排行榜中找到 {} 个参与用户", allUids.size());
        } catch (Exception e) {
            logger.error("获取活动参与用户失败: {}", e.getMessage(), e);
        }

        return allUids;
    }

    /**
     * 转化单个用户的国家
     *
     * @return true=迁移数据，false=清空数据
     */
    private boolean convertSingleUserCountry(String uid) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null) {
                logger.warn("用户{}不存在", uid);
                return false;
            }

            String accountCountryCode = ActorUtils.getCountryCode(actorData.getCountry());
            String ipCountryCode = ActorUtils.getCountryCode(actorData.getIpCodeCountry());

            if (ALL_SELECT_COUNTRY_LIST.contains(ipCountryCode)) {
                // 将用户IP国家存入Redis Hash，hash字段存uid
                String ipCountryHashKey = getUserIpCountryHashKey(ACTIVITY_ID);
                activityCommonRedis.setCommonHashData(ipCountryHashKey, uid, ipCountryCode);
            }


            // 如果IP国家为空或不在允许列表中，清空数据
            if (StringUtils.isEmpty(ipCountryCode) || !ALL_SELECT_COUNTRY_LIST.contains(ipCountryCode)) {
                logger.info("用户{}IP国家为空或不在允许列表 清空数据: {}", uid, ipCountryCode);
                clearUserActivityData(uid, accountCountryCode, ipCountryCode);
                return false;
            }

            // 如果账号国家不在允许列表中，也不做迁移
            if (StringUtils.isEmpty(accountCountryCode) || !ALL_SELECT_COUNTRY_LIST.contains(accountCountryCode)) {
                logger.info("用户{}账号国家不在允许列表: {}", uid, accountCountryCode);
                return true;
            }

            // 如果两个国家一致，数据不做迁移
//            if (accountCountryCode != null && accountCountryCode.equals(ipCountryCode)) {
//                logger.debug("用户{}账号国家与IP国家一致: {}", uid, accountCountryCode);
//            }

            // 迁移数据
            migrateUserActivityDataAdmin(uid, accountCountryCode, ipCountryCode);
            return true;

        } catch (Exception e) {
            logger.error("转化用户{}国家失败: {}", uid, e.getMessage(), e);
            return false;
        }
    }


    /**
     * 迁移用户活动数据
     */
    private void migrateUserActivityDataAdmin(String uid, String fromCountryCode, String toCountryCode) {
        try {
            String roomId = RoomUtils.formatRoomId(uid);

            // 获取原数据
            String oldOperationRoomWashRankKey = getOperationRoomWashRankKey(ACTIVITY_ID, fromCountryCode);
            String oldSupportRoomUserKey = getSupportRoomUserKey(ACTIVITY_ID, roomId, fromCountryCode);

            int oldRoomScore = activityCommonRedis.getCommonZSetRankingScore(oldOperationRoomWashRankKey, roomId);
            Map<String, Integer> oldSupportUsersMap = activityCommonRedis.getCommonRankingMap(oldSupportRoomUserKey, 50);

            // 备份数据
            String backupKey = getCountryConvertBackupKey(ACTIVITY_ID, uid);
            Map<String, String> backupData = new HashMap<>();
            backupData.put("fromCountryCode", fromCountryCode);
            backupData.put("toCountryCode", toCountryCode);
            backupData.put("roomScore", String.valueOf(oldRoomScore));
            backupData.put("convertTime", String.valueOf(DateHelper.getNowSeconds()));
            backupData.put("supportUsers", JSONObject.toJSONString(oldSupportUsersMap));
            activityCommonRedis.setCommonHashDataAll(backupKey, backupData);

            if (oldRoomScore > 0) {
                // 迁移房间积分到新国家
                String newOperationRoomWashRankKey = getNewOperationRoomWashRankKey(ACTIVITY_ID, toCountryCode);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(newOperationRoomWashRankKey, roomId, oldRoomScore);

                // 清空原国家房间数据
//                activityCommonRedis.removeCommonZSet(oldOperationRoomWashRankKey, roomId);
            }

            // 迁移支持用户积分
            if (!CollectionUtils.isEmpty(oldSupportUsersMap)) {
                String newSupportRoomUserKey = getNewSupportRoomUserKey(ACTIVITY_ID, roomId, toCountryCode);
                for (Map.Entry<String, Integer> entry : oldSupportUsersMap.entrySet()) {
                    String supportUid = entry.getKey();
                    int supportScore = entry.getValue();
                    if (supportScore > 0) {
                        activityCommonRedis.incrCommonZSetRankingScoreSimple(newSupportRoomUserKey, supportUid, supportScore);
                    }
                }

                // 清空原国家支持用户数据
//                activityCommonRedis.deleteCommonZSet(oldSupportRoomUserKey);
            }

            logger.info("迁移用户{}数据成功: {}→{}, 房间积分: {}, 支持用户: {}",
                    uid, fromCountryCode, toCountryCode, oldRoomScore, oldSupportUsersMap.size());

        } catch (Exception e) {
            logger.error("迁移用户{}活动数据失败: {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 迁移用户活动数据-接口
     */
    private void migrateUserActivityData(String uid, String fromCountryCode, String toCountryCode) {
        try {
            String roomId = RoomUtils.formatRoomId(uid);

            // 获取原数据
            String oldOperationRoomWashRankKey = getNewOperationRoomWashRankKey(ACTIVITY_ID, fromCountryCode);
            String oldSupportRoomUserKey = getNewSupportRoomUserKey(ACTIVITY_ID, roomId, fromCountryCode);

            int oldRoomScore = activityCommonRedis.getCommonZSetRankingScore(oldOperationRoomWashRankKey, roomId);
            Map<String, Integer> oldSupportUsersMap = activityCommonRedis.getCommonRankingMap(oldSupportRoomUserKey, 50);

            // 备份数据
            String backupKey = getCountryConvertBackupKey(ACTIVITY_ID, uid);
            Map<String, String> backupData = new HashMap<>();
            backupData.put("fromCountryCode", fromCountryCode);
            backupData.put("toCountryCode", toCountryCode);
            backupData.put("roomScore", String.valueOf(oldRoomScore));
            backupData.put("convertTime", String.valueOf(DateHelper.getNowSeconds()));
            backupData.put("supportUsers", JSONObject.toJSONString(oldSupportUsersMap));
            activityCommonRedis.setCommonHashDataAll(backupKey, backupData);

            if (oldRoomScore > 0) {
                // 迁移房间积分到新国家
                String newOperationRoomWashRankKey = getNewOperationRoomWashRankKey(ACTIVITY_ID, toCountryCode);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(newOperationRoomWashRankKey, roomId, oldRoomScore);

                // 清空原国家房间数据
                activityCommonRedis.removeCommonZSet(oldOperationRoomWashRankKey, roomId);
            }

            // 迁移支持用户积分
            if (!CollectionUtils.isEmpty(oldSupportUsersMap)) {
                String newSupportRoomUserKey = getNewSupportRoomUserKey(ACTIVITY_ID, roomId, toCountryCode);
                for (Map.Entry<String, Integer> entry : oldSupportUsersMap.entrySet()) {
                    String supportUid = entry.getKey();
                    int supportScore = entry.getValue();
                    if (supportScore > 0) {
                        activityCommonRedis.incrCommonZSetRankingScoreSimple(newSupportRoomUserKey, supportUid, supportScore);
                    }
                }

                // 清空原国家支持用户数据
                activityCommonRedis.deleteCommonZSet(oldSupportRoomUserKey);
            }

            logger.info("迁移用户{}数据成功: {}→{}, 房间积分: {}, 支持用户: {}",
                    uid, fromCountryCode, toCountryCode, oldRoomScore, oldSupportUsersMap.size());

        } catch (Exception e) {
            logger.error("迁移用户{}活动数据失败: {}", uid, e.getMessage(), e);
        }
    }

    /**
     * 清空用户活动数据
     */
    private void clearUserActivityData(String uid, String fromCountryCode, String toCountryCode) {
        try {
            String roomId = RoomUtils.formatRoomId(uid);

            if (StringUtils.isEmpty(fromCountryCode) || !ALL_SELECT_COUNTRY_LIST.contains(fromCountryCode)) {
                return; // 原本就不在活动范围内，无需清空
            }

            // 获取原数据并备份
            String oldOperationRoomWashRankKey = getOperationRoomWashRankKey(ACTIVITY_ID, fromCountryCode);
            String oldSupportRoomUserKey = getSupportRoomUserKey(ACTIVITY_ID, roomId, fromCountryCode);

            int oldRoomScore = activityCommonRedis.getCommonZSetRankingScore(oldOperationRoomWashRankKey, roomId);
            Map<String, Integer> oldSupportUsersMap = activityCommonRedis.getCommonRankingMap(oldSupportRoomUserKey, 50);

            // 备份数据
            String backupKey = getCountryConvertBackupKey(ACTIVITY_ID, uid);
            Map<String, String> backupData = new HashMap<>();
            backupData.put("fromCountryCode", fromCountryCode);
            backupData.put("toCountryCode", StringUtils.isEmpty(toCountryCode) ? "EMPTY" : toCountryCode);
            backupData.put("roomScore", String.valueOf(oldRoomScore));
            backupData.put("convertTime", String.valueOf(DateHelper.getNowSeconds()));
            backupData.put("supportUsers", JSONObject.toJSONString(oldSupportUsersMap));
            backupData.put("action", "CLEAR");
            activityCommonRedis.setCommonHashDataAll(backupKey, backupData);

            // 清空原数据
//            if (oldRoomScore > 0) {
//                activityCommonRedis.removeCommonZSet(oldOperationRoomWashRankKey, roomId);
//            }
//
//            if (!CollectionUtils.isEmpty(oldSupportUsersMap)) {
//                activityCommonRedis.deleteCommonZSet(oldSupportRoomUserKey);
//            }

            logger.info("清空用户{}数据成功: {}→{}, 房间积分: {}, 支持用户: {}",
                    uid, fromCountryCode, toCountryCode, oldRoomScore, oldSupportUsersMap.size());

        } catch (Exception e) {
            logger.error("清空用户{}活动数据失败: {}", uid, e.getMessage(), e);
        }
    }


    /**
     * 迁移国家接口 - 用于临时国家转化或恢复数据
     */
    public void migrateUserCountry(String rid, String toIpCountry) {
            logger.info("开始迁移用户{}国家到: {}", rid, toIpCountry);

            ActorData actorData = actorDao.getActorByStrRidFromDb(rid);
            if (actorData == null) {
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            String uid = actorData.getUid();

            // 验证目标国家
            if (StringUtils.isEmpty(toIpCountry) || !ALL_SELECT_COUNTRY_LIST.contains(toIpCountry)) {
                logger.error("目标国家{}不在允许列表中，无法迁移", toIpCountry);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }


            // 更新用户IP国家记录
            String ipCountryHashKey = getUserIpCountryHashKey(ACTIVITY_ID);
            String currentIpCountry = activityCommonRedis.getCommonHashStrValue(ipCountryHashKey, uid);

            if (StringUtils.isEmpty(currentIpCountry)) {
                // 获取国家转化备份数据并恢复
                String convertBackupKey = getCountryConvertBackupKey(ACTIVITY_ID, uid);
                Map<String, String> backupData = activityCommonRedis.getCommonHashAllMapStr(convertBackupKey);

                if (!CollectionUtils.isEmpty(backupData)) {
                    logger.info("发现用户{}的国家转化备份数据，开始恢复", uid);
                    String restoreResult = restoreFromCountryConvertBackup(uid, RoomUtils.formatRoomId(uid), backupData,toIpCountry);
                    logger.info("恢复结果: {}", restoreResult);
                    return;
                }else{
                    // 更新IP国家记录
                    activityCommonRedis.setCommonHashData(ipCountryHashKey, uid, toIpCountry);
                    logger.info("用户{}IP国家记录不存在，新设置为{}", uid, toIpCountry);
                    return;
                }
            }

            // 如果国家相同，无需迁移
            if (toIpCountry.equals(currentIpCountry)) {
                logger.info("用户{}国家相同，无需迁移: {}", uid, toIpCountry);
                return;
            }

            // 执行迁移
            migrateUserActivityData(uid, currentIpCountry, toIpCountry);

            // 更新IP国家记录
            activityCommonRedis.setCommonHashData(ipCountryHashKey, uid, toIpCountry);

            logger.info("用户{}国家迁移完成: {}→{}", uid, currentIpCountry, toIpCountry);


    }

    /**
     * 获取用户IP国家Hash key
     */
    private String getUserIpCountryHashKey(String activityId) {
        return String.format("userIpCountry:%s", activityId);
    }

    /**
     * 获取国家转化备份key
     */
    private String getCountryConvertBackupKey(String activityId, String uid) {
        return String.format("countryConvertBackup:%s:%s", activityId, uid);
    }

    /**
     * Web接口 - 执行账号国家改IP国家转化
     * 仅限管理员调用
     */
    public void executeCountryConversion() {
        logger.info("管理员触发账号国家改IP国家转化");
        convertAccountCountryToIpCountry();
    }

    /**
     * Web接口 - 迁移单个用户国家
     *
     * @param rid         用户rid
     * @param toIpCountry 目标IP国家
     */
    public void executeSingleUserMigration(String rid, String toIpCountry) {
        logger.info("管理员触发用户{}国家迁移到: {}", rid, toIpCountry);
        migrateUserCountry(rid, toIpCountry);
    }

    /**
     * 从国家转化备份数据中恢复
     */
    private String restoreFromCountryConvertBackup(String uid, String roomId, Map<String, String> backupData,String toIpCountry) {
        try {
            String fromCountryCode = backupData.get("fromCountryCode");
            String oldToCountryCode = backupData.get("toCountryCode");
            int roomScore = Integer.parseInt(backupData.getOrDefault("roomScore", "0"));
            String supportUsersJson = backupData.get("supportUsers");
            String action = backupData.get("action");
            

            
            // 如果之前是清空数据，恢复到目标国家
            if ("CLEAR".equals(action) && ALL_SELECT_COUNTRY_LIST.contains(toIpCountry)) {
                String toOperationRoomWashRankKey = getNewOperationRoomWashRankKey(ACTIVITY_ID, toIpCountry);
                String toSupportRoomUserKey = getNewSupportRoomUserKey(ACTIVITY_ID, roomId, toIpCountry);
                // 恢复房间积分
                if (roomScore > 0) {
                    activityCommonRedis.incrCommonZSetRankingScoreSimple(toOperationRoomWashRankKey, roomId, roomScore);
                }
                // 恢复支持用户积分
                if (!StringUtils.isEmpty(supportUsersJson) && !"null".equals(supportUsersJson)) {
                    try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> supportUsersMap = JSONObject.parseObject(supportUsersJson, Map.class);
                        for (Map.Entry<String, Object> entry : supportUsersMap.entrySet()) {
                            String supportUid = entry.getKey();
                            Object scoreObj = entry.getValue();
                            int supportScore = scoreObj instanceof Integer ? (Integer) scoreObj :
                                    Integer.parseInt(scoreObj.toString());
                            if (supportScore > 0) {
                                activityCommonRedis.incrCommonZSetRankingScoreSimple(toSupportRoomUserKey, supportUid, supportScore);
                            }
                        }
                    } catch (Exception e) {
                        logger.warn("恢复支持用户数据时解析JSON失败: {}", e.getMessage());
                    }
                }
            }

            // 更新用户IP国家记录
            String ipCountryHashKey = getUserIpCountryHashKey(ACTIVITY_ID);
            activityCommonRedis.setCommonHashData(ipCountryHashKey, uid, toIpCountry);

            String result = String.format("成功恢复用户%s的国家转化备份数据: %s→%s, oldToCountryCode:%s 房间积分: %d, 操作类型: %s",
                                        uid, fromCountryCode, toIpCountry, oldToCountryCode, roomScore, action);
            logger.info(result);
            return result;
            
        } catch (Exception e) {
            logger.error("恢复国家转化备份数据失败: {}", e.getMessage(), e);
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }
    }

}
