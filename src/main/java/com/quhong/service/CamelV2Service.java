package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.CamelMyRankVO;
import com.quhong.data.vo.CamelRankingVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class CamelV2Service extends OtherActivityService implements DailyTaskHandler {

    private static final Logger logger = LoggerFactory.getLogger(CamelV2Service.class);
    private static final List<Integer> LEVEL_NUM_LIST = Arrays.asList(30, 80, 200, 500, 4800);
    private static final List<String> LEVEL_KEY_LIST = Arrays.asList("camelUpLevel1", "camelUpLevel2", "camelUpLevel3", "camelUpLevel4", "camelUpLevel5");
    private static final List<String> DAILY_DATE_LIST = Arrays.asList("2024-10-03", "2024-10-04", "2024-10-05", "2024-10-06", "2024-10-07", "2024-10-08");
    private static final String ACTIVITY_ID = "66f916343add1e5e6bee69c0";
    private static final String ACTIVITY_DAILY_REWARD = "Camel-daily task reward";
    private static final String ACTIVITY_DAILY_HALL = "Camel-honor reward";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/camel_2024/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/camel_2024/?activityId=%s", ACTIVITY_ID);
    private static final Interner<String> stringPool = Interners.newWeakInterner();


    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;

    private String getDailyRankKey(String activityId, String dateStr){
        return String.format("dailyRank:%s:%s", activityId, dateStr);
    }

    private String getDailyDate(String activityId){
        return String.format("dailyDate:%s", activityId);
    }

    public CamelRankingVO camelConfig( String activityId, String uid) {

        CamelRankingVO vo = new CamelRankingVO();
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        // 每日相关完成全部用户用户
        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        // String currentDay = activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
        vo.setCurrentDay(currentDay);

        String dailyRankKey = this.getDailyRankKey(activityId, currentDay);
        vo.setDailySendNum(activityCommonRedis.getCommonZSetRankingScore(dailyRankKey, uid));

        List<CamelRankingVO.GloryConfig> allFinishList = new ArrayList<>();
        for (String dateStr : DAILY_DATE_LIST) {
            CamelRankingVO.GloryConfig gloryConfig = new CamelRankingVO.GloryConfig();
            gloryConfig.setDateStr(dateStr);
            List<OtherRankingListVO> finishRankList = new ArrayList<>();
            Map<String, Integer> rankingMap = activityCommonRedis.getOtherRankingMapByScoreAPage(this.getDailyRankKey(activityId, dateStr), LEVEL_NUM_LIST.get(0), 1000000, 0, 16);
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                OtherRankingListVO rankVO = new OtherRankingListVO();
                ActorData actorData = actorDao.getActorDataFromCache(entry.getKey());
                rankVO.setUid(entry.getKey());
                rankVO.setScore(entry.getValue());
                rankVO.setName(actorData.getName());
                rankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                rankVO.setRidData(actorData.getRidData());
                finishRankList.add(rankVO);
            }
            gloryConfig.setFinishRankList(finishRankList);
            allFinishList.add(gloryConfig);
        }
        vo.setAllFinishList(allFinishList);

        // 排行榜相关
        vo.setRankingList(getOtherRankingListVO(activityId, ActivityConstant.SEND_RANK, 10, activity.getRoundNum(), 0));
        CamelMyRankVO myRank = new CamelMyRankVO();
        ActorData actor = actorDao.getActorDataFromCache(uid);
        myRank.setName(actor.getName());
        myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        myRank.setSendingScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RANK, 0));
        myRank.setRank(activityOtherRedis.getOtherRank(activityId, uid, ActivityConstant.SEND_RANK, 0));
        vo.setMyRank(myRank);

        List<OtherRankingListVO> rankingList = vo.getRankingList();
        int top1Score = rankingList.size() > 0 ? rankingList.get(0).getScore() : 5000;
        int currentTime = DateHelper.getNowSeconds();
        int rDay = (currentTime - activity.getStartTime()) / 86400;
        int camelDay = 0;

        if(rDay < 0){
            camelDay = 1;
        }else{
            camelDay = rDay < 7 ? rDay + 1: 7;
        }

        int totalLength = top1Score * 7 / camelDay;
        vo.setTotalLength(totalLength);
        return vo;
    }

    public void handleSendGiftMsg(SendGiftData sendGiftData, OtherRankingActivityData activityData) {
        String activityId = activityData.get_id().toString();
        String acNameEn = activityData.getAcNameEn();
        String acNameAr = activityData.getAcNameAr();
        String fromUid = sendGiftData.getFrom_uid();

        synchronized (stringPool.intern(fromUid)) {
            int value = sendGiftData.getNumber() * sendGiftData.getAid_list().size();
            // 日榜增加数值
            String currentDay = DateHelper.ARABIAN.formatDateInDay();
            // String currentDay = activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
            String dailyRankKey = this.getDailyRankKey(activityId, currentDay);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(dailyRankKey, fromUid);
            while (value > 0){
                List<Integer> tempLevelNumList = new ArrayList<>(LEVEL_NUM_LIST);
                int currentLevelIndex = 0;
                if(tempLevelNumList.contains(currentNum)){
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                }else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if(upLevelIndex >= LEVEL_NUM_LIST.size()){
                    activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, fromUid, value);
                    value = 0;
                }else {
                    int upLevelNum = LEVEL_NUM_LIST.get(upLevelIndex);     // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if(value >= needUpNum){                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        value  = value - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, fromUid, needUpNum);
                        String resKey = LEVEL_KEY_LIST.get(upLevelIndex);
                        resourceKeyHandlerService.sendResourceData(fromUid, resKey, ACTIVITY_DAILY_REWARD, acNameAr, ACTIVITY_DAILY_REWARD, ACTIVITY_URL, "");
                    }else {
                        activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, fromUid, value);
                        value = 0;
                    }
                }
            }
        }
    }

    @Override
    public void dailyTaskRun(String dateStr) {
        try{
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if(activityData == null){
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }
            logger.info("dailyTaskRun CamelV2");

            if(dateStr == null){
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
                // dateStr = activityCommonRedis.getCommonStrValue(getDailyDate(ACTIVITY_ID));
            }

            String dailyRankKey = this.getDailyRankKey(ACTIVITY_ID, dateStr);
            Map<String, Integer> rankingMap = activityCommonRedis.getOtherRankingMapByScoreAPage(dailyRankKey, LEVEL_NUM_LIST.get(0), 1000000, 0, 16);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String uid = entry.getKey();

                if (rank == 1){
                    resourceKeyHandlerService.sendResourceData(uid, "camelUpLevelFirstFinish", ACTIVITY_DAILY_HALL, activityData.getAcNameAr(), ACTIVITY_DAILY_HALL, ACTIVITY_URL, "");
                }
                resourceKeyHandlerService.sendResourceData(uid, "camelUpLevelAllFinish", ACTIVITY_DAILY_HALL, activityData.getAcNameAr(), ACTIVITY_DAILY_HALL, ACTIVITY_URL, "");
                rank ++;
            }
        }catch (Exception e){
            logger.error("distribution Horse Racing Challenge error: {}", e.getMessage(), e);
        }
    }
}
