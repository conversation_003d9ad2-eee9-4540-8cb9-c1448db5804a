package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventReport;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.LuckyDrawHttpCode;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.TreasureDrawDTO;
import com.quhong.data.vo.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.*;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.TreasureRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.MatchUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
public class TreasureService {

    private static final Logger logger = LoggerFactory.getLogger(TreasureService.class);
    private static final int LIMIT_INIT_POOL = 30;
    private static final int ZERO_INIT_POOL = 0;
    private static final int ROUND_POOL_SIZE = 10000;
    private static final int TREASURE_MONEY_TYPE = 412;
    private static final int TREASURE_MONEY_ONE = 100;
    private static final int TREASURE_MONEY_TEN = 1000;
    private static final String GAME_NAME = "Treasure Chest";
    private static final String GAME_NAME_DESC = "Treasure Chest Fee";
    private static final String DEVICE_DRAW_100_DIAMOND_KEY = "l";
    private static final String ACCOUNT_DRAW_BUBBLE_KEY = "k";
    private static final String ROOM_MSG_CONTENT_EN = "Congratulations to #name# for obtaining #reward#. Open Treasure Chest >>";
    private static final String ROOM_MSG_CONTENT_AR = "ألف مبروك #name# للحصول على #reward#.افتح صندوق الكنز >>";
    private static final String TREASURE_URL = ServerConfig.isProduct() ? "https://static.youstar.live/treasure_chest/?screen=1" : "https://test2.qmovies.tv/treasure_chest/?screen=1";
    public static final String ACTION_EN = "View";
    public static final String ACTION_AR = "شاهد";
    public static final String MIC_TITLE_EN = "You got a treasure chest reward!";
    public static final String MIC_TITLE_AR = "لقد حصلت على مكافأة صندوق الكنز!";
    public static final String MIC_WEEK_DESC_EN = "Congratulations, you have a new MicFrame, go and wear it!";
    public static final String MIC_WEEK_DESC_AR = "تهانينا, لقد حصلت على المايك, اذهب وارتديه!";

    private static final List<Integer> DRAW_SIZE = Arrays.asList(1, 10);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private TreasureService treasureService;
    @Resource
    private TreasureDao treasureDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private TreasureRedis treasureRedis;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    protected DataCenterService dataCenterService;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private TreasureRecordDao treasureRecordDao;
    @Resource
    private TnDeviceAccountDao  tnDeviceAccountDao;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    protected OfficialDao officialDao;
    @Resource
    protected NoticeNewDao noticeNewDao;
    @Autowired
    protected RoomWebSender roomWebSender;

    @Cacheable(value = "getLastTreasureRecordDataFromCache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<TreasureRecordData> getLastTreasureRecordDataFromCache(){
        List<TreasureRecordData> records = treasureRecordDao.getLastRecords(20);
        logger.info("getLastTreasureRecordDataFromCache size={}", records.size());
        return records;
    }

    private String getCurrentSaturday(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy_MM_dd");//设置日期格式
        Calendar cld = Calendar.getInstance(TimeZone.getTimeZone("GMT+3:00"));
        cld.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        cld.set(Calendar.HOUR_OF_DAY, 24);
        cld.set(Calendar.MINUTE, 0);
        cld.set(Calendar.SECOND, 0);
        return df.format(cld.getTime());
    }

    private String getLastSaturday(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy_MM_dd");//设置日期格式
        Calendar cld = Calendar.getInstance(TimeZone.getTimeZone("GMT+3:00"));
        cld.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        cld.set(Calendar.HOUR_OF_DAY, 24);
        cld.set(Calendar.MINUTE, 0);
        cld.set(Calendar.SECOND, 0);
        cld.add(Calendar.DAY_OF_WEEK, -7);
        return df.format(cld.getTime());
    }

    private long getSaturdayEndTime(){
        Calendar cld = Calendar.getInstance(TimeZone.getTimeZone("GMT+3:00"));
        cld.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        cld.set(Calendar.HOUR_OF_DAY, 24);
        cld.set(Calendar.MINUTE, 0);
        cld.set(Calendar.SECOND, 0);
        return cld.getTimeInMillis() / 1000;
    }


    public TreasureConfigVO treasureConfig(String uid) {
        if (StringUtils.isEmpty(uid)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ActorData actor = actorDao.getActorData(uid);
        TreasureConfigVO vo = new TreasureConfigVO();
        vo.setMyBeans(MatchUtils.formatDevotes(actor.getBeans(), RoundingMode.FLOOR));
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));

        // 设置周抽奖次数
        vo.setCountDown(getSaturdayEndTime());
        vo.setMyTimes(treasureRedis.getTreasureSaturdayScore(uid, getCurrentSaturday()));
        vo.setMyLastWeekTimes(treasureRedis.getTreasureSaturdayScore(uid, getLastSaturday()));

        List<ActivityCommonConfig.TreasureWeeklyConfig> treasureWeeklyList = activityCommonConfig.getTreasureWeeklyList();
        List<TreasureConfigVO.WeeklyReward> weeklyRewardList = new ArrayList<>();
        for (ActivityCommonConfig.TreasureWeeklyConfig weeklyConfig : treasureWeeklyList) {
            TreasureConfigVO.WeeklyReward weeklyReward = new TreasureConfigVO.WeeklyReward();
            BeanUtils.copyProperties(weeklyConfig, weeklyReward);
            weeklyRewardList.add(weeklyReward);
        }
        vo.setWeeklyRewardList(weeklyRewardList);

        List<TreasureData> rewardConfigList = treasureDao.getTreasureDataFromCache();
        Map<String, TreasureData> rewardConfigMap = rewardConfigList.stream().collect(Collectors.toMap(TreasureData::getPoolKey, Function.identity()));

        // 设置最近10条记录
        List<TreasureConfigVO.DrawRecord> drawRecordList = new ArrayList<>();
        List<TreasureRecordData> records = treasureService.getLastTreasureRecordDataFromCache();
        for (TreasureRecordData data : records) {
            TreasureConfigVO.DrawRecord drawRecord = new TreasureConfigVO.DrawRecord();
            TreasureData treasureData = rewardConfigMap.get(data.getPoolKey());
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            if(treasureData != null && treasureData.getRoomMsg() == 1){
                drawRecord.setTitleEn(treasureData.getTitleEn());
                drawRecord.setTitleAr(treasureData.getTitleAr());
                drawRecord.setName(actorData.getName());
                drawRecord.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                drawRecordList.add(drawRecord);
            }
        }
        vo.setDrawRecordList(drawRecordList);

        // 设置奖励配置
        List<TreasureConfigVO.RewardConfig> rewardConfigVOList = new ArrayList<>();
        for(TreasureData rewardConfig: rewardConfigList){
            TreasureConfigVO.RewardConfig rewardConfigVO = new TreasureConfigVO.RewardConfig();
            rewardConfigVO.setTitleEn(rewardConfig.getTitleEn());
            rewardConfigVO.setTitleAr(rewardConfig.getTitleAr());
            rewardConfigVO.setIconEn(rewardConfig.getIconEn());
            rewardConfigVO.setIconAr(rewardConfig.getIconAr());
            rewardConfigVOList.add(rewardConfigVO);

        }

        vo.setRewardConfigList(rewardConfigVOList);

        return vo;
    }

    private Integer randomSampleByList(List<Integer> list){
        Random random = new Random();
        int n = random.nextInt(list.size());
        return list.get(n);
    }


    // 初始化奖池
    public void commonInitPoolSize(){
        List<String> poolList = new ArrayList<>();
        List<TreasureData> rewardConfigList = treasureDao.getTreasureDataFromCache();
        for (TreasureData treasureData : rewardConfigList) {
            int awardSize = treasureData.getRateNum();
            String poolKey = treasureData.getPoolKey();
            for (int i = 0; i < awardSize; i++) {
                poolList.add(poolKey);
            }
        }

        Collections.shuffle(poolList);
        treasureRedis.initPoolSize(poolList);

    }


    private void initPoolSize(){

        int poolSize = treasureRedis.getPoolSize();
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    commonInitPoolSize();
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            commonInitPoolSize();
        }
    }

    // 扣钻
    private void treasureDrawDiamondCost(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(TREASURE_MONEY_TYPE);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(GAME_NAME);
        moneyDetailReq.setDesc(GAME_NAME_DESC);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonH5Exception(LuckyDrawHttpCode.LUCKY_DRAW_BEANS_OUT);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
    }

    // 奖励下发
    private void treasureDrawChargeDiamonds(String uid, int changed) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(TREASURE_MONEY_TYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(GAME_NAME);
        moneyDetailReq.setDesc(GAME_NAME + " reward");
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    public void rewardDistribute(String uid, List<String> rewardResult, Map<String, TreasureData> rewardConfigMap){

        ActorData actor = actorDao.getActorDataFromCache(uid);
        for (String drawKey : rewardResult) {
            TreasureData rewardConfig = rewardConfigMap.get(drawKey);
            String rewardType = rewardConfig.getRewardType();
            int rewardNum = rewardConfig.getRewardNum() != null?rewardConfig.getRewardNum():0;
            int rewardTime = rewardConfig.getRewardTime() != null?rewardConfig.getRewardTime():0;
            switch (rewardType){
                case ResourceConstant.THANKS:
                    break;
                case ResourceConstant.HEART:
                    heartRecordDao.changeHeart(uid, rewardNum, GAME_NAME, GAME_NAME + " Reward");
                    break;
                case ResourceConstant.DIAMOND:
                    treasureDrawChargeDiamonds(uid, rewardNum);
                    break;
                default:
                    distributionService.sendRewardResource(uid, rewardConfig.getSourceId(),
                            ActivityRewardTypeEnum.getEnumByName(rewardConfig.getRewardType()), rewardTime, rewardNum, GAME_NAME, GAME_NAME, 0);
            }

            int curTime = DateHelper.getNowSeconds();
            TreasureRecordData recordData = new TreasureRecordData();
            recordData.setUid(uid);
            recordData.setPoolKey(drawKey);
            recordData.setCtime(curTime);
            treasureRecordDao.insert(recordData);

            // 推送公屏消息
            if(rewardConfig.getRoomMsg() > 0){
                // 非房间榜还需要发送公屏消息
                List<HighlightTextObject> list = new ArrayList<>();
                HighlightTextObject object = new HighlightTextObject();
                object.setText(actor.getName());
                object.setHighlightColor("#00FFCC");
                list.add(object);
                object = new HighlightTextObject();
                object.setText(rewardConfig.getTitleEn());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText(rewardConfig.getTitleAr());
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText("Open Treasure Chest >>");
                object.setHighlightColor("#FFE200");
                list.add(object);
                object = new HighlightTextObject();
                object.setText("افتح صندوق الكنز >>");
                object.setHighlightColor("#FFE200");
                list.add(object);

                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setUser_name(actor.getName());
                msg.setUser_head(actor.getHead());
                msg.setText(ROOM_MSG_CONTENT_EN.replace("#name#", actor.getName()).replace("#reward#", rewardConfig.getTitleEn()));
                msg.setText_ar(ROOM_MSG_CONTENT_AR.replace("#name#", actor.getName()).replace("#reward#", rewardConfig.getTitleAr()));
                msg.setHighlight_text(list);
                msg.setHighlight_text_ar(list);
                msg.setWeb_url(TREASURE_URL);
                msg.setWeb_type(1);
                msg.setWidth(375);
                msg.setHeight(560);
                marsMsgActivityService.asyncSendMsg("all", null, msg, false);
            }

        }
    }


    public TreasureDrawVO treasureDraw(TreasureDrawDTO dto) {
        String uid = dto.getUid();
        int drawType = dto.getDrawType();
        if(!DRAW_SIZE.contains(drawType)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        int changed = drawType == 1 ? TREASURE_MONEY_ONE : TREASURE_MONEY_TEN;

        // 抽奖逻辑
        List<TreasureData> rewardConfigList = treasureDao.getTreasureDataFromCache();
        Map<String, TreasureData> rewardConfigMap = rewardConfigList.stream().collect(Collectors.toMap(TreasureData::getPoolKey, Function.identity()));
        TreasureDrawVO vo = new TreasureDrawVO();
        List<TreasureDrawVO.RewardConfig> drawRewardList = new ArrayList<>();
        List<String> rewardResult = new ArrayList<>();

        synchronized (stringPool.intern(uid)) {

            // 扣除钻石数
            treasureDrawDiamondCost(uid, changed);

            // 统计消费排行榜
            String dailyNum = DateHelper.ARABIAN.getDayTableSuffix(new Date());
            treasureRedis.incrTreasureRankingScore(uid, changed, dailyNum);
            treasureRedis.incrTreasureSaturdayScore(uid, drawType, getCurrentSaturday());


            ActorData actor = actorDao.getActorData(uid);
            String userTnId = actor.getTn_id();
            if(StringUtils.isEmpty(userTnId)){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            // 设备检测
            TnDeviceAccountData tnAccountData = tnDeviceAccountDao.findTreasureDrawOne(userTnId);
            if(tnAccountData == null){
                TreasureData treasureData = rewardConfigMap.get(DEVICE_DRAW_100_DIAMOND_KEY);
                TreasureDrawVO.RewardConfig rewardConfig = new TreasureDrawVO.RewardConfig();
                rewardConfig.setIconEn(treasureData.getIconEn());
                rewardConfig.setIconAr(treasureData.getIconAr());
                rewardConfig.setTitleEn(treasureData.getTitleEn());
                rewardConfig.setTitleAr(treasureData.getTitleAr());
                drawRewardList.add(rewardConfig);
                rewardResult.add(DEVICE_DRAW_100_DIAMOND_KEY);

                tnAccountData = new TnDeviceAccountData();
                tnAccountData.setTnId(userTnId);
                int now = DateHelper.getNowSeconds();
                tnAccountData.setIsTreasureDraw(1);
                tnAccountData.setCtime(now);
                tnAccountData.setMtime(now);
                tnDeviceAccountDao.addOrUpdate(tnAccountData);
                drawType -= 1;
            }else {
                TreasureRecordData userRecordOne = treasureRecordDao.getUserRecordOne(uid);
                if(userRecordOne == null){
                    TreasureData treasureData = rewardConfigMap.get(ACCOUNT_DRAW_BUBBLE_KEY);
                    TreasureDrawVO.RewardConfig rewardConfig = new TreasureDrawVO.RewardConfig();
                    rewardConfig.setIconEn(treasureData.getIconEn());
                    rewardConfig.setIconAr(treasureData.getIconAr());
                    rewardConfig.setTitleEn(treasureData.getTitleEn());
                    rewardConfig.setTitleAr(treasureData.getTitleAr());
                    drawRewardList.add(rewardConfig);
                    rewardResult.add(ACCOUNT_DRAW_BUBBLE_KEY);
                    drawType -= 1;
                }
            }


            for (int i = 0; i < drawType; i++) {
                initPoolSize();
                String drawKey = treasureRedis.drawTreasureKey();
                if(StringUtils.isEmpty(drawKey)){
                    logger.info("treasureDra drawKey empty: uid:{}, drawKey:{}", uid, drawKey);
                    continue;
                }

                TreasureData treasureData = rewardConfigMap.get(drawKey);
                if (treasureData == null){
                    logger.info("treasureDra empty: uid:{}, treasureData:{}", uid, drawKey);
                    continue;
                }

                TreasureDrawVO.RewardConfig rewardConfig = new TreasureDrawVO.RewardConfig();
                rewardConfig.setIconEn(treasureData.getIconEn());
                rewardConfig.setIconAr(treasureData.getIconAr());
                rewardConfig.setTitleEn(treasureData.getTitleEn());
                rewardConfig.setTitleAr(treasureData.getTitleAr());
                drawRewardList.add(rewardConfig);
                rewardResult.add(drawKey);

            }

        }


        // 奖励下发及下发记录(异步)
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                rewardDistribute(uid, rewardResult, rewardConfigMap);
            }
        });

        vo.setRewardList(drawRewardList);
        return vo;
    }

    public TreasureRecordVO treasureDrawRecord(String uid, int page) {
        TreasureRecordVO vo = new TreasureRecordVO();
        int page_size = 20;
        if (page < 1) {
            page = 1;
        }

        List<TreasureRecordData> dataList = treasureRecordDao.getRecords(uid, page, page_size);
        List<TreasureData> rewardConfigList = treasureDao.getTreasureDataFromCache();
        Map<String, TreasureData> rewardConfigMap = rewardConfigList.stream().collect(Collectors.toMap(TreasureData::getPoolKey, Function.identity()));

        List<TreasureRecordVO.DrawRecord> recordList = new ArrayList<>();
        for (TreasureRecordData data : dataList) {
            TreasureRecordVO.DrawRecord drawRecord = new TreasureRecordVO.DrawRecord();
            TreasureData treasureData = rewardConfigMap.get(data.getPoolKey());
            if(treasureData != null){
                drawRecord.setTitleEn(treasureData.getTitleEn());
                drawRecord.setTitleAr(treasureData.getTitleAr());
                drawRecord.setIconEn(treasureData.getIconEn());
                drawRecord.setIconAr(treasureData.getIconAr());
                drawRecord.setCtime(data.getCtime());
                recordList.add(drawRecord);
            }
        }

        vo.setDrawRecordList(recordList);
        if (dataList.size() < page_size) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }


    protected TreasureMyRankVO getTreasureDailyMyRank(String uid, String dailyNum) {
        TreasureMyRankVO myRank = new TreasureMyRankVO();
        ActorData actor = actorDao.getActorDataFromCache(uid);
        myRank.setName(actor.getName());
        myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        myRank.setBeans(treasureRedis.getTreasureRankingScore(uid, dailyNum));
        myRank.setRank(treasureRedis.getTreasureRank(uid, dailyNum));
        return myRank;
    }


    // 排行榜
    public TreasureRankingVO treasureRanking(String uid) {
        TreasureRankingVO vo = new TreasureRankingVO();
        List<TreasureRankingVO.RewardConfig> rewardConfigList = new ArrayList<>();
        List<TreasureRankingListVO> treasureRankingList = new ArrayList<>();
        List<ActivityCommonConfig.TreasureRewardConfig> treasureDrawConfigList = activityCommonConfig.getTreasureRankList();

        // 设置倒计时
        vo.setRankingCountDown(DateHelper.ARABIAN.getTodayStartTime() / 1000 + 86400L);

        // 设置排行榜奖励信息
        for (ActivityCommonConfig.TreasureRewardConfig treasureRewardConfig: treasureDrawConfigList) {
            TreasureRankingVO.RewardConfig rewardConfig = new TreasureRankingVO.RewardConfig();
            rewardConfig.setTitleEn(treasureRewardConfig.getTitleEn());
            rewardConfig.setTitleAr(treasureRewardConfig.getTitleAr());
            rewardConfig.setIconEn(treasureRewardConfig.getIcon());
            rewardConfig.setIconAr(treasureRewardConfig.getIcon());
            rewardConfigList.add(rewardConfig);
        }
        vo.setRankAwardList(rewardConfigList);

        // 设置前一天的top3
        List<TreasureRankingVO.PreRanking> preRankingList = new ArrayList<>();
        String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date()).replace("-", "_");
        List<String> lastRankingList = treasureRedis.getTreasureRankingList(yesterday, 3);
        for (String rankUid : lastRankingList) {
            ActorData rankActor = actorDao.getActorDataFromCache(rankUid);
            TreasureRankingVO.PreRanking preRanking = new TreasureRankingVO.PreRanking();
            preRanking.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            preRanking.setUid(rankUid);
            preRankingList.add(preRanking);
        }
        vo.setPreRankingList(preRankingList);

        // 设置排行榜信息
        String dailyNum = DateHelper.ARABIAN.getDayTableSuffix(new Date());
        Map<String, Integer> rankingMap = treasureRedis.getTreasureRankingMap(dailyNum, 30);

        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            TreasureRankingListVO rankingListVO = new TreasureRankingListVO();
            rankingListVO.setScore("*****");
            rankingListVO.setUid(entry.getKey());

            ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
            rankingListVO.setName(rankActor.getName());
            rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));

            treasureRankingList.add(rankingListVO);
        }

        vo.setRankingList(treasureRankingList);
        vo.setMyRank(getTreasureDailyMyRank(uid, dailyNum));

        return vo;
    }


    public void commonOfficialMsg(String uid, String picture, int actionType, String actText, String title, String body) {
        OfficialData officialData = new OfficialData();
        officialData.setSubTitle("");
        officialData.setValid(1);
        officialData.setPicture(picture);
        officialData.setTo_uid(uid);
        officialData.setCtime(DateHelper.getNowSeconds());
        officialData.setNews_type(5);
        officialData.setAtype(actionType);
        officialData.setAct(actText);
        officialData.setTitle(title);
        officialData.setBody(body);
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            roomWebSender.sendPlayerWebMsg("", uid, uid, msg, false);
        }
    }

    // 开宝箱每日top3排行榜奖励下发
    public void treasureRankingTopReward(){

        try{
            String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date()).replace("-", "_");
            List<ActivityCommonConfig.TreasureRewardConfig> treasureDrawConfigList = activityCommonConfig.getTreasureRankList();
            List<String> lastRankingList = treasureRedis.getTreasureRankingList(yesterday, 3);
            for (int i = 0; i < lastRankingList.size(); i++) {
                String rankUid  = lastRankingList.get(i);
                ActivityCommonConfig.TreasureRewardConfig config = treasureDrawConfigList.get(i);
                distributionService.sendRewardResource(rankUid, config.getSourceId(),
                        ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), config.getRewardTime(), config.getRewardNum(), GAME_NAME, GAME_NAME, 0);

                ActorData actorData = actorDao.getActorDataFromCache(rankUid);
                int slang = actorData.getSlang();
                String actText = slang == SLangType.ARABIC ? ACTION_AR : ACTION_EN;
                String title = slang == SLangType.ARABIC ? MIC_TITLE_AR : MIC_TITLE_EN;
                String body = "";
                commonOfficialMsg(rankUid, config.getIcon(), 14, actText, title, body);

            }
        } catch (Exception e) {
            logger.error("treasureRankingTopReward error. {}", e.getMessage(), e);
        }
    }


    // 开宝箱周排行榜奖励下发
    public void treasureWeeklyTopReward() {
        try {
            String saturday = getLastSaturday();
            logger.info("treasureWeeklyTop info saturday: {}", saturday);
            List<ActivityCommonConfig.TreasureWeeklyConfig> treasureDrawConfigList = activityCommonConfig.getTreasureWeeklyList();
            Map<String, Integer>  weeklyMap = treasureRedis.getTreasureWeeklyMap(saturday);
            for (Map.Entry<String, Integer> entry : weeklyMap.entrySet()) {
                String rankUid = entry.getKey();
                int curScore = entry.getValue();
                ActivityCommonConfig.TreasureWeeklyConfig  weeklyConfig = null;

                logger.info("treasureWeeklyTop info rankUid: {}, curScore:{}", rankUid, curScore);
                if(curScore>= 800 && curScore < 1500){
                    weeklyConfig = treasureDrawConfigList.get(0);
                } else if (curScore >= 1500 && curScore < 2000) {
                    weeklyConfig = treasureDrawConfigList.get(1);
                } else if (curScore >= 2000) {
                    weeklyConfig = treasureDrawConfigList.get(2);
                }

                if(weeklyConfig != null){
                    distributionService.sendRewardResource(rankUid, weeklyConfig.getSourceId(),
                            ActivityRewardTypeEnum.getEnumByName(weeklyConfig.getRewardType()), weeklyConfig.getRewardTime(), weeklyConfig.getRewardNum(), GAME_NAME, GAME_NAME, 0);

                    ActorData actorData = actorDao.getActorDataFromCache(rankUid);
                    int slang = actorData.getSlang();
                    String actText = slang == SLangType.ARABIC ? ACTION_AR : ACTION_EN;
                    String title = slang == SLangType.ARABIC ? MIC_TITLE_AR : MIC_TITLE_EN;
                    String body = slang == SLangType.ARABIC ? MIC_WEEK_DESC_AR : MIC_WEEK_DESC_EN;;
                    commonOfficialMsg(rankUid, weeklyConfig.getIcon(), 0, actText, title, body);
                }
            }

        } catch (Exception e) {
            logger.error("treasureWeeklyTopReward error. {}", e.getMessage(), e);
        }
    }


}
