package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.clients.AWSUploader;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.ServiceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.SmashEggDTO;
import com.quhong.data.dto.UserFeedbackDTO;
import com.quhong.data.vo.FeedbackConfigVO;
import com.quhong.data.vo.SmashEggRecordVO;
import com.quhong.data.vo.UserFeedbackRecordVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mysql.dao.UserFeedbackDao;
import com.quhong.mysql.dao.UserFeedbackNumDao;
import com.quhong.mysql.data.SmashEggConfigData;
import com.quhong.mysql.data.SmashEggData;
import com.quhong.mysql.data.UserFeedbackData;
import com.quhong.mysql.data.UserFeedbackNumData;
import com.quhong.redis.UserFeedbackRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class UserFeedbackService {
    private static final Logger logger = LoggerFactory.getLogger(UserFeedbackService.class);
    private static final String ORIGIN_LOGIN = "login";
    private static final List<String> ORIGIN_LIST = Arrays.asList(ORIGIN_LOGIN, "settings", "invitation", "rateUs");
    private static List<String> CUSTOMER_ROOM_LIST = Arrays.asList("r:5ac220d61bad4898c89dad7e"); // 1806074

    @Resource
    private AWSUploader awsUploader;
    @Resource
    private UserFeedbackDao userFeedbackDao;
    @Resource
    private UserFeedbackNumDao userFeedbackNumDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private UserFeedbackRedis userFeedbackRedis;

    @PostConstruct
    public void postInit() {
        if (ServerConfig.isNotProduct()) {
            CUSTOMER_ROOM_LIST = Arrays.asList("r:5cb1c79c644f8e0022dff564"); // 1003881
        }
    }

    public FeedbackConfigVO feedbackConfig(String feedbackId) {
        if (StringUtils.isEmpty(feedbackId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        FeedbackConfigVO vo = new FeedbackConfigVO();
        List<ActivityCommonConfig.FeedbackConfig> feedbackConfigList = activityCommonConfig.getFeedbackConfigList();
        vo.setFeedbackConfigList(feedbackConfigList);
        vo.setUnread(userFeedbackDao.selectCount(feedbackId));
        userFeedbackRedis.removeFeedbackStatus(feedbackId);
        return vo;

    }


    private String createCdnUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        url = url.replaceAll("https://qhcf.s3.ap-southeast-1.amazonaws.com", "https://cdn3.qmovies.tv");
        return url;
    }

    public void userFeedback(UserFeedbackDTO dto, MultipartFile[] images) {
        String feedbackId = dto.getFeedbackId();
        String origin = dto.getOrigin();
        String contactInfo = dto.getContactInfo();
        String problemInfo = dto.getProblemInfo();
        if (StringUtils.isEmpty(feedbackId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (ObjectUtils.isEmpty(origin)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (StringUtils.isEmpty(problemInfo)) {
            throw new CommonH5Exception(ActivityHttpCode.FEED_BACK_PROBLEM_EMPTY);
        }

        if (ORIGIN_LOGIN.equals(origin) && StringUtils.isEmpty(contactInfo)) {
            throw new CommonH5Exception(ActivityHttpCode.FEED_BACK_CONTACT_ERROR);
        }

        if (images != null && images.length > 3) {
            throw new CommonH5Exception(ActivityHttpCode.IMAGE_UPLOAD_FAILED);
        }

        List<String> imgUrls = new ArrayList<>();
        if (images != null && images.length > 0) {
            for (MultipartFile file : images) {
                if (file == null || file.isEmpty()) {
                    continue;
                }
                String url = awsUploader.updateLoad(file, "feedback/");
                if (!StringUtils.isEmpty(url)) {
                    url = createCdnUrl(url);
                    imgUrls.add(url);
                } else {
                    logger.info("Image upload failed, please resubmit. DTO={}", JSONObject.toJSONString(dto));
                    throw new CommonH5Exception(ActivityHttpCode.IMAGE_UPLOAD_FAILED);
                }
            }
        }
        int currentTime = DateHelper.getNowSeconds();

        try {
            UserFeedbackData userFeedbackData = new UserFeedbackData();
            BeanUtils.copyProperties(dto, userFeedbackData);
            userFeedbackData.setImageInfo(JSONObject.toJSONString(imgUrls));
            userFeedbackData.setStatus(0);
            userFeedbackData.setMtime(currentTime);
            userFeedbackData.setCtime(currentTime);
            userFeedbackDao.insertOne(userFeedbackData);

            UserFeedbackNumData userFeedbackNumData = userFeedbackNumDao.selectOne(feedbackId);
            if (userFeedbackNumData == null) {
                userFeedbackNumData = new UserFeedbackNumData();
                userFeedbackNumData.setFeedbackId(feedbackId);
                userFeedbackNumData.setTotal(1);
                userFeedbackNumData.setMtime(currentTime);
                userFeedbackNumData.setCtime(currentTime);
                userFeedbackNumDao.insertOne(userFeedbackNumData);
            } else {
                userFeedbackNumData.setTotal(userFeedbackNumData.getTotal() + 1);
                userFeedbackNumData.setMtime(currentTime);
                userFeedbackNumDao.updateOne(userFeedbackNumData);
            }

        } catch (Exception e) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
    }


    public UserFeedbackRecordVO userFeedbackRecord(String feedbackId, int page) {
        if (StringUtils.isEmpty(feedbackId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        UserFeedbackRecordVO vo = new UserFeedbackRecordVO();
        page = page <= 0 ? 1 : page;
        int pageSize = 20;

        List<UserFeedbackData> dataList = userFeedbackDao.selectPageList(feedbackId, page, pageSize);
        List<UserFeedbackRecordVO.UserFeedbackRecord> records = new ArrayList<>();

        for (UserFeedbackData data : dataList) {
            UserFeedbackRecordVO.UserFeedbackRecord record = new UserFeedbackRecordVO.UserFeedbackRecord();
            BeanUtils.copyProperties(data, record);
            record.setImages(JSON.parseArray(data.getImageInfo(), String.class));
            records.add(record);
        }

        vo.setRecordList(records);
        if (dataList.size() < pageSize) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }

    public void feedbackSetUnread(UserFeedbackDTO dto) {
        int id = dto.getId();
        UserFeedbackData data = userFeedbackDao.selectById(id);
        if (data == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        data.setUnread(0);
        userFeedbackDao.updateOne(data);
    }

    public FeedbackConfigVO customerHome(String uid) {
        FeedbackConfigVO vo = new FeedbackConfigVO();
        FeedbackConfigVO.CustomerHomeVO customerHomeVO = new FeedbackConfigVO.CustomerHomeVO();
        customerHomeVO.setCustomerRoomId(CUSTOMER_ROOM_LIST.get(0));
        customerHomeVO.setCustomerUid("");
        vo.setCustomerHomeVO(customerHomeVO);
        return vo;
    }
}
