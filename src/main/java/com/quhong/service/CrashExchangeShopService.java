package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.CrashExchangeShopVO;
import com.quhong.data.vo.Eid2025VO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * crash兑换商店
 */
@Service
public class CrashExchangeShopService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(CrashExchangeShopService.class);
    private static final String ACTIVITY_TITLE_EN = "Crash Exchange Shop";
    public static final String ACTIVITY_ID = "6870e4878a8c7b207e5a3f24";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/crash_exchange_shop/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/crash_exchange_shop/?activityId=%s", ACTIVITY_ID);

    // 积分兑换比例：每100钻石=1积分
    private static final int DIAMONDS_PER_POINT = 100;

    //    private static final String CRASH_SHOP_EXCHANGE_KEY = "CrashShopExchangeKey";
    private static final String CRASH_SHOP_DRAW_KEY = "CrashExchangeShop";

    // 榜单前10名奖励
    private static final List<String> CRASH_SHOP_TOP_KEY_LIST = Arrays.asList("CrashExchangeShopTop1", "CrashExchangeShopTop2", "CrashExchangeShopTop3", "CrashExchangeShopTop4", "CrashExchangeShopTop5"
            , "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10", "CrashExchangeShopTop6-10");

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String eventExchangeTitle = "Crash Exchange Shop-exchange";
    private static final String eventDrawTitle = "Crash Exchange Shop-draw";
    private static final String eventRankingTitle = "Crash Exchange Shop-ranking";

    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int HISTORY_PAGE_SIZE = 20;

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(CommonMqTaskConstant.PLAY_CRASH_GAME);
    private static final String GAME_URL = ServerConfig.isProduct() ? "https://h5.bigluckygame.com/game_crash_ys/?appId=888888" : "https://api.springluckygame.com/game_crash/?appId=880088";

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;

    @PostConstruct
    public void init() {
        logger.info("CrashExchangeShopService init");
    }

    // Redis key 方法
    private String getLocalLockKey(String activityId, String uid) {
        return String.format("getLocalLockKey:%s:%s", activityId, uid);
    }

    // 积分key - zset存储用户积分
    private String getPointsKey(String activityId) {
        return String.format("crash:points:%s", activityId);
    }

    // 下注钻石排行榜key - zset存储用户下注总钻石数
    private String getBetRankingKey(String activityId) {
        return String.format("crash:bet:ranking:%s", activityId);
    }

    // 自动抽奖配置key - hash存储用户自动抽奖设置
    private String getAutoDrawConfigKey(String activityId) {
        return String.format("crash:auto:draw:%s", activityId);
    }

    // 抽奖流水记录key - list存储最近抽奖记录
    private String getRollRecordKey(String activityId) {
        return String.format("crash:roll:record:%s", activityId);
    }

    // 个人的抽奖历史记录key
    private String getListHistoryKey(String activityId, String uid) {
        return activityId + ":crash:exchange:draw:history2:" + uid;
    }

    public CrashExchangeShopVO crashExchangeShopConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);

        CrashExchangeShopVO vo = new CrashExchangeShopVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setGameUrl(GAME_URL);

        vo.setIsAutoDraw(activityCommonRedis.getCommonHashValue(getAutoDrawConfigKey(activityId), uid));

        // 获取当前积分
        int nowPoint = activityCommonRedis.getCommonZSetRankingScore(getPointsKey(activityId), uid);
        vo.setNowPoint(nowPoint);

        // 获取下注钻石排行榜
        List<OtherRankingListVO> gameRankingList = new ArrayList<>();
        OtherRankingListVO myRank = new OtherRankingListVO();
        String betRankingKey = getBetRankingKey(activityId);
        makeOtherRankingData(gameRankingList, myRank, betRankingKey, uid, 10, true);
        vo.setGameRankingList(gameRankingList);
        vo.setMyRank(myRank);

        // 获取最近抽奖记录
        List<String> recordJsonList = activityCommonRedis.getCommonListPageRecord(getRollRecordKey(activityId), 0, 5);
        List<CrashExchangeShopVO.RollRecordVO> rollRecordList = new ArrayList<>();
        for (String json : recordJsonList) {
            if (!StringUtils.isEmpty(json)) {
                try {
                    CrashExchangeShopVO.RollRecordVO rollRecord = JSON.parseObject(json, CrashExchangeShopVO.RollRecordVO.class);
                    if (rollRecord != null) {
                        rollRecord.setName(actorDao.getActorDataFromCache(rollRecord.getUid()).getName());
                        rollRecord.setCostValue(rollRecord.getCostValue() == null ? 0 : rollRecord.getCostValue());
                        rollRecordList.add(rollRecord);
                    }
                } catch (Exception e) {
                    logger.error("parse roll record failed, json:{}, error:{}", json, e.getMessage());
                }
            }
        }
        vo.setRollRecordList(rollRecordList);

        return vo;
    }

    public CrashExchangeShopVO.CrashExchangeDrawVO draw(String activityId, String uid, int drawNum, boolean checkDrawNum) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        if (drawNum != 1 && drawNum != 20 && checkDrawNum) {
            logger.info("drawNum param error. uid={} drawNum={}", uid, drawNum);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        CrashExchangeShopVO.CrashExchangeDrawVO vo = new CrashExchangeShopVO.CrashExchangeDrawVO();

        synchronized (stringPool.intern(getLocalLockKey(activityId, uid))) {
            String pointsKey = getPointsKey(activityId);
            int currentPoints = activityCommonRedis.getCommonZSetRankingScore(pointsKey, uid);

            if (currentPoints < drawNum) {
                logger.info("insufficient points. uid={} currentPoints={} drawNum={}", uid, currentPoints, drawNum);
                throw new CommonH5Exception(ActivityHttpCode.CRASH_DRAW_INSUFFICIENT_NUMBER_POINT);
            }

            // 扣除积分
            activityCommonRedis.incrCommonZSetRankingScoreSimple(pointsKey, uid, -drawNum);

            Map<String, CrashExchangeShopVO.ResourceMetaTmp> countMap = new HashMap<>();
            // 执行抽奖逻辑
            List<CrashExchangeShopVO.ResourceMetaTmp> resultList = new ArrayList<>();
            for (int i = 0; i < drawNum; i++) {
                ResourceKeyConfigData.ResourceMeta meta = drawOne(uid, CRASH_SHOP_DRAW_KEY, eventDrawTitle);
                if (meta != null) {
                    CrashExchangeShopVO.ResourceMetaTmp resultMeta = new CrashExchangeShopVO.ResourceMetaTmp();
                    BeanUtils.copyProperties(meta, resultMeta);
                    resultMeta.setAwardType(1); // 1表示盲盒
                    resultMeta.setCostPoint(1);
                    resultMeta.setCtime(DateHelper.getNowSeconds());
                    resultList.add(resultMeta);

                    if (!countMap.containsKey(meta.getMetaId())) {
                        resultMeta.setAwardNum(1);
                        countMap.put(meta.getMetaId(), resultMeta);
                    } else {
                        countMap.get(meta.getMetaId())
                                .setAwardNum(countMap.get(meta.getMetaId()).getAwardNum() + 1);
                    }
                }
            }
            doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 3, drawNum);
            vo.setDrawNum(drawNum);
            List<CrashExchangeShopVO.ResourceMetaTmp> popList = new ArrayList<>(countMap.values());
            vo.setMyDrawyList(popList);

            // 保存抽奖历史
            leftPushAllHistoryList(uid, resultList);

            // 添加抽奖流水记录
            addRollRecord(activityId, uid, resultList);
        }

        return vo;
    }

    public void exchange(String activityId, String uid, String metaId) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        if (StringUtils.isEmpty(metaId)) {
            logger.info("metaId is empty. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        // 获取兑换商品配置
        ResourceKeyConfigData keyConfigData = resourceKeyHandlerService.getConfigData(CRASH_SHOP_DRAW_KEY);
        ResourceKeyConfigData.ResourceMeta targetMeta = null;

        if (keyConfigData != null && !CollectionUtils.isEmpty(keyConfigData.getResourceMetaList())) {
            for (ResourceKeyConfigData.ResourceMeta meta : keyConfigData.getResourceMetaList()) {
                if (metaId.equals(meta.getMetaId())) {
                    targetMeta = meta;
                    break;
                }
            }
        }

        if (targetMeta == null) {
            logger.info("targetMeta is null. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(ActivityHttpCode.INCORRECT_INPUT_ID);
        }

        synchronized (stringPool.intern(getLocalLockKey(activityId, uid))) {

            String pointsKey = getPointsKey(activityId);
            int currentPoints = activityCommonRedis.getCommonZSetRankingScore(pointsKey, uid);
            int requiredPoints = targetMeta.getResourcePrice();

            if (currentPoints < requiredPoints) {
                throw new CommonH5Exception(ActivityHttpCode.CRASH_EXCHANGE_INSUFFICIENT_POINT);
            }

            // 扣除积分
            activityCommonRedis.incrCommonZSetRankingScoreSimple(pointsKey, uid, -requiredPoints);


            // 发放奖励
            resourceKeyHandlerService.sendOneResourceData(uid, targetMeta, 905,
                    eventExchangeTitle, eventExchangeTitle, eventExchangeTitle, ACTIVITY_URL, "", 1);

            // 保存兑换历史
            CrashExchangeShopVO.ResourceMetaTmp historyMeta = new CrashExchangeShopVO.ResourceMetaTmp();
            BeanUtils.copyProperties(targetMeta, historyMeta);
            historyMeta.setCostPoint(requiredPoints);
            historyMeta.setAwardType(2); // 2表示商店兑换
            historyMeta.setAwardNum(1);
            historyMeta.setCtime(DateHelper.getNowSeconds());

            leftPushAllHistoryList(uid, Arrays.asList(historyMeta));

            doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 2, requiredPoints);
        }
    }


    public void setAutoDraw(String activityId, String uid, int status) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
        if (status != 0 && status != 1) {
            logger.info("status param error. uid={} status={}", uid, status);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        String autoDrawKey = getAutoDrawConfigKey(activityId);
        int currentConfig = activityCommonRedis.getCommonHashValue(autoDrawKey, uid);
        if (currentConfig != status) {
            activityCommonRedis.setCommonHashNum(autoDrawKey, uid, status);
            logger.info("set auto draw. uid={} newConfig={}", uid, status);
            if(status == 1){
                // 自动抽奖：有多少积分就抽多少次
                int currentPoints = activityCommonRedis.getCommonZSetRankingScore(getPointsKey(activityId), uid);
                if (currentPoints > 0) {
                    try {
                        draw(activityId, uid, currentPoints, false);
                    } catch (Exception e) {
                        logger.error("auto draw failed, uid:{}, error:{}", uid, e.getMessage(), e);
                    }
                }
            }
        }


    }

    public CrashExchangeShopVO.CrashExchangeHistoryVO getHistoryListPageRecord(String activityId, String uid, int page) {
        CrashExchangeShopVO.CrashExchangeHistoryVO vo = new CrashExchangeShopVO.CrashExchangeHistoryVO();
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        String key = getListHistoryKey(activityId, uid);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        List<CrashExchangeShopVO.ResourceMetaTmp> resultList = new ArrayList<>();
        for (String json : jsonList) {
            CrashExchangeShopVO.ResourceMetaTmp rewardData = JSON.parseObject(json, CrashExchangeShopVO.ResourceMetaTmp.class);
            resultList.add(rewardData);
        }
        vo.setMyHistoryList(resultList);
        vo.setNextPage(resultList.size() < HISTORY_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();


        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }

        if (!checkAc(uid)) {
            return;
        }
        syncAddPoint(uid, data);
    }

    private void syncAddPoint(String uid, CommonMqTopicData data) {
        int betDiamonds = Math.abs(data.getValue()); // 取绝对值，因为下注是负数
        // 积分计算：每100钻=1积分，不足100的不累计
        int points = betDiamonds / DIAMONDS_PER_POINT;

        if (points > 0) {
            String pointsKey = getPointsKey(ACTIVITY_ID);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(pointsKey, uid, points);
            doReportSpecialItemsEvent(ACTIVITY_ID, uid, 1, 1, points);
            // 检查是否需要自动抽奖
            String autoDrawKey = getAutoDrawConfigKey(ACTIVITY_ID);
            int autoDrawConfig = activityCommonRedis.getCommonHashValue(autoDrawKey, uid);

            if (autoDrawConfig == 1) {
                // 自动抽奖：有多少积分就抽多少次
                int currentPoints = activityCommonRedis.getCommonZSetRankingScore(pointsKey, uid);
                if (currentPoints > 0) {
                    try {
                        draw(ACTIVITY_ID, uid, currentPoints, false);
                    } catch (Exception e) {
                        logger.error("auto draw failed, uid:{}, error:{}", uid, e.getMessage(), e);
                    }
                }
            }
        }

        // 更新下注钻石排行榜
        String betRankingKey = getBetRankingKey(ACTIVITY_ID);
        activityCommonRedis.incrCommonZSetRankingScoreSimple(betRankingKey, uid, betDiamonds);
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    // 保存历史记录
    private void leftPushAllHistoryList(String uid, List<CrashExchangeShopVO.ResourceMetaTmp> srcList) {
        String historyKey = getListHistoryKey(ACTIVITY_ID, uid);
        List<String> strList = new ArrayList<>();
        for (CrashExchangeShopVO.ResourceMetaTmp meta : srcList) {
            String json = JSONObject.toJSONString(meta);
            strList.add(json);
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(historyKey, strList, HISTORY_USER_MAX_SIZE);
    }

    // 添加抽奖流水记录
    private void addRollRecord(String activityId, String uid, List<CrashExchangeShopVO.ResourceMetaTmp> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        String rollRecordKey = getRollRecordKey(activityId);
        List<String> strList = new ArrayList<>();
        // 只记录有价值的奖励到流水
        for (CrashExchangeShopVO.ResourceMetaTmp meta : resultList) {
            if (meta.getResourceType() != BaseDataResourcesConstant.TYPE_OTHER) {
                // 只记录有价值的奖励到流水
                CrashExchangeShopVO.RollRecordVO rollRecord = new CrashExchangeShopVO.RollRecordVO();
                rollRecord.setUid(uid);
                rollRecord.setResourceIcon(meta.getResourceIcon());
                rollRecord.setCtime(DateHelper.getNowSeconds());
                rollRecord.setCostValue(meta.getResourcePrice());

                String json = JSONObject.toJSONString(rollRecord);
                strList.add(json);
                break; // 每次抽奖只记录一条流水
            }
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(rollRecordKey, strList, HISTORY_USER_MAX_SIZE);
    }

    // 下发榜单奖励
    public void distributionRanking(String activityId) {
        try {
            int length = CRASH_SHOP_TOP_KEY_LIST.size();
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getBetRankingKey(activityId), length);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = entry.getKey();
                String resKey = CRASH_SHOP_TOP_KEY_LIST.get(rank - 1);
                handleRes(aid, resKey, eventRankingTitle);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int source, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }


}
