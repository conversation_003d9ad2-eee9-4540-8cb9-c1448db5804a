package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.QuizActivityRecordEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.CheckpointQuizConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IMomentService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.QuestionAwardData;
import com.quhong.mysql.data.QuestionData;
import com.quhong.mysql.data.QuizActivityTemplateData;
import com.quhong.mysql.data.QuizCheckpointConfigData;
import com.quhong.redis.CheckpointQuizRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
@Service
public class CheckpointQuizService {

    private static final Logger logger = LoggerFactory.getLogger(CheckpointQuizService.class);

    private static final String SEND_GIFT_TITLE = "Quiz Activity Rewards";
    private static final String SEND_GIFT_DESC = "quiz activity rewards";

    private static final int QUIZ_REBIRTH_A_TYPE = 931;
    private static final String QUIZ_REBIRTH_TITLE = "Quiz Rebirth";
    private static final String QUIZ_REBIRTH_DESC = "Quiz Rebirth";

    private static final long MAX_TIME = 7 * 24 * 60 * 60 * 1000L;

    private static final String RECORD_TITLE = "Buy Coins";
    private static final String RECORD_DESC = "quiz activity buy coins";
    private static final int COINS_MONEY_TYPE = 180;

    private static final Map<Integer, CoinProductVO> COIN_PRODUCT_MAP = new HashMap<>();
    private static final List<String> QUIZ_TITLE_EN = Arrays.asList("Quiz Master", "Master of speed", "High IQ Ph.D.");
    private static final List<String> QUIZ_TITLE_AR = Arrays.asList("ماجستير في الاختبار", "ماجستير في السرعة", "دكتوراه ذكاء عالي.");
    private static final List<String> QUIZ_SHARE_DESC_EN = Arrays.asList(
            "#Chinese New Year Quiz Master#\n" + "It's amazing that I have achieved such a result\n" + "Do you have the confidence to surpass me? Come and challenge!",
            "#Chinese New Year Quiz Master#\n" + "It's really interesting, come and challenge\n" + "See how many points you can get?!",
            "#Chinese New Year Quiz Master#\n" + "WHAT? You haven't played this Quiz game yet, have you?\n" + "Don't miss such a fun Quiz game!"
    );
    private static final List<String> QUIZ_SHARE_DESC_AR = Arrays.asList(
            "#عريف مسابقة العام الجديد في الصين#\n" + "إنه لأمر مدهش أنني حققت مثل هذه النتيجة\n" + "هل لديك الثقة لتتفوق علي؟ تعال وتحدي!",
            "#عريف مسابقة العام الجديد في الصين#\n" + "إنها لعبة مثيرة للاهتمام حقًا تأتي وتتحدى\n" + "انظر كم عدد النقاط التي يمكنك الحصول عليها ؟!",
            "#عريف مسابقة العام الجديد في الصين#\n" + "ماذا؟ لم تلعب لعبة الاختبار هذه بعد ، أليس كذلك؟\n" + "لا تفوت مثل هذه اللعبة الممتعة مسابقة!"
    );

    @Resource
    private QuestionDao questionDao;
    @Resource
    private QuizActivityTemplateDao templateDao;
    @Resource
    private QuestionAwardDao questionAwardDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CheckpointQuizRedis checkpointQuizRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private QuizCheckpointConfigDao quizCheckpointConfigDao;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private DataCenterService dataCenterService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private IMomentService iMomentService;

    @PostConstruct
    public void postInit() {
        COIN_PRODUCT_MAP.put(1, new CoinProductVO(1, 5, 50, "https://cdn3.qmovies.tv/youstar/op_sys_1658145686_coins.png"));
        COIN_PRODUCT_MAP.put(2, new CoinProductVO(2, 10, 100, "https://cdn3.qmovies.tv/youstar/op_sys_1658145686_coins.png"));
        COIN_PRODUCT_MAP.put(3, new CoinProductVO(3, 100, 1000, "https://cdn3.qmovies.tv/youstar/op_sys_1658145686_coins.png"));
    }

    /**
     * 获取闯关答题配置
     */
    public CheckpointQuizVO checkpointQuiz(String uid, Integer activityId, QuizActivityTemplateData templateData, ActorData actorData) {
        CheckpointQuizVO vo = new CheckpointQuizVO();
        vo.setUserName(actorData.getName());
        vo.setUserHead(actorData.getHead());
        vo.setActivityId(templateData.getId());
        vo.setQuizType(templateData.getQuizType());
        // 模板配置
        CheckpointQuizConfigVO configVO = new CheckpointQuizConfigVO();
        BeanUtils.copyProperties(templateData, configVO);
        // 关卡配置
        List<CheckpointConfigVO> checkpointConfigVOList = new ArrayList<>();
        List<QuizCheckpointConfigData> quizCheckpointConfigList = getCheckpointConfigList(activityId);
        if (!CollectionUtils.isEmpty(quizCheckpointConfigList)) {
            for (QuizCheckpointConfigData checkpointConfig : quizCheckpointConfigList) {
                CheckpointConfigVO checkpointConfigVO = new CheckpointConfigVO();
                BeanUtils.copyProperties(checkpointConfig, checkpointConfigVO);
                // 关卡奖励配置
                List<QuestionAwardData> questionAwardList = getCheckpointAwardList(activityId, checkpointConfig.getCheckpointNo());
                checkpointConfigVO.setAwardList(CollectionUtils.isEmpty(questionAwardList) ? new ArrayList<>() : questionAwardList);
                checkpointConfigVOList.add(checkpointConfigVO);
            }
        }
        configVO.setCheckpointConfigList(checkpointConfigVOList);

        int joinNum = 0;
        if (configVO.getShowJoinNum() == 1) {
            joinNum = checkpointQuizRedis.getQuizJoinNum(activityId);
        }
        configVO.setAcBeginTime(dealTime(configVO.getAcBeginTime()));
        configVO.setAcEndTime(dealTime(configVO.getAcEndTime()));
        configVO.setJoinNum(joinNum);
        vo.setConfigVO(configVO);
        // 活动排行榜
        vo.setRankingList(getRankingList(activityId));
        vo.setMyGainRewards(getMyGainRewards(uid, activityId, null));
        return vo;
    }

    /**
     * 获取闯关页详情信息
     */
    public CheckpointPageVO getCheckpointPage(String uid, Integer activityId) {
        MongoActorData actorData = actorDao.findActorDataFromDB(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        CheckpointPageVO vo = new CheckpointPageVO();
        vo.setCoinBalance(actorData.getHeartGot());
        vo.setDiamondBalance(actorData.getBeans());
        List<CheckpointPageVO.CheckpointVO> checkpointVOList = new ArrayList<>();
        List<QuizCheckpointConfigData> checkpointList = getCheckpointConfigList(templateData.getId());
        int currentCheckpointNo = 1;
        if (!CollectionUtils.isEmpty(checkpointList)) {
            for (QuizCheckpointConfigData checkpointData : checkpointList) {
                CheckpointPageVO.CheckpointVO checkpointVO = new CheckpointPageVO.CheckpointVO();
                checkpointVO.setCheckpointNo(checkpointData.getCheckpointNo());
                checkpointVO.setAnswerCount(checkpointData.getTimesLimit());
                boolean isUnlock = checkpointQuizRedis.checkCheckpointUnlock(activityId, checkpointData.getCheckpointNo(), actorData.getTn_id());
                if (isUnlock) {
                    currentCheckpointNo ++;
                }
                checkpointVO.setUnlocked(isUnlock ? 1 : 0);
                if (currentCheckpointNo == checkpointData.getCheckpointNo()) {
                    int quizCount = checkpointQuizRedis.getCheckpointQuizCount(activityId, checkpointData.getCheckpointNo(), actorData.getTn_id(), checkpointData.getTimesType());
                    checkpointVO.setFreeAnswerCount(Math.max(checkpointData.getTimesLimit() - quizCount, 0));
                }
                checkpointVOList.add(checkpointVO);
            }
        }
        vo.setCurrentCheckpointNo(currentCheckpointNo);
        vo.setCheckpointList(checkpointVOList);
        List<CoinProductVO> coinProductList = new ArrayList<>();
        for (Map.Entry<Integer, CoinProductVO> entry : COIN_PRODUCT_MAP.entrySet()) {
            coinProductList.add(entry.getValue());
        }
        vo.setCoinProductList(coinProductList);
        return vo;
    }

    /**
     * 钻石兑换金币
     */
    public void buyCoins(String uid, int pid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        CoinProductVO coinProductVO = COIN_PRODUCT_MAP.get(pid);
        if (coinProductVO == null) {
            logger.error("buyCoins param error. uid={} pid={}", uid, pid);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        deductDiamonds(uid, COINS_MONEY_TYPE, coinProductVO.getDiamonds(), RECORD_TITLE, RECORD_DESC);
        boolean heartFlag = heartRecordDao.changeHeart(uid, coinProductVO.getCoins(), RECORD_TITLE, RECORD_DESC);
        if (!heartFlag) {
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 获取题目列表
     */
    public QuizQuestionVO getQuestionList(String uid, Integer activityId, Integer checkpointNo, Integer slang, Integer quizRebirth) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        if (checkpointQuizRedis.checkCheckpointUnlock(activityId, checkpointNo, actorData.getTn_id())) {
            logger.info("Unlocked, please do not answer again. activityId={} uid={} checkpoint={}", activityId, uid, checkpointNo);
            throw new CommonException(ActivityHttpCode.NOT_ANSWER_AGAIN);
        }
        // 获取用户当前所在关卡
        int currentCheckpointNo = checkpointQuizRedis.getCurrentCheckpointNo(activityId, uid);
        if (checkpointNo > currentCheckpointNo) {
            logger.info("This level has not been unlocked and cannot be answered. activityId={} uid={} checkpoint={}", activityId, uid, checkpointNo);
            throw new CommonException(ActivityHttpCode.NOT_BEEN_UNLOCKED);
        }
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizCheckpointConfigData checkpointConfigData = getCheckpointConfig(templateData.getId(), checkpointNo);
        if (checkpointConfigData == null) {
            logger.error("can not find checkpoint config data. activityId={} checkpointNo={}", activityId, checkpointNo);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        if (new Date().before(dealTime(templateData.getAcBeginTime()))) {
            // 活动还未开始
            logger.info("The quiz activity has not started yet. uid={} activityId={}", uid, activityId);
            throw new CommonException(ActivityHttpCode.QUIZ_ACTIVITY_HAS_NOT_STARTED);
        }
        if (new Date().after(dealTime(templateData.getAcEndTime()))) {
            // 活动已经结束
            logger.info("The quiz activity has ended uid={} activityId={}", uid, activityId);
            throw new CommonException(ActivityHttpCode.QUIZ_ACTIVITY_HAS_ENDED);
        }
        QuizQuestionVO vo = new QuizQuestionVO();
        if (quizRebirth == 1) {
            // 消耗金币或钻石复活
            quizRebirth(uid, checkpointConfigData);
        } else {
            int quizCount = checkpointQuizRedis.getCheckpointQuizCount(activityId, checkpointNo, actorData.getTn_id(), checkpointConfigData.getTimesType());
            if (checkpointConfigData.getTimesLimit() - quizCount <= 0) {
                // 关卡答题次数已用完
                logger.info("The number of answers has been used up. uid={} activityId={} checkpointNo={}", uid, activityId, checkpointNo);
                throw new CommonException(ActivityHttpCode.NUM_OF_ANSWERS_HAS_BEEN_USED_UP);
            }
        }
        // 获取题目
        vo.setList(getQuestionVOList(uid, activityId, checkpointNo, slang, checkpointConfigData, templateData));
        if (quizRebirth != 1) {
            checkpointQuizRedis.saveCheckpointQuizCount(activityId, checkpointNo, actorData.getTn_id(), checkpointConfigData.getTimesType());
        }
        // 开始答题数数埋点
        startQuizEventReport(uid, checkpointNo, quizRebirth, checkpointConfigData);
        return vo;
    }

    /**
     * 提交答题回答
     */
    public CheckpointQuizResultVO submitAnswer(QuizActivityDTO dto) {
        String uid = dto.getUid();
        Integer activityId = dto.getActivityId();
        Integer checkpointNo = dto.getCheckpointNo();
        if (activityId == null || StringUtils.isEmpty(uid) || checkpointNo == null || dto.getTime() == null || dto.getTime() == 0) {
            logger.error("submit answer param error. activityId={} uid={} time={}", activityId, uid, dto.getTime());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        int maxTime = 24 * 60 * 60 * 1000;
        if (dto.getTime() > maxTime) {
            logger.error("submit answer time param error. uid={} time={}", dto.getUid(), dto.getTime());
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        int time = Math.toIntExact(dto.getTime());
        MongoActorData actorData = actorDao.findActorDataFromDB(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find quiz activity template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizCheckpointConfigData checkpointConfigData = getCheckpointConfig(templateData.getId(), checkpointNo);
        if (checkpointConfigData == null) {
            logger.error("can not find checkpoint config data. activityId={} checkpointNo={}", activityId, checkpointNo);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        CheckpointQuizResultVO vo = new CheckpointQuizResultVO();
        Map<Integer, String> quizAnswer = dto.getQuizAnswer();
        List<QuestionData> questionList = getQuestionList(dto.getSlang() == SLangType.ARABIC ? checkpointConfigData.getArGid() : checkpointConfigData.getGid());
        Map<Integer, String> correctOptionMap;
        if (!CollectionUtils.isEmpty(questionList)) {
            correctOptionMap = questionList.stream().collect(Collectors.toMap(QuestionData::getId, QuestionData::getCorrectOption));
        } else {
            correctOptionMap = new HashMap<>(1);
        }
        if (CollectionUtils.isEmpty(quizAnswer)) {
            logger.info("quizAnswer is empty.");
            quizAnswer = new HashMap<>(1);
        }
        // 计数分数
        int correctCount = 0;
        for (Map.Entry<Integer, String> entry : quizAnswer.entrySet()) {
            if (!correctOptionMap.containsKey(entry.getKey())) {
                continue;
            }
            String correctOption = correctOptionMap.get(entry.getKey());
            if (Objects.equals(entry.getValue(), correctOption)) {
                correctCount ++;
            }
        }
        int score = correctCount == checkpointConfigData.getQuestionNum() ? 100 : 100 * correctCount / checkpointConfigData.getQuestionNum();
        if (checkPass(checkpointConfigData.getLimitType(), checkpointConfigData.getLimitScore(), checkpointConfigData.getQuestionNum(), score, correctCount)) {
            // 闯关成功
            passSuccess(vo, activityId, checkpointNo, uid, actorData.getTn_id(), score, time, dto.getSlang());
        } else {
            // 闯关失败
            vo.setPassSuccess(0);
            vo.setPassAll(0);
            vo.setAgainConditionType(checkpointConfigData.getAgainConditionType());
            vo.setAgainCostNum(checkpointConfigData.getAgainCostNum());
            if (checkpointConfigData.getAgainConditionType() == CheckpointQuizConstant.AGAIN_CONDITION_DIAMOND) {
                vo.setBalance(actorData.getBeans());
            } else {
                vo.setBalance(actorData.getHeartGot());
            }
            // 获取关卡剩余答题次数
            int quizCount = checkpointQuizRedis.getCheckpointQuizCount(activityId, checkpointNo, actorData.getTn_id(), checkpointConfigData.getTimesType());
            vo.setFreeAnswerCount(Math.max(checkpointConfigData.getTimesLimit() - quizCount, 0));
            vo.setMyGainRewards(getMyGainRewards(uid, activityId, null));
        }
        // 结束答题埋点
        endQuizEventReport(uid, checkpointNo, vo.getPassSuccess(), score, time);
        return vo;
    }

    /**
     * 分享答题活动到朋友圈
     */
    public void quizShare(String uid, Integer activityId, String img, Integer slang) {
        InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
        publishMomentDTO.setUid(uid);
        List<String> shareDesc = slang == SLangType.ARABIC ? QUIZ_SHARE_DESC_AR : QUIZ_SHARE_DESC_EN;
        Collections.shuffle(shareDesc);
        publishMomentDTO.setText(shareDesc.get(0));
        publishMomentDTO.setShow(1);
        InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
        imageDTO.setUrl(img);
        imageDTO.setWidth("1080");
        imageDTO.setHeight("1920");
        publishMomentDTO.setImgs(Collections.singletonList(imageDTO));
        publishMomentDTO.setActiveId(activityId + "");
        HttpResult<String> result = iMomentService.publish(publishMomentDTO);
        if (result.isError()) {
            logger.error("quiz share error. uid={} code={}", uid, result.getCode());
            if (result.getCode() == 20) {
                throw new CommonException(ActivityHttpCode.LEVEL_LIMIT);
            }
            throw new CommonException(ActivityHttpCode.SERVER_ERROR);
        }
    }

    public void quizRevive(String uid, Integer activityId, Integer checkpointNo) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizCheckpointConfigData checkpointConfigData = getCheckpointConfig(templateData.getId(), checkpointNo);
        if (checkpointConfigData == null) {
            logger.error("can not find checkpoint config data. activityId={} checkpointNo={}", activityId, checkpointNo);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        // 消耗金币或钻石复活
        quizRebirth(uid, checkpointConfigData);
        // 开始答题数数埋点
        startQuizEventReport(uid, checkpointNo, 1, checkpointConfigData);
    }

    /**
     * 获取题目
     */
    private List<QuizQuestionVO.QuestionVO> getQuestionVOList(String uid, Integer activityId, Integer checkpointNo, Integer slang, QuizCheckpointConfigData checkpointConfigData, QuizActivityTemplateData templateData) {
        List<QuizQuestionVO.QuestionVO> list = new ArrayList<>();
        int questionNum = checkpointQuizRedis.getQuestionNum(uid, activityId, checkpointNo, slang);
        List<QuestionData> questionList = getQuestionList(slang == SLangType.ARABIC ? checkpointConfigData.getArGid() : checkpointConfigData.getGid());
        if (CollectionUtils.isEmpty(questionList)) {
           return list;
        }
        List<String> strTidList = questionList.stream().map(k -> String.valueOf(k.getId())).collect(Collectors.toList());
        Map<Integer, QuestionData> questionMap = questionList.stream().collect(Collectors.toMap(QuestionData::getId, Function.identity()));
        if (questionNum < checkpointConfigData.getQuestionNum()) {
            if (CheckpointQuizConstant.RANDOM_ANSWER_TYPE == templateData.getAnswerType()) {
                Collections.shuffle(strTidList);
            }
            checkpointQuizRedis.saveQuestionInRedis(uid, activityId, checkpointNo, slang, strTidList);
        }
        List<Integer> tidList = checkpointQuizRedis.getQuestionFromRedis(uid, activityId, checkpointNo, checkpointConfigData.getQuestionNum(), slang);
        if (!CollectionUtils.isEmpty(tidList)) {
            if (tidList.size() < checkpointConfigData.getQuestionNum()) {
                for (String strTid : strTidList) {
                    int tid = Integer.parseInt(strTid);
                    if (!tidList.contains(tid)) {
                        tidList.add(tid);
                    }
                    if (tidList.size() >= checkpointConfigData.getQuestionNum()) {
                        break;
                    }
                }
            }
            for (Integer tid : tidList) {
                QuizQuestionVO.QuestionVO questionVO = new QuizQuestionVO.QuestionVO();
                QuestionData questionData = questionMap.get(tid);
                questionVO.setId(questionData.getId());
                questionVO.setPictureUrl(questionData.getPictureUrl());
                questionVO.setContent(questionData.getContent());
                Map<String, String> optionContentMap;
                String optionContent = questionData.getOptionContent();
                if (!StringUtils.isEmpty(optionContent)) {
                    optionContentMap = JSON.parseObject(optionContent, new TypeReference<HashMap<String, String>>() {
                    });
                } else {
                    optionContentMap = new HashMap<>(1);
                }
                questionVO.setOptionContent(optionContentMap);
                questionVO.setCorrectOption(questionData.getCorrectOption());
                list.add(questionVO);
            }
        }
        logger.info("tid.size={}", list.size());
        return list;
    }

    /**
     * 扣除钻石
     */
    private void deductDiamonds(String uid, int aType, int changed, String title, String desc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(aType);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonException(ActivityHttpCode.DIAMOND_NOT_ENOUGH);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 获取排行榜信息
     */
    private List<CheckpointQuizRankingVO> getRankingList(Integer activityId) {
        List<CheckpointQuizRankingVO> rankingVOList = new ArrayList<>();
        Map<String, Double> rankingMap = checkpointQuizRedis.getRankingMap(activityId);
        if (!CollectionUtils.isEmpty(rankingMap)) {
            int count = 1;
            for (Map.Entry<String, Double> entry : rankingMap.entrySet()) {
                CheckpointQuizRankingVO rankingVO = new CheckpointQuizRankingVO();
                ActorData actorData = actorDao.getActorDataFromCache(entry.getKey());
                rankingVO.setName(actorData.getName());
                rankingVO.setHead(actorData.getHead());
                rankingVO.setRank(count);
                rankingVO.setCheckpointNo(entry.getValue().intValue());
                rankingVO.setTime(new BigDecimal(getQuizTime(entry.getValue()) + "").divide(new BigDecimal(1000), 1, BigDecimal.ROUND_HALF_UP));
                rankingVOList.add(rankingVO);
                count++;
            }
        }
        return rankingVOList;
    }

    /**
     * 开始答题数数埋点
     */
    private void startQuizEventReport(String uid, Integer checkpointNo, Integer quizRebirth, QuizCheckpointConfigData checkpointConfigData) {
        QuizActivityRecordEvent event = new QuizActivityRecordEvent();
        event.setUid(uid);
        event.setQuiz_action(quizRebirth == 1 ? 3 : 1);
        event.setCtime(DateHelper.getNowSeconds());
        event.setQuiz_level(checkpointNo);
        event.setQuiz_rebirth_price(checkpointConfigData.getAgainCostNum());
        if (checkpointConfigData.getAgainCostNum() == 0) {
            event.setQuiz_rebirth_price_type(0);
        } else {
            event.setQuiz_rebirth_price_type(checkpointConfigData.getAgainConditionType() == 0 ? 1 : 2);
        }
        eventReport.track(new EventDTO(event));
    }

    /**
     * 答题复活扣除费用
     */
    private void quizRebirth(String uid, QuizCheckpointConfigData checkpointConfigData) {
        if (1 == checkpointConfigData.getAgainConditionType()) {
            boolean heartFlag = heartRecordDao.changeHeart(uid, -checkpointConfigData.getAgainCostNum(), QUIZ_REBIRTH_TITLE, QUIZ_REBIRTH_DESC);
            if (!heartFlag) {
                logger.info("user not enough heart. uid={} change={}", uid, -checkpointConfigData.getAgainCostNum());
                throw new CommonException(ActivityHttpCode.NOT_ENOUGH_COIN);
            }
        } else if (0 == checkpointConfigData.getAgainConditionType()) {
            deductDiamonds(uid, QUIZ_REBIRTH_A_TYPE, checkpointConfigData.getAgainCostNum(), QUIZ_REBIRTH_TITLE, QUIZ_REBIRTH_DESC);
        }
    }

    /**
     * 结束答题埋点
     */
    private void endQuizEventReport(String uid, int checkpointNo, int passSuccess, int score, int time) {
        QuizActivityRecordEvent event = new QuizActivityRecordEvent();
        event.setUid(uid);
        event.setQuiz_action(2);
        event.setCtime(DateHelper.getNowSeconds());
        event.setQuiz_score(score);
        event.setQuiz_time(time);
        event.setQuiz_level(checkpointNo);
        event.setQuit_end_reason(passSuccess == 1 ? 1 : 2);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 闯关成功
     */
    private void passSuccess(CheckpointQuizResultVO vo, Integer activityId, Integer checkpointNo, String uid, String tnId, Integer score, Integer time, Integer slang) {
        vo.setPassSuccess(1);
        int checkpointCount = quizCheckpointConfigDao.getCountByTemplateId(activityId);
        List<QuestionAwardData> allAwardList = getAllCheckpointAwardList(activityId);
        Map<Integer, List<QuestionAwardData>> awardMap;
        if (!CollectionUtils.isEmpty(allAwardList)) {
            awardMap = allAwardList.stream().collect(Collectors.groupingBy(QuestionAwardData::getCheckpointNo));
        } else {
            awardMap = new HashMap<>(1);
        }
        if (!checkpointQuizRedis.checkCheckpointUnlock(activityId, checkpointNo, tnId) && !CollectionUtils.isEmpty(awardMap.get(checkpointNo))) {
            // 奖励下发
            for (QuestionAwardData awardData : awardMap.get(checkpointNo)) {
                if (awardData.getRewardLimit() > score) {
                    continue;
                }
                QuizGainRewardVO rewardVO = awardDataToVo(awardData);
                // 结算奖励
                distributionService.sendRewardResource(uid, rewardVO.getSourceId(), ActivityRewardTypeEnum.getEnumByName(rewardVO.getRewardType()), rewardVO.getRewardTimes(),
                        rewardVO.getRewardNum(), SEND_GIFT_TITLE, SEND_GIFT_DESC, 0);
                checkpointQuizRedis.saveGainReward(uid, activityId, JSONObject.toJSONString(rewardVO));
            }
        }
        // 更新排名
        int quizSumTime = updateRanking(activityId, checkpointNo, uid, time);
        // 保存用户解锁关卡记录
        checkpointQuizRedis.saveCheckpointUnlockRecord(activityId, checkpointNo, tnId);
        if (checkpointCount == checkpointNo) {
            // 全部通关
            vo.setPassAll(1);
            vo.setCostTime(quizSumTime / 1000);
            vo.setRank(checkpointQuizRedis.getQuizRanking(uid, activityId));
            // 随机获取称谓
            List<String> titleList = slang == SLangType.ARABIC ? QUIZ_TITLE_AR : QUIZ_TITLE_EN;
            Collections.shuffle(titleList);
            vo.setTitle(titleList.get(0));
            vo.setMyGainRewards(getMyGainRewards(uid, activityId, null));
        } else {
            // 未全部通关
            vo.setPassAll(0);
            vo.setNextCheckpointNo(checkpointNo + 1);
            vo.setLeftRewards(getLeftRewards(checkpointNo, awardMap));
            vo.setMyGainRewards(getMyGainRewards(uid, activityId, checkpointNo));
        }
    }

    /**
     * 更新排行榜，同时返回答题总耗时
     */
    private int updateRanking(Integer activityId, Integer checkpointNo, String uid, int time) {
        double score = checkpointQuizRedis.getQuizRankingScore(uid, activityId);
        int sumTime = getQuizTime(score) + time;
        checkpointQuizRedis.saveQuizRanking(uid, activityId, getRankingScore(checkpointNo, sumTime));
        return sumTime;
    }

    /**
     * 计算排行榜分值
     */
    private Double getRankingScore(Integer checkpointNo, Integer time) {
        return new BigDecimal(checkpointNo + "." + (MAX_TIME - time)).doubleValue();
    }

    /**
     * 通关排行榜分值获得答题总耗时
     */
    private int getQuizTime(Double score) {
        if (score == 0) {
            return 0;
        }
        return (int) (MAX_TIME - (score - score.intValue()) * 1000000000);
    }

    /**
     * 获取剩余未解锁关卡奖励
     */
    private List<QuizGainRewardVO> getLeftRewards(int currentCheckpointNo, Map<Integer, List<QuestionAwardData>> awardMap) {
        List<QuizGainRewardVO> list = new ArrayList<>();
        for (Map.Entry<Integer, List<QuestionAwardData>> entry : awardMap.entrySet()) {
            if (entry.getKey() <= currentCheckpointNo) {
                continue;
            }
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                for (QuestionAwardData awardData : entry.getValue()) {
                    list.add(awardDataToVo(awardData));
                }
            }
        }
        return list;
    }

    private QuizGainRewardVO awardDataToVo(QuestionAwardData awardData) {
        QuizGainRewardVO rewardVO = new QuizGainRewardVO();
        BeanUtils.copyProperties(awardData, rewardVO);
        int showTimeOrNum = 0;
        if ("gift".equals(awardData.getRewardType()) || "diamond".equals(awardData.getRewardType()) || "coin".equals(awardData.getRewardType())) {
            showTimeOrNum = 1;
        } else if ("other".equals(awardData.getRewardType())) {
            if (awardData.getRewardTimes() == 0) {
                showTimeOrNum = 1;
            }
        }
        rewardVO.setShowTimeOrNum(showTimeOrNum);
        return rewardVO;
    }

    /**
     * 校验是否通关
     */
    private boolean checkPass(int limitType, Integer limitScore, Integer questionNum, Integer score, int correctCount) {
        logger.info("limitType={}, limitScore={}, questionNum={}, score={}, correctCount={}", limitType, limitScore, questionNum, score, correctCount);
        if (CheckpointQuizConstant.CLEARED_WITHOUT_ERRORS == limitType) {
            return correctCount >= questionNum;
        } else {
            return score >= limitScore;
        }
    }

    /**
     * 获取我获得的奖励
     */
    private List<QuizGainRewardVO> getMyGainRewards(String uid, int activityId, Integer checkpointNo) {
        Set<String> gainReward = checkpointQuizRedis.getGainReward(uid, activityId);
        List<QuizGainRewardVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(gainReward)) {
            for (String jsonValue : gainReward) {
                QuizGainRewardVO reward = JSONObject.parseObject(jsonValue, QuizGainRewardVO.class);
                if (checkpointNo == null || reward.getCheckpointNo().equals(checkpointNo)) {
                    list.add(reward);
                }
            }
        }
        return list;
    }

    private Date dealTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, 2);
        calendar.add(Calendar.MINUTE, 30);
        return calendar.getTime();
    }

    /**
     * 测试服清单个用户的答题记录
     */
    public void quizClear(String uid, Integer activityId) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        QuizActivityTemplateData templateData = templateDao.selectTemplate(activityId);
        if (templateData == null) {
            logger.error("can not find template data. activityId={}", activityId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        List<QuizCheckpointConfigData> list = getCheckpointConfigList(templateData.getId());
        if (!CollectionUtils.isEmpty(list)) {
            for (QuizCheckpointConfigData checkpointConfigData : list) {
                checkpointQuizRedis.removeQuizActivityRedis(uid, actorData.getTn_id(), activityId, checkpointConfigData.getCheckpointNo(), checkpointConfigData.getTimesType());
            }
        }
    }

    @Cacheable(value = "getCheckpointConfig", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public QuizCheckpointConfigData getCheckpointConfig(Integer activityId, Integer checkpointNo) {
        return quizCheckpointConfigDao.selectOne(activityId, checkpointNo);
    }

    @Cacheable(value = "getCheckpointConfigList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<QuizCheckpointConfigData> getCheckpointConfigList(Integer activityId) {
        return quizCheckpointConfigDao.selectByTemplateId(activityId);
    }

    @Cacheable(value = "getQuestionList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<QuestionData> getQuestionList(String gid) {
        return questionDao.selectList(gid);
    }

    @Cacheable(value = "getAllCheckpointAwardList", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<QuestionAwardData> getAllCheckpointAwardList(Integer activityId) {
        return questionAwardDao.selectCheckpointAwardList(activityId);
    }

    @Cacheable(value = "getCheckpointAwardList", key = "#p0 + #p1", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<QuestionAwardData> getCheckpointAwardList(Integer activityId, Integer checkpointNo) {
        return questionAwardDao.selectByAcIdAndCheckpointNo(activityId, checkpointNo);
    }
}
