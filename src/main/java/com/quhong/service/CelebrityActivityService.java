package com.quhong.service;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.CelebrityActivityVO;
import com.quhong.data.vo.CelebrityLikeVO;
import com.quhong.data.vo.CelebrityRankingVO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.CelebrityActivityDao;
import com.quhong.mongo.data.CelebrityActivity;
import com.quhong.redis.CelebrityActivityRedis;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Service
public class CelebrityActivityService {
    private static final Logger logger = LoggerFactory.getLogger(CelebrityActivityService.class);
    private static final int FREE_LIKE_COUNT = 3;
    private static int PER_LIKE_TO_SENT = 30;

    @Resource
    private CelebrityActivityDao celebrityActivityDao;
    @Resource
    private CelebrityActivityRedis celebrityActivityRedis;
    @Resource
    private CelebrityActivityService celebrityActivityService;

    @PostConstruct
    public void postInit() {
        if (ServerConfig.isNotProduct()) {
            // 测试服2特殊处理
            PER_LIKE_TO_SENT = 2;
        }
    }

    @Cacheable(value = "getCelebrityActivity", key = "#p0", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public CelebrityActivity getCelebrityActivity(String activityId) {
        if (StringUtils.isEmpty(activityId)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        CelebrityActivity data = celebrityActivityDao.findData(activityId);
        if (null == data) {
            logger.error("cannot find celebrity activity activityId={}", activityId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        return data;
    }

    public CelebrityActivityVO celebrityRanking(String uid, String activityId) {
        CelebrityActivity celebrityActivity = celebrityActivityService.getCelebrityActivity(activityId);
        CelebrityActivityVO vo = new CelebrityActivityVO();
        BeanUtils.copyProperties(celebrityActivity, vo);
        Set<String> likeMembers = celebrityActivityRedis.getLikeMembers(uid, activityId);
        vo.setFreeLikeCount(FREE_LIKE_COUNT - likeMembers.size());
        // 处理排行榜及点赞数量
        List<CelebrityRankingVO> rankingList = getRankingListVO(activityId, celebrityActivity.getActivityGiftList());
        for (CelebrityRankingVO rankingVO : rankingList) {
            rankingVO.setHasLike(likeMembers.contains(rankingVO.getGiftId() + ""));
            // 点赞数15s缓存
            rankingVO.setLikes(celebrityActivityService.getCelebrityLikes(activityId, rankingVO.getGiftId()));
        }
        vo.setRankingList(rankingList);
        return vo;
    }

    @Cacheable(value = "getCelebrityLikes", key = "T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')",
            cacheManager = CaffeineCacheConfig.EXPIRE_5S_AFTER_WRITE)
    public int getCelebrityLikes(String activityId, int giftId) {
        return celebrityActivityRedis.getCelebrityLikes(activityId, giftId);
    }

    /**
     * 获取礼物发送排行榜
     */
    private List<CelebrityRankingVO> getRankingListVO(String activityId, List<CelebrityActivity.ActivityGift> activityGiftList) {
        List<CelebrityRankingVO> rankingList = new ArrayList<>();
        Map<Integer, CelebrityActivity.ActivityGift> giftIdMap = CollectionUtil.listToKeyMap(activityGiftList, CelebrityActivity.ActivityGift::getGiftId);
        Map<String, Integer> rankingMap = celebrityActivityRedis.getRankingMap(activityId, activityGiftList.size());
        Map<Integer, Integer> sendBeanMap = celebrityActivityRedis.getSendBeanMap(activityId, activityGiftList.size());
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            CelebrityRankingVO rankingListVO = new CelebrityRankingVO();
            int giftId = Integer.parseInt(entry.getKey());
            rankingListVO.setGiftId(giftId);
            rankingListVO.setSent(entry.getValue());
            rankingListVO.setSentBeans(sendBeanMap.getOrDefault(giftId, 0));
            CelebrityActivity.ActivityGift activityGift = giftIdMap.get(Integer.valueOf(entry.getKey()));
            BeanUtils.copyProperties(activityGift, rankingListVO);
            rankingList.add(rankingListVO);
        }
        // 处理还未上榜的名人礼物
        if (rankingList.size() != activityGiftList.size()) {
            for (CelebrityActivity.ActivityGift activityGift : activityGiftList) {
                boolean notExist = true;
                for (CelebrityRankingVO celebrityRankingVO : rankingList) {
                    if (activityGift.getGiftId() == celebrityRankingVO.getGiftId()) {
                        notExist = false;
                        break;
                    }
                }
                if (notExist) {
                    CelebrityRankingVO rankingListVO = new CelebrityRankingVO();
                    rankingListVO.setGiftId(activityGift.getGiftId());
                    rankingListVO.setSent(0);
                    BeanUtils.copyProperties(activityGift, rankingListVO);
                    rankingList.add(rankingListVO);
                }
            }
        }
        return rankingList;
    }

    public CelebrityLikeVO celebrityLike(String uid, String activityId, String giftId) {
        int nowSeconds = DateHelper.getNowSeconds();
        CelebrityActivity activity = celebrityActivityService.getCelebrityActivity(activityId);
        if (activity.getStatus() == ActivityConstant.STATUS_DONE || activity.getStartTime() > nowSeconds || activity.getEndTime() < nowSeconds) {
            logger.error("celebrity activity has finish uid={} activityId={} giftId={}", uid, activityId, giftId);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        Set<String> likeMembers = celebrityActivityRedis.getLikeMembers(uid, activityId);
        if (likeMembers.contains(giftId)) {
            logger.error("celebrityLike has like uid={} activityId={} giftId={}", uid, activityId, giftId);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (likeMembers.size() >= 3) {
            logger.error("celebrityLike like limit uid={} activityId={} giftId={} count={}", uid, activityId, giftId, likeMembers.size());
            throw new CommonH5Exception(ActivityHttpCode.THREE_TIMES_LIMIT);
        }
        int celebrityLike = celebrityActivityRedis.addCelebrityLike(uid, activityId, giftId);
        // 每30个点赞当作发送1个礼物处理
        int sent = 0;
        if (0 != celebrityLike && celebrityLike % PER_LIKE_TO_SENT == 0) {
            sent = celebrityActivityRedis.incrCelebrityScore(activityId, uid, Integer.parseInt(giftId), 1);
        }
        return new CelebrityLikeVO(FREE_LIKE_COUNT - likeMembers.size() - 1, celebrityLike, sent);
    }
}
