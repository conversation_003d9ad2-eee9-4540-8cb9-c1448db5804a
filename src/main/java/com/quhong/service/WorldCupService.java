package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.TemporaryMoneyEvent;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.ActivityOtherRedis;
import com.quhong.redis.WorldCupRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
public class WorldCupService {

    private static final Logger logger = LoggerFactory.getLogger(WorldCupService.class);
    private static final String CHANGE_TITLE = "WorldCup Activity";
    private static final String CHANGE_DESC = "WorldCup activity Change";


    private static final String CHAMPION_BET = "Champion";
    private static final Integer CHAMP_START_TIME = 1668133561; // 冠军竞猜开始时间
    private static final Integer CHAMP_END_TIME = 1669366800;  // 冠军竞猜结束时间
    private static final Integer CHANGE_TIME = 1669367700;   // 兑换钻石开始时间

    // 数数event
    private static final String EVENT_MONEY_NANE = "counter";
    private static final Integer EVENT_TYPE_BUY = 1;  // 筹码购买
    private static final Integer EVENT_TYPE_CHANGE= 2;  // 筹码换钻石
    private static final Integer EVENT_TYPE_BET = 3;  // 投注
    private static final Integer EVENT_TYPE_AWARD = 4;  // 奖励
    private static final String EVENT_TITLE_COUNTER = "bean to counter";
    private static final String EVENT_TITLE_BEAN = "counter to bean";
    private static final String EVENT_TITLE_WIN_A = "team a win";
    private static final String EVENT_TITLE_WIN_B = "team b win";
    private static final String EVENT_TITLE_WIN_DRAW = "dram";
    private static final String EVENT_TITLE_WIN_CHAMPION = "champion country";
    private static final Interner<String> stringPool = Interners.newWeakInterner();


    private static final Map<String, Integer> CHANGE_MAP = new HashMap<>();
    static {
        CHANGE_MAP.put("1", 50);
        CHANGE_MAP.put("2", 300);
        CHANGE_MAP.put("3", 1000);
    }
    @Resource
    private WorldCupTeamDao worldCupTeamDao;
    @Resource
    private WorldCupMatchDao worldCupMatchDao;
    @Resource
    private WorldCupChipDao worldCupChipDao;
    @Resource
    private WorldCupBetDao worldCupBetDao;
    @Resource
    private WorldCupRedis worldCupRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    protected OtherActivityService otherActivityService;
    @Resource
    protected MongoRoomDao mongoRoomDao;
    @Resource
    protected ActorDao actorDao;
    @Resource
    protected ActivityOtherRedis activityOtherRedis;
    @Resource
    private GiftRecordDao giftRecordDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private MqSenderService mqSenderService;


    public WorldCupConfigVO worldCupConfig(String uid){
        WorldCupConfigVO vo = new WorldCupConfigVO();
        WorldCupChipData worldCupChipData = worldCupChipDao.selectOne(uid);
        vo.setMyChip(worldCupChipData == null ? 0 : worldCupChipData.getChipNum());

        // 设置是否可兑换钻石
        int curTime = DateHelper.getNowSeconds();
        vo.setChangeStatus(curTime >= activityCommonConfig.getBetChangeStartTime());

        List<WorldCupConfigVO.WorldCup> competeList = new ArrayList<>();
        List<WorldCupMatchData> worldCupList = worldCupMatchDao.getWorldCupFromCache();
        Map<Integer, WorldCupTeamData> teamMap = worldCupTeamDao.getTeamFromCache().stream().collect(Collectors.toMap(WorldCupTeamData::getId, Function.identity()));

        for(WorldCupMatchData matchData : worldCupList){

            WorldCupConfigVO.WorldCup worldCup = new WorldCupConfigVO.WorldCup();
            BeanUtils.copyProperties(matchData, worldCup);

            WorldCupTeamData firstTeam = teamMap.get(matchData.getFirstKey());
            WorldCupTeamData secondTeam = teamMap.get(matchData.getSecondKey());
            worldCup.setRecordId(matchData.getId());

            int firstChip = worldCupRedis.getMatchSize(matchData.getMatchType(), matchData.getFirstKey());
            int secondChip = worldCupRedis.getMatchSize(matchData.getMatchType(), matchData.getSecondKey());
            int drawChip = worldCupRedis.getMatchSize(matchData.getMatchType(), -1);
            // int firstSize = (int) ((secondChip + drawChip) * (1 - SERVICE_FREE));
            // int secondSize = (int) ((firstChip + drawChip) * (1 - SERVICE_FREE));
            // int drawSize = (int) ((firstChip + secondChip) * (1 - SERVICE_FREE));

            // int firstSize = (int) (firstChip * (1 - SERVICE_FREE));
            // int secondSize = (int) (secondChip * (1 - SERVICE_FREE));
            // int drawSize = (int) (drawChip * (1 - SERVICE_FREE));

            worldCup.setFirstChip(firstChip);
            worldCup.setFirstTeam(firstTeam.getTeamName());
            worldCup.setFirstTeamAr(firstTeam.getTeamNameAr());
            worldCup.setFirstTeamIcon(firstTeam.getTeamIcon());

            worldCup.setSecondChip(secondChip);
            worldCup.setSecondTeam(secondTeam.getTeamName());
            worldCup.setSecondTeamAr(secondTeam.getTeamNameAr());
            worldCup.setSecondTeamIcon(secondTeam.getTeamIcon());
            worldCup.setDrawChip(drawChip);
            competeList.add(worldCup);
        }

        vo.setWorldCupList(competeList);
        return vo;
    }

    // 事件上报
    public void worldCupEventReport(String uid, String matchType, int ship, int afterChange, int aType, String title, String teamName){
        TemporaryMoneyEvent moneyEvent = new TemporaryMoneyEvent();
        moneyEvent.setUid(uid);
        moneyEvent.setTemporary_money_name(EVENT_MONEY_NANE);
        moneyEvent.setTemporary_money_changed(ship);
        moneyEvent.setTemporary_money_after_changed(afterChange);
        moneyEvent.setTemporary_money_atype(aType);
        moneyEvent.setTemporary_money_title(title);
        moneyEvent.setMatch_id(matchType);
        moneyEvent.setCountry_code(teamName);
        moneyEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(moneyEvent));
    }

    public void worldCupBetRecord(String uid, String matchType, int ship, int teamKey){

        worldCupRedis.incMatchSize(matchType, teamKey, ship);
        WorldCupBetData betData = worldCupBetDao.selectOne(uid, matchType, teamKey);
        if(betData == null) {
            WorldCupBetData worldCupBetData = new WorldCupBetData();
            worldCupBetData.setUid(uid);
            worldCupBetData.setBetNum(ship);
            worldCupBetData.setTeamKey(teamKey);
            worldCupBetData.setMatchType(matchType);
            worldCupBetData.setWinNum(0);
            worldCupBetData.setWinType(-1);
            worldCupBetData.setMtime(DateHelper.getNowSeconds());
            worldCupBetData.setCtime(DateHelper.getNowSeconds());
            worldCupBetDao.insertOne(worldCupBetData);
        }else {
            betData.setBetNum(betData.getBetNum() + ship);
            betData.setMtime(DateHelper.getNowSeconds());
            worldCupBetDao.updateOne(betData);
        }

    }



    public void betWorldCup(String uid, String matchType, int ship, int teamKey){
        if (ship < 30){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        WorldCupChipData myChipData = worldCupChipDao.selectOne(uid);
        if (myChipData == null || myChipData.getChipNum() < ship){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_NUMS_ENOUGH);
        }

        WorldCupMatchData matchData = worldCupMatchDao.selectOneByMatchType(matchType);
        if (matchData == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        int curTime = DateHelper.getNowSeconds();
        if (curTime < matchData.getStartTime() || curTime > matchData.getEndTime()){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        List<Integer> teamKeyList = Arrays.asList(matchData.getFirstKey(), matchData.getSecondKey(), -1);
        if(!teamKeyList.contains(teamKey)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(uid + matchType + teamKey)) {
            // 扣筹码
            int afterChange = myChipData.getChipNum() - ship;
            myChipData.setChipNum(afterChange);
            int updateRecord = worldCupChipDao.updateOne(myChipData);

            if(updateRecord == 1){
                worldCupBetRecord(uid, matchType, ship, teamKey);
                // 写数数
                String recordTitle = teamKey == -1 ? EVENT_TITLE_WIN_DRAW : teamKey == matchData.getFirstKey() ? EVENT_TITLE_WIN_A : EVENT_TITLE_WIN_B;
                worldCupEventReport(uid, matchType, -ship, afterChange, EVENT_TYPE_BET, recordTitle, "");

            }else {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_NUMS_ENOUGH);
            }
        }

    }

    public WorldCupBetRecordVO worldCupBetRecordList(String uid, int page) {
        int pageSize = 10;

        List<WorldCupBetData> recordList = worldCupBetDao.selectListPage(uid, page, pageSize);
        WorldCupBetRecordVO betRecordVO = new WorldCupBetRecordVO();
        List<WorldCupBetRecordVO.BetRecord> betRecordList = new ArrayList<>();

        Map<Integer, WorldCupTeamData> teamMap = worldCupTeamDao.getTeamFromCache().stream().collect(Collectors.toMap(WorldCupTeamData::getId, Function.identity()));

        for(WorldCupBetData betData: recordList){
            WorldCupBetRecordVO.BetRecord betRecord = new WorldCupBetRecordVO.BetRecord();
            BeanUtils.copyProperties(betData, betRecord);
            if(betData.getTeamKey() == -1){
                betRecord.setTeamIcon("-1");
            }else {
                WorldCupTeamData teamData= teamMap.get(betData.getTeamKey());
                betRecord.setTeamIcon(teamData.getTeamIcon());
            }
            betRecordList.add(betRecord);
        }

        betRecordVO.setBetRecordList(betRecordList);
        if(recordList.size() < pageSize){
            betRecordVO.setNextUrl(-1);
        }else {
            betRecordVO.setNextUrl(page + 1);
        }
        return betRecordVO;
    }


    private void deductDiamonds(String uid, int changed){
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(902);
        moneyDetailReq.setChanged(-changed);
        moneyDetailReq.setTitle(CHANGE_TITLE);
        moneyDetailReq.setDesc(CHANGE_DESC);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);

        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
    }


    private void rechargeDiamonds(String uid, int changed){
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(902);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(CHANGE_TITLE);
        moneyDetailReq.setDesc(CHANGE_DESC);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }


    public WorldCupChangeVO worldCupChange(String uid, String changeType) {
        WorldCupChangeVO vo = new WorldCupChangeVO();

        Integer diamond = CHANGE_MAP.get(changeType);
        if (diamond == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        // 减钻
        deductDiamonds(uid, diamond);
        int afterChange = 0;


        WorldCupChipData chipData = worldCupChipDao.selectOne(uid);
        if (chipData == null) {
            chipData = new WorldCupChipData();
            chipData.setChipNum(diamond);
            chipData.setUid(uid);
            chipData.setCtime(DateHelper.getNowSeconds());
            worldCupChipDao.insertOne(chipData);
            afterChange = diamond;

        } else {
            afterChange = chipData.getChipNum() + diamond;
            chipData.setChipNum(afterChange);
            worldCupChipDao.updateOne(chipData);
        }

        worldCupEventReport(uid, "", diamond, afterChange, EVENT_TYPE_BUY, EVENT_TITLE_COUNTER, "");

        vo.setMyChip(chipData.getChipNum());
        return vo;

    }

    public void worldCupChangeDiamond(String uid) {

        int curTime = DateHelper.getNowSeconds();
        if (curTime < activityCommonConfig.getBetChangeStartTime()){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        WorldCupChipData chipData = worldCupChipDao.selectOne(uid);
        if (chipData == null || chipData.getChipNum() <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_NUMS_ENOUGH);
        }

        rechargeDiamonds(uid, chipData.getChipNum());
        chipData.setChipNum(0);
        worldCupChipDao.updateOne(chipData);
        worldCupEventReport(uid, "", -chipData.getChipNum(), 0, EVENT_TYPE_CHANGE, EVENT_TITLE_BEAN, "");
    }



    public WorldCupChampionVO worldCupChampion(String uid) {
        List<WorldCupTeamData> recordList = worldCupTeamDao.selectChampionList();
        WorldCupChampionVO championVO = new  WorldCupChampionVO();
        List<WorldCupChampionVO.TeamRecord> teamRecordList = new ArrayList<>();
        int totalChip = 0;

        for(WorldCupTeamData championData: recordList){
            WorldCupChampionVO.TeamRecord teamRecord = new WorldCupChampionVO.TeamRecord();
            BeanUtils.copyProperties(championData, teamRecord);

            teamRecord.setRecordId(championData.getId());

            int teamShip = worldCupRedis.getChampSize(championData.getId());
            // int teamSize = (int) (teamShip * (1 - SERVICE_FREE));
            teamRecord.setTeamChip(teamShip);
            totalChip += teamShip;
            teamRecordList.add(teamRecord);

        }

        championVO.setTotalChip(totalChip);
        championVO.setTeamList(teamRecordList);
        championVO.setStartTime(activityCommonConfig.getChampionStartTime());
        championVO.setEndTime(activityCommonConfig.getChampionEndTime());
        WorldCupChipData worldCupChipData = worldCupChipDao.selectOne(uid);
        championVO.setMyChip(worldCupChipData == null ? 0 : worldCupChipData.getChipNum());

        return championVO;
    }


    public void worldCupChampionBet(String uid, int ship, int teamKey){
        if (ship < 100){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        WorldCupChipData myChipData = worldCupChipDao.selectOne(uid);
        if (myChipData == null || myChipData.getChipNum() < ship){
            throw new CommonH5Exception(ActivityHttpCode.QUEEN_NUMS_ENOUGH);
        }

        int curTime = DateHelper.getNowSeconds();
        if (curTime < activityCommonConfig.getChampionStartTime() || curTime > activityCommonConfig.getChampionEndTime()){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        WorldCupTeamData champData = worldCupTeamDao.selectOne(teamKey);
        if (champData == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(uid + CHAMPION_BET + teamKey)) {
            int afterChange = myChipData.getChipNum() - ship;
            myChipData.setChipNum(afterChange);
            int updateNum = worldCupChipDao.updateOne(myChipData);
            if (updateNum == 1) {
                worldCupBetRecord(uid, CHAMPION_BET, ship, teamKey);
                // 写数数
                worldCupEventReport(uid, CHAMPION_BET, -ship, afterChange, EVENT_TYPE_BET, EVENT_TITLE_WIN_CHAMPION, champData.getTeamName());

            } else {
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_NUMS_ENOUGH);
            }
        }

    }



    public WorldCupRoomVO worldCupRoomInfo(String uid, String activityId, int rankType){

        if (StringUtils.isEmpty(uid)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        ActorData actor = actorDao.getActorDataFromCache(uid);

        if (activity == null || actor == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        WorldCupRoomVO roomVO = new WorldCupRoomVO();
        roomVO.setStartTime(activity.getStartTime());
        roomVO.setEndTime(activity.getEndTime());
        String roomId = "r:" + uid;
        roomVO.setEnroll(worldCupRedis.isWorldCupRoom(roomId));
        roomVO.setRoomConsume(activityOtherRedis.getOtherRankingScore(activityId, roomId, rankType, activity.getRoundNum()));

        WorldCupRoomVO.UserInfo userInfo = new WorldCupRoomVO.UserInfo();
        userInfo.setName(actor.getName());
        userInfo.setRid(actor.getRid());
        userInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
        roomVO.setUserInfo(userInfo);

        WorldCupRoomVO.RoomInfo roomInfo = new WorldCupRoomVO.RoomInfo();
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);

        roomInfo.setRid(actor.getRid());
        roomInfo.setName(roomData != null ? roomData.getName() : "");
        roomInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData != null ? roomData.getHead() : ""));
        roomVO.setRoomInfo(roomInfo);
        return roomVO;
    }

    public void countBeanRoomGift(String uid, String activityId, String roomId){
        long startTimeSec = DateHelper.DEFAULT.getTodayStartTime();
        int totalBeans = (int) giftRecordDao.calculateRoomGiftDevote(roomId, startTimeSec);
        logger.info("worldCupRoomAdd  uid: {}, roomId: {}, startTimeSec: {}, totalBeans:{}", uid, roomId, startTimeSec, totalBeans);
        if (totalBeans > 0){
            activityOtherRedis.incrOtherRankingScore(activityId, roomId, totalBeans, 3, 0);
        }
    }


    public void worldCupRoomAdd(String uid, String activityId){

        if (StringUtils.isEmpty(uid)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        if (activity == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        int curTime = DateHelper.getNowSeconds();
        if(curTime > activity.getEndTime()){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        String roomId = "r:" + uid;
        if(!worldCupRedis.isWorldCupRoom(roomId)){
            worldCupRedis.addWorldCupRoom(roomId);
            if(curTime >= activity.getStartTime() && curTime < activity.getEndTime()){
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        countBeanRoomGift(uid, activityId, roomId);
                    }
                });
            }
        }

    }

}
