package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.MomentHotPostVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.msg.room.UserCommonPopupMessage;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每周热帖
 */
@Service
public class MomentHotPostService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(MomentHotPostService.class);
    private static final String ACTIVITY_TITLE_EN = "Weekly Hot Posts";
    private static final String ACTIVITY_TITLE_AR = "المنشورات الرائجة الأسبوعية";
    private static final String ACTIVITY_DESC = "Weekly Hot Posts Reward";
    public static final String ACTIVITY_ID = "66a3c69c3dcd9febbbd45d0b";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/weekly_hot_posts/?activityId=%s", ACTIVITY_ID): String.format("https://test2.qmovies.tv/weekly_hot_posts/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/game/op_1720608261_7.png";
    private static final List<TaskConfigVO> FINISH_DAY_TASK_LIST = new ArrayList<>();
    private static final List<TaskConfigVO> DAILY_TASK_LIST = new ArrayList<>();
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final String TASK_LIKE_MOMENT = "like_moment";
    private static final String TASK_SEND_MOMENT_GIFT = "send_moment_gift";
    private static final String TASK_POST_MOMENT = "post_moment";
    private static final String TASK_RECEIVE_LIKE_MOMENT = "receive_like_moment";
    private static final String TASK_RECEIVE_GIFT_MOMENT = "receive_gift_moment";
    private static final String TASK_RECEIVE_GIFT_ID = "receive_gift_id";
    private static final String TASK_RECEIVE_GIFT_UID = "receive_gift_uid";
    private static final String TASK_FINISH_DAY = "finish_day_num";
    private static final List<String> LIKE_RANK_KEY = Arrays.asList("hotPostLikeTop1", "hotPostLikeTop2", "hotPostLikeTop3", "hotPostLikeTop4-5", "hotPostLikeTop6-10");
    private static final List<String> GIFT_RANK_KEY = Arrays.asList("hotPostReceiveTop1", "hotPostReceiveTop2", "hotPostReceiveTop3", "hotPostReceiveTop4-5", "hotPostReceiveTop6-10");

    // 10000、 2133501、2531569、1739435、2914899、1950844、2251044
    private static final List<String> NOT_SHOW_RANK_USER = Arrays.asList("5cdf784961d047a4adf44064", "5b0737441bad485d2389e679",
            "5b5defb5acc6cb0035b6cc81", "5aafad091bad4807d134c2d4", "5c75364e66dc630028e7323d", "5ade03ef1bad48aa748aa0ee", "5b184dc691f8d0001856a48c", "6708c45f75a0b006f392407b");

    static {
        DAILY_TASK_LIST.add(new TaskConfigVO(3, 0, "like_moment", "", "Like Moments", "الإعجاب باللحظات", null, null, null, null, "hotPostTaskAward1"));
        DAILY_TASK_LIST.add(new TaskConfigVO(2, 0, "send_moment_gift", "", "Send Gifts in Moments", "إرسال هدايا في اللحظات", null, null, null, null, "hotPostTaskAward2"));
        DAILY_TASK_LIST.add(new TaskConfigVO(1, 0, "post_moment", "", "Post to Moments", "نشر في اللحظات", null, null, null, null, "hotPostTaskAward3"));
        DAILY_TASK_LIST.add(new TaskConfigVO(3, 0, "receive_like_moment", "", "Received likes from moment", "تلقي الإعجابات من اللحظات", null, null, null, null, "hotPostTaskAward4"));
        DAILY_TASK_LIST.add(new TaskConfigVO(1, 0, "receive_gift_moment", "", "Received gifts from moment", "تلقي الهدايا من اللحظات", null, null, null, null, "hotPostTaskAward5"));

        FINISH_DAY_TASK_LIST.add(new TaskConfigVO(1, null, "1", null, null, null, null, null, null, null, "hotPostTaskDaily1"));
        FINISH_DAY_TASK_LIST.add(new TaskConfigVO(2, null, "2", null, null, null, null, null, null, null, "hotPostTaskDaily2"));
        FINISH_DAY_TASK_LIST.add(new TaskConfigVO(3, null, "3", null, null, null, null, null, null, null, "hotPostTaskDaily3"));
        FINISH_DAY_TASK_LIST.add(new TaskConfigVO(5, null, "5", null, null, null, null, null, null, null, "hotPostTaskDaily5"));
        FINISH_DAY_TASK_LIST.add(new TaskConfigVO(7, null, "7", null, null, null, null, null, null, null, "hotPostTaskDaily7"));
    }

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private GiftDao giftDao;

    private String getActivityDateKey(String activityId){
        return String.format("activityDate:%s", activityId);
    }

    // 每日任务及状态
    private String getDailyHashActivityId(String activityId, String uid, int roundNum, String dateStr){
        return String.format("hotPostDailyTask:%s:%s:%s:%s", activityId, uid, roundNum, dateStr);
    }

    private String getDailyHashGiftReceiveActivityId(String activityId, String uid, int roundNum, String dateStr){
        return String.format("hotPostDailyGift:%s:%s:%s:%s", activityId, uid, roundNum, dateStr);
    }

    private String getDailySetActivityId(String activityId, String taskKey, int roundNum, String dateStr){
        return String.format("hotPostSet:%s:%s:%s:%s", activityId, taskKey, roundNum, dateStr);
    }

    // 任务对象去重
    private String getDailyTaskSetActivityId(String activityId, String taskKey, String uid, int roundNum, String dateStr){
        return String.format("hotPostDailyTaskSet:%s:%s:%s:%s:%s", activityId, taskKey, uid, roundNum, dateStr);
    }

    private String getTaskStatusKey(String taskKey){
        return String.format("%s:status", taskKey);
    }

    // 周任务及状态
    private String getFinishHashActivityId(String activityId, String uid, int roundNum){
        return String.format("hotPost:%s:%s:%s", activityId, uid, roundNum);
    }

    private String getFinishDayStatusKey(String key){
        return String.format("%s:status", key);
    }

    private String getFinishDaySetActivityId(String activityId, String taskKey, int roundNum){
        return String.format("hotPostFinishDaySet:%s:%s:%s", activityId, taskKey, roundNum);
    }


    private String getWeeklyLikeRankKey(String activityId, Integer roundNum){
        return String.format("weeklyLikeRank:%s:%s", activityId, roundNum);
    }

    private String getWeeklyGiftRankKey(String activityId, Integer roundNum){
        return String.format("weeklyGiftRank:%s:%s", activityId, roundNum);
    }

    public List<MomentHotPostVO.HotPostRank> makePostRankingData(String rankKey) {
        List<MomentHotPostVO.HotPostRank> rankingList = new ArrayList<>();
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankKey, 50);
        Set<String> setData = new HashSet<>();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String uidMomentId = entry.getKey();
            String[] splitData = uidMomentId.split("-");
            String uid = splitData[0];
            String momentId = splitData[1];
            long score = entry.getValue();
            if(score <= 0){
                continue;
            }

            if(NOT_SHOW_RANK_USER.contains(uid)){
                continue;
            }

            MomentActivityData momentData = momentActivityDao.findMomentById(momentId);
            if(momentData == null){
                continue;
            }

            if(setData.contains(uid)){
                continue;
            }
            setData.add(uid);

            MomentHotPostVO.HotPostRank hotPostRank = new MomentHotPostVO.HotPostRank();
            ActorData rankActor = actorDao.getActorDataFromCache(uid);
            hotPostRank.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            hotPostRank.setUid(uid);
            hotPostRank.setMomentId(momentId);
            hotPostRank.setText(momentData.getText());
            hotPostRank.setQuote(momentData.getQuote());
            hotPostRank.setImgs(momentData.getImgs());
            hotPostRank.setC_time(momentData.getC_time());
            hotPostRank.setScore(entry.getValue());
            rankingList.add(hotPostRank);
            if(rankingList.size() >= 10){
                break;
            }
        }
        return rankingList;
    }



    public MomentHotPostVO momentHotPostConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        MomentHotPostVO vo = new MomentHotPostVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        int roundNum = activity.getRoundNum();
        int lastRoundNum = roundNum - 1;

        // 设置点赞榜、收礼榜
        String lastLikeRankKey = getWeeklyLikeRankKey(activityId, lastRoundNum);
        List<MomentHotPostVO.HotPostRank> lastLikeRankList = this.makePostRankingData(lastLikeRankKey);
        vo.setLikeLastWeeklyRank(lastLikeRankList.size() >= 3 ? lastLikeRankList.subList(0, 3) : lastLikeRankList);

        String likeRankKey = getWeeklyLikeRankKey(activityId, roundNum);
        List<MomentHotPostVO.HotPostRank> thisLikeRankList = this.makePostRankingData(likeRankKey);
        vo.setLikeThisWeeklyRank(thisLikeRankList);

        String lastGiftRankKey = getWeeklyGiftRankKey(activityId, lastRoundNum);
        List<MomentHotPostVO.HotPostRank> lastGiftRankList = this.makePostRankingData(lastGiftRankKey);
        vo.setGiftLastWeeklyRank(lastGiftRankList.size() >= 3 ? lastGiftRankList.subList(0, 3) : lastGiftRankList);

        String giftRankKey = getWeeklyGiftRankKey(activityId, roundNum);
        List<MomentHotPostVO.HotPostRank> thisGiftRankList = this.makePostRankingData(giftRankKey);
        vo.setGiftThisWeeklyRank(thisGiftRankList);

        // 设置每日任务
        String currentDate = DateHelper.ARABIAN.formatDateInDay();
        // String currentDate = activityCommonRedis.getCommonStrValue(getActivityDateKey(activityId));
        String dailyHashActivityId = getDailyHashActivityId(activityId, uid, roundNum, currentDate);
        Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        List<TaskConfigVO> dailyTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : DAILY_TASK_LIST) {
            TaskConfigVO taskConfigVO = new TaskConfigVO();
            BeanUtils.copyProperties(taskConfig, taskConfigVO);
            taskConfigVO.setCurrentProcess(Math.min(taskConfig.getTotalProcess(), userDailyMap.getOrDefault(taskConfig.getTaskKey(), 0)));
            if(TASK_RECEIVE_GIFT_MOMENT.equals(taskConfig.getTaskKey()) && taskConfigVO.getCurrentProcess() > 0){
                String receiveActivityId = getDailyHashGiftReceiveActivityId(activityId, uid, roundNum, currentDate);
                Map<String, String> receiveDailyMap = activityCommonRedis.getCommonHashAllMapStr(receiveActivityId);
                String giftFromUid = receiveDailyMap.get(TASK_RECEIVE_GIFT_UID);
                if(!StringUtils.isEmpty(giftFromUid)){
                    taskConfigVO.setTargetId(giftFromUid);
                    ActorData fromActorData = actorDao.getActorDataFromCache(giftFromUid);
                    taskConfigVO.setTargetId(giftFromUid);
                    taskConfigVO.setTargetIcon(ImageUrlGenerator.generateRoomUserUrl(fromActorData.getHead()));
                }
                String giftIdStr = receiveDailyMap.get(TASK_RECEIVE_GIFT_ID);
                if(!StringUtils.isEmpty(giftIdStr)){
                    GiftData giftData = giftDao.getGiftFromCache(Integer.parseInt(giftIdStr));
                    taskConfigVO.setGiftIcon(giftData.getGicon());
                }
            }
            dailyTaskList.add(taskConfigVO);
        }
        vo.setDailyTaskList(dailyTaskList);

        // 设置任务完成天数
        String finishDayKey = getFinishHashActivityId(activityId, uid, roundNum);
        Map<String, Integer> userFinishDayMap = activityCommonRedis.getCommonHashAll(finishDayKey);
        int finishDayNum = userFinishDayMap.getOrDefault(TASK_FINISH_DAY, 0);
        List<TaskConfigVO> finishDayList = new ArrayList<>();
        for (TaskConfigVO taskConfig : FINISH_DAY_TASK_LIST) {
            TaskConfigVO dateVO = new TaskConfigVO();
            BeanUtils.copyProperties(taskConfig, dateVO);
            int status = 0;
            if(finishDayNum >= taskConfig.getTotalProcess()){
                status = 1;
            }

            String finishStatusKey = getFinishDayStatusKey(taskConfig.getTaskKey());
            int finishStatus = userFinishDayMap.getOrDefault(finishStatusKey, 0);
            if(finishStatus > 0){
                status = 2;
            }
            dateVO.setStatus(status);
            finishDayList.add(dateVO);
        }
        vo.setFinishDayList(finishDayList);
        return vo;
    }

    public void momentHotPostGet(String activityId, String uid, String taskKey) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if(activityData == null){
            logger.error("not find activity config");
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        int roundNum = activityData.getRoundNum();
        int currentTime = DateHelper.getNowSeconds();
        if (currentTime < activityData.getStartTime() || currentTime > activityData.getEndTime()) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        synchronized (stringPool.intern(uid)) {
            Map<String, TaskConfigVO> taskConfigMap = FINISH_DAY_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = taskConfigMap.get(taskKey);
            if(taskConfig == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            String finishDayKey = getFinishHashActivityId(activityId, uid, activityData.getRoundNum());
            Map<String, Integer> userFinishDayMap = activityCommonRedis.getCommonHashAll(finishDayKey);
            int finishDayNum = userFinishDayMap.getOrDefault(TASK_FINISH_DAY, 0);
            if(finishDayNum < taskConfig.getTotalProcess()){
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if(actorData == null || StringUtils.isEmpty(actorData.getTn_id())){
                logger.info("handleDailyTask not user uid:{}", uid);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            String tnId = actorData.getTn_id();
            String finishDaySetKey = getFinishDaySetActivityId(activityId, taskKey, roundNum);
            if(activityCommonRedis.isCommonSetData(finishDaySetKey, tnId) > 0){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "تم استلام المكافآت لنفس الجهاز ولا يمكن استلامها مرة أخرى~");
            }

            String finishStatusKey = getFinishDayStatusKey(taskKey);
            int finishStatus = userFinishDayMap.getOrDefault(finishStatusKey, 0);
            if(finishStatus > 0){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }
            activityCommonRedis.setCommonHashNum(finishDayKey, finishStatusKey, 1);
            activityCommonRedis.addCommonSetData(finishDaySetKey, tnId);
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
        }
    }

    // 处理pk数据
    public void handleMomentHotTOPRankingSend(OtherRankingActivityData activity){
        try {
            String activityId = activity.get_id().toString();
            String acNameEn = activity.getAcNameEn();
            String acNameAr = activity.getAcNameAr();
            int roundNum = activity.getRoundNum();
            String likeRankKey = getWeeklyLikeRankKey(activityId, roundNum);
            List<MomentHotPostVO.HotPostRank> thisLikeRankList = this.makePostRankingData(likeRankKey);
            int likeRank = 1;
            for (MomentHotPostVO.HotPostRank hotPostRank : thisLikeRankList){
                String resourceKey = null;
                if(likeRank == 1){
                    resourceKey = LIKE_RANK_KEY.get(0);
                }else if(likeRank == 2){
                    resourceKey = LIKE_RANK_KEY.get(1);
                }else if(likeRank == 3){
                    resourceKey = LIKE_RANK_KEY.get(2);
                }else if(likeRank >= 4 && likeRank <= 5){
                    resourceKey = LIKE_RANK_KEY.get(3);
                }else {
                    resourceKey = LIKE_RANK_KEY.get(4);
                }
                resourceKeyHandlerService.sendResourceData(hotPostRank.getUid(), resourceKey, acNameEn, acNameAr, acNameEn, ACTIVITY_URL, "");
                likeRank += 1;
            }

            String giftRankKey = getWeeklyGiftRankKey(activityId, roundNum);
            List<MomentHotPostVO.HotPostRank> thisGiftRankList = this.makePostRankingData(giftRankKey);
            int giftRank = 1;
            for (MomentHotPostVO.HotPostRank hotPostRank : thisGiftRankList){
                String resourceKey = null;
                if(giftRank == 1){
                    resourceKey = GIFT_RANK_KEY.get(0);
                }else if(giftRank == 2){
                    resourceKey = GIFT_RANK_KEY.get(1);
                }else if(giftRank == 3){
                    resourceKey = GIFT_RANK_KEY.get(2);
                }else if(giftRank >= 4 && giftRank <= 5){
                    resourceKey = GIFT_RANK_KEY.get(3);
                }else {
                    resourceKey = GIFT_RANK_KEY.get(4);
                }
                resourceKeyHandlerService.sendResourceData(hotPostRank.getUid(), resourceKey, acNameEn, acNameAr, acNameEn, ACTIVITY_URL, "");
                giftRank += 1;
            }

        }catch (Exception e){
            logger.error("handleMomentHotTOPRankingSend e={}", e.getMessage(), e);
        }
    }



    private void handleDailyTask(String uid, String taskKey, int roundNum, int incNum, String jsonData, String fromUid, String handleId){
        Map<String, TaskConfigVO> taskConfigMap = DAILY_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
        TaskConfigVO taskConfig = taskConfigMap.get(taskKey);
        if(taskConfig == null){
            return;
        }

        synchronized (stringPool.intern(uid)) {
            String taskStatusKey = getTaskStatusKey(taskKey);
            String currentDate = DateHelper.ARABIAN.formatDateInDay();
            // String currentDate = activityCommonRedis.getCommonStrValue(getActivityDateKey(ACTIVITY_ID));

            String dailyHashActivityId = getDailyHashActivityId(ACTIVITY_ID, uid, roundNum, currentDate);
            Map<String, Integer> taskNumMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int taskStatus = taskNumMap.getOrDefault(taskStatusKey, 0);
            logger.info("handleDailyTask currentDate:{}, dailyHashActivityId:{}, taskNumMap:{}", currentDate, dailyHashActivityId, taskNumMap);
            if(taskStatus > 0){
                return;
            }


            ActorData actorData = actorDao.getActorDataFromCache(uid);

            if(actorData == null || StringUtils.isEmpty(actorData.getTn_id())){
                logger.info("handleDailyTask not user uid:{}", uid);
                return;
            }
            String tnId = actorData.getTn_id();


            if (TASK_LIKE_MOMENT.equals(taskKey) || TASK_RECEIVE_LIKE_MOMENT.equals(taskKey)){
                String setDataId = TASK_RECEIVE_LIKE_MOMENT.equals(taskKey) ? fromUid : handleId;
                String dailyTaskSetKey = getDailyTaskSetActivityId(ACTIVITY_ID, taskKey, uid, roundNum, currentDate);
                if(activityCommonRedis.isCommonSetData(dailyTaskSetKey, setDataId) > 0){
                    logger.info("dailyTaskSet not user uid:{}, setDataId:{}", uid, setDataId);
                    return;
                }
                activityCommonRedis.addCommonSetData(dailyTaskSetKey, setDataId);
            }

            int afterNum = activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskKey, incNum);
            int maxNum = taskConfig.getTotalProcess();
            if (afterNum >= maxNum){
                activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskStatusKey, 1);
                taskNumMap.put(taskStatusKey, 1);

                if (TASK_RECEIVE_LIKE_MOMENT.equals(taskKey) || TASK_RECEIVE_GIFT_MOMENT.equals(taskKey)){
                    UserCommonPopupMessage userMsg = new UserCommonPopupMessage();
                    userMsg.setUid(uid);
                    userMsg.setIcon(TASK_RECEIVE_LIKE_MOMENT.equals(taskKey)? "https://cdn3.qmovies.tv/gift/op_1694762495_coffee.png" : "https://cdn3.qmovies.tv/gift/op_sys_1659425236_cover_1_1.png");
                    userMsg.setTitleEn(ACTIVITY_TITLE_EN);
                    userMsg.setTitleAr(ACTIVITY_TITLE_AR);
                    userMsg.setTextEn("Activity rewards has already received.");
                    userMsg.setTextAr("المنشورات الرائجة الأسبوعية");
                    userMsg.setActionType(19);
                    userMsg.setActionValue(ACTIVITY_URL);
                    roomWebSender.sendPlayerWebMsg("", uid, uid, userMsg, false);
                }

                String dailySetActivityId = getDailySetActivityId(ACTIVITY_ID, taskKey, roundNum, currentDate);
                if(activityCommonRedis.isCommonSetData(dailySetActivityId, tnId) <= 0){
                    resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                    activityCommonRedis.addCommonSetData(dailySetActivityId, tnId);
                }
            }

            // 特殊展示礼物及发送者
            if(TASK_RECEIVE_GIFT_MOMENT.equals(taskKey)){
                String dailyHashGiftActivityId = getDailyHashGiftReceiveActivityId(ACTIVITY_ID, uid, roundNum, currentDate);
                activityCommonRedis.setCommonHashData(dailyHashGiftActivityId, TASK_RECEIVE_GIFT_ID, jsonData);
                activityCommonRedis.setCommonHashData(dailyHashGiftActivityId, TASK_RECEIVE_GIFT_UID, fromUid);
            }

            // 增加完成天数
            int finishNum = 0;
            Map<String, Integer> taskNumFinishMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            for (TaskConfigVO task : DAILY_TASK_LIST) {
                String taskFinishStatusKey = getTaskStatusKey(task.getTaskKey());
                int taskFinishStatus = taskNumFinishMap.getOrDefault(taskFinishStatusKey, 0);
                if(taskFinishStatus > 0){
                    finishNum += 1;
                }
            }

            logger.info("setFinishHashActivityId uid:{}, taskNumFinishMap:{} finishNum:{}, roundNum: {}", uid, taskNumFinishMap, finishNum, roundNum);

            if(finishNum >= DAILY_TASK_LIST.size()){
                String finishDayKey = getFinishHashActivityId(ACTIVITY_ID, uid, roundNum);
                activityCommonRedis.incCommonHashNum(finishDayKey, TASK_FINISH_DAY, 1);
            }
        }
    }


    public void handleMqMsg(CommonMqTopicData data) {
        String fromUid = data.getUid();
        String toUid = data.getAid();
        String handleId = data.getHandleId();
        String item = data.getItem();
        int currentTime = DateHelper.getNowSeconds();

        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if(activityData == null){
            logger.error("not find activity config");
            return;
        }

        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        if (currentTime < startTime || currentTime > endTime) {
            return;
        }

        // if (ServerConfig.isProduct() && !whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
        //     // 灰度测试
        //     return;
        // }

        int roundNum = activityData.getRoundNum();
        String jsonData = data.getJsonData();
        switch (item) {
            case CommonMqTaskConstant.LIKE_MOMENT:
                synchronized (stringPool.intern(toUid)) {
                    if(new ObjectId(handleId).getTimestamp() > startTime){
                        String mergeData = toUid + "-" + handleId;
                        String likeRankKey = getWeeklyLikeRankKey(ACTIVITY_ID, roundNum);
                        activityCommonRedis.incrCommonZSetRankingScore(likeRankKey, mergeData, 1);
                    }
                }

                if(!fromUid.equals(toUid)){
                    this.handleDailyTask(fromUid, TASK_LIKE_MOMENT, roundNum, 1, jsonData, null, handleId);
                    this.handleDailyTask(toUid, TASK_RECEIVE_LIKE_MOMENT, roundNum, 1, jsonData, fromUid, handleId);
                }
                break;
            case CommonMqTaskConstant.CANCEL_LIKE_MOMENT:
                synchronized (stringPool.intern(toUid)) {
                    if(new ObjectId(handleId).getTimestamp() > startTime){
                        String mergeData = toUid + "-" + handleId;
                        String likeRankKey = getWeeklyLikeRankKey(ACTIVITY_ID, roundNum);
                        activityCommonRedis.incrCommonZSetRankingScore(likeRankKey, mergeData, -1);
                    }
                }
                break;
            case CommonMqTaskConstant.SEND_MOMENT_GIFT:
                synchronized (stringPool.intern(toUid)) {
                    if(new ObjectId(handleId).getTimestamp() > startTime){
                        String mergeData = toUid + "-" + handleId;
                        String giftRankKey = getWeeklyGiftRankKey(ACTIVITY_ID, roundNum);
                        int totalBeans = data.getRemainValue();
                        activityCommonRedis.incrCommonZSetRankingScore(giftRankKey, mergeData, totalBeans);
                    }
                }
                this.handleDailyTask(fromUid, TASK_SEND_MOMENT_GIFT, roundNum, 1, jsonData, "", handleId);
                this.handleDailyTask(toUid, TASK_RECEIVE_GIFT_MOMENT, roundNum, 1, jsonData, fromUid, handleId);
                break;
            case CommonMqTaskConstant.POST_MOMENT:
                this.handleDailyTask(fromUid, TASK_POST_MOMENT, roundNum, 1, jsonData, null, handleId);
                break;
        }
    }

}
