package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.ActivityOtherRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MissContestService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(MissContestService.class);
    public static final String ACTIVITY_ID = "66eeaa2b5835362f3818ddd5";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/king_queen/?activityId=%s", ACTIVITY_ID): String.format("https://test2.qmovies.tv/king_queen/?activityId=%s", ACTIVITY_ID);
    public static final String BASE_BEAN_NAME = "baseBean";
    public static final List<String> QUEEN_ACTIVITY_ID_LIST = Arrays.asList("66ed402aa15760d6955a69c1", "66ed402aa15760d6955a69c2", "66ed402aa15760d6955a69c3", "66ed402aa15760d6955a69c4", "66ed402aa15760d6955a69c5", "66ed402aa15760d6955a69c6");
    public static final List<String> KING_ACTIVITY_ID_LIST = Arrays.asList("66ed402aa15760d6955a69b1", "66ed402aa15760d6955a69b2", "66ed402aa15760d6955a69b3", "66ed402aa15760d6955a69b4", "66ed402aa15760d6955a69b5", "66ed402aa15760d6955a69b6");
    private static final List<String> KING_ROUND_1_KEY_LIST = Arrays.asList("anniversaryV7Round1KingTop1", "anniversaryV7Round1KingTop2", "anniversaryV7Round1KingTop3",
            "anniversaryV7Round1KingTop4-10", "anniversaryV7Round1KingTop11-16", "anniversaryV7Round1KingTop17-24", "anniversaryV7Round1KingTop25-32", "anniversaryV7Round1KingTop33-64");
    private static final List<String> KING_ROUND_2_KEY_LIST = Arrays.asList("anniversaryV7Round2-4KingTop1", "anniversaryV7Round2KingTop2-16", "anniversaryV7Round2KingTop17-32", "anniversaryV7Round2-4KingLose");
    private static final List<String> KING_ROUND_3_KEY_LIST = Arrays.asList("anniversaryV7Round2-4KingTop1", "anniversaryV7Round3KingTop2-8", "anniversaryV7Round3KingTop9-16", "anniversaryV7Round2-4KingLose");
    private static final List<String> KING_ROUND_4_KEY_LIST = Arrays.asList("anniversaryV7Round2-4KingTop1", "anniversaryV7Round4KingTop2-4", "anniversaryV7Round4KingTop5-8", "anniversaryV7Round2-4KingLose");
    private static final List<String> KING_ROUND_5_KEY_LIST = Arrays.asList("anniversaryV7Round5KingTop1", "anniversaryV7Round5KingTop2-4", "anniversaryV7Round5KingTop5-8");
    private static final List<String> KING_ROUND_6_KEY_LIST = Arrays.asList("anniversaryV7Round6KingTop1", "anniversaryV7Round6KingTop2", "anniversaryV7Round6KingTop3", "anniversaryV7Round6KingTop4");

    private static final List<String> QUEEN_ROUND_1_KEY_LIST = Arrays.asList("anniversaryV7Round1QueenTop1", "anniversaryV7Round1QueenTop2", "anniversaryV7Round1QueenTop3",
            "anniversaryV7Round1QueenTop4-10", "anniversaryV7Round1QueenTop11-16", "anniversaryV7Round1QueenTop17-24", "anniversaryV7Round1QueenTop25-32", "anniversaryV7Round1QueenTop33-64");
    private static final List<String> QUEEN_ROUND_2_KEY_LIST = Arrays.asList("anniversaryV7Round2-4QueenTop1", "anniversaryV7Round2QueenTop2-16", "anniversaryV7Round2QueenTop17-32", "anniversaryV7Round2-4QueenLose");
    private static final List<String> QUEEN_ROUND_3_KEY_LIST = Arrays.asList("anniversaryV7Round2-4QueenTop1", "anniversaryV7Round3QueenTop2-8", "anniversaryV7Round3QueenTop9-16", "anniversaryV7Round2-4QueenLose");
    private static final List<String> QUEEN_ROUND_4_KEY_LIST = Arrays.asList("anniversaryV7Round2-4QueenTop1", "anniversaryV7Round4QueenTop2-4", "anniversaryV7Round4QueenTop5-8", "anniversaryV7Round2-4QueenLose");
    private static final List<String> QUEEN_ROUND_5_KEY_LIST = Arrays.asList("anniversaryV7Round5QueenTop1", "anniversaryV7Round5QueenTop2-4", "anniversaryV7Round5QueenTop5-8");
    private static final List<String> QUEEN_ROUND_6_KEY_LIST = Arrays.asList("anniversaryV7Round6QueenTop1", "anniversaryV7Round6QueenTop2", "anniversaryV7Round6QueenTop3", "anniversaryV7Round6QueenTop4");

    private static final TreeMap<Integer, Integer> KING_ROUND_1_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_2_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_3_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_4_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_5_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> KING_ROUND_6_MAP = new TreeMap<>();

    private static final TreeMap<Integer, Integer> QUEEN_ROUND_1_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_2_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_3_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_4_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_5_MAP = new TreeMap<>();
    private static final TreeMap<Integer, Integer> QUEEN_ROUND_6_MAP = new TreeMap<>();
    static {
        KING_ROUND_1_MAP.put(1, 0);
        KING_ROUND_1_MAP.put(2, 1);
        KING_ROUND_1_MAP.put(3, 2);
        KING_ROUND_1_MAP.put(10, 3); // 4-10 映射到同一个索引
        KING_ROUND_1_MAP.put(16, 4); // 11-16 映射到同一个索引
        KING_ROUND_1_MAP.put(24, 5); // 17-24 映射到同一个索引
        KING_ROUND_1_MAP.put(32, 6); // 25-32 映射到同一个索引
        KING_ROUND_1_MAP.put(64, 7); // 33-64 映射到同一个索引
        KING_ROUND_2_MAP.put(1, 0);
        KING_ROUND_2_MAP.put(16, 1);
        KING_ROUND_2_MAP.put(32, 2);
        KING_ROUND_2_MAP.put(64, 3);
        KING_ROUND_3_MAP.put(1, 0);
        KING_ROUND_3_MAP.put(8, 1);
        KING_ROUND_3_MAP.put(16, 2);
        KING_ROUND_3_MAP.put(64, 3);
        KING_ROUND_4_MAP.put(1, 0);
        KING_ROUND_4_MAP.put(4, 1);
        KING_ROUND_4_MAP.put(8, 2);
        KING_ROUND_4_MAP.put(64, 3);
        KING_ROUND_5_MAP.put(1, 0);
        KING_ROUND_5_MAP.put(4, 1);
        KING_ROUND_5_MAP.put(8, 2);
        KING_ROUND_6_MAP.put(1, 0);
        KING_ROUND_6_MAP.put(2, 1);
        KING_ROUND_6_MAP.put(3, 2);
        KING_ROUND_6_MAP.put(4, 3);

        QUEEN_ROUND_1_MAP.put(1, 0);
        QUEEN_ROUND_1_MAP.put(2, 1);
        QUEEN_ROUND_1_MAP.put(3, 2);
        QUEEN_ROUND_1_MAP.put(10, 3); // 4-10 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(16, 4); // 11-16 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(24, 5); // 17-24 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(32, 6); // 25-32 映射到同一个索引
        QUEEN_ROUND_1_MAP.put(64, 7); // 33-64 映射到同一个索引
        QUEEN_ROUND_2_MAP.put(1, 0);
        QUEEN_ROUND_2_MAP.put(16, 1);
        QUEEN_ROUND_2_MAP.put(32, 2);
        QUEEN_ROUND_2_MAP.put(64, 3);
        QUEEN_ROUND_3_MAP.put(1, 0);
        QUEEN_ROUND_3_MAP.put(8, 1);
        QUEEN_ROUND_3_MAP.put(16, 2);
        QUEEN_ROUND_3_MAP.put(64, 3);
        QUEEN_ROUND_4_MAP.put(1, 0);
        QUEEN_ROUND_4_MAP.put(4, 1);
        QUEEN_ROUND_4_MAP.put(8, 2);
        QUEEN_ROUND_4_MAP.put(64, 3);
        QUEEN_ROUND_5_MAP.put(1, 0);
        QUEEN_ROUND_5_MAP.put(4, 1);
        QUEEN_ROUND_5_MAP.put(8, 2);
        QUEEN_ROUND_6_MAP.put(1, 0);
        QUEEN_ROUND_6_MAP.put(2, 1);
        QUEEN_ROUND_6_MAP.put(3, 2);

    }

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ActivityOtherRedis activityOtherRedis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;

    private String getHashActivityId(String activityId){
        return String.format("missContestConfig:%s", activityId);
    }

    private void fillMiss1v1PKData(MissContestVO.MissPkVO vo, String uid, String missTopStrList, String topActivityId) {
        List<CompetitionVO> missTopVOList = new ArrayList<>();
        List<CompetitionVO> missRankVOList = new ArrayList<>();
        List<CompetitionVO> missSortVOList = new ArrayList<>();
        OtherMyRankVO topMyRank = new OtherMyRankVO();
        if (!StringUtils.isEmpty(missTopStrList)) {
            List<String> missTopList = JSONObject.parseObject(missTopStrList, List.class);
            List<Integer> rankWinList = new ArrayList<>();
            Map<Integer, String> rankWinMap = new HashMap<>();
            int defaultIndex = 0;
            for (String pkUser : missTopList) {
                String[] pkUserList = pkUser.split("-");
                CompetitionVO cpVO = new CompetitionVO();
                CompetitionVO cpRankVO = new CompetitionVO();

                String[] pk1UidRank = pkUserList[0].split("_");
                String pk1Uid = pk1UidRank[0];
                String pk1Rank = pk1UidRank[1];
                ActorData pk1User = actorDao.getActorDataFromCache(pk1Uid);
                cpVO.setPlayer1Uid(pk1Uid);
                cpVO.setPlayer1Name(pk1User.getName());
                cpVO.setPlayer1Head(ImageUrlGenerator.generateRoomUserUrl(pk1User.getHead()));
                cpVO.setPlayer1Bean(activityCommonRedis.getCommonZSetRankingScore(topActivityId, pk1Uid));
                cpVO.setPlayer1Rank(Integer.parseInt(pk1Rank));


                String[] pk2UidRank = pkUserList[1].split("_");
                String pk2Uid = pk2UidRank[0];
                String pk2Rank = pk2UidRank[1];
                ActorData pk2User = actorDao.getActorDataFromCache(pk2Uid);
                cpVO.setPlayer2Uid(pk2Uid);
                cpVO.setPlayer2Name(pk2User.getName());
                cpVO.setPlayer2Head(ImageUrlGenerator.generateRoomUserUrl(pk2User.getHead()));
                cpVO.setPlayer2Bean(activityCommonRedis.getCommonZSetRankingScore(topActivityId, pk2Uid));
                cpVO.setPlayer2Rank(Integer.parseInt(pk2Rank));
                missTopVOList.add(cpVO);
                BeanUtils.copyProperties(cpVO, cpRankVO);

                // 排名top
                int player1Rank = activityCommonRedis.getCommonZSetRank(topActivityId, pk1Uid);
                player1Rank = player1Rank == 0 ? 99 : player1Rank;
                cpRankVO.setPlayer1Rank(player1Rank);

                int player2Rank = activityCommonRedis.getCommonZSetRank(topActivityId, pk2Uid);
                player2Rank = player2Rank == 0 ? 99 : player2Rank;
                cpRankVO.setPlayer2Rank(player2Rank);
                missRankVOList.add(cpRankVO);

                int playerRank = Math.min(player1Rank, player2Rank);
                playerRank = playerRank >= 99 ? playerRank + defaultIndex : playerRank;
                defaultIndex += 1;


                // logger.info("fillMiss1v1PKData: topActivityId: {} pk1Uid:{} pk2Uid:{}, player1Rank:{}, player2Rank:{}", topActivityId, pk1Uid, pk2Uid, player1Rank, player2Rank);
                rankWinList.add(playerRank);
                rankWinMap.put(playerRank, String.format("%s-%s", pk1Uid, pk2Uid));


                if (uid.equals(pk1Uid)) {
                    topMyRank.setName(pk1User.getName());
                    topMyRank.setHead(ImageUrlGenerator.generateRoomUserUrl(pk1User.getHead()));
                    topMyRank.setScore(cpVO.getPlayer1Bean());
                } else if (uid.equals(pk2Uid)) {
                    topMyRank.setName(pk2User.getName());
                    topMyRank.setHead(ImageUrlGenerator.generateRoomUserUrl(pk2User.getHead()));
                    topMyRank.setScore(cpVO.getPlayer2Bean());
                }
            }


            Map<String, CompetitionVO> player1Map = missRankVOList.stream().collect(Collectors.toMap(CompetitionVO::getPlayer1Uid, Function.identity()));
            Map<String, CompetitionVO> player2Map = missRankVOList.stream().collect(Collectors.toMap(CompetitionVO::getPlayer2Uid, Function.identity()));
            Collections.sort(rankWinList);

            // logger.info("fillMiss1v1PKData: topActivityId: {} rankWinList:{} rankWinMap:{}", topActivityId, rankWinList, rankWinMap);
            for (Integer rank : rankWinList) {
                String pkUser = rankWinMap.get(rank);
                String[] pkUserList = pkUser.split("-");
                String pk1Uid = pkUserList[0];
                String pk2Uid = pkUserList[1];
                CompetitionVO cpVO = new CompetitionVO();
                CompetitionVO cpVO1 = player1Map.get(pk1Uid);
                if (cpVO1 != null) {
                    BeanUtils.copyProperties(cpVO1, cpVO);
                    missSortVOList.add(cpVO);
                    continue;
                }

                CompetitionVO cpVO2 = player2Map.get(pk2Uid);
                if (cpVO2 != null) {
                    BeanUtils.copyProperties(cpVO2, cpVO);
                    missSortVOList.add(cpVO);
                }
            }
        }

        vo.setPkRankList(missTopVOList);
        vo.setPkSortList(missSortVOList);
        vo.setPkMyRank(topMyRank);
    }


    public MissContestVO missContestConfig(String activityId, String uid, int missType) {
        // 总活动时间配置
        OtherRankingActivityData activityDataConfig = otherActivityService.getOtherRankingActivity(activityId);
        MissContestVO vo = new MissContestVO();
        vo.setStartTime(activityDataConfig.getStartTime());
        vo.setEndTime(activityDataConfig.getEndTime());

        // 每轮活动配置
        List<String> activityIdList = missType == 0 ? QUEEN_ACTIVITY_ID_LIST : KING_ACTIVITY_ID_LIST;
        List<OtherRankingActivityData> activityDataList = otherActivityService.getOtherRankingActivityList(activityIdList);
        Map<String, OtherRankingActivityData> activityDataMap = activityDataList.stream().collect(Collectors.toMap(k -> k.get_id().toString(), Function.identity()));
        Map<String, String> hashAllMap = activityCommonRedis.getCommonHashAllMapStr(this.getHashActivityId(activityId));
        List<MissContestVO.MissPkVO> missPkVOList = new ArrayList<>();

        for (String activityIdItem : activityIdList) {
            OtherRankingActivityData pkActivity= activityDataMap.get(activityIdItem);
            if(pkActivity == null){
                continue;
            }
            MissContestVO.MissPkVO missPkVO = new MissContestVO.MissPkVO();
            missPkVO.setPkActivityId(activityIdItem);
            missPkVO.setPkStartTime(pkActivity.getStartTime());
            missPkVO.setPkEndTime(pkActivity.getEndTime());
            if(activityIdItem.equals(activityIdList.get(0))){
                missPkVO.setTopRankList(getOtherRankingListVO(activityIdItem, ActivityConstant.RECEIVE_RANK, 64, 0, 0));
                missPkVO.setPkMyRank(getOtherMyRank(activityIdItem, uid, ActivityConstant.RECEIVE_RANK, 0, 0));
            }else if (activityIdItem.equals(activityIdList.get(5))){
                List<OtherRankingListVO> rankingList = new ArrayList<>();
                OtherMyRankVO myRank = new OtherMyRankVO();
                Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(activityIdItem, 10);
                int rank = 1;
                for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                    OtherRankingListVO rankingVO = new OtherRankingListVO();
                    String aid = entry.getKey();
                    ActorData rankActor = actorDao.getActorDataFromCache(aid);
                    rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                    rankingVO.setName(rankActor.getName());
                    rankingVO.setUid(aid);
                    rankingVO.setScore(entry.getValue());
                    rankingVO.setRank(rank);
                    rankingVO.setRidData(rankActor.getRidData());
                    if (aid.equals(uid)) {
                        BeanUtils.copyProperties(rankingVO, myRank);
                    }
                    rankingList.add(rankingVO);
                    rank += 1;
                }

                missPkVO.setTopRankList(rankingList);
                if (myRank.getRank() != 0) {
                    missPkVO.setPkMyRank(myRank);
                }
            }else{
                String missPkConfig = hashAllMap.get(activityIdItem);  // 获取pk对抗配置
                this.fillMiss1v1PKData(missPkVO, uid, missPkConfig, activityIdItem);
            }
            missPkVOList.add(missPkVO);
        }
        vo.setAllPkRankList(missPkVOList);
        return vo;
    }

    // 处理pk数据
    public void handleMissData(OtherRankingActivityData otherActivity){
        String acNameEn = otherActivity.getAcNameEn();
        if (acNameEn.contains("Round 1")){
            // top64
            this.handleMissRound1TopRankData(otherActivity);
        } else if (acNameEn.contains("Round 2")){
            // 64->32
            this.handleMissRoundPkData(otherActivity);
        }else if (acNameEn.contains("Round 3")){
            // 32->16
            this.handleMissRoundPkData(otherActivity);
        }else if (acNameEn.contains("Round 4")){
            // 16->8
            this.handleMissRoundPkData(otherActivity);
        }else if (acNameEn.contains("Round 5")){
            // 8->4
            this.handleMissRoundPkData(otherActivity);
        }else if (acNameEn.contains("Round 6")){
            // top4 rank
            this.handleLastRoundData(otherActivity);
        }
    }

    /**
     * 处理从榜单生成PK对-第一轮
     */
    public void handleMissRound1TopRankData(OtherRankingActivityData otherActivity) {
        try {
            String activityId = otherActivity.get_id().toString();
            TreeMap<Integer, Integer> ROUND_1_MAP;
            List<String> ROUND_1_KEY_LIST;
            if(QUEEN_ACTIVITY_ID_LIST.contains(activityId)){
                ROUND_1_MAP = QUEEN_ROUND_1_MAP;
                ROUND_1_KEY_LIST = QUEEN_ROUND_1_KEY_LIST;
            }else {
                ROUND_1_MAP = KING_ROUND_1_MAP;
                ROUND_1_KEY_LIST = KING_ROUND_1_KEY_LIST;
            }
            List<String> top64RankList = activityOtherRedis.getOtherRankingList(activityId, ActivityConstant.RECEIVE_RANK, 64, 0);

            String round2ActivityId = otherActivity.getNextActivityId();
            Map<String, Integer> uidRankMap = new HashMap<>();

            // TreeMap higherKey 方法 返回大于给定键的下一个键
            // TreeMap lowerKey 方法 返回小于给定键的下一个键
            int rank = 1;
            for (String aid : top64RankList) {
                Integer higherKey = ROUND_1_MAP.higherKey(rank - 1);
                int resKeyIndex =  higherKey != null ? ROUND_1_MAP.get(higherKey) : ROUND_1_MAP.lastEntry().getValue();
                String resourceKey = ROUND_1_KEY_LIST.get(resKeyIndex);
                ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
                for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceKeyConfigData.getResourceMetaList()) {
                    if(BASE_BEAN_NAME.equals(resourceMeta.getResourceNameEn())){
                        activityCommonRedis.incrCommonZSetRankingScore(round2ActivityId, aid, resourceMeta.getResourceNumber());
                    }
                }
                resourceKeyHandlerService.sendResourceData(aid, resourceKey, otherActivity.getAcNameEn(), otherActivity.getAcNameAr(), otherActivity.getAcNameEn(), ACTIVITY_URL, "");
                uidRankMap.put(aid, rank);
                rank += 1;
            }

            int listSize = top64RankList.size();
            if (listSize < 2) {
                return;
            }
            int endListIndex = listSize - 1;
            int halfLength = listSize / 2;
            List<String> missPkRankList = new ArrayList<>();
            List<String> missPkUidList = new ArrayList<>();
            for (int i = 0; i < halfLength; i++) {
                String player1 = top64RankList.get(i);
                String player2 = top64RankList.get(endListIndex - i);
                int player1Rank = uidRankMap.getOrDefault(player1, 0);
                int player2Rank = uidRankMap.getOrDefault(player2, 0);
                String playUser = String.format("%s_%s-%s_%s", player1, player1Rank, player2, player2Rank);
                missPkUidList.add(player1);
                missPkUidList.add(player2);
                missPkRankList.add(playUser);
            }
            activityCommonRedis.setCommonHashData(getHashActivityId(ACTIVITY_ID), round2ActivityId, JSONObject.toJSONString(missPkRankList));
            OtherRankingActivityData nextActivityData = otherRankingActivityDao.findData(round2ActivityId);
            Update update = new Update();
            update.set("rankUidList", missPkUidList);
            otherRankingActivityDao.updateData(nextActivityData, update);
        } catch (Exception e) {
            logger.error("handleMissRound1TopRankData error={}", e.getMessage(), e);
        }
    }

    public void handleMissRoundPkData(OtherRankingActivityData otherActivity) {
        try {
            String activityId = otherActivity.get_id().toString();
            TreeMap<Integer, Integer> ROUND_MAP = null;
            List<String> ROUND_KEY_LIST = null;
            String acNameEn = otherActivity.getAcNameEn();
            if(QUEEN_ACTIVITY_ID_LIST.contains(activityId)){
                if (acNameEn.contains("Round 2")){
                    ROUND_MAP = QUEEN_ROUND_2_MAP;
                    ROUND_KEY_LIST = QUEEN_ROUND_2_KEY_LIST;
                }else if (acNameEn.contains("Round 3")){
                    ROUND_MAP = QUEEN_ROUND_3_MAP;
                    ROUND_KEY_LIST = QUEEN_ROUND_3_KEY_LIST;
                }else if (acNameEn.contains("Round 4")){
                    ROUND_MAP = QUEEN_ROUND_4_MAP;
                    ROUND_KEY_LIST = QUEEN_ROUND_4_KEY_LIST;
                }else if (acNameEn.contains("Round 5")){
                    ROUND_MAP = QUEEN_ROUND_5_MAP;
                    ROUND_KEY_LIST = QUEEN_ROUND_5_KEY_LIST;
                }
            }else {
                if (acNameEn.contains("Round 2")){
                    ROUND_MAP = KING_ROUND_2_MAP;
                    ROUND_KEY_LIST = KING_ROUND_2_KEY_LIST;
                }else if (acNameEn.contains("Round 3")){
                    ROUND_MAP = KING_ROUND_3_MAP;
                    ROUND_KEY_LIST = KING_ROUND_3_KEY_LIST;
                }else if (acNameEn.contains("Round 4")){
                    ROUND_MAP = KING_ROUND_4_MAP;
                    ROUND_KEY_LIST = KING_ROUND_4_KEY_LIST;
                }else if (acNameEn.contains("Round 5")){
                    ROUND_MAP = KING_ROUND_5_MAP;
                    ROUND_KEY_LIST = KING_ROUND_5_KEY_LIST;
                }
            }

            if(ROUND_MAP == null){
                logger.error("handleMissRoundPkData not find ROUND_MAP activityId:{}", activityId);
                return;
            }

            Map<String, String> hashAllMap = activityCommonRedis.getCommonHashAllMapStr(getHashActivityId(ACTIVITY_ID));
            String roundStrPKData = hashAllMap.get(activityId);
            String nextActivityId = otherActivity.getNextActivityId();

            if (!StringUtils.isEmpty(roundStrPKData)) {
                List<String> missPKList = JSONObject.parseObject(roundStrPKData, List.class);
                int pkListSize = missPKList.size();
                if (pkListSize < 2) {
                    return;
                }

                List<Integer> rankWinList = new ArrayList<>();
                List<String> rankLoseList = new ArrayList<>();
                Map<Integer, String> rankWinMap = new HashMap<>();
                int defaultIndex = 1;
                for (String pkUser : missPKList) {
                    String[] pkUserList = pkUser.split("-");
                    String[] pk1UidRank = pkUserList[0].split("_");
                    String[] pk2UidRank = pkUserList[1].split("_");
                    String pk1Uid = pk1UidRank[0];
                    String pk2Uid = pk2UidRank[0];
                    int rank1 = activityCommonRedis.getCommonZSetRank(activityId, pk1Uid);
                    rank1 = rank1 == 0 ? 99 : rank1;
                    int rank2 = activityCommonRedis.getCommonZSetRank(activityId, pk2Uid);
                    rank2 = rank2 == 0 ? 99 : rank2;

                    String winUid = rank1 <= rank2 ? pk1Uid : pk2Uid;
                    rankLoseList.add(winUid.equals(pk1Uid) ? pk2Uid : pk1Uid);

                    int winRank = Math.min(rank1, rank2);
                    winRank = winRank == 99 ? winRank + defaultIndex : winRank;
                    defaultIndex += 1;

                    rankWinList.add(winRank);
                    rankWinMap.put(winRank, winUid);
                }
                Collections.sort(rankWinList);

                // 发放奖励
                int rank = 1;
                Map<String, Integer> uidRankMap = new HashMap<>();
                for (Integer winRank : rankWinList){
                    String winUid = rankWinMap.get(winRank);
                    if(winUid != null){
                        Integer higherKey = ROUND_MAP.higherKey(rank - 1);
                        int resKeyIndex =  higherKey != null ? ROUND_MAP.get(higherKey) : ROUND_MAP.lastEntry().getValue();
                        String resKey = ROUND_KEY_LIST.get(resKeyIndex);
                        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resKey);
                        if (resourceKeyConfigData != null){
                            for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceKeyConfigData.getResourceMetaList()) {
                                if(BASE_BEAN_NAME.equals(resourceMeta.getResourceNameEn())){
                                    activityCommonRedis.incrCommonZSetRankingScore(nextActivityId, winUid, resourceMeta.getResourceNumber());
                                }
                            }
                            resourceKeyHandlerService.sendResourceData(winUid, resKey, otherActivity.getAcNameEn(), otherActivity.getAcNameAr(), otherActivity.getAcNameEn(), ACTIVITY_URL, "");
                        }
                        uidRankMap.put(winUid, rank);
                    }
                    rank += 1;
                }

                for (String loseUid : rankLoseList) {
                    resourceKeyHandlerService.sendResourceData(loseUid, ROUND_KEY_LIST.get(ROUND_KEY_LIST.size() - 1), otherActivity.getAcNameEn(), otherActivity.getAcNameAr(), otherActivity.getAcNameEn(), ACTIVITY_URL, "");
                }

                List<String> pkRankStrList = new ArrayList<>();
                List<String> missPkUidList = new ArrayList<>();
                int listSize = rankWinList.size();
                int endListIndex = listSize - 1;
                int halfLength = listSize / 2;
                for (int i = 0; i < halfLength; i++) {
                    int winRank1 = rankWinList.get(i);
                    int winRank2 = rankWinList.get(endListIndex - i);
                    String win1Uid = rankWinMap.get(winRank1);
                    String win2Uid = rankWinMap.get(winRank2);
                    int player1Rank = uidRankMap.getOrDefault(win1Uid, 0);
                    int player2Rank = uidRankMap.getOrDefault(win2Uid, 0);
                    String playUser = String.format("%s_%s-%s_%s", win1Uid, player1Rank, win2Uid, player2Rank);
                    pkRankStrList.add(playUser);
                    missPkUidList.add(win1Uid);
                    missPkUidList.add(win2Uid);
                }
                activityCommonRedis.setCommonHashData(this.getHashActivityId(ACTIVITY_ID), nextActivityId, JSONObject.toJSONString(pkRankStrList));
                OtherRankingActivityData nextActivityData = otherRankingActivityDao.findData(nextActivityId);
                Update update = new Update();
                update.set("rankUidList", missPkUidList);
                otherRankingActivityDao.updateData(nextActivityData, update);
            }
        } catch (Exception e) {
            logger.error("handleWritePKData error={}", e.getMessage(), e);
        }
    }

    // 下发最后1轮奖励
    public void handleLastRoundData(OtherRankingActivityData otherActivity) {
        try {
            String activityId = otherActivity.get_id().toString();
            TreeMap<Integer, Integer> ROUND_MAP = null;
            List<String> ROUND_KEY_LIST = null;
            if(QUEEN_ACTIVITY_ID_LIST.contains(activityId)){
                ROUND_MAP = QUEEN_ROUND_6_MAP;
                ROUND_KEY_LIST = QUEEN_ROUND_6_KEY_LIST;
            }else {
                ROUND_MAP = KING_ROUND_6_MAP;
                ROUND_KEY_LIST = KING_ROUND_6_KEY_LIST;
            }

            List<String> rankingList = activityCommonRedis.getCommonRankingList(activityId, 0);
            int rank = 1;
            for (String rankUid : rankingList) {
                Integer higherKey = ROUND_MAP.higherKey(rank - 1);
                int resKeyIndex =  higherKey != null ? ROUND_MAP.get(higherKey) : ROUND_MAP.lastEntry().getValue();
                String resKey = ROUND_KEY_LIST.get(resKeyIndex);
                resourceKeyHandlerService.sendResourceData(rankUid, resKey, otherActivity.getAcNameEn(), otherActivity.getAcNameAr(), otherActivity.getAcNameEn(), ACTIVITY_URL, "");
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("handleRound5Data error={}", e.getMessage(), e);
        }
    }

    public void handleSendGift(SendGiftData sendGiftData, OtherRankingActivityData activityData) {
        try {
            int sendBeans = sendGiftData.getNumber() * sendGiftData.getPrice();
            Set<String> aidSet = sendGiftData.getAid_list();
            List<String> pkUidList = activityData.getRankUidList();
            if(pkUidList == null){
                return;
            }

            String activityId = activityData.get_id().toString();
            for (String aid : aidSet) {
                if (!pkUidList.contains(aid)) {
                    continue;
                }
                activityCommonRedis.incrCommonZSetRankingScore(activityId, aid, sendBeans);
            }
        } catch (Exception e) {
            logger.error("handleReceiveGift error={}", e.getMessage(), e);
        }
    }

}
