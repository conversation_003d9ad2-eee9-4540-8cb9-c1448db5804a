package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.MotherConfigVO;
import com.quhong.data.vo.PopularListVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityOtherRedis;
import com.quhong.redis.MotherRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class MotherService extends OtherActivityService{

    private static final Logger logger = LoggerFactory.getLogger(MotherService.class);
    private static final String MOTHER_ORIGIN = "mother_day";
    private static final String SHARE_DESC_EN = "On this special day, i want to express gratitude to my grate mother by YouStar. and the flower i send to her. I love you mom, wish you like the flower, always be vibrant.";
    private static final String SHARE_DESC_AR = "في هذا اليوم الخاص ، أود أن أعبر عن امتناني لأمي العظيمة بواسطة يوستار. والوردة التي أرسلتها إليها. أحبك يا أمي ، أتمنى أن تعجبك الوردة  ، وكني دائمًا نابضًة بالحياة";
    private static final Integer PART_1_START = 1678827600;
    private static final Integer PART_1_END = 1679000400;
    private static final Integer PART_2_START = 1679000400;
    private static final Integer PART_2_END = 1679173200;
    private static final Integer PART_3_START = 1679173200;
    private static final Integer PART_3_END = 1679346000;
    private static final Integer PART_4_START = 1679346000;
    private static final Integer PART_4_END = 1679432400;
    private static final String PART_1_IMAGE = "web/flower_01.png";
    private static final String PART_2_IMAGE = "web/flower_02.png";
    private static final String PART_3_IMAGE = "web/flower_03.png";
    private static final String PART_4_IMAGE = "web/flower_04.png";
    private static final String PART_1_IMAGE_AR = "web/flower_01_ar.png";
    private static final String PART_2_IMAGE_AR = "web/flower_02_ar.png";
    private static final String PART_3_IMAGE_AR = "web/flower_03_ar.png";
    private static final String PART_4_IMAGE_AR = "web/flower_04_ar.png";
    private static final Integer GIFT_DIAMOND = 3;



    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private IMomentService iMomentService;
    @Resource
    private MotherRedis motherRedis;
    @Resource
    private ActivityOtherRedis activityOtherRedis;
    @Resource
    private ActorDao actorDao;

    public MotherConfigVO motherConfig(String uid, String activityId) {
        OtherRankingActivityData otherActivity = getOtherRankingActivity(activityId);
        if(otherActivity == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        ActorData actor = actorDao.getActorDataFromCache(uid);
        if(actor == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        MotherConfigVO vo = new MotherConfigVO();
        vo.setStartTime(otherActivity.getStartTime());
        vo.setEndTime(otherActivity.getEndTime());
        vo.setRid(actor.getRid());

        int totalPool = (int) (motherRedis.getMotherTotal(activityId) * 0.3);
        vo.setPoolAmount(totalPool);

        int roundNum = otherActivity.getRoundNum();
        int rank = activityOtherRedis.getOtherRank(activityId, uid, ActivityConstant.SEND_RANK, roundNum);

        if(rank > 0 && rank <= 20){
            Map<String, Integer>  rankingMap = activityOtherRedis.getOtherRankingMap(activityId, ActivityConstant.SEND_RANK, 20, roundNum);
            int topTotalScore = 0;
            for (int score : rankingMap.values()) {
                int scoreDiamond = score * GIFT_DIAMOND;
                topTotalScore += scoreDiamond;
            }

            int myScore = activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RANK, roundNum);

            BigDecimal totalScoreDec = new BigDecimal(topTotalScore);
            BigDecimal myScoreDec = new BigDecimal(myScore * GIFT_DIAMOND);
            BigDecimal totalPoolDec = new BigDecimal(totalPool);
            BigDecimal awardAmount = topTotalScore > 0 ? totalPoolDec.multiply(myScoreDec).divide(totalScoreDec, 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.valueOf(0);
            vo.setMyAmount(awardAmount.intValue());

        }else {
            vo.setMyAmount(-1);
        }

        vo.setRoomId("");
        List<PopularListVO> popularList = motherRedis.getPopularList(1, 1);
        if(popularList != null && popularList.size() > 0){
            PopularListVO popularVO = popularList.get(0);
            vo.setRoomId(popularVO.getRoomId());
        }

        return vo;

    }

    private String getPartTimeImage(int currentTime, int slang){
        if(currentTime >= PART_1_START && currentTime < PART_1_END){
            return slang == SLangType.ARABIC ? PART_1_IMAGE_AR : PART_1_IMAGE;
        } else if (currentTime >= PART_2_START && currentTime < PART_2_END) {
            return slang == SLangType.ARABIC ? PART_2_IMAGE_AR : PART_2_IMAGE;
        } else if (currentTime >= PART_3_START && currentTime < PART_3_END) {
            return slang == SLangType.ARABIC ? PART_3_IMAGE_AR : PART_3_IMAGE;
        } else if (currentTime >= PART_4_START && currentTime < PART_4_END) {
            return slang == SLangType.ARABIC ? PART_4_IMAGE_AR : PART_4_IMAGE;
        }else {
            return "";
        }
    }

    public void motherMomentPush(String uid, String activityId, int slang) {

        if(StringUtils.isEmpty(activityId)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        OtherRankingActivityData otherActivity = getOtherRankingActivity(activityId);
        if(otherActivity == null){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        int currentTime = DateHelper.getNowSeconds();
        if (currentTime < otherActivity.getStartTime() || currentTime > otherActivity.getEndTime()) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        String img = getPartTimeImage(currentTime, slang);
        if(StringUtils.isEmpty(img)){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        synchronized (stringPool.intern(uid)) {

            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();

            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText(slang == SLangType.ARABIC ? SHARE_DESC_AR : SHARE_DESC_EN);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(MOTHER_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(img);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("2000");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));
            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if(result.getCode() == 20){
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

        }
    }
}
