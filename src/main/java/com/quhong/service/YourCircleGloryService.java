package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.ScoreRecordEvent;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.YourCircleVO;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 你的荣耀圈
 */
@Service
public class YourCircleGloryService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(YourCircleGloryService.class);
    private static final String ACTIVITY_TITLE_EN = "Your Circle of Glory";
    public static final int CHARM_TYPE = 1; // 魅力
    public static final int GENEROUS_TYPE = 2; // 慷慨
    public static final int SUPPORT_TYPE = 9; // 扶持者

    public static final int SUPPORT_MAN_TYPE = 1; // 支持我的男用户
    public static final int SUPPORT_QUEEN_TYPE = 2; // 我支持的女用户

    public static final String ACTIVITY_ID = "6846cd21619765a0e117bd69";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/CircleGlory/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/CircleGlory/?activityId=%s", ACTIVITY_ID);


    private static final Map<Integer, List<String>> TYPE_RANK_KEY_MAP = new HashMap<>();

    private static final Map<Integer, String> TYPE_RANK_EVENT_MAP = new HashMap<>();

    private static final Interner<String> stringPool = Interners.newWeakInterner();


    static {
        TYPE_RANK_KEY_MAP.put(CHARM_TYPE, Arrays.asList("yourgloryCTOP1", "yourgloryCTOP2", "yourgloryCTOP3"));
        TYPE_RANK_KEY_MAP.put(GENEROUS_TYPE, Arrays.asList("yourgloryGTOP1", "yourgloryGTOP2", "yourgloryGTOP3"));
        TYPE_RANK_KEY_MAP.put(SUPPORT_TYPE, Arrays.asList("yourglorySTOP1", "yourglorySTOP2", "yourglorySTOP3"));

        TYPE_RANK_EVENT_MAP.put(CHARM_TYPE, "Your Circle of Glory-charm rank reward");
        TYPE_RANK_EVENT_MAP.put(GENEROUS_TYPE, "Your Circle of Glory-generous rank reward");
        TYPE_RANK_EVENT_MAP.put(SUPPORT_TYPE, "Your Circle of Glory-supporter rank reward");
    }

    private static final int GIFT_ONE_BEANS = 10;

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private SuperQueen2025Service superQueen2025Service;
    @Resource
    private CacheDataService cacheDataService;

    private String getLocalLockKey(String activityId, String uid) {
        return String.format("getLocalLockKey:%s:%s", activityId, uid);
    }

    // 下面是redis的key

    /**
     * @param activityId
     * @param type       1 慷慨值 ,2 魅力值, 9 扶持值
     * @return
     */
    private String getTypeScoreKey(String activityId, int type) {
        return String.format("type:score:key:%s:%s", activityId, type);
    }

    /**
     * @param activityId
     * @param type       1 慷慨值 ,2 魅力值, 9 扶持值
     * @return
     */
    private String getTypeDiamondsKey(String activityId, int type) {
        return String.format("type:diamonds:key:%s:%s", activityId, type);
    }

    /**
     * @param activityId
     * @param uid        type1 为女用户uid  type2 为扶持者uid
     * @param type       1 我的扶持者 2 我扶持的女王
     * @return
     */
    private String getTypeSupportKey(String activityId, String uid, int type) {
        return String.format("type:support:key:%s:%s:%s", activityId, uid, type);
    }


    private float getScoreRatio(String activityId, String uid, int type, int nowScore) {
        if (type == SUPPORT_TYPE) {
            if (nowScore >= 10000 && nowScore < 20000) {
                return 1.5f;
            } else if (nowScore >= 30000 && nowScore < 40000) {
                return 2f;
            } else if (nowScore >= 40000) {
                return getTimeRatio(activityId, uid, type, nowScore);
            }
        } else {
            if (nowScore >= 20000 && nowScore < 25000) {
                return 1.5f;
            } else if (nowScore >= 25000 && nowScore < 30000) {
                return 2f;
            } else if (nowScore >= 30000) {
                return getTimeRatio(activityId, uid, type, nowScore);
            }
        }
        return 1f;
    }

    private float getTimeRatio(String activityId, String uid, int type, int nowScore) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        int now = DateHelper.getNowSeconds();
        // 在活动第一天、活动第二天、活动最后一天
        if ((now > activity.getStartTime() && now <= activity.getStartTime() + TimeUnit.DAYS.toSeconds(2))
                || (now > activity.getEndTime() - TimeUnit.DAYS.toSeconds(1) && now <= activity.getEndTime())) {
            String startHMS;
            String endHMS;
            if (ServerConfig.isNotProduct()) {

                if (type == SUPPORT_TYPE) {
                    startHMS = "06:00:00";
                    endHMS = "13:30:00";
                } else {
                    startHMS = "06:00:00";
                    endHMS = "13:00:00";
                }
            } else {
                if (type == SUPPORT_TYPE) {
                    startHMS = "20:00:00";
                    endHMS = "21:00:00";
                } else {
                    startHMS = "20:00:00";
                    endHMS = "20:30:00";
                }
            }
            String currentDate = getDayByBase(activityId, uid);
            int startTime = DateHelper.ARABIAN.stringDateTimeToStampSecond(String.format("%s %s", currentDate, startHMS));
            int endTime = DateHelper.ARABIAN.stringDateTimeToStampSecond(String.format("%s %s", currentDate, endHMS));
            if (now >= startTime && now <= endTime) {
//                logger.info("in time ratio uid:{} now:{} startTime:{} endTime:{}", uid, now, startTime, endTime);
                if (type == SUPPORT_TYPE) {
                    return 2f;
                }
                return 1.5f;
            }
        }
        return 1f;
    }

    public YourCircleVO yourCircleConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        YourCircleVO vo = new YourCircleVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        vo.setFbGender(actorData.getFb_gender() == 2 ? 2 : 1);
        vo.setIsJoinSuperQueen(activityCommonRedis.isCommonSetData(superQueen2025Service.getSetJoinKey(null), uid));
        YourCircleVO.RankInfo charmRankInfo = new YourCircleVO.RankInfo();
        YourCircleVO.RankInfo generousRankInfo = new YourCircleVO.RankInfo();
        YourCircleVO.RankInfo supportInfo = new YourCircleVO.RankInfo();
        if (actorData.getFb_gender() == 2) {
            fillRankInfo(activityId, uid, charmRankInfo, CHARM_TYPE);
            fillRankInfo(activityId, uid, generousRankInfo, GENEROUS_TYPE);
        } else {
            fillRankInfo(activityId, uid, supportInfo, SUPPORT_TYPE);
        }
        vo.setCharmRankInfo(charmRankInfo);
        vo.setGenerousRankInfo(generousRankInfo);
        vo.setSupportInfo(supportInfo);
        return vo;
    }

    private void fillRankInfo(String activityId, String uid, YourCircleVO.RankInfo rankInfo, int type) {
        String typeScoreKey = getTypeScoreKey(activityId, type);
        String typeDiamondsKey = getTypeDiamondsKey(activityId, type);
        Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();


        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(typeScoreKey, 10);


        List<OtherRankingListVO> rankingList = new ArrayList<>();
        List<OtherRankingListVO> supportList = new ArrayList<>();

        YourCircleVO.MyCircleRankVO myRankVO = new YourCircleVO.MyCircleRankVO();
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO vo = new OtherRankingListVO();
            ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
            vo.setName(rankActor.getName());
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            vo.setUid(entry.getKey());
            vo.setScore(entry.getValue());
            String aidCountryCode = ActorUtils.getCountryCode(rankActor.getCountry());
            vo.setCountryFlag(commonFlagConfigMap.getOrDefault(aidCountryCode, ""));
            vo.setRank(rank);
            int aidDiamonds = activityCommonRedis.getCommonZSetRankingScore(typeDiamondsKey, entry.getKey());
            float ratio = getScoreRatio(activityId, uid, type, aidDiamonds);
            vo.setRankDate(String.format("%.1f", ratio));
            if (entry.getKey().equals(uid)) {
                BeanUtils.copyProperties(vo, myRankVO);
            }
            rankingList.add(vo);
            rank += 1;
        }


        int myDiamonds = activityCommonRedis.getCommonZSetRankingScore(typeDiamondsKey, uid);

        if (type == CHARM_TYPE) {
            myRankVO.setReceiveScore(myDiamonds); // 我收到的钻石
        } else {
            myRankVO.setSendingScore(myDiamonds); // 我发出的钻石
        }
        if (myRankVO.getRank() == null) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            myRankVO.setName(actorData.getName());
            myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRankVO.setUid(uid);
            int myScore = activityCommonRedis.getCommonZSetRankingScore(typeScoreKey, uid);
            myRankVO.setScore(myScore); // 慷慨值等
            myRankVO.setRank(activityCommonRedis.getCommonZSetRank(typeScoreKey, uid));
            String countryCode = ActorUtils.getCountryCode(actorData.getCountry());
            myRankVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
            float ratio = getScoreRatio(activityId, uid, type, myDiamonds);
            myRankVO.setRankDate(String.valueOf(getNowStep(type, ratio, myDiamonds)));
        }else {
            float ratio = myRankVO.getRankDate() == null ? 1f : Float.parseFloat(myRankVO.getRankDate());
            myRankVO.setRankDate(String.valueOf(getNowStep(type, ratio, myDiamonds)));
        }



        rank = 1;
        if (type != GENEROUS_TYPE) {
            String supportKey = getTypeSupportKey(activityId, uid, type == SUPPORT_TYPE ? SUPPORT_QUEEN_TYPE : SUPPORT_MAN_TYPE);
            Map<String, Integer> supportMap = activityCommonRedis.getCommonRankingMap(supportKey, type == SUPPORT_TYPE ? 5 : 3);
            for (Map.Entry<String, Integer> entry : supportMap.entrySet()) {
                OtherRankingListVO vo = new OtherRankingListVO();
                ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
                vo.setName(rankActor.getName());
                vo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
                vo.setUid(entry.getKey());
                vo.setScore(entry.getValue());
                String aidCountryCode = ActorUtils.getCountryCode(rankActor.getCountry());
                vo.setCountryFlag(commonFlagConfigMap.getOrDefault(aidCountryCode, ""));
                vo.setRank(rank);
                supportList.add(vo);
                rank += 1;
            }
        }
        rankInfo.setRankingList(rankingList);
        rankInfo.setSupportList(supportList);
        rankInfo.setMyRankVO(myRankVO);

    }

    public void sendGiftHandle(SendGiftData data, String activityId) {
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }

        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }
        // 礼物
        if (data == null || !RoomUtils.isVoiceRoom(data.getRoomId()) || StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }
        String uid = data.getFrom_uid();
        if (!checkAc(uid)) {
            return;
        }
        GiftData giftData = giftDao.getGiftFromCache(data.getGid());
        if (giftData != null && giftData.getGtype() == 2) {
            // 金币礼物不加
            return;
        }

        int sendBeans = data.getNumber() * data.getPrice() * data.getAid_list().size();
        int oneGiftBeans = data.getPrice() * data.getNumber();
        int incNum = sendBeans / GIFT_ONE_BEANS;

        if (incNum == 0) {
            return;
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData.getFb_gender() == 2) {
            String typeScoreKey = getTypeScoreKey(activityId, GENEROUS_TYPE);
            String typeDiamondsKey = getTypeDiamondsKey(activityId, GENEROUS_TYPE);

            int oldDiamonds = activityCommonRedis.getCommonZSetRankingScore(typeDiamondsKey, uid);
            float ratio = getScoreRatio(activityId, uid, GENEROUS_TYPE, oldDiamonds);
            incNum = ratio > 1 ? (int) (incNum * ratio) : incNum;
            logger.info("sendGiftHandle uid:{} oldDiamonds:{} ratio:{} incNum:{} sendBeans:{}", uid, oldDiamonds, ratio, incNum, sendBeans);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(typeScoreKey, uid, incNum);
            activityCommonRedis.incrCommonZSetRankingScoreSimple(typeDiamondsKey, uid, sendBeans);
            asyncAddSuperQueenScore(uid, GENEROUS_TYPE, incNum);
            doReportScoreRecordEvent(uid, GENEROUS_TYPE, incNum, ratio, oldDiamonds);
        } else {
            int oneGiftIncNum = oneGiftBeans / GIFT_ONE_BEANS;
            if (oneGiftIncNum == 0) {
                return;
            }

            int validGiftBeans = 0;
            int validGiftIncNum = 0;

            String typeScoreKeyS = getTypeScoreKey(activityId, SUPPORT_TYPE);
            String typeDiamondKeyS = getTypeDiamondsKey(activityId, SUPPORT_TYPE);
            int oldDiamondScoreS = activityCommonRedis.getCommonZSetRankingScore(typeDiamondKeyS, uid);
            float ratioS = getScoreRatio(activityId, uid, SUPPORT_TYPE, oldDiamondScoreS);
            int validOneIncNumS = oneGiftBeans / GIFT_ONE_BEANS;
            validOneIncNumS = ratioS > 1 ? (int) (validOneIncNumS * ratioS) : validOneIncNumS;

            String typeScoreKey = getTypeScoreKey(activityId, CHARM_TYPE);
            String typeDiamondsKey = getTypeDiamondsKey(activityId, CHARM_TYPE);

            for (String aid : data.getAid_list()) {
                ActorData aidActorData = actorDao.getActorDataFromCache(aid);
                if (aidActorData.getFb_gender() != 2) {
                    // 不是女王
                    continue;
                }


                int oldDiamondScore = activityCommonRedis.getCommonZSetRankingScore(typeDiamondsKey, aid);
                float ratio = getScoreRatio(activityId, aid, CHARM_TYPE, oldDiamondScore);
                oneGiftIncNum = ratio > 1 ? (int) (oneGiftIncNum * ratio) : oneGiftIncNum;

                logger.info("sendGiftHandle aid:{} oldDiamondScore:{} ratio:{} oneGiftIncNum:{} oneGiftBeans:{}  ratioS:{} validOneIncNumS:{}"
                        , aid, oldDiamondScore, ratio, oneGiftIncNum, oneGiftBeans, ratioS, validOneIncNumS);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeDiamondsKey, aid, oneGiftBeans);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeScoreKey, aid, oneGiftIncNum);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(getTypeSupportKey(activityId, aid, SUPPORT_MAN_TYPE), uid, oneGiftIncNum);
                asyncAddSuperQueenScore(aid, CHARM_TYPE, oneGiftIncNum);
                doReportScoreRecordEvent(aid, CHARM_TYPE, incNum, ratio, oldDiamondScore);

                validGiftBeans += oneGiftBeans;
                validGiftIncNum += validOneIncNumS;
                activityCommonRedis.incrCommonZSetRankingScoreSimple(getTypeSupportKey(activityId, uid, SUPPORT_QUEEN_TYPE), aid, validOneIncNumS);
            }

            if (validGiftBeans > 0) {
                // 增加男用户的扶持数据
                logger.info("sendGiftHandle uid:{} validGiftBeans:{} validGiftIncNum:{}", uid, validGiftBeans, validGiftIncNum);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeDiamondKeyS, uid, validGiftBeans);
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeScoreKeyS, uid, validGiftIncNum);
                doReportScoreRecordEvent(uid, SUPPORT_TYPE, validGiftIncNum, ratioS, oldDiamondScoreS);
            }
        }

    }


    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    public void distributionRanking(String activityId) {
        distributionRanking(GENEROUS_TYPE);
        distributionRanking(CHARM_TYPE);
        distributionRanking(SUPPORT_TYPE);
    }

    // 下发榜单奖励
    private void distributionRanking(int type) {
        try {
            int length = 3;
            String typeScoreKey = getTypeScoreKey(ACTIVITY_ID, type);
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(typeScoreKey, length);
            int rank = 1;
            List<String> keyList = TYPE_RANK_KEY_MAP.get(type);
            String eventTitle = TYPE_RANK_EVENT_MAP.getOrDefault(type, ACTIVITY_TITLE_EN);
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = entry.getKey();
                String resKey = keyList.get(rank - 1);
                handleRes(aid, resKey, eventTitle);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    private void asyncAddSuperQueenScore(String uid, int type, int value) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                CommonMqTopicData mqData = new CommonMqTopicData(uid, "", "", String.valueOf(type)
                        , SuperQueen2025Service.FROM_YOUR_CIRCLE_EVENT, value);
                superQueen2025Service.handleUserScore(null, mqData);
            }
        });
    }


    private void doActivityParticipationEvent(String uid, int type) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(type);
        eventReport.track(new EventDTO(event));
    }

    private int getNowStep(int type, float ratio, int nowScore) {
        if (type == SUPPORT_TYPE) {
            if (nowScore >= 10000 && nowScore < 20000) {
                return 1;
            } else if (nowScore >= 30000 && nowScore < 40000) {
                return 2;
            } else if (nowScore >= 40000 && ratio > 1) {
                return 3;
            }
        } else {
            if (nowScore >= 20000 && nowScore < 25000) {
                return 1;
            } else if (nowScore >= 25000 && nowScore < 30000) {
                return 2;
            } else if (nowScore >= 30000 && ratio > 1) {
                return 3;
            }
        }
        return 0;

    }

    private void doReportScoreRecordEvent(String uid, int type, int changed, float ratio, int beforeScore) {
        String changedDesc = String.valueOf(ratio);
        if (beforeScore >= 40000 && type == SUPPORT_TYPE && ratio > 1) {
            changedDesc = String.format("c-%s", ratio);
        } else if (beforeScore >= 30000 && type != SUPPORT_TYPE && ratio > 1) {
            changedDesc = String.format("c-%s", ratio);
        }
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(22);
        event.setScore_changed_detail(type);
        event.setScore_changed_desc(changed > 0 ? changedDesc : "daily_reward");
        eventReport.track(new EventDTO(event));
    }
}
