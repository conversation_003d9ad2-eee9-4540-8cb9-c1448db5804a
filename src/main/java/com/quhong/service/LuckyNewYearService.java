package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.LuckyNewYearVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.dao.ExtraMicFrameDao;
import com.quhong.mongo.data.ExtraMicFrameData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class LuckyNewYearService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(LuckyNewYearService.class);
    public static final String ACTIVITY_NAME = "Lucky NewYear Activity";
    public static final String ACTIVITY_ID = "658fd54fd75c2861abd961aa";
    public static final String SELECT_TEAM_KEY = "selectTeam";
    public static final String SELECT_ABLE_KEY = "selectable";
    public static final String SELECT_RECHARGE = "selectRecharge";
    private static final int LIMIT_INIT_POOL = 10;
    private static final int ZERO_INIT_POOL = 0;
    private static final List<String> TEAM_LIST = Arrays.asList("golden", "red", "green", "blue");
    private static final Map<Integer, String> GIFT_TEAM_MAP = new HashMap<>();
    private static final Map<String, Integer> TEAM_MIC_MAP = new HashMap<>();
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        if(ServerConfig.isProduct()){
            GIFT_TEAM_MAP.put(678, "golden");
            GIFT_TEAM_MAP.put(679, "red");
            GIFT_TEAM_MAP.put(680, "green");
            GIFT_TEAM_MAP.put(681, "blue");

            TEAM_MIC_MAP.put("golden", 532);
            TEAM_MIC_MAP.put("red", 533);
            TEAM_MIC_MAP.put("green", 534);
            TEAM_MIC_MAP.put("blue", 535);
        }else {
            GIFT_TEAM_MAP.put(819, "golden");
            GIFT_TEAM_MAP.put(817, "red");
            GIFT_TEAM_MAP.put(820, "green");
            GIFT_TEAM_MAP.put(818, "blue");

            TEAM_MIC_MAP.put("golden", 566);
            TEAM_MIC_MAP.put("red", 565);
            TEAM_MIC_MAP.put("green", 568);
            TEAM_MIC_MAP.put("blue", 567);
        }

    }


    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private LuckyNewYearService luckyNewYearService;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ExtraMicFrameDao extraMicFrameDao;

    private String getHashActivityId(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }

    private String getPoolSizeActivityId(String activityId){
        return String.format("PoolSize:%s", activityId);
    }

    private String getTeamActivityId(String activityId, String teamKey){
        return String.format("%s:%s", activityId, teamKey);
    }

    private String getNoSelectTeamActivityId(String activityId, String teamKey){
        return String.format("noSelect:%s:%s", activityId, teamKey);
    }

    private String getTeamRankKey(String activityId){
        return String.format("teamRank:%s", activityId);
    }




    public LuckyNewYearVO luckyNewYearConfig(String activityId, String uid){
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        Map<String, String> taskNumMap =  activityCommonRedis.getCommonHashAllMapStr(getHashActivityId(activityId, uid));

        // 设置选择的队伍
        LuckyNewYearVO vo = new LuckyNewYearVO();
        vo.setFirstClick(1);
        String selectTeam = taskNumMap.get(SELECT_TEAM_KEY);
        if(StringUtils.isEmpty(selectTeam)){
            vo.setSelectTeam(autoSelectTeam(activityId, uid));
        }else {
            vo.setFirstClick(0);
            vo.setSelectTeam(selectTeam);
        }

        vo.setSelectTeam(StringUtils.isEmpty(vo.getSelectTeam()) ? "" : vo.getSelectTeam());

        String selectAble = taskNumMap.get(SELECT_ABLE_KEY);
        vo.setSelectable(StringUtils.isEmpty(selectAble) ? 0 : Integer.parseInt(selectAble));
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        List<LuckyNewYearVO.TeamRank> teamRankList = new ArrayList<>();
        List<LuckyNewYearVO.MyTeamRank> myTeamRankList = new ArrayList<>();

        for (String teamKey: TEAM_LIST) {

            // 设置队伍排名
            LuckyNewYearVO.TeamRank teamRank = new LuckyNewYearVO.TeamRank();
            teamRank.setTeamKey(teamKey);

            String teamActivityId = getTeamActivityId(activityId, teamKey);
            int teamScore = activityCommonRedis.getCommonStrScore(teamActivityId);

            String noSelectTeamActivityId = getNoSelectTeamActivityId(activityId, teamKey);
            int noSelectTeamScore = activityCommonRedis.getCommonStrScore(noSelectTeamActivityId);
            teamRank.setTeamScore(teamScore + noSelectTeamScore);

            List<String> supportRankList = activityCommonRedis.getCommonRankingList(teamActivityId, 3);

            List<String> supportHead = new ArrayList<>();
            for (String supportUid : supportRankList) {
                ActorData actorData = actorDao.getActorDataFromCache(supportUid);
                supportHead.add(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            }
            teamRank.setSupportHeadList(supportHead);
            teamRankList.add(teamRank);

            // 设置我对队伍的贡献数据
            LuckyNewYearVO.MyTeamRank myTeam = new LuckyNewYearVO.MyTeamRank();
            myTeam.setTeamKey(teamKey);
            int supportScore = activityCommonRedis.getCommonZSetRankingScore(teamActivityId, uid);
            int noSelectSupportScore = activityCommonRedis.getCommonZSetRankingScore(noSelectTeamActivityId, uid);
            myTeam.setSupportScore(supportScore + noSelectSupportScore);
            myTeam.setSelected(teamKey.equals(vo.getSelectTeam()) ? 1 : 0);
            myTeamRankList.add(myTeam);
        }

        vo.setTeamRankList(teamRankList);
        vo.setMyTeamRankList(myTeamRankList);

        // 设置等级勋章数量
        vo.setSendBadgeScore(activityOtherRedis.getOtherReachingScore(activityId, uid, ActivityConstant.SEND_RANK, 0));
        vo.setReceiveBadgeScore(activityOtherRedis.getOtherReachingScore(activityId, uid, ActivityConstant.RECEIVE_RANK, 0));
        return vo;
    }

    public void luckyNewYearSelectTeam(String activityId, String uid, String selectTeam){
        synchronized (stringPool.intern(SELECT_TEAM_KEY)) {

            otherActivityService.checkActivityTime(activityId);
            if(!TEAM_LIST.contains(selectTeam)){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, String> allMap = activityCommonRedis.getCommonHashAllMapStr(hashActivityId);

            String beforeTeam = allMap.get(SELECT_TEAM_KEY);
            if(StringUtils.isEmpty(beforeTeam)){
                logger.error("no select team uid:{}", uid);
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            if(beforeTeam.equals(selectTeam)){
                logger.error("select same team uid:{}, selectTeam:{}", uid, selectTeam);
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }

            int selectable = Integer.parseInt(allMap.getOrDefault(SELECT_ABLE_KEY, "0"));
            if(selectable <= 0){
                logger.error("no select team change uid:{}, selectable:{}", uid, selectable);
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }

            // 取出之前队伍的值
            String beforeTeamActivityId = getTeamActivityId(activityId, beforeTeam);
            int beforeSendScore = activityCommonRedis.getCommonZSetRankingScore(beforeTeamActivityId, uid);
            if(beforeSendScore > 0){
                int beforeTeamScore = activityCommonRedis.getCommonStrScore(beforeTeamActivityId);
                int changeBeforeTeamScore = beforeTeamScore - beforeSendScore * 2;
                changeBeforeTeamScore = Math.max(changeBeforeTeamScore, 0);
                activityCommonRedis.setCommonStrScore(beforeTeamActivityId, changeBeforeTeamScore);
                activityCommonRedis.removeCommonZSet(beforeTeamActivityId, uid);

                String beforeNoSelectTeamActivityId = getNoSelectTeamActivityId(activityId, beforeTeam);
                activityCommonRedis.incCommonStrScore(beforeNoSelectTeamActivityId, beforeSendScore);
                activityCommonRedis.incrCommonZSetRankingScore(beforeNoSelectTeamActivityId, uid, beforeSendScore);
            }

            String afterNoSelectTeamActivityId = getNoSelectTeamActivityId(activityId, selectTeam);
            int afterSendScore = activityCommonRedis.getCommonZSetRankingScore(afterNoSelectTeamActivityId, uid);

            if(afterSendScore > 0){
                int afterNoSelectTeamScore = activityCommonRedis.getCommonStrScore(afterNoSelectTeamActivityId);
                afterNoSelectTeamScore = afterNoSelectTeamScore - afterSendScore;
                afterNoSelectTeamScore = Math.max(afterNoSelectTeamScore, 0);
                activityCommonRedis.setCommonStrScore(afterNoSelectTeamActivityId, afterNoSelectTeamScore);
                activityCommonRedis.removeCommonZSet(afterNoSelectTeamActivityId, uid);

                int afterIncScore = afterSendScore * 2;
                String afterSelectTeamActivityId = getTeamActivityId(activityId, selectTeam);
                activityCommonRedis.incCommonStrScore(afterSelectTeamActivityId, afterIncScore);
                activityCommonRedis.incrCommonZSetRankingScore(afterSelectTeamActivityId, uid, afterSendScore);
            }

            int oldMicId = TEAM_MIC_MAP.getOrDefault(beforeTeam, 0);
            if(oldMicId > 0){
                ExtraMicFrameData micFrameData = extraMicFrameDao.findData(uid, oldMicId);
                if(micFrameData != null){
                    extraMicFrameDao.removeMicFrame(micFrameData);
                }
            }

            int newMicId = TEAM_MIC_MAP.getOrDefault(selectTeam, 0);
            if(newMicId > 0){
                distributionService.sendRewardResource(uid, newMicId, ActivityRewardTypeEnum.getEnumByName("mic"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
            }

            activityCommonRedis.setCommonHashData(hashActivityId, SELECT_TEAM_KEY, selectTeam);
            activityCommonRedis.setCommonHashNum(hashActivityId, SELECT_ABLE_KEY, 0);

        }
    }

    private String autoSelectTeam(String activityId, String uid){

        synchronized (stringPool.intern(SELECT_TEAM_KEY)) {
            String teamKey = drawTeamKey(activityId);
            activityCommonRedis.setCommonHashData(getHashActivityId(activityId, uid), SELECT_TEAM_KEY, teamKey);

            String noSelectTeamActivityId = getNoSelectTeamActivityId(activityId, teamKey);
            int sendScore = activityCommonRedis.getCommonZSetRankingScore(noSelectTeamActivityId, uid);

            if(sendScore > 0){

                int noSelectTeamScore = activityCommonRedis.getCommonStrScore(noSelectTeamActivityId);
                noSelectTeamScore = noSelectTeamScore - sendScore;
                noSelectTeamScore = Math.max(noSelectTeamScore, 0);
                activityCommonRedis.setCommonStrScore(noSelectTeamActivityId, noSelectTeamScore);
                activityCommonRedis.removeCommonZSet(noSelectTeamActivityId, uid);

                int incTeamScore = sendScore * 2;
                String selectTeamActivityId = getTeamActivityId(activityId, teamKey);
                activityCommonRedis.incCommonStrScore(selectTeamActivityId, incTeamScore);
                activityCommonRedis.incrCommonZSetRankingScore(selectTeamActivityId, uid, sendScore);
            }

            int micId = TEAM_MIC_MAP.getOrDefault(teamKey, 0);
            if(micId > 0){
                distributionService.sendRewardResource(uid, micId, ActivityRewardTypeEnum.getEnumByName("mic"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
            }
            return teamKey;
        }
    }


    // 颜色奖池
    public String drawTeamKey(String activityId) {
        try {
            String poolSizeActivityId = getPoolSizeActivityId(activityId);
            int poolSize = activityCommonRedis.getCommonListSize(poolSizeActivityId);
            if (poolSize <= LIMIT_INIT_POOL){
                initTeamPoolSize(activityId);
            }
            String prizeKey = activityCommonRedis.leftPopCommonListKey(poolSizeActivityId);
            logger.info("drawTeamKey poolSize:{}  prizeKey: {}", poolSize, prizeKey);
            return prizeKey != null ? prizeKey : "";
        } catch (Exception e) {
            logger.error("drawTeamKey error e={}", e.getMessage());
            return "";
        }
    }

    public void initTeamPoolSize(String activityId){
        List<String> poolList = new ArrayList<>();
        for (String teamKey : TEAM_LIST) {
            for (int i = 0; i < 25; i++) {
                poolList.add(teamKey);
            }
        }
        Collections.shuffle(poolList);
        activityCommonRedis.rightPushAllCommonList(getPoolSizeActivityId(activityId), poolList);
    }

    public void handleLuckyNewYearData(SendGiftData sendGiftData, String activityId) {
        try {

            int giftId = sendGiftData.getGid();
            String fromUid = sendGiftData.getFrom_uid();
            String teamKey = GIFT_TEAM_MAP.get(giftId);
            if(StringUtils.isEmpty(teamKey)){
                return;
            }

            // 没有选择队伍时发送礼物: 单独榜单
            // 已选择队伍时发送礼物: 加入榜单

            // 两个榜单、 两个总值

            int sendTotalBeans = sendGiftData.getPrice() * sendGiftData.getNumber() * sendGiftData.getAid_list().size();
            Map<String, String> taskNumMap =  activityCommonRedis.getCommonHashAllMapStr(getHashActivityId(activityId, fromUid));
            String selectTeam = taskNumMap.get(SELECT_TEAM_KEY);

            int incTeamScore;
            String teamActivityId;
            if(!StringUtils.isEmpty(selectTeam) && teamKey.equals(selectTeam)){
                teamActivityId = getTeamActivityId(activityId, teamKey);
                incTeamScore = sendTotalBeans * 2;
            }else {
                incTeamScore = sendTotalBeans;
                teamActivityId = getNoSelectTeamActivityId(activityId, teamKey);
            }
            logger.info("handleLuckyNewYearData uid:{}, incTeamScore:{}, teamActivityId:{}", fromUid, incTeamScore, teamActivityId);

            // 用户对队伍的贡献
            activityCommonRedis.incrCommonZSetRankingScore(teamActivityId, fromUid, sendTotalBeans);
            activityCommonRedis.incCommonStrScore(teamActivityId, incTeamScore);

        }catch (Exception e){
            logger.error("handleChristmasGiftData error={}", e.getMessage(), e);
        }
    }

    public void handleUserRecharge(RechargeInfo rechargeInfo){

        try {

            String uid = rechargeInfo.getUid();
            int rechargeType = rechargeInfo.getRechargeType();
            if (rechargeType != 1) {
                logger.info("not honor recharge return rechargeInfo:{}", rechargeInfo);
                return;
            }

            if(!inActivityTime(ACTIVITY_ID)){
                return;
            }

            String hashActivityId = getHashActivityId(ACTIVITY_ID, uid);
            int rechargeFlag = activityCommonRedis.getCommonHashValue(hashActivityId, SELECT_RECHARGE);
            if(rechargeFlag > 0){
                return;
            }

            activityCommonRedis.setCommonHashNum(hashActivityId, SELECT_ABLE_KEY, 1);
            activityCommonRedis.setCommonHashNum(hashActivityId, SELECT_RECHARGE, 1);
        }catch (Exception e){
            logger.error("handleUserRecharge error :{}", e.getMessage(), e);
        }
    }

    public void handleTopTeamData(String activityId) {
        try {
            int maxScore = 0;
            String maxRankTeamActivityId = null;
            for (String teamKey : TEAM_LIST) {
                String teamActivityId = getTeamActivityId(activityId, teamKey);
                int teamScore = activityCommonRedis.getCommonStrScore(teamActivityId);
                if(teamScore > maxScore){
                    maxScore = teamScore;
                    maxRankTeamActivityId = teamActivityId;
                }
            }

            if(StringUtils.isEmpty(maxRankTeamActivityId)){
                logger.error("handleTopTeamData no find maxRankTeamActivityId");
                return;
            }

            logger.info("top1 maxRankTeamActivityId :{}", maxRankTeamActivityId);
            List<String> rankingList = activityCommonRedis.getCommonRankingList(maxRankTeamActivityId, 0);
            int rank = 1;
            for (String uid : rankingList) {
                if(rank <= 3){
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 30000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }

                distributionService.sendRewardResource(uid, 25, ActivityRewardTypeEnum.getEnumByName("honor_title"), 365, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                rank += 1;
            }



        }catch (Exception e){
            logger.error("handleTopTeamData error e={}", e.getMessage(), e);
        }
    }
}
