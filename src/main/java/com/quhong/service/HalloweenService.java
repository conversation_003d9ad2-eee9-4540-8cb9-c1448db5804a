package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GetActivitySpecialItemsRecordEvent;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.vo.HalloweenVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigV2VO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mq.MqSenderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class HalloweenService extends OtherActivityService implements DailyTaskHandler {


    private static final Logger logger = LoggerFactory.getLogger(HalloweenService.class);
    private static final String ACTIVITY_TITLE = "Happy Halloween";
    private static final String ACTIVITY_EXCHANGE = "Halloween Exchange";
    private static final String ACTIVITY_ID = "67221923fa47e6b4f6110e2d";
    private static final String ACTIVITY_BROADCAST_ICON = "";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/halloween_2024/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/halloween_2024/?activityId=%s", ACTIVITY_ID);
    private static final List<String> DAILY_DATE_LIST = Arrays.asList("2024-10-31", "2024-11-01", "2024-11-02", "2024-11-03", "2024-11-04");
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final String DAILY_TOTAL_CANDIE = "dailyTotalCandie";
    private static final Integer DAILY_DEFAULT_TOTAL_DIAMOND = 100000;
    private static final String DAILY_TOTAL_DIAMOND = "dailyTotalDiamond";

    // 资源key
    private static final String HALLOWEEN_RESOURCE_KEY = "HappyHalloweenResource";
    private static final String HALLOWEEN_EXCHANGE_KEY = "HappyHalloweenExchange";

    private static final Integer DAILY_COST_NUM = 100;
    private static final String DAILY_LEFT_BEAN = "leftBeans";
    private static final String DAILY_LEFT_CANDIE = "leftCandie";
    private static final String DAILY_USER_TOTAL_CANDIE = "userTotalCandie";
    private static final Integer RECORD_PAGE_SIZE = 10;

    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private EventReport eventReport;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    // 抽奖相关的每日key
    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("halloween:%s:%s", activityId, uid);
    }

    private String getHashDailyActivityId(String activityId, String dateStr) {
        return String.format("halloweenDaily:%s:%s", activityId, dateStr);
    }

    // 每日榜单
    private String getDailyRankKey(String activityId, String dateStr) {
        return String.format("dailyRank:%s:%s", activityId, dateStr);
    }

    // 滚动记录
    private String getRollRecordListKey(String activityId) {
        return String.format("rollRecord:%s", activityId);
    }

    // 抓鬼历史记录key
    private String getHistoryRecordListKey(String activityId, String uid) {
        return String.format("historyRecord:%s:%s", activityId, uid);
    }

    // 兑换奖品历史记录key
    private String getExchangeHistoryRecordListKey(String activityId, String uid) {
        return String.format("exchangeHistoryRecord:%s:%s", activityId, uid);
    }

    private String getDateStrFormat(String dateStr) {
        if (dateStr.equals("2024-10-30")) {
            return "2024-10-31";
        }
        return dateStr;
    }


    public HalloweenVO halloweenConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        HalloweenVO vo = new HalloweenVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        currentDay = this.getDateStrFormat(currentDay);
        vo.setDateStr(currentDay);

        // 活动相关数据
        String hashDailyActivityId = getHashDailyActivityId(activityId, currentDay);
        Map<String, Integer> dailyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);
        vo.setTotalCandie(dailyDataMap.getOrDefault(DAILY_TOTAL_CANDIE, 0));
        vo.setPoolDiamond(dailyDataMap.getOrDefault(DAILY_TOTAL_DIAMOND, 0));


        // 用户相关数据
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
        vo.setLeftBeans(userDataMap.getOrDefault(DAILY_LEFT_BEAN, 0));
        vo.setUserTotalCandie(userDataMap.getOrDefault(DAILY_USER_TOTAL_CANDIE, 0));
        vo.setLeftCandie(userDataMap.getOrDefault(DAILY_LEFT_CANDIE, 0));


        // 每日榜单
        List<HalloweenVO.DailyRankConfig> dailyRankConfigList = new ArrayList<>();
        for (String dateStr : DAILY_DATE_LIST) {
            HalloweenVO.DailyRankConfig dailyRankConfig = new HalloweenVO.DailyRankConfig();
            String dateRankKey = getDailyRankKey(activityId, dateStr);
            List<OtherRankingListVO> rankingList = new ArrayList<>();
            OtherRankingListVO myRank = new OtherRankingListVO();
            makeOtherRankingData(rankingList, myRank, dateRankKey, uid, 50);

            dailyRankConfig.setDateStr(dateStr);
            Map<String, Integer> everyDataMap = activityCommonRedis.getCommonHashAll(getHashDailyActivityId(activityId, dateStr));
            dailyRankConfig.setPoolDiamond(everyDataMap.getOrDefault(DAILY_TOTAL_DIAMOND, DAILY_DEFAULT_TOTAL_DIAMOND));
            dailyRankConfig.setRankingListVOList(rankingList);
            dailyRankConfig.setMyRankVO(myRank);
            dailyRankConfigList.add(dailyRankConfig);
        }
        vo.setDailyRankConfigList(dailyRankConfigList);

        // 滚动记录
        String rollListKey = getRollRecordListKey(activityId);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListRecord(rollListKey);
        List<PrizeConfigV2VO> rollRecordList = new ArrayList<>();
        for (String item : prizeKeyTimeList) {
            PrizeConfigV2VO record = JSON.parseObject(item, PrizeConfigV2VO.class);
            ActorData actorData = actorDao.getActorDataFromCache(record.getUid());
            record.setUserName(actorData.getName());
            record.setUserHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            record.setUserRid(actorData.getStrRid());
            rollRecordList.add(record);
        }
        vo.setRecordList(rollRecordList);
        return vo;
    }

    /**
     * 抓鬼
     */
    public HalloweenVO halloweenCatchSpirit(String activityId, String uid, String metaId) {
        checkActivityTime(activityId);

        // 查询中奖道具
        ResourceKeyConfigData seaTypeConfig = resourceKeyConfigDao.findByKey(HALLOWEEN_RESOURCE_KEY);
        if (seaTypeConfig == null) {
            logger.error("halloweenCatchSpirit not find config metaId:{}", metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        Map<String, ResourceKeyConfigData.ResourceMeta> seaTypeConfigMap = seaTypeConfig.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        ResourceKeyConfigData.ResourceMeta resMeta = seaTypeConfigMap.get(metaId);
        if (resMeta == null) {
            logger.error("halloweenCatchSpirit not find resMeta metaId:{}", metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        HalloweenVO vo = new HalloweenVO();
        synchronized (stringPool.intern(uid)) {
            String currentDay = DateHelper.ARABIAN.formatDateInDay();
            // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
            currentDay = this.getDateStrFormat(currentDay);

            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

            int leftBean = userDataMap.getOrDefault(DAILY_LEFT_BEAN, 0);
            if (leftBean < DAILY_COST_NUM) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            int afterNum = activityCommonRedis.incCommonHashNum(hashActivityId, DAILY_LEFT_BEAN, -DAILY_COST_NUM);
            int currentTime = DateHelper.getNowSeconds();
            List<PrizeConfigV2VO> drawRecordList = new ArrayList<>();
            vo.setLeftBeans(afterNum);
            PrizeConfigV2VO record = new PrizeConfigV2VO();
            BeanUtils.copyProperties(resMeta, record);
            record.setUid(uid);
            record.setCtime(currentTime);
            drawRecordList.add(record);
            activityCommonRedis.incCommonHashNum(hashActivityId, DAILY_LEFT_CANDIE, resMeta.getResourceNumber());  // 增加用户糖果数量
            activityCommonRedis.incCommonHashNum(hashActivityId, DAILY_USER_TOTAL_CANDIE, resMeta.getResourceNumber());  // 增加用户糖果总量

            String hashDailyActivityId = getHashDailyActivityId(activityId, currentDay);
            int afterCandie = activityCommonRedis.incCommonHashNum(hashDailyActivityId, DAILY_TOTAL_CANDIE, resMeta.getResourceNumber());  // 增加总糖果数量
            if (afterCandie >= 1000) {
                int incTotalBeans = (afterCandie / 1000) * 5000;
                int leftCandie = afterCandie % 1000;
                int dailyTotalBeans = activityCommonRedis.getCommonHashValue(hashDailyActivityId, DAILY_TOTAL_DIAMOND);
                dailyTotalBeans = dailyTotalBeans == 0 ? DAILY_DEFAULT_TOTAL_DIAMOND : dailyTotalBeans;
                dailyTotalBeans += incTotalBeans;
                activityCommonRedis.setCommonHashNum(hashDailyActivityId, DAILY_TOTAL_DIAMOND, dailyTotalBeans);
                activityCommonRedis.setCommonHashNum(hashDailyActivityId, DAILY_TOTAL_CANDIE, leftCandie);
            }
            this.doItemsEvent(activityId, uid, resMeta);
            activityCommonRedis.addCommonListRecord(getRollRecordListKey(activityId), JSON.toJSONString(record));
            activityCommonRedis.addCommonListData(getHistoryRecordListKey(activityId, uid), JSON.toJSONString(record));
            vo.setRecordList(drawRecordList);
        }
        return vo;
    }

    /**
     * 开启海域埋点
     */
    private void doItemsEvent(String activityId, String uid, ResourceKeyConfigData.ResourceMeta resourceMeta) {
        GetActivitySpecialItemsRecordEvent event = new GetActivitySpecialItemsRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE);
        event.setActive_id(activityId);
        event.setActivity_special_items_id(resourceMeta.getMetaId());
        event.setActivity_special_items_resource(resourceMeta.getResourceNameEn());
        event.setGet_activity_special_items_nums(resourceMeta.getResourceNumber());
        eventReport.track(new EventDTO(event));
    }


    // 发送礼物统计
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        int totalPrice = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();
        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));
        currentDay = this.getDateStrFormat(currentDay);

        String hashActivityId = getHashActivityId(activityId, fromUid);
        activityCommonRedis.incCommonHashNum(hashActivityId, DAILY_LEFT_BEAN, totalPrice);
        activityCommonRedis.incrCommonZSetRankingScore(getDailyRankKey(activityId, currentDay), fromUid, totalPrice);
    }

    /**
     * 抓鬼记录
     */
    public HalloweenVO halloweenRecord(String activityId, String uid, int page, int type) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        String recordListKey = type == 0 ? getHistoryRecordListKey(activityId, uid) : getExchangeHistoryRecordListKey(activityId, uid);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        HalloweenVO drawRecordVO = new HalloweenVO();
        List<PrizeConfigV2VO> drawRecordList = new ArrayList<>();
        for (String item : prizeKeyTimeList) {
            PrizeConfigV2VO drawRecord = JSON.parseObject(item, PrizeConfigV2VO.class);
            drawRecordList.add(drawRecord);
        }
        drawRecordVO.setRecordList(drawRecordList);
        if (drawRecordList.size() < RECORD_PAGE_SIZE) {
            drawRecordVO.setNextUrl(-1);
        } else {
            drawRecordVO.setNextUrl(page + 1);
        }
        return drawRecordVO;
    }

    /**
     * 兑换奖品
     */
    public HalloweenVO halloweenExchange(String activityId, String uid, String metaId) {
        checkActivityTime(activityId);
        // 查询中奖道具
        ResourceKeyConfigData itemConfig = resourceKeyConfigDao.findByKey(HALLOWEEN_EXCHANGE_KEY);
        if (itemConfig == null) {
            logger.error("halloweenExchange not find metaId:{}", metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        Map<String, ResourceKeyConfigData.ResourceMeta> itemConfigMap = itemConfig.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        ResourceKeyConfigData.ResourceMeta resMeta = itemConfigMap.get(metaId);
        if (resMeta == null) {
            logger.error("halloweenExchange not find resMeta metaId:{}", metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        HalloweenVO vo = new HalloweenVO();
        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

            int leftCandie = userDataMap.getOrDefault(DAILY_LEFT_CANDIE, 0);
            if (leftCandie <= 0) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            int reduceNum = Integer.parseInt(resMeta.getRateNumber());
            int afterNum = activityCommonRedis.incCommonHashNum(hashActivityId, DAILY_LEFT_CANDIE, -reduceNum);
            resourceKeyHandlerService.sendOneResourceData(actorData, resMeta, 905, ACTIVITY_EXCHANGE, ACTIVITY_EXCHANGE, ACTIVITY_EXCHANGE, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);

            int currentTime = DateHelper.getNowSeconds();
            List<PrizeConfigV2VO> drawRecordList = new ArrayList<>();
            vo.setLeftCandie(afterNum);
            PrizeConfigV2VO record = new PrizeConfigV2VO();
            BeanUtils.copyProperties(resMeta, record);
            record.setUid(uid);
            record.setCtime(currentTime);
            drawRecordList.add(record);
            activityCommonRedis.addCommonListData(getExchangeHistoryRecordListKey(activityId, uid), JSON.toJSONString(record));
            vo.setRecordList(drawRecordList);
        }
        return vo;
    }


    @Override
    public void dailyTaskRun(String dateStr) {
        try {
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if (activityData == null) {
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }
            if (dateStr == null) {
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            }
            logger.info("dailyTaskRun Happy Halloween!");
            List<String> rankingList = activityCommonRedis.getCommonRankingList(getDailyRankKey(ACTIVITY_ID, dateStr), 50);
            String hashDailyActivityId = getHashDailyActivityId(ACTIVITY_ID, dateStr);
            Map<String, Integer> everyDataMap = activityCommonRedis.getCommonHashAll(hashDailyActivityId);
            int poolDiamond = everyDataMap.getOrDefault(DAILY_TOTAL_DIAMOND, DAILY_DEFAULT_TOTAL_DIAMOND);
            int rank = 1;
            for (String rankUid : rankingList) {
                int rewardDiamond = 0;
                if (rank == 1) {
                    rewardDiamond = (int) (poolDiamond * 0.15);
                } else if (rank == 2) {
                    rewardDiamond = (int) (poolDiamond * 0.115);
                } else if (rank == 3) {
                    rewardDiamond = (int) (poolDiamond * 0.09);
                } else if (rank >= 4 && rank <= 5) {
                    rewardDiamond = (int) (poolDiamond * 0.06);
                } else if (rank >= 6 && rank <= 10) {
                    rewardDiamond = (int) (poolDiamond * 0.04);
                } else if (rank >= 11 && rank <= 15) {
                    rewardDiamond = (int) (poolDiamond * 0.02);
                } else if (rank >= 16 && rank <= 25) {
                    rewardDiamond = (int) (poolDiamond * 0.01);
                } else {
                    rewardDiamond = (int) (poolDiamond * 0.005);
                }

                if (rewardDiamond > 0) {
                    MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                    moneyDetailReq.setRandomId();
                    moneyDetailReq.setUid(rankUid);
                    moneyDetailReq.setAtype(905);
                    moneyDetailReq.setChanged(rewardDiamond);
                    moneyDetailReq.setTitle(ACTIVITY_TITLE);
                    moneyDetailReq.setDesc(ACTIVITY_TITLE);
                    mqSenderService.asyncChargeDiamonds(moneyDetailReq);
                }
                rank++;
            }

        } catch (Exception e) {
            logger.error("distribution Happy Halloween error: {}", e.getMessage(), e);
        }
    }
}
