package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventReport;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.FataPlazaDTO;
import com.quhong.data.vo.TreeHoleVO;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IFriendService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendApplyDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.FriendApplyData;
import com.quhong.mongo.data.LightweightActorData;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.FriendsListRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.redis.UserOnlineRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.MatchUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 树洞活动
 */
@Service
public class TreeHoleService extends OtherActivityService  {


    private static final Logger logger = LoggerFactory.getLogger(TreeHoleService.class);
    private static final String ACTIVITY_TITLE_EN = "Tree Hole";
    private static final String ACTIVITY_TITLE_AR = "Tree Hole";
    public static String ACTIVITY_ID = "tree_hole";

    private static String ACTIVITY_URL = String.format("https://static.youstar.live/tree_hole/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";


    private static final List<String> AREA_LIST = Arrays.asList("sa", "kw", "ae", "qa", "om", "bh", "us");
    private static final int AREA_1 = 1;
    private static final int AREA_2 = 2;

    // 常量定义

    private static final int MAX_FRIEND_REQUESTS_PER_DAY = 5;
    private static final int FRIEND_REQUEST_COUNT = 10;
    private static final int REGISTER_DAYS_LIMIT = 30;
    private static final int HOURS_24 = 24;

    private static final int ALL_GENDER = 3;

    private static final String FILED_RECEIVE_NOTE_COUNT = "filed_receive_note_count";
    private static final String FILED_SEND_NOTE_COUNT = "filed_send_note_count";
    private static final String FILED_SEND_GREET_COUNT = "filed_send_greet_count";
    private static final String FILED_DEVIVE_SEND_COUNT = "filed_devive_send_count";




    private static final Interner<String> stringPool = Interners.newWeakInterner();




    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private EventReport eventReport;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private IFriendService ifriendService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private DAUDao dauDao;
    @Resource
    private FriendApplyDao friendApplyDao;
    @Resource
    private IDetectService idetectService;
    //    @Resource(name = AsyncConfig.ASYNC_TASK)
//    private Executor executor;
    @Resource
    private TreeHoleService treeHoleService;
    @Resource
    private BasePlayerRedis basePlayerRedis ;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "tree_hole";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/tree_hole/?activityId=%s", ACTIVITY_ID);
        }
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:tree_hole:user:" + uid;
    }

    private String getLocalSendNoticeKey(String uid) {
        return "lock:tree_hole:user:send:notice:" + uid;
    }

    private String getLocalReplyKey(String uid) {
        return "lock:tree_hole:user:reply:" + uid;
    }

    /**
     * 获取纸条列表ActivityId (用于Hash存储)
     */
    private String getNoteListActivityId(String activityId) {
        return String.format("tree_hole_notes_%s", activityId);
    }

    /**
     * 获取广场纸条ActivityId (用于ZSet存储)
     */
    private String getSquareNoteActivityId(String activityId) {
        return String.format("tree_hole_square_%s", activityId);
    }

    /**
     * 获取广场纸条-地区-性别ActivityId (用于ZSet存储)
     */
    private String getSquareNoteAreaGender(String activityId, int area, int gender) {
        return String.format("tree_hole_square_%s_%s_%s", activityId, area, gender);
    }

    /**
     * 获取用户每日任务配置限制ActivityId (用于Hash存储)
     */
    private String getDailyConfigLimitActivityId(String activityId, String uid, String dateStr) {
        return String.format("tree_hole_daily_limit_%s_%s_%s", activityId, dateStr, uid);
    }

    /**
     * 获取每日任务数据ActivityId (用于Hash存储)
     */
    private String getDailyHashActivityId(String activityId, String dateStr) {
        return String.format("tree_hole_daily_task_%s_%s", activityId, dateStr);
    }


    /**
     * 获取ActivityId (用于set存储)
     */
    private String getAllJoinActivityId(String activityId) {
        return String.format("tree_hole_all_join_%s", activityId);
    }

    /**
     * 获取友谊任务ActivityId (用于Hash存储)
     */
    private String getFriendshipTaskActivityId(String activityId) {
        return String.format("tree_hole_friendship_task_%s", activityId);
    }

    /**
     * 交友纸条列表
     *
     * @param activityId
     * @param uid
     * @return
     */
    public TreeHoleVO noteList(String activityId, String uid, int page) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        TreeHoleVO vo = new TreeHoleVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        page = page <= 0 ? 1 : page;
        List<TreeHoleVO.NoteInfoVO> noteInfoList = getNoteInfoList(activityId, uid, page);
        TreeHoleVO.TreeHoleListVO listVO = new TreeHoleVO.TreeHoleListVO(noteInfoList, noteInfoList.size() == 0 ? -1 : page + 1);
        vo.setNoteInfoList(listVO);
        return vo;
    }

    //    @Cacheable(value = "cache", key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<TreeHoleVO.NoteInfoVO> getNoteInfoList(String activityId, String uid, int page) {
        Set<String> myFriendSet = friendsListRedis.getFriendList(uid);
        // 获取在线用户
        Set<String> onlineUsers = userOnlineRedis.getAllUserOnline(); // 最近1小时在线
        List<TreeHoleVO.NoteInfoVO> noteInfoList = new ArrayList<>();
        try {
            // 获取当前用户信息
            ActorData currentUser = actorDao.getActorDataFromCache(uid);
            if (currentUser == null) {
                logger.error("获取用户信息失败, uid={}", uid);
                return noteInfoList;
            }

            // 获取最近24小时的广场纸条
            long currentTime = DateHelper.getNowSeconds();
            long startTime = currentTime - HOURS_24 * 3600;
            int pageSize = 20;

            int start = (page - 1) * pageSize;
            int end = start + pageSize;

//            String squareActivityId = getSquareNoteActivityId(activityId);
//            Map<String, Integer> noteIdsWithScore = activityCommonRedis.getCommonRankingMapByPage(
//                    squareActivityId, start, end);
//            Map<String, Integer> noteIdsWithScore = activityCommonRedis.getOtherRankingMapByScoreAPage(squareActivityId,
//                    (int) startTime, (int) currentTime, start, pageSize);

            int filterArea = AREA_LIST.contains(ActorUtils.getCountryCode(currentUser.getCountry())) ? AREA_1 : AREA_2;
            // int filterGender = currentUser.getFb_gender() == 1 ? 2 : 1;
            String squareAreaGenderActivityId = getSquareNoteAreaGender(activityId, filterArea, ALL_GENDER);

            Map<String, Integer> noteIdsWithScore = activityCommonRedis.getOtherRankingMapByScoreAPage(squareAreaGenderActivityId,
                    (int) startTime, (int) currentTime, start, pageSize);


            if (CollectionUtils.isEmpty(noteIdsWithScore)) {
                return noteInfoList;
            }

            String noteListActivityId = getNoteListActivityId(activityId);
//            int count = 0;
            for (String noteId : noteIdsWithScore.keySet()) {
                try {
                    // 从Redis Hash获取纸条信息
//                    String noteInfoJson = activityCommonRedis.getCommonHashStrValue(noteListActivityId, noteId);
                    String noteInfoJson = cacheDataService.getCommonHashStrValue(noteListActivityId, noteId);
                    if (StringUtils.isEmpty(noteInfoJson)) {
                        continue;
                    }

                    TreeHoleVO.NoteInfo noteInfo = JSONObject.parseObject(noteInfoJson, TreeHoleVO.NoteInfo.class);
                    if (noteInfo == null) {
                        continue;
                    }

                    // 获取发送者信息
                    ActorData senderData = actorDao.getActorDataFromCache(noteInfo.getUid());
                    if (senderData == null) {
                        continue;
                    }

                    // 过滤条件：非好友
                    if (!shouldShowNote(currentUser, senderData, myFriendSet)) {
                        continue;
                    }

                    // 构建NoteInfoVO
                    TreeHoleVO.NoteInfoVO noteInfoVO = buildNoteInfoVO(noteInfo, senderData, uid, onlineUsers);
                    if (noteInfoVO != null) {
                        noteInfoList.add(noteInfoVO);
                    }
                } catch (Exception e) {
                    logger.error("处理纸条信息失败, noteId={}, error={}", noteId, e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            logger.error("获取纸条列表失败, activityId={}, uid={}, error={}", activityId, uid, e.getMessage(), e);
        }

        return noteInfoList;
    }

    /**
     * 发送小纸条
     */
    public void sendNote(FataPlazaDTO reqDTO) {
        String activityId = reqDTO.getActivityId();
        String uid = reqDTO.getUid();
        String content = reqDTO.getContent();
        int isSendSquare = reqDTO.getIsSendSquare();
        String dateStr = getDayByBase(activityId, uid);

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);

        if (StringUtils.isEmpty(content) || content.length() > 100) {
            logger.error("内容为空或过长, content={}", content);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        ActorData aidInfo = basePlayerRedis.getActorFromRedis(uid);
        if (aidInfo != null && aidInfo.getGeneralConfActorData() != null && aidInfo.getGeneralConfActorData().getBuddy() != 0) {
            logger.info("you have set refuse to add friends. uid={}", uid);
            throw new CommonException(ActivityHttpCode.SET_REFUSE_TO_ADD_FRIENDS);
        }
        String tnId = aidInfo.getTn_id();
        if(StringUtils.isEmpty(tnId)){
            logger.info("tnId is empty. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }

        // 脏词检测
        if (idetectService.detectText(new TextDTO(content, "activity")).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }

        synchronized (stringPool.intern(getLocalSendNoticeKey(uid))) {
            // 检查是否达到发送限制
            if (getDailyCount(activityId, uid, dateStr, FILED_SEND_NOTE_COUNT) >= 1) {
                logger.info("发送纸条失败, 已达到发送限制, activityId={}, uid={}", activityId, uid);
                throw new CommonH5Exception(ActivityHttpCode.SEND_NOTE_COUNT_MAX);
            }

            if (getDailyCount(activityId, tnId, dateStr, FILED_DEVIVE_SEND_COUNT) >= 2) {
                logger.info("发送纸条失败, 已达到设备发送限制, activityId={}, uid={} tnId={}", activityId, uid,tnId);
                throw new CommonH5Exception(ActivityHttpCode.SEND_NOTE_DEVICE_COUNT_MAX);
            }


            String noteId = new ObjectId().toString();
            int publishTime = DateHelper.getNowSeconds();

            // 创建纸条信息
            TreeHoleVO.NoteInfo noteInfo = new TreeHoleVO.NoteInfo();
            noteInfo.setNoteId(noteId);
            noteInfo.setUid(uid);
            noteInfo.setContent(content);
            noteInfo.setPublishTime(publishTime);

            // 存储到Redis Hash
            String noteListActivityId = getNoteListActivityId(activityId);
            String noteInfoJson = JSONObject.toJSONString(noteInfo);
            activityCommonRedis.setCommonHashData(noteListActivityId, noteId, noteInfoJson);

            // 如果发送到广场，存储到Redis ZSet
            if (isSendSquare == 1) {
                // String squareActivityId = getSquareNoteActivityId(activityId);
                // activityCommonRedis.addCommonZSetRankingScore(squareActivityId, noteId, publishTime);

                ActorData actorData = actorDao.getActorDataFromCache(uid);
                int area = AREA_LIST.contains(ActorUtils.getCountryCode(actorData.getCountry())) ? AREA_1 : AREA_2;
                // int gender = actorData.getFb_gender() == 2 ? 2 : 1;
                String squareNoteAreaGenderActivityId = getSquareNoteAreaGender(activityId, area, ALL_GENDER);
                activityCommonRedis.addCommonZSetRankingScore(squareNoteAreaGenderActivityId, noteId, publishTime);

                // 更新每日任务数据
                // updateDailyTaskSendNote(activityId, uid, dateStr);
            }
            incrementDailyCount(activityId, uid, dateStr, FILED_SEND_NOTE_COUNT);
            incrementDailyCount(activityId, tnId, dateStr, FILED_DEVIVE_SEND_COUNT);
            logger.info("发送纸条成功, activityId={}, uid={}, noteId={}, isSendSquare={}",
                    activityId, uid, noteId, isSendSquare);
        }
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {

        } else {
            // 非灰度测试发送异步发送好友申请
            sendFriendRequestsAsync(activityId, uid, content);
        }

    }

    /**
     * 打招呼
     */
    public void greet(FataPlazaDTO reqDTO) {
        String activityId = reqDTO.getActivityId();
        String uid = reqDTO.getUid();
        String aid = reqDTO.getAid();
        String msg = reqDTO.getContent();
        String dateStr = getDayByBase(activityId, uid);
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        if (StringUtils.isEmpty(msg) || msg.length() > 100) {
            logger.error("内容为空或过长, content={}", msg);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (idetectService.detectText(new TextDTO(msg, DetectOriginConstant.ACTIVITY_RELATED)).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }
        if (StringUtils.isEmpty(aid)) {
            logger.error("打招呼失败, aid为空, activityId={}, uid={}", activityId, uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        int maxLimit = actorData.getFb_gender() == 2 ? 100 : 5;
        synchronized (stringPool.intern(getLocalReplyKey(uid))) {
            // 检查是否达到发送限制
            if (getDailyCount(activityId, uid, dateStr, FILED_SEND_GREET_COUNT) >= maxLimit) {
                throw new CommonH5Exception(ActivityHttpCode.SEND_GREET_COUNT_MAX);
            }
            FriendApplyData friendApplyData = friendApplyDao.findData(uid, aid);
            if (friendApplyData != null && friendApplyData.getOptType() == 0) {
                logger.info("打招呼失败, 已经发送过打招呼, activityId={}, uid={}, aid={}", activityId, uid, aid);
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_APPLIED);
            }
            // 发送好友申请
            addFriendApply(activityId, uid, aid, msg, false);
//        cacheDataService.delSetCache(getAllJoinActivityId(activityId), 2);
            // 更新每日任务数据
            // updateDailyTaskGreet(activityId, uid, aid, dateStr);
            // 增加发送计数
            incrementDailyCount(activityId, uid, dateStr, FILED_SEND_GREET_COUNT);

        }


        logger.info("打招呼成功, activityId={}, uid={}, aid={}", activityId, uid, aid);
    }




    private boolean checkAc(String uid, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            boolean isWhiteTestAid = whiteTestDao.isMemberByType(mqData.getAid(), WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest && !isWhiteTestAid) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return false;
        }
        return true;
    }




    // 发送好友请求
    private void addFriendApply(String activityId, String uid, String aid, String msg, boolean isBack) {
        ApiResult<List<String>> msgFriendList = ifriendService.addFriendApply(uid, aid, msg);
        if (msgFriendList.isOk()) {
            List<String> msgListData = msgFriendList.getData();
            if (!CollectionUtils.isEmpty(msgListData)) {
                if (isBack) {
                    logger.info("添加好友申请失败, uid={}, aid={}, error={}", uid, aid, msgListData.get(0));
                    return;
                }
                logger.info("添加好友申请失败, uid={}, aid={}, error={}", uid, aid, msgListData.get(0));
                throw new CommonH5Exception(new HttpCode(72, msgListData.get(0),
                        StringUtils.isEmpty(msgListData.get(1)) ? msgListData.get(0) : msgListData.get(1)));
            } else {
                String allJoinKey = getAllJoinActivityId(activityId);
                activityCommonRedis.addCommonSetData(allJoinKey, MatchUtils.generateFriendIndex(uid, aid));
                logger.info("添加好友申请成功, uid={}, aid={}", uid, aid);
            }
        } else {
            logger.error("添加好友申请失败, uid={}, aid={}, error={}", uid, aid, msgFriendList.getCode().getMsg());
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }

    }

    private void sendOfficialMsg(List<String> titleList, List<String> bodyList, String item) {
        ActorData actorData = actorDao.getActorDataFromCache(item);
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleList.get(1) : titleList.get(0);
        String body = slang == SLangType.ARABIC ? bodyList.get(1) : bodyList.get(0);
        commonOfficialMsg(item, "", 0, 0, "",
                title, body, ACTIVITY_URL);
    }


    /**
     * 判断是否应该显示纸条
     */
    public boolean shouldShowNote(ActorData currentUser, ActorData senderData, Set<String> myFriendSet) {
        if (currentUser.getUid().equals(senderData.getUid())) {
            return false;
        }

        // 检查是否已是好友
        if (myFriendSet.contains(senderData.getUid())) {
            return false;
        }

        return true;
    }

    /**
     * 构建NoteInfoVO对象
     */
    private TreeHoleVO.NoteInfoVO buildNoteInfoVO(TreeHoleVO.NoteInfo noteInfo, ActorData senderData, String currentUid, Set<String> onlineUsers) {
        try {
            TreeHoleVO.NoteInfoVO noteInfoVO = new TreeHoleVO.NoteInfoVO();
            Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
            String countryCode = ActorUtils.getCountryCode(senderData.getCountry());
            noteInfoVO.setNoteId(noteInfo.getNoteId());
            noteInfoVO.setUid(senderData.getUid());
            noteInfoVO.setName(senderData.getName());
            noteInfoVO.setHead(ImageUrlGenerator.generateRoomUserUrl(senderData.getHead()));
            noteInfoVO.setGender(senderData.getFb_gender() == 1 ? 1 : 2);
            noteInfoVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
//            noteInfoVO.setStarSign(getConstellation(senderData.getBirthday()));
            noteInfoVO.setContent(noteInfo.getContent());
            noteInfoVO.setPublishTime(noteInfo.getPublishTime());

            // 获取用户标签（随机3个）
//            List<UserLabelData> labelList = getUserLabels(senderData);
//            noteInfoVO.setLabelList(labelList);


            // 检查打招呼状态（是否有好友申请）
            int greetStatus = checkGreetStatus(currentUid, senderData.getUid());
            noteInfoVO.setStatus(0);

            return noteInfoVO;

        } catch (Exception e) {
            logger.error("构建NoteInfoVO失败, noteId={}, error={}", noteInfo.getNoteId(), e.getMessage(), e);
            return null;
        }
    }



    /**
     * 检查打招呼状态
     */
    private int checkGreetStatus(String uid, String aid) {
        try {
            FriendApplyData data = friendApplyDao.findData(uid, aid);
            if (data != null && data.getOptType() == 0) {
                return 1;
            }
            return 0;
        } catch (Exception e) {
            logger.error("检查打招呼状态失败, uid={}, aid={}, error={}", uid, aid, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 异步发送好友申请
     */
    public void sendFriendRequestsAsync(String activityId, String uid, String content) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    logger.info("开始异步发送好友申请, activityId={}, uid={}", activityId, uid);


                    // 获取符合条件的用户列表
                    List<String> targetUsers = getTargetUsersForFriendRequest(uid);
                    if (CollectionUtils.isEmpty(targetUsers)) {
                        logger.info("没有找到符合条件的用户, uid={}", uid);
                        return;
                    }

                    String dateStr = getDayByBase(activityId, uid);
                    int successCount = 0;
                    Set<String> myFriendSet = friendsListRedis.getFriendList(uid);
                    for (String targetUid : targetUsers) {
                        try {
                            // 检查目标用户今日接收申请数量
                            if (getDailyCount(activityId, targetUid, dateStr, FILED_RECEIVE_NOTE_COUNT) >= MAX_FRIEND_REQUESTS_PER_DAY) {
                                continue;
                            }
                            // 检查是否已是好友
//                        if (friendsListRedis.isFriend(uid, targetUid)) {
//                            continue;
//                        }
                            if (myFriendSet.contains(targetUid)) {
                                continue;
                            }

                            // 检查是否已经发送过好友申请
                            FriendApplyData data = friendApplyDao.findData(uid, targetUid);
                            if (data != null && data.getOptType() == 0) {
                                continue;
                            }
                            // 发送好友申请
                            addFriendApply(activityId, uid, targetUid, content, true);
                            // 增加目标用户接收计数
                            incrementDailyCount(activityId, targetUid, dateStr, FILED_RECEIVE_NOTE_COUNT);

                            successCount++;

                            // 限制发送数量为10个
                            if (successCount >= FRIEND_REQUEST_COUNT) {
                                break;
                            }

                        } catch (Exception e) {
                            logger.error("发送好友申请失败, uid={}, targetUid={}, error={}", uid, targetUid, e.getMessage(), e);
                        }
                    }
//                cacheDataService.delSetCache(getAllJoinActivityId(activityId), 2);
                    logger.info("异步发送好友申请完成, uid={}, 成功发送数量={}", uid, successCount);

                } catch (Exception e) {
                    logger.error("异步发送好友申请异常, activityId={}, uid={}, error={}", activityId, uid, e.getMessage(), e);
                }

            }
        });
//        executor.execute(() -> {
//            });
    }


    /**
     * @deprecated 此方法存在内存问题，请使用 getNewUserActorListByPage 替代
     */
    @Deprecated
    @Cacheable(value = "cache", key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<MongoActorData> getNewUserActorList(int startDay, int endDay, int gender) {
        logger.info("使用已废弃的getNewUserActorList方法，建议使用分页版本");
        return actorDao.getNewUserActorListByPage(startDay, endDay, gender, 0, 10000); // 限制500条并缓存时间缩短
    }

    /**
     * 优化版本：分页获取新用户列表，避免内存问题
     */
    @Cacheable(value = "cache", key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<LightweightActorData> getNewUserActorListByPage(int startDay, int endDay, int gender, int page, int pageSize) {
        return actorDao.getLightweightNewUserActorListByPage(startDay, endDay, gender, page, pageSize);
    }

    /**
     * 获取符合条件的目标用户
     */

    public List<String> getTargetUsersForFriendRequest(String uid) {
        try {
            // 获取发送者信息
            ActorData senderData = actorDao.getActorDataFromCache(uid);
            if (senderData == null) {
                logger.error("获取发送者信息失败, uid={}", uid);
                return Collections.emptyList();
            }
            //  获取在线用户
            Set<String> onlineUsers = userOnlineRedis.getAllUserOnline(); // 最近3分钟在线
            // 获取最近3天活跃用户
//            Set<String> last3DayUsers = dauDao.getActiveUserSet(3);

            // 获取最近3天新增异性，使用分页版本避免内存问题
            int days = ServerConfig.isNotProduct() ? 30 : 3;
            List<LightweightActorData> last3DayUsers = treeHoleService.getNewUserActorListByPage(days, 0, -1, 0, 10000);

//            List<String> candidateUsers = new ArrayList<>(last3DayUsers);

            // 过滤条件
            List<String> filteredUsers = last3DayUsers.stream()
                    .filter(item -> isValidTargetUser(item, senderData))
                    .map(item -> item.get_id().toString())
                    .collect(Collectors.toList());

            // Collections.shuffle(filteredUsers);
            // return filteredUsers;

            // 在线用户优先
            List<String> result = new ArrayList<>();
            List<String> onlineTargets = filteredUsers.stream()
                    .filter(onlineUsers::contains)
                    .collect(Collectors.toList());
//
            Collections.shuffle(onlineTargets);
            result.addAll(onlineTargets);

//            // 如果在线用户不够，添加离线用户
            List<String> offlineTargets = filteredUsers.stream()
                    .filter(itemUid -> !onlineUsers.contains(itemUid))
                    .collect(Collectors.toList());
            Collections.shuffle(offlineTargets);
            result.addAll(offlineTargets);
            return result;

        } catch (Exception e) {
            logger.error("获取目标用户失败, senderUid={}, error={}", uid, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查是否是有效的目标用户
     */
    private boolean isValidTargetUser(LightweightActorData targetData, ActorData senderData) {
        String targetUid = targetData.get_id().toString();
        try {
//            ActorData targetData = actorDao.getActorDataFromCache(targetUid);
            if (targetData == null) {
                return false;
            }
            if (targetUid.equals(senderData.getUid())) {
                return false;
            }
            // 检查注册时间（0-30天）
//            int regDays = ActorUtils.getRegDays(targetUid);
//            if (regDays > REGISTER_DAYS_LIMIT) {
//                return false;
//            }

            // 检查地区是否相同
            int targetArea = AREA_LIST.contains(ActorUtils.getCountryCode(targetData.getCountry())) ? AREA_1 : AREA_2;
            int senderArea = AREA_LIST.contains(ActorUtils.getCountryCode(senderData.getCountry())) ? AREA_1 : AREA_2;
            if (targetArea != senderArea) {
                return false;
            }

            // 检查是否异性
//            int senderGender = senderData.getFb_gender() == 1 ? 1 : 2;
//            int targetGender = targetData.getFb_gender() == 1 ? 1 : 2;
//            if (senderGender == targetGender) {
//                return false;
//            }

            return true;

        } catch (Exception e) {
            logger.error("检查目标用户有效性失败, targetUid={}, error={}", targetUid, e.getMessage(), e);
            return false;
        }
    }


    private int getDailyCount(String activityId, String uid, String dateStr, String hashFiled) {
        try {
            String dailyActivityId = getDailyConfigLimitActivityId(activityId, uid, dateStr);
            int count = activityCommonRedis.getCommonHashValue(dailyActivityId, hashFiled);
            return count;
        } catch (Exception e) {
            logger.error("检查每日限制失败, uid={}, error={}", uid, e.getMessage(), e);
            return 0;
        }
    }


    private void incrementDailyCount(String activityId, String uid, String dateStr, String hashFiled) {
        try {
            String dailyActivityId = getDailyConfigLimitActivityId(activityId, uid, dateStr);
            activityCommonRedis.incCommonHashNum(dailyActivityId, hashFiled, 1);
        } catch (Exception e) {
            logger.error("增加每日接收计数失败, uid={}, error={}", uid, e.getMessage(), e);
        }
    }







    /**
     * 处理资源发放（带埋点标题）
     */
    private void handleRes(String uid, String resKey, String eventTitle) {
        try {
            resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
            logger.info("友谊任务资源发放成功, uid={}, resKey={}, eventTitle={}", uid, resKey, eventTitle);
        } catch (Exception e) {
            logger.error("友谊任务资源发放失败, uid={}, resKey={}, error={}", uid, resKey, e.getMessage(), e);
        }
    }

}
