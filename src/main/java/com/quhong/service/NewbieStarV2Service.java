package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityApplicationEvent;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.vo.NewbieStarVO;
import com.quhong.data.vo.PrizeConfigV2VO;
import com.quhong.data.vo.ResKeyConfigVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.MsgType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMsgService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.FollowDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mongo.data.*;
import com.quhong.monitor.MonitorSender;
import com.quhong.redis.DauUserRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class NewbieStarV2Service extends OtherActivityService implements TaskMsgHandler {

    private static final Logger logger = LoggerFactory.getLogger(NewbieStarV2Service.class);
    public static final String ACTIVITY_ID = "672c7f7df3d1fef5319d2893";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/welcome_star/?activityId=%s", ACTIVITY_ID): String.format("https://test2.qmovies.tv/welcome_star/?activityId=%s", ACTIVITY_ID);
    private static final String NEW_BIE_TITLE_EN = "Welcome New House2";
    private static final String NEW_BIE_TITLE_EN_DAILY = "Welcome New House2-daily task";
    private static final String NEW_BIE_TITLE_EN_ON_MIC = "Welcome New House2-Official-effective invite on mic";
    private static final String NEW_BIE_TASK_FINISH = "taskFinishStatus";   // 任务完成状态
    private static final String NEW_BIE_FORMAL_TASK_START = "formalTaskStart";   // 正式任务开始日期

    private static final Integer NEW_BIE_MAX_NUM_PRE = 3;
    private static final Integer NEW_BIE_MAX_NUM = 7;
    private static final String NEW_BIE_CONTINUE_NUM_PRE = "continueNumPre";   // 预播连续完成天数
    private static final String NEW_BIE_CONTINUE_NUM = "continueNum";          // 正播连续完成天数
    private static final String NEW_BIE_CONTINUE_DAY_PRE = "continueDayPre";   // 预播设置完成天
    private static final String NEW_BIE_CONTINUE_DAY = "continueDay";          // 正播设置完成天

    private static final String LIVE_START = "start";
    private static final String LIVE_END = "end";
    private static final String LIVE_ALARM = "alarm";

    // 开播提醒
    private static final String LIVE_SET_WEEK = "setWeek";
    private static final String LIVE_START_HMS = "startHMS";
    private static final String LIVE_END_HMS = "endHMS";
    private static final String LIVE_ALARM_STATUS = "alarmStatus";
    // 房主开播上麦时长
    private static final String DAILY_LIVE_ON_MIC_TIME = "liveOnMicTime";
    private static final Integer DAILY_ON_MIC_TIME_MAX = 90;
    private static final String DAILY_LIVE_ON_MIC_REWARD = "liveOnMicTimeReward";

    private static final Integer RECORD_PAGE_SIZE = 30;
    private static final String CUSTOMER_ID = ServerConfig.isProduct() ? "5ac220d61bad4898c89dad7e" : "655c4c24b661b86b85455f3b";
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<TaskConfigVO> SIGN_TASK_LIST = new ArrayList<>();
    private static final List<TaskConfigVO> TRY_LIVE_TASK_LIST = new ArrayList<>();
    private static final List<String> TRY_LIVE_RES_KEY = Arrays.asList("welcomeStarTryLiveDay1", "welcomeStarTryLiveDay2", "welcomeStarTryLiveDay3");

    private static final List<TaskConfigVO> BASE_LIVE_TASK_LIST = new ArrayList<>();
    private static final List<String> BASE_LIVE_RES_KEY = Arrays.asList("welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay3", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay7");
    private static final List<TaskConfigVO> HIGH_LIVE_TASK_LIST = new ArrayList<>();

    // 在房间上麦任务key
    private static final String DAILY_NEW_USER_ON_MIC = "newUserOnMicNum";
    private static final Integer DAILY_NEW_USER_ON_MIC_MAX = 30;
    private static final Integer DAILY_NEW_USER_ON_MIC_TIME = 5;
    private static final List<TaskConfigVO> NEW_USER_ON_MIC_TASK_LIST = new ArrayList<>();

    // 每日发放奖励余额
    private static final String DAILY_DEVOTE_LEFT_KEY = "devoteLeftNum";
    private static final Integer DAILY_DEVOTE_NUM = 3;

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(CommonMqTaskConstant.ON_MIC_TIME, CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL, CommonMqTaskConstant.INVITE_USER_ACTION,
            CommonMqTaskConstant.FOLLOW_USER, CommonMqTaskConstant.ADD_FRIEND);

    static {
        SIGN_TASK_LIST.add(new TaskConfigVO(1, 0, "sign_sex", "", "فقط للمستخدمين الإناث", "فقط للمستخدمين الإناث", null, null, null, null, null));
        SIGN_TASK_LIST.add(new TaskConfigVO(30, 0, "register_day", "", "التسجيل أكثر من 30 يوما", "التسجيل أكثر من 30 يوما", null, null, null, null, null));
        SIGN_TASK_LIST.add(new TaskConfigVO(50, 0, "follow_num", "", "عدد المتابعين أكثر من 50", "عدد المتابعين أكثر من 50", null, null, null, null, null));
        SIGN_TASK_LIST.add(new TaskConfigVO(3, 0, "live_daily", "", "أيام الاتصال بالاونلاين ≥ 3 أيام في آخر 7 أيام", "أيام الاتصال بالاونلاين ≥ 3 أيام في آخر 7 أيام", null, null, null, null, null));

        // 试播任务
        TRY_LIVE_TASK_LIST.add(new TaskConfigVO(60, 0, "on_mic_time", "", "", "افتح غرفتك واجلس على الميكروفون لمدة 60 دقيقة في غرفة الصوت الخاصة بك", null, null, null, null, null));
        TRY_LIVE_TASK_LIST.add(new TaskConfigVO(5, 0, "invite_user_on_mic_all", "", "", "إرسال طلبات دعوة الميكروفون بشكل نشط إلى 5 مستخدمًا جديدًا", null, null, null, null, null));
        TRY_LIVE_TASK_LIST.add(new TaskConfigVO(3, 0, "on_mic_time_other", "", "", "قم بدعوة 3 مستخدمين جدد لإكمال وقت الميكروفون الفعال في غرفتك  \n (وقت الميكروفون ≥ 3 دقائق)", null, null, null, null, null));

        // 正式任务
        BASE_LIVE_TASK_LIST.add(new TaskConfigVO(90, 0, "on_mic_time", "", "", "افتح غرفتك واجلس على الميكروفون لمدة 90 دقيقة في غرفة الصوت الخاصة بك", null, null, null, null, null));
        BASE_LIVE_TASK_LIST.add(new TaskConfigVO(10, 0, "invite_user_on_mic_all", "", "", "إرسال طلبات دعوة الميكروفون بشكل نشط إلى 10 مستخدمًا جديدًا", null, null, null, null, null));
        BASE_LIVE_TASK_LIST.add(new TaskConfigVO(5, 0, "on_mic_time_other", "", "", "قم بدعوة 5 مستخدمين جدد لإكمال وقت الميكروفون الفعال في غرفتك \n (وقت الميكروفون ≥ 5 دقائق)", null, null, null, null, null));

        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(2, 0, "high_follow_user", "", "Welcome New House2-Official-follow user", "احصل على مستخدم جديد يتابعك كل يوم؛", null, null, null, null, "welcomeStarAdvanceTask1"));
        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(2, 0, "high_add_friend", "", "Welcome New House2-Official-become friends ", "كن صديقًا لمستخدم واحد جديد كل يوم؛", null, null, null, null, "welcomeStarAdvanceTask2"));
        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(10, 0, "high_receive_rose_gift", "", "Welcome New House2-Official-receive rose gifts", "احصل على 10 ورود من المستخدمين الجدد؛", null, null, null, null, "welcomeStarAdvanceTask3"));
        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(1, 0, "high_on_mic_time_other", "", "Welcome New House2-Official-invite female friend to complete mic time", "قم بدعوة مستخدمة واحدة إلى الميكروفون وتتجاوز مدة الميكروفون 15 دقيقة؛(يجب ان تكون صديقتك )", null, null, null, null, "welcomeStarAdvanceTask4"));

        // 新用户上麦任务
        NEW_USER_ON_MIC_TASK_LIST.add(new TaskConfigVO(10, 0, "new_user_on_mic_10", "", "", "", null, null, null, null, "welcomeStarEffectiveMic10"));
        NEW_USER_ON_MIC_TASK_LIST.add(new TaskConfigVO(15, 0, "new_user_on_mic_15", "", "", "", null, null, null, null, "welcomeStarEffectiveMic15"));
        NEW_USER_ON_MIC_TASK_LIST.add(new TaskConfigVO(20, 0, "new_user_on_mic_20", "", "", "", null, null, null, null, "welcomeStarEffectiveMic20"));
        NEW_USER_ON_MIC_TASK_LIST.add(new TaskConfigVO(30, 0, "new_user_on_mic_30", "", "", "", null, null, null, null, "welcomeStarEffectiveMic30"));


    }


    @Resource
    private FollowDao followDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private DauUserRedis dauUserRedis;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private IMsgService iMsgService;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private BasePlayerRedis basePlayerRedis;

    // 抽奖相关的每日key
    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("newbie:%s:%s", activityId, uid);
    }

    private String getHashStrActivityId(String activityId, String uid) {
        return String.format("newbieStr:%s:%s", activityId, uid);
    }

    private String getDailyHashActivityId(String activityId, String uid, int liveStatus, String dateStr) {
        return String.format("dailyNewbie:%s:%s:%s:%s", activityId, uid, liveStatus, dateStr);
    }

    private String getDailyHighTaskStatus(String taskKey) {
        return String.format("status:%s", taskKey);
    }

    private String getUserSignSetKey(String activityId) {
        return String.format("userSign:%s", activityId);
    }

    private String getDeviceSignSetKey(String activityId) {
        return String.format("deviceSign:%s", activityId);
    }

    // 新用户给房主完成用户Key
    private String getNewUserOnMicZSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("newUserOnMic:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    // 用户帮助完成用户数
    private String getUserHelpSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("userHelpSet:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    private String getTaskUserSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("taskUserSet:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    // 直播定时推送
    private String getLiveUserSetByHourKey(String activityId, String setWeek) {
        return String.format("liveUser:%s:%s", activityId, setWeek);
    }


    // 已邀请key
    private String getDailyInviteSetKey(String activityId, String uid, String dateStr) {
        return String.format("newbieDailyInvite:%s:%s:%s", activityId, uid, dateStr);
    }

    // 用户被邀请次数
    private String getDailyUserInvitedNumZSetKey(String activityId, String uid, String dateStr) {
        return String.format("newbieDailyUserInvitedNum:%s:%s:%s", activityId, uid, dateStr);
    }

    // 用户在房间上麦时长
    private String getDailyUserOnMicTimeZSetKey(String activityId, String roomId, String dateStr) {
        return String.format("newbieDailyUserOnMicTime:%s:%s:%s", activityId, roomId, dateStr);
    }

    // 已赠送奖励Key
    private String getDailySendRewardSetKey(String activityId, String uid, String dateStr) {
        return String.format("newbieDailySendReward:%s:%s:%s", activityId, uid, dateStr);
    }


    public NewbieStarVO newbieConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        NewbieStarVO vo = new NewbieStarVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String userSignSetKey = getUserSignSetKey(activityId);
        vo.setSignNumber(activityCommonRedis.getCommonSetNum(userSignSetKey));

        ActorData customerActor = actorDao.getActorDataFromCache(CUSTOMER_ID);
        vo.setUserId(customerActor.getStrRid());
        vo.setUserUId(CUSTOMER_ID);
        vo.setUserName(customerActor.getName());

        List<TaskConfigVO> signTaskList = new ArrayList<>();
        int finishNum = 0;
        int regDay = ActorUtils.getRegDays(uid);
        int userSex = actorData.getFb_gender();

        for (TaskConfigVO taskConfig : new ArrayList<>(SIGN_TASK_LIST)) {
            String taskKey = taskConfig.getTaskKey();
            int totalProcess = taskConfig.getTotalProcess();
            if (taskKey.equals(SIGN_TASK_LIST.get(0).getTaskKey())) {
                taskConfig.setCurrentProcess(userSex == 2 ? 1 : 0);
                taskConfig.setStatus(userSex == 2 ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(1).getTaskKey())) {
                taskConfig.setCurrentProcess(Math.min(regDay, totalProcess));
                taskConfig.setStatus(regDay >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(2).getTaskKey())) {
                int fanNumber = followDao.getFollowsCountByMonGo(uid);
                taskConfig.setCurrentProcess(Math.min(fanNumber, totalProcess));
                taskConfig.setStatus(fanNumber >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(3).getTaskKey())) {
                int liveNum = this.getLastDailyLiveDay(uid);
                taskConfig.setCurrentProcess(Math.min(liveNum, totalProcess));
                taskConfig.setStatus(liveNum >= totalProcess ? 1 : 0);
            }
            if (taskConfig.getStatus() > 0) {
                finishNum += 1;
            }
            signTaskList.add(taskConfig);
        }
        vo.setSignTaskList(signTaskList);


        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        int finishStatus = finishNum >= SIGN_TASK_LIST.size() ? 1 : 0;
        int signStatus = finishStatus > 0 ? 1 : 0;
        signStatus = currentSignStatus > 0 ? 2 : signStatus;
        vo.setSignStatus(signStatus);
        return vo;
    }

    // 最近7天日活天数
    private int getLastDailyLiveDay(String uid) {
        int liveNum = 0;
        List<DayTimeData> dateList = DateHelper.ARABIAN.getContinuesDays(-6, 0);
        for (DayTimeData item : dateList) {
            if (dauUserRedis.hasLogByDate(uid, item.getDate())) {
                liveNum += 1;
            }
        }
        return liveNum;
    }

    /**
     * 获取前n天日期
     */
    private List<String> getContinueDayListByDateStr(String dateStr, int reqNum) {
        List<String> continueDayList = new ArrayList<>();
        int timeStamp = DateHelper.ARABIAN.stringDateToStampSecond(dateStr);
        for (int i = reqNum; i >= 0; i--) {
            int reqTimeStamp = timeStamp - i * 86400;
            String reqDateStr = DateHelper.ARABIAN.formatDateInDay(new Date(reqTimeStamp * 1000L));
            continueDayList.add(reqDateStr);
        }
        return continueDayList;
    }

    /**
     * 报名
     */
    public void newbieSign(String activityId, String uid) {
        checkActivityTime(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        int userSex = actorData.getFb_gender();
        if (userSex == 1) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        String tnId = actorData.getTn_id();
        if (StringUtils.isEmpty(tnId)) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus > 0) {
            throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY.getCode(), "مسجلة بالفعل");
        }

        String deviceSignSetKey = getDeviceSignSetKey(activityId);
        int deviceStatus = activityCommonRedis.isCommonSetData(deviceSignSetKey, tnId);
        if (deviceStatus > 0) {
            logger.info("newbieSign newbieSign:{}", deviceStatus);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        int finishNum = 0;
        int regDay = ActorUtils.getRegDays(uid);
        for (TaskConfigVO taskConfig : new ArrayList<>(SIGN_TASK_LIST)) {
            String taskKey = taskConfig.getTaskKey();
            int totalProcess = taskConfig.getTotalProcess();
            if (taskKey.equals(SIGN_TASK_LIST.get(0).getTaskKey())) {
                taskConfig.setCurrentProcess(userSex == 2 ? 1 : 0);
                taskConfig.setStatus(userSex == 2 ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(1).getTaskKey())) {
                taskConfig.setCurrentProcess(Math.min(regDay, totalProcess));
                taskConfig.setStatus(regDay >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(2).getTaskKey())) {
                int fanNumber = followDao.getFollowsCountByMonGo(uid);
                taskConfig.setCurrentProcess(Math.min(fanNumber, totalProcess));
                taskConfig.setStatus(fanNumber >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(3).getTaskKey())) {
                int liveNum = this.getLastDailyLiveDay(uid);
                taskConfig.setCurrentProcess(Math.min(liveNum, totalProcess));
                taskConfig.setStatus(liveNum >= totalProcess ? 1 : 0);
            }
            if (taskConfig.getStatus() > 0) {
                finishNum += 1;
            }
        }

        int finishStatus = finishNum >= SIGN_TASK_LIST.size() ? 1 : 0;
        if (finishStatus == 0) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        synchronized (stringPool.intern(uid)) {
            activityCommonRedis.addCommonSetData(userSignSetKey, uid);
            activityCommonRedis.addCommonSetData(deviceSignSetKey, tnId);
            doJoinReportEvent(NEW_BIE_TITLE_EN, uid, "", "");
        }
    }

    private void doJoinReportEvent(String origin, String uid, String status, String clockData) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(origin);
        event.setStatus(status);
        event.setScene_desc(clockData);
        eventReport.track(new EventDTO(event));
    }


    private String getCurrentDay(String activityId){
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }


    public NewbieStarVO newbieLiveConfig(String activityId, String uid) {
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }

        NewbieStarVO vo = new NewbieStarVO();
        String currentDay = this.getCurrentDay(activityId);
        vo.setCurrentDay(currentDay);
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        String hashStrActivityId = getHashStrActivityId(activityId, uid);
        Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

        vo.setStart(userDataMap.getOrDefault(LIVE_START, 0));
        vo.setEnd(userDataMap.getOrDefault(LIVE_END, 0));
        vo.setAlarm(userDataMap.getOrDefault(LIVE_ALARM, 0));


        String curWeek = DateSupport.ARABIAN.getStrCurrentWeek();
        String setWeek = userDataStrMap.getOrDefault(LIVE_SET_WEEK, "");
        vo.setStartHMS(userDataStrMap.getOrDefault(LIVE_START_HMS, ""));
        vo.setEndHMS(userDataStrMap.getOrDefault(LIVE_END_HMS, ""));
        vo.setAlarmStatus(userDataStrMap.getOrDefault(LIVE_ALARM_STATUS, "0"));
        vo.setEnableSetTime(StringUtils.isEmpty(setWeek) || !curWeek.equals(setWeek) ? 1 : 0);
        vo.setLiveOnMicReward(0);   // 默认不可领取

        // 正式任务开播时间戳, 为0试播
        int liveStatus = this.getUserLiveStatus(userDataStrMap, currentDay);

        // 调整当前连续天数
        String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
        String continueSetDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;
        formatTaskContinueNum(userDataMap, hashActivityId, userDataStrMap, hashStrActivityId, currentDay, liveStatus, continueNumKey, continueSetDayKey);

        int continueNum = userDataMap.getOrDefault(continueNumKey, 0);
        String continueSetDay = userDataStrMap.getOrDefault(continueSetDayKey, "");
        vo.setLiveStatus(liveStatus);
        vo.setFinishContinueDay(continueNum);


        // 设置最近n天数完成状态
        int reqDayNum = liveStatus == 0 ? 2 : 6;
        List<String> dateList = this.getContinueDayListByDateStr(currentDay, reqDayNum);
        List<TaskConfigVO> continueList = new ArrayList<>();
        int lastDayTaskStatus = 0;
        for (String dateStr : dateList) {
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, dateStr);
            Map<String, Integer> dailyHashUserData = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            TaskConfigVO statusVO = new TaskConfigVO();
            statusVO.setTaskKey(dateStr);
            statusVO.setStatus(dailyHashUserData.getOrDefault(NEW_BIE_TASK_FINISH, 0));

            // 设置
            if (dateStr.equals(dateList.get(dateList.size() - 1))) {
                lastDayTaskStatus = statusVO.getStatus();
            }

            // 设置开播上麦奖励状态
            if(currentDay.equals(dateStr) && liveStatus > 0){
                vo.setLiveOnMicTime(dailyHashUserData.getOrDefault(DAILY_LIVE_ON_MIC_TIME, 0));
                vo.setLiveOnMicReward(dailyHashUserData.getOrDefault(DAILY_LIVE_ON_MIC_REWARD, 0));
            }
            continueList.add(statusVO);
        }
        vo.setContinueList(continueList);


        // 设置奖励
        int resKeyIndex = 0;
        if (!currentDay.equals(continueSetDay)){
            resKeyIndex = continueNum;
        }else {
            int afterIndex = continueNum - 1;
            resKeyIndex = Math.max(afterIndex, 0);
        }
        List<String> rewardList = vo.getLiveStatus() == 0 ? TRY_LIVE_RES_KEY : BASE_LIVE_RES_KEY;
        String resKey = rewardList.get(resKeyIndex);
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resKey);
        ResKeyConfigVO resKeyConfigVO = new ResKeyConfigVO();
        BeanUtils.copyProperties(resourceKeyConfigData, resKeyConfigVO);
        resKeyConfigVO.setStatus(lastDayTaskStatus);
        vo.setResKeyConfigData(resKeyConfigVO);

        // 设置任务列表
        fillTaskList(vo, activityId, uid, liveStatus, currentDay);
        return vo;
    }

    private void formatTaskContinueNum(Map<String, Integer> userDataMap, String hashActivityId, Map<String, String> userDataStrMap,
                                       String hashStrActivityId, String currentDay, int liveStatus, String continueNumKey,
                                       String continueSetDayKey){
        // 查看昨天状态, 设置完成次数、今日奖励资源
        int timeStamp = DateHelper.ARABIAN.stringDateToStampSecond(currentDay);
        String yesterdayStr = DateHelper.ARABIAN.getYesterdayStr(new Date(timeStamp * 1000L));
        int continueMaxNum = liveStatus == 0 ? NEW_BIE_MAX_NUM_PRE : NEW_BIE_MAX_NUM;
        // 获取当前连续完成天数
        int continueNum = userDataMap.getOrDefault(continueNumKey, 0);
        // 初始化设置当前连续完成天数
        String continueSetDay = userDataStrMap.getOrDefault(continueSetDayKey, "");

        synchronized (stringPool.intern("formatTaskContinueNum" + hashActivityId)) {
            if(StringUtils.isEmpty(continueSetDay) || (!currentDay.equals(continueSetDay) && !yesterdayStr.equals(continueSetDay))){
                activityCommonRedis.setCommonHashData(hashStrActivityId, continueSetDayKey, currentDay);
                activityCommonRedis.setCommonHashNum(hashActivityId, continueNumKey, 0);
                String warnDetail = String.format("hashStrActivityId:%s, currentDay: %s, continueSetDay:%s, yesterdayStr: %s", hashStrActivityId, currentDay, continueSetDay, yesterdayStr);
                // monitorSender.info("hwm_warn", "【明星房主任务】连续天数设置为0", warnDetail);
                logger.info("formatTaskContinueNum1 : {}", warnDetail);
                userDataStrMap.put(continueSetDayKey, currentDay);
                userDataMap.put(continueNumKey, 0);
            }

            // 达到最大天数，第二天要设置为0;
            if (!StringUtils.isEmpty(continueSetDay) && continueNum >= continueMaxNum && !currentDay.equals(continueSetDay)){
                activityCommonRedis.setCommonHashData(hashStrActivityId, continueSetDayKey, currentDay);
                activityCommonRedis.setCommonHashNum(hashActivityId, continueNumKey, 0);
                userDataStrMap.put(continueSetDayKey, currentDay);
                userDataMap.put(continueNumKey, 0);
                String warnDetail = String.format("hashStrActivityId:%s, currentDay: %s, continueSetDay:%s, continueNum: %s， continueMaxNum: %s", hashStrActivityId, currentDay, continueSetDay, continueNum, continueMaxNum);
                logger.info("formatTaskContinueNum2 : {}", warnDetail);
                // monitorSender.info("hwm_warn", "【明星房主任务】连续天数设置为0", warnDetail);
            }
        }
    }

    private int getUserLiveStatus(Map<String, String> userDataStrMap, String currentDay){
        String formalLiveDate = userDataStrMap.getOrDefault(NEW_BIE_FORMAL_TASK_START, "");
        if (StringUtils.isEmpty(formalLiveDate)){
            return 0;
        }

        int currentTimestamp = DateHelper.ARABIAN.stringDateToStampSecond(currentDay);
        int startTimestamp = DateHelper.ARABIAN.stringDateToStampSecond(formalLiveDate);

        if (currentTimestamp >= startTimestamp){
            return 1;
        }
        return 0;
    }

    private void fillTaskList(NewbieStarVO vo, String activityId, String uid, int liveStatus, String dateStr) {
        List<TaskConfigVO> taskConfigList = liveStatus == 0 ? TRY_LIVE_TASK_LIST : BASE_LIVE_TASK_LIST;
        String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, dateStr);
        Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        List<TaskConfigVO> taskConfigVOList = new ArrayList<>();
        for (TaskConfigVO taskConfig : taskConfigList) {
            int currentProcess = userDailyMap.getOrDefault(taskConfig.getTaskKey(), 0);
            int totalProcess = taskConfig.getTotalProcess();
            taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
            taskConfig.setStatus(currentProcess >= totalProcess ? 1 : 0);
            taskConfigVOList.add(taskConfig);
        }
        vo.setBaseTaskList(taskConfigVOList);


        if (liveStatus > 0) {

            // 进阶任务
            List<TaskConfigVO> highVOList = new ArrayList<>();
            for (TaskConfigVO taskConfig : HIGH_LIVE_TASK_LIST) {
                int currentProcess = userDailyMap.getOrDefault(taskConfig.getTaskKey(), 0);
                int totalProcess = taskConfig.getTotalProcess();
                taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
                int taskStatus = userDailyMap.getOrDefault(this.getDailyHighTaskStatus(taskConfig.getTaskKey()), 0);
                taskConfig.setStatus(taskStatus > 0 ? taskStatus : (currentProcess >= totalProcess ? 1 : 0));
                highVOList.add(taskConfig);
            }
            vo.setHighTaskList(highVOList);


            // 新用户上麦任务
            List<TaskConfigVO> newUserOnMicVOList = new ArrayList<>();
            int dailyNewUserOnMicNumber = userDailyMap.getOrDefault(DAILY_NEW_USER_ON_MIC, 0);
            for (TaskConfigVO taskConfig : NEW_USER_ON_MIC_TASK_LIST) {
                int totalProcess = taskConfig.getTotalProcess();
                taskConfig.setCurrentProcess(Math.min(dailyNewUserOnMicNumber, totalProcess));
                int taskStatus = userDailyMap.getOrDefault(this.getDailyHighTaskStatus(taskConfig.getTaskKey()), 0);
                taskConfig.setStatus(taskStatus > 0 ? taskStatus : (dailyNewUserOnMicNumber >= totalProcess ? 1 : 0));
                newUserOnMicVOList.add(taskConfig);
            }
            vo.setNewUserOnMicList(newUserOnMicVOList);
            vo.setNewUserOnMicNum(dailyNewUserOnMicNumber);

            // 奖励发放次数
            vo.setDevoteLeftNum(userDailyMap.getOrDefault(DAILY_DEVOTE_LEFT_KEY, 0));
        }
    }

    /**
     * 领取基础任务奖励
     */
    public void newbieLiveReward(String activityId, String uid, int liveStatus, String resKey) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);
            int formalLiveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            if (liveStatus != formalLiveStatus) {
                logger.error("newbieLiveReward1 uid:{} liveStatus:{}, formalLiveStatus:{}", uid, liveStatus, formalLiveStatus);
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "المهمة لم تكتمل");
            }

            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
            int rewardStatus = activityCommonRedis.getCommonHashValue(dailyHashActivityId, NEW_BIE_TASK_FINISH);
            if (rewardStatus > 1) {
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY.getCode(), "تم جمع المكافأة");
            }

            String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
            int resKeyIndex = userDataMap.getOrDefault(continueNumKey, 0);

            // 设置奖励
            String formalResKey = "";
            if (resKeyIndex > 0) {
                List<String> rewardList = liveStatus == 0 ? TRY_LIVE_RES_KEY : BASE_LIVE_RES_KEY;
                formalResKey = rewardList.get(resKeyIndex - 1);
            }

            if (!resKey.equals(formalResKey)) {
                String warnDetail = String.format("uid:%s, resKey: %s, resKeyIndex:%s, formalResKey: %s\n", uid, resKey, resKeyIndex, formalResKey);
                String opUid = "624d7016872f5d952d531ee4";
                String warnDetail2 = String.format("https://apiv2.qmovies.tv/activity/newbieLiveSetContinueNum?activityId=%s&uid=%s&token=%s&aid=%s", ACTIVITY_ID, opUid, basePlayerRedis.getToken(opUid), uid);
                String warnDetailTotal = warnDetail + warnDetail2;
                monitorSender.info("hwm_warn", "【明星房主任务】奖励领取告警", warnDetailTotal);
                logger.error("newbieLiveReward3 liveStatus:{}, resKey:{}, formalResKey:{}", liveStatus, resKey, formalResKey);
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "المهمة لم تكتمل");
            }
            resourceKeyHandlerService.sendResourceData(uid, resKey, NEW_BIE_TITLE_EN_DAILY, NEW_BIE_TITLE_EN_DAILY, NEW_BIE_TITLE_EN_DAILY, "", "");
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, NEW_BIE_TASK_FINISH, 2);
        }
    }

    /**
     * 领取基础任务奖励
     */
    public void newbieLiveSetContinueNum(String activityId, String aid) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, aid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern(aid)) {
            String hashActivityId = getHashActivityId(activityId, aid);
            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, aid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);
            int formalLiveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            String continueNumKey = formalLiveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
            activityCommonRedis.setCommonHashNum(hashActivityId, continueNumKey, formalLiveStatus == 0 ? 3 : 7);
        }
    }

    /**
     * 领取进阶任务奖励
     */
    public void newbieLiveHighReward(String activityId, String uid, String taskKey) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern(uid)) {
            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

            int liveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
            Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);

            Map<String, TaskConfigVO> rewardConfigMap =  HIGH_LIVE_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = rewardConfigMap.get(taskKey);
            if(taskConfig == null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String statusKey = getDailyHighTaskStatus(taskKey);
            int statusNum = userDailyMap.getOrDefault(statusKey, 0);
            if(statusNum > 0){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            int currentProcess = userDailyMap.getOrDefault(taskKey, 0);
            int totalProcess = taskConfig.getTotalProcess();
            if(currentProcess < totalProcess){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }
            String itemTitle = taskConfig.getNameEn();
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), itemTitle, itemTitle, itemTitle, "", "");
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, statusKey, 2);
        }
    }

    /**
     * 领取邀请新用户挑战奖励
     */
    public void newbieNewUserRewardV2(String activityId, String uid, String taskKey) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern(uid)) {
            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

            int liveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
            Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);

            Map<String, TaskConfigVO> rewardConfigMap =  NEW_USER_ON_MIC_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = rewardConfigMap.get(taskKey);
            if(taskConfig == null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String statusKey = getDailyHighTaskStatus(taskKey);
            int statusNum = userDailyMap.getOrDefault(statusKey, 0);
            if(statusNum > 0){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            int currentProcess = userDailyMap.getOrDefault(DAILY_NEW_USER_ON_MIC, 0);
            int totalProcess = taskConfig.getTotalProcess();
            if(currentProcess < totalProcess){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), NEW_BIE_TITLE_EN_ON_MIC, NEW_BIE_TITLE_EN_ON_MIC, NEW_BIE_TITLE_EN_ON_MIC, "", "");
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, statusKey, 2);
        }
    }

    /**
     * 设置开播时间
     */
    public void newbieSetLiveTime(String activityId, String uid, String startHMS, String endHMS, String alarm) {

        String hashStrActivityId = getHashStrActivityId(activityId, uid);
        String currentWeek = DateSupport.ARABIAN.getStrCurrentWeek();
        Map<String, String> userStrHashData = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);
        String userSetWeek = userStrHashData.get(LIVE_SET_WEEK);
        if(!currentWeek.equals(userSetWeek)){
            activityCommonRedis.setCommonHashData(hashStrActivityId, LIVE_START_HMS, startHMS);
            activityCommonRedis.setCommonHashData(hashStrActivityId, LIVE_END_HMS, endHMS);
            activityCommonRedis.setCommonHashData(hashStrActivityId, LIVE_SET_WEEK, currentWeek);

            // TODO 设置日期
            // String liveUserSetKey = getLiveUserSetByHourKey(activityId, currentWeek);
            // activityCommonRedis.addCommonSetData(liveUserSetKey, uid);
        }
        activityCommonRedis.setCommonHashData(hashStrActivityId, LIVE_ALARM_STATUS, alarm);
        doJoinReportEvent("Welcome New House2-clock", uid, alarm, String.format("%s-%s", startHMS, endHMS));
    }

    /**
     * 领取主播在麦时长奖励
     */
    public void newbieOnMicReward(String activityId, String uid) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern("onMicReward" + uid)) {
            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

            int liveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
            Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int statusNum = userDailyMap.getOrDefault(DAILY_LIVE_ON_MIC_REWARD, 0);
            if(statusNum != 1){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "المهمة لم تكتمل");
            }
            int currentProcess = userDailyMap.getOrDefault(DAILY_LIVE_ON_MIC_TIME, 0);
            if(currentProcess < DAILY_ON_MIC_TIME_MAX){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "المهمة لم تكتمل");
            }
            resourceKeyHandlerService.sendResourceData(uid, "welcomeStarClockReward", "Welcome New House2-clock task", "Welcome New House2-clock task", "Welcome New House2-clock task", "", "");
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, DAILY_LIVE_ON_MIC_REWARD, 2);
        }
    }


    public NewbieStarVO newbieNewUserListV2(String activityId, String uid, int page) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int currentTime = DateHelper.getNowSeconds();
        int startTime = currentTime - 7 * 86400;
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String country = actorData.getCountry();
        List<MongoActorData> mongoActorDataList = actorDao.getNewUserActorList(startTime * 1000L, currentTime * 1000L, country, start, RECORD_PAGE_SIZE);
        NewbieStarVO vo = new NewbieStarVO();
        List<PrizeConfigV2VO> newUserList = new ArrayList<>();
        String currentDay = getCurrentDay(activityId);
        String inviteSetKey = getDailyInviteSetKey(activityId, uid, currentDay);
        for (MongoActorData mongoActorData : mongoActorDataList) {
            String aid = mongoActorData.get_id().toString();
            int invitedNum = activityCommonRedis.getCommonZSetRankingScore(getDailyUserInvitedNumZSetKey(activityId, aid, currentDay), aid);
            if(invitedNum >= 5){
                continue;
            }
            PrizeConfigV2VO prizeConfigV2VO = new  PrizeConfigV2VO();
            prizeConfigV2VO.setUid(aid);
            prizeConfigV2VO.setUserName(mongoActorData.getName());
            prizeConfigV2VO.setUserHead(ImageUrlGenerator.generateRoomUserUrl(mongoActorData.getHead()));
            prizeConfigV2VO.setUserGender(mongoActorData.getFb_gender());
            prizeConfigV2VO.setStatus(activityCommonRedis.isCommonSetData(inviteSetKey, aid));
            prizeConfigV2VO.setCtime(ActorUtils.getRegDays(aid));
            newUserList.add(prizeConfigV2VO);
        }
        vo.setNewUserList(newUserList);
        vo.setNextUrl(mongoActorDataList.size() < RECORD_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

    // 可赠送礼物用户列表
    public NewbieStarVO newbieNewDevoteUserListV2(String activityId, String uid) {
        NewbieStarVO vo = new  NewbieStarVO();
        List<PrizeConfigV2VO> devoteUserList = new ArrayList<>();
        String currentDay = getCurrentDay(activityId);
        String roomId = RoomUtils.formatRoomId(uid);
        Map<String, Integer> onMicTimeRankMap = activityCommonRedis.getOtherRankingMapByScoreAPage(getDailyUserOnMicTimeZSetKey(activityId, roomId, currentDay), 5, 2000, 0, 10);
        String sendRewardSetKey = getDailySendRewardSetKey(activityId, uid, currentDay);
        for (Map.Entry<String, Integer> entry : onMicTimeRankMap.entrySet()) {
            String aid = entry.getKey();
            int onMicTime = entry.getValue();
            if (aid.equals(uid)){
                continue;
            }

            PrizeConfigV2VO prizeConfigV2VO = new  PrizeConfigV2VO();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            prizeConfigV2VO.setUid(aid);
            prizeConfigV2VO.setUserName(actorData.getName());
            prizeConfigV2VO.setUserHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            prizeConfigV2VO.setStatus(activityCommonRedis.isCommonSetData(sendRewardSetKey, aid));
            prizeConfigV2VO.setUserGender(actorData.getFb_gender());
            prizeConfigV2VO.setCtime(onMicTime);
            devoteUserList.add(prizeConfigV2VO);
        }
        vo.setDevoteUserList(devoteUserList);
        return vo;
    }

    /**
     * 房主完成任务下发奖励给其他用户
     */
    public void newbieHostSendReward(String activityId, String uid, String aid) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern("hostSendReward" + uid)) {
            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

            int liveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
            Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int devoteNum = userDailyMap.getOrDefault(DAILY_DEVOTE_LEFT_KEY, 0);
            if(devoteNum <= 0){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "ليس لديك رصيد مكافأة");
            }
            resourceKeyHandlerService.sendResourceData(aid, "welcomeStarMemberGift", "Welcome New House2-Official-room host grant", "Welcome New House2-Official-room host grant", "Welcome New House2-Official-room host grant", "", "");
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, DAILY_DEVOTE_LEFT_KEY, -1);
            activityCommonRedis.addCommonSetData(getDailySendRewardSetKey(activityId, uid, currentDay), aid);

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            SendChatMsgDTO msgTextDto = new SendChatMsgDTO();
            msgTextDto.setUid(uid);
            msgTextDto.setAid(aid);
            msgTextDto.setMsgType(MsgType.TEXT);
            msgTextDto.setOs(actorData.getIntOs());
            msgTextDto.setMsgBody("لقد أرسلت لك هدية، اذهب لتفقد حقيبتك");
            msgTextDto.setSlang(actorData.getSlang());
            JSONObject jsonTextObject = new JSONObject();
            msgTextDto.setMsgInfo(jsonTextObject);
            msgTextDto.setVersioncode(actorData.getVersion_code());
            msgTextDto.setNew_versioncode(5);
            iMsgService.sendMsg(msgTextDto);
        }
    }

    public void newbieInviteV2(String activityId, String uid, String aid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String roomId = RoomUtils.formatRoomId(uid);
        MongoRoomData mongoRoomData = mongoRoomDao.getDataFromCache(roomId);
        if(mongoRoomData  == null){
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST.getCode(), "لم تقم بإنشاء غرفة");
        }

        if(uid.equals(aid)){
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR.getCode(), "لا أستطيع المشاركة مع نفسي");
        }

        String currentDay = getCurrentDay(activityId);
        String inviteSetKey = getDailyInviteSetKey(activityId, uid, currentDay);
        if(activityCommonRedis.isCommonSetData(inviteSetKey, aid) > 0){
            return;
        }
        activityCommonRedis.addCommonSetData(inviteSetKey, aid);
        activityCommonRedis.incrCommonZSetRankingScore(getDailyUserInvitedNumZSetKey(activityId, aid, currentDay), aid, 1);

        SendChatMsgDTO msgDto = new SendChatMsgDTO();
        msgDto.setUid(uid);
        msgDto.setAid(aid);
        msgDto.setMsgType(MsgType.SHARE_ROOM);
        msgDto.setOs(actorData.getIntOs());
        msgDto.setMsgBody("");
        msgDto.setSlang(actorData.getSlang());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("icon", ImageUrlGenerator.generateNormalUrl(mongoRoomData.getHead()));
        jsonObject.put("action", roomId);
        jsonObject.put("ridInfo", JSONObject.toJSONString(actorData.getRidData()));
        jsonObject.put("title", mongoRoomData.getName());
        jsonObject.put("type", 5);
        jsonObject.put("content", actorData.getStrRid());
        msgDto.setMsgInfo(jsonObject);
        msgDto.setVersioncode(actorData.getVersion_code());
        msgDto.setNew_versioncode(5);
        iMsgService.sendMsg(msgDto);

        SendChatMsgDTO msgTextDto = new SendChatMsgDTO();
        msgTextDto.setUid(uid);
        msgTextDto.setAid(aid);
        msgTextDto.setMsgType(MsgType.TEXT);
        msgTextDto.setOs(actorData.getIntOs());
        msgTextDto.setMsgBody("تشرفت بلقائك \uD83D\uDE42 \n سأنتظرك في الغرفة");
        msgTextDto.setSlang(actorData.getSlang());
        JSONObject jsonTextObject = new JSONObject();
        msgTextDto.setMsgInfo(jsonTextObject);
        msgTextDto.setVersioncode(actorData.getVersion_code());
        msgTextDto.setNew_versioncode(5);
        iMsgService.sendMsg(msgTextDto);
    }


    // 统一加积分方法
    private void incActionUserPoint(TaskConfigVO taskConfig, int taskInnerNum, String uid, String aid, int incPoint, int liveStatus, String dateStr, boolean setFlag, int limitNum, boolean newUserFlag) {
        String taskKey = taskConfig.getTaskKey();
        int maxNum = taskConfig.getTotalProcess();
        synchronized (stringPool.intern("incPoint" + uid)) {

            String dailyHashActivityId = this.getDailyHashActivityId(ACTIVITY_ID, uid, liveStatus, dateStr);
            Map<String, Integer> dailyUserDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int dailyGetPoint = dailyUserDataMap.getOrDefault(taskKey, 0);
            if (dailyGetPoint >= maxNum) {
                return;
            }

            // 新用户限制
            if (newUserFlag) {
                boolean newRegisterFlag = ActorUtils.isNewRegisterActor(aid, 7);
                if (!newRegisterFlag) {
                    return;
                }

                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (StringUtils.isEmpty(actorData.getFirstTnId())) {
                    return;
                }
            }

            // 任务要达到某一额度才算完成taskInnerNum
            if (taskInnerNum > 0) {
                String newUserOnMicKey = getNewUserOnMicZSetKey(ACTIVITY_ID, uid, taskKey, dateStr);
                int beforeScore = activityCommonRedis.getCommonZSetRankingScore(newUserOnMicKey, aid);
                if (beforeScore >= taskInnerNum) {
                    return;
                }

                int afterMicTime = activityCommonRedis.incrCommonZSetRankingScoreSimple(newUserOnMicKey, aid, incPoint);
                if (afterMicTime < taskInnerNum) {
                    return;
                }
            }

            // 帮助完成限制
            if (limitNum > 0) {
                String userHelpSetKey = getUserHelpSetKey(ACTIVITY_ID, aid, taskKey, dateStr);
                int finishNum = activityCommonRedis.getCommonSetNum(userHelpSetKey);
                if (finishNum >= limitNum) {
                    return;
                }
                activityCommonRedis.addCommonSetData(userHelpSetKey, uid);
            }

            // 同一个用户操作限制
            if (setFlag) {
                String taskUserSetKey = getTaskUserSetKey(ACTIVITY_ID, uid, taskKey, dateStr);
                int isSet = activityCommonRedis.isCommonSetData(taskUserSetKey, aid);
                if (isSet > 0) {
                    return;
                }
                activityCommonRedis.addCommonSetData(taskUserSetKey, aid);
            }
            int leftIncPoint = maxNum - dailyGetPoint;
            incPoint = Math.min(incPoint, leftIncPoint);
            int afterPoint = activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskKey, incPoint);
            if(afterPoint >= maxNum){
                doReportEvent(uid, liveStatus, taskKey);
            }
        }
    }

    // 更新任务状态
    private void updateTaskUserStatus(String uid, int liveStatus, List<TaskConfigVO> taskConfigList, String currentDay) {

        synchronized (stringPool.intern("updateTaskUser" + uid)) {
            String dailyHashActivityId = getDailyHashActivityId(ACTIVITY_ID, uid, liveStatus, currentDay);
            Map<String, Integer> dailyUserHashMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int rewardStatus = dailyUserHashMap.getOrDefault(NEW_BIE_TASK_FINISH, 0);
            if (rewardStatus >= 1) {
                return;
            }

            int finishNum = 0;
            for (TaskConfigVO taskConfig : taskConfigList) {
                String taskKey = taskConfig.getTaskKey();
                Integer totalProcess = taskConfig.getTotalProcess();
                int currentProcess = dailyUserHashMap.getOrDefault(taskKey, 0);
                if (currentProcess >= totalProcess) {
                    finishNum += 1;
                }
            }
            if (finishNum >= taskConfigList.size()) {
                activityCommonRedis.setCommonHashNum(dailyHashActivityId, NEW_BIE_TASK_FINISH, 1);
                String hashActivityId = getHashActivityId(ACTIVITY_ID, uid);
                String hashStrActivityId = getHashStrActivityId(ACTIVITY_ID, uid);

                // 连续完成任务天数Key
                String continueKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
                // 完成任务天Key
                String continueDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;

                Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
                int finishContinueDay = userDataMap.getOrDefault(continueKey, 0);
                finishContinueDay += 1;
                if(liveStatus == 0){
                    finishContinueDay = Math.min(3, finishContinueDay);

                    // 如果完成三天试播任务; 给予正播时间
                    if (finishContinueDay == 3) {
                        Date currentDate = DateHelper.ARABIAN.parseDate(currentDay);
                        String nextDay = DateHelper.ARABIAN.formatDateInDay(DateHelper.ARABIAN.getNextDay(currentDate.getTime()));
                        activityCommonRedis.setCommonHashData(hashStrActivityId, NEW_BIE_FORMAL_TASK_START, nextDay);
                    }
                }else {
                    finishContinueDay = Math.min(7, finishContinueDay);
                    activityCommonRedis.setCommonHashNum(dailyHashActivityId, DAILY_DEVOTE_LEFT_KEY, DAILY_DEVOTE_NUM);
                }
                activityCommonRedis.setCommonHashNum(hashActivityId, continueKey, finishContinueDay);
                activityCommonRedis.setCommonHashData(hashStrActivityId, continueDayKey, currentDay);
                doReportEvent(uid, liveStatus, String.valueOf(finishContinueDay));
            }
        }
    }


    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        Set<String> aidList = giftData.getAid_list();
        int incNumber = giftData.getNumber();

        TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(2);
        String currentDay = this.getCurrentDay(activityId);

        for (String aid : aidList) {
            if (activityCommonRedis.isCommonSetData(getUserSignSetKey(ACTIVITY_ID), aid) <= 0) {
                continue;
            }
            String hashActivityId = getHashActivityId(activityId, aid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

            String hashStrActivityId = getHashStrActivityId(activityId, aid);
            Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

            int liveStatus = getUserLiveStatus(userDataStrMap, currentDay);
            String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
            String continueSetDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;
            formatTaskContinueNum(userDataMap, hashActivityId, userDataStrMap, hashStrActivityId, currentDay, liveStatus, continueNumKey, continueSetDayKey);
            incActionUserPoint(taskConfig, 0, aid, fromUid, incNumber, liveStatus, currentDay, false, 0, true);
        }
    }

    // public void officialMsgPush() {
    //     String currentHour = DateHelper.ARABIAN.formatDateInHour();
    //     String liveUserSetKey = getLiveUserSetByHourKey(ACTIVITY_ID, Integer.parseInt(currentHour.substring(11, 13)));
    //     Set<String>  allPushMember = activityCommonRedis.getCommonSetMember(liveUserSetKey);
    //     String currentDay = this.getCurrentDay(ACTIVITY_ID);
    //
    //     for (String pushUid : allPushMember){
    //         String hashActivityId = getHashActivityId(ACTIVITY_ID, pushUid);
    //         Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
    //         int alarm = userDataMap.getOrDefault(LIVE_ALARM, 0);
    //
    //         if(alarm > 0){
    //             ActorData actorData = actorDao.getActorDataFromCache(pushUid);
    //             String hashStrActivityId = getHashStrActivityId(ACTIVITY_ID, pushUid);
    //             Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);
    //             int timeStamp = DateHelper.ARABIAN.stringDateToStampSecond(currentDay);
    //             String yesterdayStr = DateHelper.ARABIAN.getYesterdayStr(new Date(timeStamp * 1000L));
    //             int liveStatus = getUserLiveStatus(userDataStrMap, yesterdayStr);
    //             List<TaskConfigVO> taskConfigList = liveStatus == 0 ? TRY_LIVE_TASK_LIST : BASE_LIVE_TASK_LIST;
    //
    //             TaskConfigVO taskConfig = taskConfigList.get(1);
    //             String taskUserSetKey = getTaskUserSetKey(ACTIVITY_ID, pushUid, taskConfig.getTaskKey(), yesterdayStr);
    //             int setNum = activityCommonRedis.getCommonSetNum(taskUserSetKey);
    //             String actText = "شاهد";
    //             String title = "تحية النجم الجديد";
    //             String body = String.format("عزيزي %s، شكرًا لك على جلب الدفء والسعادة إلى %s مستخدم جديد أمس. يمكنك الاستمرار في تلقي باقات هدايا غنية من خلال إكمال المهام اليوم. لنبدأ رحلتنا الاجتماعية الرائعة>>", actorData.getName(), setNum);
    //             String picture = "";
    //             commonOfficialMsg(pushUid, picture, 0, 0, actText, title, body, ACTIVITY_URL);
    //         }
    //     }
    // }

    private void doReportEvent(String uid, int liveStatus, String stageDesc) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setActivity_name(NEW_BIE_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(liveStatus);
        event.setActivity_stage_desc(String.valueOf(stageDesc));
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    // 统计房主开播上麦时长
    private void incHostOnMicTime(String activityId, String uid, int liveStatus, int incTime){
        try {
            String currentDay = getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);
            String alarmStatus = userStrDataMap.get(LIVE_ALARM_STATUS);
            String startHMS = userStrDataMap.getOrDefault(LIVE_START_HMS, "");
            String endHMS = userStrDataMap.getOrDefault(LIVE_END_HMS, "");
            if(StringUtils.isEmpty(alarmStatus) || Integer.parseInt(alarmStatus) != 1 || StringUtils.isEmpty(startHMS) || StringUtils.isEmpty(endHMS)){
                return;
            }

            int currentTime = DateHelper.getNowSeconds();
            Date curDate = DateHelper.formatDate(currentTime);
            String curHMS = DateHelper.ARABIAN.formatTimeInDay3(curDate);
            int currentTime2 = DateHelper.ARABIAN.stringDateTimeToStampSecond(String.format("%s %s", currentDay, curHMS));
            int startTime = DateHelper.ARABIAN.stringDateTimeToStampSecond(String.format("%s %s", currentDay, startHMS));
            int endTime = DateHelper.ARABIAN.stringDateTimeToStampSecond(String.format("%s %s", currentDay, endHMS));
            // logger.info("incHostOnMicTime currentTime:{} currentTime2:{}, startTime:{}, endTime:{}", currentTime, currentTime2, startTime, endTime);
            if(currentTime2 >= startTime && currentTime2 <= endTime){
                String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
                int dailyOnMicReward = activityCommonRedis.getCommonHashValue(dailyHashActivityId, DAILY_LIVE_ON_MIC_REWARD);
                if(dailyOnMicReward > 0){
                    return;
                }
                int afterOnMicTime = activityCommonRedis.incCommonHashNum(dailyHashActivityId, DAILY_LIVE_ON_MIC_TIME, incTime);
                if(afterOnMicTime >= DAILY_ON_MIC_TIME_MAX){
                    activityCommonRedis.incCommonHashNum(dailyHashActivityId, DAILY_LIVE_ON_MIC_REWARD, 1);
                }
            }
        }catch (Exception e){
            logger.error("incHostOnMicTime error:{}", e.getMessage(), e);
        }

    }

    // 统计新用户上麦超过5分钟的人数
    private void incNewUserNumOnMic(String hostUid, String uid, int liveStatus, int incTime, String currentDay) {
        String dailyHashActivityId = this.getDailyHashActivityId(ACTIVITY_ID, hostUid, liveStatus, currentDay);
        Map<String, Integer> dailyUserDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        int dailyGetPoint = dailyUserDataMap.getOrDefault(DAILY_NEW_USER_ON_MIC, 0);
        if (dailyGetPoint >= DAILY_NEW_USER_ON_MIC_MAX) {
            return;
        }

        boolean newRegisterFlag = ActorUtils.isNewRegisterActor(uid, 7);
        if (!newRegisterFlag) {
            return;
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (StringUtils.isEmpty(actorData.getFirstTnId())) {
            return;
        }

        String taskKey = DAILY_NEW_USER_ON_MIC;
        String newUserOnMicKey = getNewUserOnMicZSetKey(ACTIVITY_ID, hostUid, taskKey, currentDay);
        int beforeScore = activityCommonRedis.getCommonZSetRankingScore(newUserOnMicKey, uid);
        if (beforeScore >= DAILY_NEW_USER_ON_MIC_TIME) {
            return;
        }

        int afterMicTime = activityCommonRedis.incrCommonZSetRankingScoreSimple(newUserOnMicKey, uid, incTime);
        if (afterMicTime < DAILY_NEW_USER_ON_MIC_TIME) {
            return;
        }

        synchronized (stringPool.intern("incNewUserNumOnMic" + hostUid)) {

            String taskUserSetKey = getTaskUserSetKey(ACTIVITY_ID, hostUid, taskKey, currentDay);
            int isSet = activityCommonRedis.isCommonSetData(taskUserSetKey, uid);
            if (isSet > 0) {
                return;
            }
            activityCommonRedis.addCommonSetData(taskUserSetKey, uid);
            int leftIncPoint = DAILY_NEW_USER_ON_MIC_MAX - dailyGetPoint;
            int incPoint = Math.min(1, leftIncPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskKey, incPoint);
        }
    }

    // 统计房间会员上麦时长
    private void incRoomMemberOnMicTime(String roomId, String uid, int incTime, String currentDay) {
        RoomMemberData roomMember = roomMemberDao.findData(roomId, uid);
        if(roomMember == null){
            return;
        }
        activityCommonRedis.incrCommonZSetRankingScore(getDailyUserOnMicTimeZSetKey(ACTIVITY_ID, roomId, currentDay), uid, incTime);
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }

        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }


        String hostUid = "";
        if (CommonMqTaskConstant.FOLLOW_USER.equals(item)) {
            hostUid = aid;
        } else if (CommonMqTaskConstant.INVITE_USER_ACTION.equals(item)) {
            hostUid = uid;
        } else {
            hostUid = StringUtils.isEmpty(roomId) ? uid : RoomUtils.getRoomHostId(roomId);
        }

        if (activityCommonRedis.isCommonSetData(getUserSignSetKey(ACTIVITY_ID), hostUid) <= 0) {
            return;
        }

        String currentDay = this.getCurrentDay(ACTIVITY_ID);
        String hashActivityId = getHashActivityId(ACTIVITY_ID, hostUid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        String hashStrActivityId = getHashStrActivityId(ACTIVITY_ID, hostUid);
        Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

        int liveStatus = getUserLiveStatus(userDataStrMap, currentDay);
        String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
        String continueSetDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;
        formatTaskContinueNum(userDataMap, hashActivityId, userDataStrMap, hashStrActivityId, currentDay, liveStatus, continueNumKey, continueSetDayKey);

        List<TaskConfigVO> taskConfigList = liveStatus == 0 ? TRY_LIVE_TASK_LIST : BASE_LIVE_TASK_LIST;
        if (CommonMqTaskConstant.ON_MIC_TIME.equals(data.getItem())) {
            if (hostUid.equals(uid)) {
                TaskConfigVO taskConfig = taskConfigList.get(0);
                incActionUserPoint(taskConfig, 0, hostUid, uid, 1, liveStatus, currentDay, false, 0, false);
                this.updateTaskUserStatus(hostUid, liveStatus, taskConfigList, currentDay);
            }
            int maxOnMicTime = liveStatus == 0 ? 3 : 5;
            incActionUserPoint(taskConfigList.get(2), maxOnMicTime, hostUid, uid, 1, liveStatus, currentDay, true, 5, true);
            this.updateTaskUserStatus(hostUid, liveStatus, taskConfigList, currentDay);

        } else if (CommonMqTaskConstant.INVITE_USER_ACTION.equals(data.getItem())) {
            incActionUserPoint(taskConfigList.get(1), 0, hostUid, aid, 1, liveStatus, currentDay, true, 5, true);
            this.updateTaskUserStatus(hostUid, liveStatus, taskConfigList, currentDay);
        }

        // 进阶任务
        if (liveStatus == 1) {
            if (CommonMqTaskConstant.FOLLOW_USER.equals(data.getItem())) {
                TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(0);
                incActionUserPoint(taskConfig, 0, hostUid, uid, 1, liveStatus, currentDay, true, 5, true);
            }
            else if (CommonMqTaskConstant.ADD_FRIEND.equals(data.getItem())) {
                TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(1);
                incActionUserPoint(taskConfig, 0, hostUid, aid, 1, liveStatus, currentDay, true, 5, true);

            } else if (CommonMqTaskConstant.ON_MIC_TIME.equals(data.getItem())) {
                // 统计自己房间开播上麦时长
                if(hostUid.equals(uid)){
                    incHostOnMicTime(ACTIVITY_ID, uid, liveStatus, 1);
                }
                // 统计新用户上麦超过5分钟的人数
                incNewUserNumOnMic(hostUid, uid, liveStatus, 1, currentDay);
                // 统计房间会员上麦时长
                incRoomMemberOnMicTime(roomId, uid, 1, currentDay);

                // 统计新用户在房间上麦超过5分钟
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (actorData.getFb_gender() == 2 && friendsDao.isFriend(hostUid, uid)) {
                    TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(3);
                    incActionUserPoint(taskConfig, 15, hostUid, uid, 1, liveStatus, currentDay, true, 5, false);
                }
            }
        }
    }
}
