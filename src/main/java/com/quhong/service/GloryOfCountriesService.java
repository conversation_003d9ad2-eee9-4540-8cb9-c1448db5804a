package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 国家荣耀比赛
 */
@Service
public class GloryOfCountriesService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(GloryOfCountriesService.class);
    public static final String ACTIVITY_CONTAINS_NAME = "Glory Of Countries";
    private static final String ACTIVITY_TITLE_EN = "Glory Of Countries";
    public static final String ACTIVITY_ID = "686e57ab570af666b97099b0";
    private static final String ALL_COUNTRIES_FLAG = "allCountriesFlag";
    private static final List<Integer> SEND_OR_RECEIVE_BEANS_LIST = Arrays.asList(5000, 10000, 20000);

    private static Map<Integer, List<Integer>> SEND_GIFTID_BADGEID_MAP = new HashMap<Integer, List<Integer>>() {
        {
            put(122, Arrays.asList(3393, 3394, 3395));
            put(123, Arrays.asList(3408, 3409, 3410));
            put(124, Arrays.asList(3402, 3403, 3404));
            put(125, Arrays.asList(3460, 3461, 3462));
            put(126, Arrays.asList(3469, 3470, 3471));
            put(127, Arrays.asList(3424, 3425, 3426));
            put(128, Arrays.asList(3427, 3428, 3429));
            put(129, Arrays.asList(3430, 3431, 3432));
            put(130, Arrays.asList(3436, 3437, 3438));
            put(131, Arrays.asList(3399, 3400, 3401));
            put(132, Arrays.asList(3405, 3406, 3407));
            put(133, Arrays.asList(3421, 3422, 3423));
            put(134, Arrays.asList(3439, 3440, 3441));
            put(135, Arrays.asList(3451, 3452, 3453));
            put(136, Arrays.asList(3454, 3455, 3456));
            put(137, Arrays.asList(3445, 3446, 3447));
            put(138, Arrays.asList(3448, 3449, 3450));
            put(139, Arrays.asList(3396, 3397, 3398));
            put(140, Arrays.asList(3433, 3434, 3435));
            put(141, Arrays.asList(3457, 3458, 3459));
            put(146, Arrays.asList(3418, 3419, 3420));
            put(208, Arrays.asList(3442, 3443, 3444));
            put(272, Arrays.asList(3414, 3415, 3416));
            put(273, Arrays.asList(3411, 3412, 3413));
            put(274, Arrays.asList(3466, 3467, 3468));
            put(1048, Arrays.asList(3463, 3464, 3465));
        }
    };

    private static Map<Integer, List<Integer>> RECEIVE_GIFTID_BADGEID_MAP = new HashMap<Integer, List<Integer>>() {
        {
            put(122, Arrays.asList(3472, 3473, 3474));
            put(123, Arrays.asList(3487, 3488, 3489));
            put(124, Arrays.asList(3481, 3482, 3483));
            put(125, Arrays.asList(3538, 3539, 3540));
            put(126, Arrays.asList(3547, 3548, 3549));
            put(127, Arrays.asList(3502, 3503, 3504));
            put(128, Arrays.asList(3505, 3506, 3507));
            put(129, Arrays.asList(3508, 3509, 3510));
            put(130, Arrays.asList(3514, 3515, 3516));
            put(131, Arrays.asList(3478, 3479, 3480));
            put(132, Arrays.asList(3484, 3485, 3486));
            put(133, Arrays.asList(3499, 3500, 3501));
            put(134, Arrays.asList(3517, 3518, 3519));
            put(135, Arrays.asList(3529, 3530, 3531));
            put(136, Arrays.asList(3532, 3533, 3534));
            put(137, Arrays.asList(3523, 3524, 3525));
            put(138, Arrays.asList(3526, 3527, 3528));
            put(139, Arrays.asList(3475, 3476, 3477));
            put(140, Arrays.asList(3511, 3512, 3513));
            put(141, Arrays.asList(3535, 3536, 3537));
            put(146, Arrays.asList(3496, 3497, 3498));
            put(208, Arrays.asList(3520, 3521, 3522));
            put(272, Arrays.asList(3493, 3494, 3495));
            put(273, Arrays.asList(3490, 3491, 3492));
            put(274, Arrays.asList(3544, 3545, 3546));
            put(1048, Arrays.asList(3541, 3542, 3543));
        }
    };

    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private OtherActivityService otherActivityService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private BadgeService badgeService;
    @Resource
    private GiftDao giftDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            SEND_GIFTID_BADGEID_MAP = new HashMap<Integer, List<Integer>>() {
                {
                    put(381, Arrays.asList(1, 1, 1));
                    put(382, Arrays.asList(2, 2, 2));
                    put(837, Arrays.asList(3, 3, 3));
                }
            };
            RECEIVE_GIFTID_BADGEID_MAP = new HashMap<Integer, List<Integer>>() {
                {
                    put(381, Arrays.asList(1, 1, 1));
                    put(382, Arrays.asList(2, 2, 2));
                    put(837, Arrays.asList(3, 3, 3));
                }
            };
        }
    }

    private String getGloryOfCountriesKey(String activityId) {
        return String.format("gloryOfCountries:%s", activityId);
    }

    private String getSupportCountriesKey(String activityId, int giftId) {
        return String.format("gloryOfCountries:%s:%s", activityId, giftId);
    }

    private String getGiftRecordKey(String activityId) {
        return String.format("gloryOfCountries:giftRecord:%s", activityId);
    }

    private String getSendBadgeKey(String activityId, int giftId) {
        return String.format("gloryOfCountries:sendBadge:%s:%s", activityId, giftId);
    }

    private String getReceiveBadgeKey(String activityId, int giftId) {
        return String.format("gloryOfCountries:receiveBadge:%s:%s", activityId, giftId);
    }


    private Map<String, ResourceKeyConfigData.ResourceMeta> getGiftIdMap() {
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(ALL_COUNTRIES_FLAG);
        if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
            logger.info("not find; resourceKey={}", ALL_COUNTRIES_FLAG);
            return Collections.emptyMap();
        }

        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();

        Map<String, ResourceKeyConfigData.ResourceMeta> giftIdMap = resourceMetaList.stream()
                .filter(item -> item.getResourceType() == -1)
                .collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        return giftIdMap;

    }

    public GloryOfCountriesVO gloryOfCountriesInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        GloryOfCountriesVO vo = new GloryOfCountriesVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        // 获取国家榜
        List<OtherRankingListVO> otherRankingList = new ArrayList<>();
        String operationRoomWashRankKey = getGloryOfCountriesKey(activityId);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(operationRoomWashRankKey, 10);
        int rank = 1;
        Map<String, ResourceKeyConfigData.ResourceMeta> giftIdMap = getGiftIdMap();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            String giftId = entry.getKey();
            int intGiftId = Integer.parseInt(giftId);
            rankingListVO.setScoreStr(entry.getValue().toString());
            rankingListVO.setRank(rank);
            ResourceKeyConfigData.ResourceMeta oneResourceMeta = giftIdMap.getOrDefault(giftId, null);
            rankingListVO.setCountryFlag(oneResourceMeta != null ? oneResourceMeta.getResourceIcon() : "");
            String supportUserKey = getSupportCountriesKey(activityId, intGiftId);
            List<String> supportUserList = activityCommonRedis.getCommonRankingList(supportUserKey, 3);

            List<OtherSupportUserVO> supportUserVOList = new ArrayList<>();
            for (String supportUid : supportUserList) {
                OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
                ActorData supportActorData = actorDao.getActorDataFromCache(supportUid);
                supportUserVO.setName(supportActorData.getName());
                supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(supportActorData.getHead()));
                supportUserVO.setUid(supportUid);
                supportUserVOList.add(supportUserVO);
            }
            rankingListVO.setSupportUserList(supportUserVOList);
            rank += 1;
            otherRankingList.add(rankingListVO);
        }
        vo.setCountryRankingList(otherRankingList);

        // 获取发送榜-钻石
        OtherRankingVO sendRankVO = otherActivityService.otherRanking(uid, activityId,
                ActivityConstant.SEND_RANK, 10, 1);
        OtherMyRankVO myRankVO = sendRankVO.getMyRank();
//        myRankVO.setReceiveScore(activityOtherRedis.getOtherReachingScore(activityId, uid, ActivityConstant.RECEIVE_RANK, activityData.getRoundNum()));
        myRankVO.setReceiveScore(getTotalDiamondNum(activityId, uid, 2));
        vo.setSendRankVO(sendRankVO);

        // 获取最近5条发送礼物数据
        List<GloryOfCountriesVO.RollRecordVO> rollRecordList = getRecentGiftRecords(activityId);
        vo.setRollRecordList(rollRecordList);

        return vo;
    }

    private int getTotalDiamondNum(String activityId, String uid, int badgeType) {
        int totalDiamondNum = 0;
        Map<Integer, List<Integer>> giftIdBadgeIdMap;
        Function<Integer, String> badgeKeyFunction;

        if (badgeType == 1) {
            giftIdBadgeIdMap = SEND_GIFTID_BADGEID_MAP;
            badgeKeyFunction = (giftId) -> getSendBadgeKey(activityId, giftId);
        } else {
            giftIdBadgeIdMap = RECEIVE_GIFTID_BADGEID_MAP;
            badgeKeyFunction = (giftId) -> getReceiveBadgeKey(activityId, giftId);
        }

        for (Integer giftId : giftIdBadgeIdMap.keySet()) {
            String badgeKey = badgeKeyFunction.apply(giftId);
            totalDiamondNum += activityCommonRedis.getCommonZSetRankingScore(badgeKey, uid);
        }
        return totalDiamondNum;
    }

    public GloryOfCountriesVO.BadgeRecordVO badgeRecordInfo(String activityId, String uid ,int badgeType) {
        GloryOfCountriesVO.BadgeRecordVO vo = new GloryOfCountriesVO.BadgeRecordVO();
        Map<String, ResourceKeyConfigData.ResourceMeta> giftIdMap = getGiftIdMap();
        
        // 根据badgeType选择对应的映射和key获取方法
        Map<Integer, List<Integer>> giftIdBadgeIdMap;
        Function<Integer, String> badgeKeyFunction;
        
        if (badgeType == 1) {
            // 发送类型
            giftIdBadgeIdMap = SEND_GIFTID_BADGEID_MAP;
            badgeKeyFunction = (giftId) -> getSendBadgeKey(activityId, giftId);
        } else {
            // 接收类型
            giftIdBadgeIdMap = RECEIVE_GIFTID_BADGEID_MAP;
            badgeKeyFunction = (giftId) -> getReceiveBadgeKey(activityId, giftId);
        }
        
        List<GloryOfCountriesVO.BadgeItemVO> badgeItemVOList = new ArrayList<>();
        int totalDiamondNum = 0;
        
        // 遍历所有礼物ID，获取用户数据
        for (Integer giftId : giftIdBadgeIdMap.keySet()) {
            try {
                String badgeKey = badgeKeyFunction.apply(giftId);
                int giftDiamondNum = activityCommonRedis.getCommonZSetRankingScore(badgeKey, uid);
                
                if (giftDiamondNum > 0) {
                    GloryOfCountriesVO.BadgeItemVO badgeItemVO = new GloryOfCountriesVO.BadgeItemVO();
                    
                    // 设置钻石数量
                    badgeItemVO.setGiftDiamondNum(giftDiamondNum);
                    totalDiamondNum += giftDiamondNum;
                    
                    // 设置国家旗帜和名称
                    ResourceKeyConfigData.ResourceMeta resourceMeta = giftIdMap.get(String.valueOf(giftId));
                    if (resourceMeta != null) {
                        badgeItemVO.setCountryFlag(resourceMeta.getResourceIcon());
                    }
                    
                    // 获取礼物名称作为国家名称
                    GiftData giftData = giftDao.getGiftFromCache(giftId);
                    if (giftData != null) {
                        badgeItemVO.setCountryName(giftData.getGnamear());
                    }
                    
                    badgeItemVOList.add(badgeItemVO);
                }
            } catch (Exception e) {
                logger.error("处理礼物ID{}的徽章数据失败: activityId={} uid={} badgeType={}", 
                           giftId, activityId, uid, badgeType, e);
            }
        }
        
        // 按钻石数量降序排序
        badgeItemVOList.sort((a, b) -> Integer.compare(b.getGiftDiamondNum(), a.getGiftDiamondNum()));
        
        vo.setTotalDiamondNum(totalDiamondNum);
        vo.setBadgeItemVOList(badgeItemVOList);
        
        return vo;
    }


    // 发送礼物统计,统计国家版（发送榜，跟接收榜走通用发送和接收）
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        int totalNum = giftData.getNumber() * giftData.getAid_list().size();
        int giftId = giftData.getGid();
        String fromUid = giftData.getFrom_uid();
        int totalBeans = totalNum * giftData.getPrice();

        String gloryOfCountriesKey = getGloryOfCountriesKey(activityId);
        String supportCountriesKey = getSupportCountriesKey(activityId, giftId);
        activityCommonRedis.incrCommonZSetRankingScore(gloryOfCountriesKey, String.valueOf(giftId), totalNum);
        activityCommonRedis.incrCommonZSetRankingScore(supportCountriesKey, fromUid, totalNum);

        // 记录发送礼物数据到list
        recordGiftSendData(activityId, giftData, giftId);

        // 处理发送礼物勋章
        handleSendGiftBadge(activityId, fromUid, giftId, totalBeans);

        // 处理接收礼物勋章
        for (String toUid : giftData.getAid_list()) {
            handleReceiveGiftBadge(activityId, toUid, giftId, giftData.getNumber()*giftData.getPrice());
        }
    }

    /**
     * 获取最近5条发送礼物记录
     */
    private List<GloryOfCountriesVO.RollRecordVO> getRecentGiftRecords(String activityId) {
        List<GloryOfCountriesVO.RollRecordVO> rollRecordList = new ArrayList<>();
        try {
            String giftRecordKey = getGiftRecordKey(activityId);
            List<String> recordList = activityCommonRedis.getCommonListRecord(giftRecordKey, 5);

            for (String recordJson : recordList) {
                if (StringUtils.isEmpty(recordJson)) {
                    continue;
                }
                try {
                    GiftRecordData recordData = JSON.parseObject(recordJson, GiftRecordData.class);
                    GloryOfCountriesVO.RollRecordVO rollRecord = new GloryOfCountriesVO.RollRecordVO();
                    rollRecord.setUserName(recordData.getUserName());
                    rollRecord.setGiftNum(recordData.getGiftNum());

                    // 使用giftDao获取礼物名称
                    GiftData giftData = giftDao.getGiftFromCache(recordData.getGiftId());
                    rollRecord.setCountryName(giftData != null ? giftData.getGnamear() : "");

                    rollRecordList.add(rollRecord);
                } catch (Exception e) {
                    logger.error("解析礼物记录失败: recordJson={}", recordJson, e);
                }
            }
        } catch (Exception e) {
            logger.error("获取最近礼物记录失败: activityId={}", activityId, e);
        }
        return rollRecordList;
    }

    /**
     * 记录发送礼物数据
     */
    private void recordGiftSendData(String activityId, SendGiftData giftData, int giftId) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(giftData.getFrom_uid());
            if (actorData == null) {
                return;
            }

            GiftRecordData recordData = new GiftRecordData();
            recordData.setUserName(actorData.getName());
            recordData.setGiftId(giftId);
            recordData.setGiftNum(giftData.getNumber() * giftData.getAid_list().size());
            recordData.setTimestamp(System.currentTimeMillis());

            String recordJson = JSON.toJSONString(recordData);
            String giftRecordKey = getGiftRecordKey(activityId);
            activityCommonRedis.addCommonListRecord(giftRecordKey, recordJson);
        } catch (Exception e) {
            logger.error("记录发送礼物数据失败: activityId={} giftData={}", activityId, JSON.toJSONString(giftData), e);
        }
    }

    /**
     * 处理发送礼物勋章
     */
    private void handleSendGiftBadge(String activityId, String uid, int giftId, int giftBeans) {
        try {
            if (!SEND_GIFTID_BADGEID_MAP.containsKey(giftId)) {
                return;
            }

            String sendBadgeKey = getSendBadgeKey(activityId, giftId);
            int oldScore = activityCommonRedis.getCommonZSetRankingScore(sendBadgeKey, uid);
            int newScore = activityCommonRedis.incrCommonZSetRankingScoreSimple(sendBadgeKey, uid, giftBeans);

            // 检查是否需要发放勋章
            badgeService.doGiftBadge(uid, newScore, oldScore, SEND_GIFTID_BADGEID_MAP.get(giftId), SEND_OR_RECEIVE_BEANS_LIST);
        } catch (Exception e) {
            logger.error("处理发送礼物勋章失败: activityId={} uid={} giftId={} giftBeans={}", activityId, uid, giftId, giftBeans, e);
        }
    }

    /**
     * 处理接收礼物勋章
     */
    private void handleReceiveGiftBadge(String activityId, String uid, int giftId, int giftBeans) {
        try {
            if (!RECEIVE_GIFTID_BADGEID_MAP.containsKey(giftId)) {
                return;
            }

            String receiveBadgeKey = getReceiveBadgeKey(activityId, giftId);
            int oldScore = activityCommonRedis.getCommonZSetRankingScore(receiveBadgeKey, uid);
            int newScore = activityCommonRedis.incrCommonZSetRankingScoreSimple(receiveBadgeKey, uid, giftBeans);

            // 检查是否需要发放勋章
            badgeService.doGiftBadge(uid, newScore, oldScore, RECEIVE_GIFTID_BADGEID_MAP.get(giftId), SEND_OR_RECEIVE_BEANS_LIST);
        } catch (Exception e) {
            logger.error("处理接收礼物勋章失败: activityId={} uid={} giftId={} giftBeans={}", activityId, uid, giftId,giftBeans , e);
        }
    }

    /**
     * 礼物记录数据类
     */
    private static class GiftRecordData {
        private String userName;
        private int giftId;
        private int giftNum;
        private long timestamp;

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public int getGiftId() {
            return giftId;
        }

        public void setGiftId(int giftId) {
            this.giftId = giftId;
        }

        public int getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(int giftNum) {
            this.giftNum = giftNum;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }

}
