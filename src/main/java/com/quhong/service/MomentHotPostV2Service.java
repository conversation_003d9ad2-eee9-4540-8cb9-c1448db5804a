package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.MomentHotPostV2VO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.msg.room.UserCommonPopupMessage;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.MomentTopicDao;
import com.quhong.mysql.dao.MomentTopicMemberDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.MomentTopicData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 每周热帖
 */
@Service
public class MomentHotPostV2Service extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(MomentHotPostV2Service.class);
    private static final String ACTIVITY_TITLE_EN = "Weekly Hot Posts";
    private static final String ACTIVITY_TITLE_AR = "المنشورات الرائجة الأسبوعية";
    private static final String ACTIVITY_DESC = "Weekly Hot Posts Reward";
    public static final String ACTIVITY_ID = "67921358fba6f7ebcc91e189";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/weekly_hot_posts2025/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/weekly_hot_posts2025/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/game/op_1720608261_7.png";

    private static final List<TaskConfigVO> MOMENT_DAILY_TASK_LIST = new ArrayList<>();
    private static final List<TaskConfigVO> MOMENT_WEEK_TASK_LIST = new ArrayList<>();
    private static final List<TaskConfigVO> TOPIC_DAILY_TASK_LIST = new ArrayList<>();
    private static final List<TaskConfigVO> TOPIC_WEEK_TASK_LIST = new ArrayList<>();
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final String TASK_RECEIVE_LIKE_MOMENT = "receive_like_moment";
    private static final String TASK_RECEIVE_GIFT_MOMENT = "receive_gift_moment";
    private static final String TASK_RECEIVE_GIFT_ID = "receive_gift_id";
    private static final String TASK_RECEIVE_GIFT_UID = "receive_gift_uid";

    // 任务完成天数
    private static final String MOMENT_WEEK_TASK_FINISH_DAY = "momentWeekFinishDay";
    private static final List<String> MOMENT_LIKE_RANK_KEY = Arrays.asList("hotPostLikeV2Top1", "hotPostLikeV2Top2", "hotPostLikeV2Top3", "hotPostLikeV2Top4-5", "hotPostLikeV2Top6-10");
    private static final List<String> MOMENT_GIFT_RANK_KEY = Arrays.asList("hotPostGiftV2Top1", "hotPostGiftV2Top2", "hotPostGiftV2Top3", "hotPostGiftV2Top4-5", "hotPostGiftV2Top6-10");

    // 话题榜单奖励key
    private static final List<String> TOPIC_POST_RANK_KEY = Arrays.asList("hotTopicPostV2HostTop1", "hotTopicPostV2HostTop2", "hotTopicPostV2HostTop3", "hotTopicPostV2HostTop4-10");
    private static final List<String> TOPIC_POST_MANAGER_RANK_KEY = Arrays.asList("hotTopicPostV2ManagerTop1", "hotTopicPostV2ManagerTop2", "hotTopicPostV2ManagerTop3", "hotTopicPostV2ManagerTop4-10");

    private static final String TOPIC_WEEK_TASK_FINISH_DAY = "topicWeekFinishDay";
    private static final List<String> TASK_TYPE_LIST = Arrays.asList("moment", "topic");
    private static final List<String> TOPIC_RANK_TYPE_LIST = Arrays.asList("post", "like", "gift");

    // 10000、 2133501、2531569、1739435、2914899、1950844、2251044、 10003
    private static final List<String> NOT_SHOW_RANK_USER = Arrays.asList("5cdf784961d047a4adf44064", "5b0737441bad485d2389e679",
            "5b5defb5acc6cb0035b6cc81", "5aafad091bad4807d134c2d4", "5c75364e66dc630028e7323d", "5ade03ef1bad48aa748aa0ee", "5b184dc691f8d0001856a48c", "6708c45f75a0b006f392407b", "67da919addcaf36b7aa8a993");

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(CommonMqTaskConstant.LIKE_MOMENT, CommonMqTaskConstant.CANCEL_LIKE_MOMENT, CommonMqTaskConstant.SEND_MOMENT_GIFT, CommonMqTaskConstant.POST_MOMENT,
            CommonMqTaskConstant.FOLLOW_MOMENT_TOPIC, CommonMqTaskConstant.BECOME_MOMENT_TOPIC_ADMIN);

    static {
        // 朋友圈相关task
        MOMENT_DAILY_TASK_LIST.add(new TaskConfigVO(3, 0, "like_moment", "", "Like Moments", "الإعجاب باللحظات", null, null, null, null, "hotPostTaskAward1"));
        MOMENT_DAILY_TASK_LIST.add(new TaskConfigVO(2, 0, "send_moment_gift", "", "Send Gifts in Moments", "إرسال هدايا في اللحظات", null, null, null, null, "hotPostTaskAward2"));
        MOMENT_DAILY_TASK_LIST.add(new TaskConfigVO(1, 0, "post_moment", "", "Post to Moments", "نشر في اللحظات", null, null, null, null, "hotPostTaskAward3"));
        MOMENT_DAILY_TASK_LIST.add(new TaskConfigVO(3, 0, "receive_like_moment", "", "Received likes from moment", "تلقي الإعجابات من اللحظات", null, null, null, null, "hotPostTaskAward4"));
        MOMENT_DAILY_TASK_LIST.add(new TaskConfigVO(1, 0, "receive_gift_moment", "", "Received gifts from moment", "تلقي الهدايا من اللحظات", null, null, null, null, "hotPostTaskAward5"));

        MOMENT_WEEK_TASK_LIST.add(new TaskConfigVO(1, null, "moment_1", null, "Complete daily tasks for 1 day", "أكمل المهام اليومية لمدة يوم واحد", null, null, null, null, "hotPostTaskDaily1"));
        MOMENT_WEEK_TASK_LIST.add(new TaskConfigVO(2, null, "moment_2", null, "Complete daily tasks for 2 day", "أكمل المهام اليومية لمدة يومين", null, null, null, null, "hotPostTaskDaily2"));
        MOMENT_WEEK_TASK_LIST.add(new TaskConfigVO(3, null, "moment_3", null, "Complete daily tasks for 3 day", "أكمل المهام اليومية لمدة ثلاثة أيام", null, null, null, null, "hotPostTaskDaily3"));
        MOMENT_WEEK_TASK_LIST.add(new TaskConfigVO(5, null, "moment_5", null, "Complete daily tasks for 5 day", "أكمل المهام اليومية لمدة خمسة أيام", null, null, null, null, "hotPostTaskDaily5"));
        MOMENT_WEEK_TASK_LIST.add(new TaskConfigVO(7, null, "moment_7", null, "Complete daily tasks for 7 day", "أكمل المهام اليومية لمدة سبعة ايام", null, null, null, null, "hotPostTaskDaily7"));

        // 话题相关task
        TOPIC_DAILY_TASK_LIST.add(new TaskConfigVO(3, 0, "topic_follow", "", "Follow the Topic", "تابع الموضوع", null, null, null, null, "hotPostTaskAward1"));
        TOPIC_DAILY_TASK_LIST.add(new TaskConfigVO(3, 0, "topic_like", "", "Like the post with Topic", "إعجاب بالمنشور الذي يحتوي على الموضوع ", null, null, null, null, "hotPostTaskAward2"));
        TOPIC_DAILY_TASK_LIST.add(new TaskConfigVO(1, 0, "topic_receive_gift", "", "Send gift to the post with Topic", "إرسال هدية إلى آالمنشور مع الموضوع ", null, null, null, null, "hotPostTaskAward3"));
        TOPIC_DAILY_TASK_LIST.add(new TaskConfigVO(1, 0, "topic_become_administrator", "", "Become a topic administrator", "كن مسؤولاً عن الموضوع", null, null, null, null, "hotPostTaskAward4"));
        TOPIC_DAILY_TASK_LIST.add(new TaskConfigVO(1, 0, "topic_post", "", "Post a moment with Topic", "أضف لحظة مع الموضوع ", null, null, null, null, "hotPostTaskAward5"));

        TOPIC_WEEK_TASK_LIST.add(new TaskConfigVO(1, null, "topic_1", null, "Complete daily tasks for 1 day", "أكمل المهام اليومية لمدة يوم واحد", null, null, null, null, "hotPostTaskDaily1"));
        TOPIC_WEEK_TASK_LIST.add(new TaskConfigVO(2, null, "topic_2", null, "Complete daily tasks for 2 day", "أكمل المهام اليومية لمدة يومين", null, null, null, null, "hotPostTaskDaily2"));
        TOPIC_WEEK_TASK_LIST.add(new TaskConfigVO(3, null, "topic_3", null, "Complete daily tasks for 3 day", "أكمل المهام اليومية لمدة ثلاثة أيام", null, null, null, null, "hotPostTaskDaily3"));
        TOPIC_WEEK_TASK_LIST.add(new TaskConfigVO(5, null, "topic_5", null, "Complete daily tasks for 5 day", "أكمل المهام اليومية لمدة خمسة أيام", null, null, null, null, "hotPostTaskDaily5"));
        TOPIC_WEEK_TASK_LIST.add(new TaskConfigVO(7, null, "topic_7", null, "Complete daily tasks for 7 day", "أكمل المهام اليومية لمدة سبعة ايام", null, null, null, null, "hotPostTaskDaily7"));


    }

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private GiftDao giftDao;
    @Resource
    private MomentTopicDao momentTopicDao;
    @Resource
    private MomentTopicMemberDao momentTopicMemberDao;

    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    /**
     * 朋友圈榜单相关key
     */
    // 朋友圈点赞rank-key
    private String getWeeklyLikeRankKey(String activityId, Integer roundNum) {
        return String.format("weeklyLikeRank:%s:%s", activityId, roundNum);
    }

    // 朋友圈收礼rank-key
    private String getWeeklyGiftRankKey(String activityId, Integer roundNum) {
        return String.format("weeklyGiftRank:%s:%s", activityId, roundNum);
    }


    /**
     * 话题榜单相关rank-key
     */
    private String getTopicRankKey(String activityId, String rankType, Integer roundNum) {
        return String.format("topicWeekly%sRank:%s:%s", rankType, activityId, roundNum);
    }

    private String getTopicTnLikeZSetKey(String activityId, int roundNum, String dateStr) {
        return String.format("hotPostTopicTnLike:%s:%s:%s", activityId, roundNum, dateStr);
    }

    /**
     * 每日任务及状态
     */
    private String getDailyHashActivityId(String activityId, String uid, int roundNum, String dateStr) {
        return String.format("hotPostDailyTask:%s:%s:%s:%s", activityId, uid, roundNum, dateStr);
    }

    private String getDailyHashGiftReceiveActivityId(String activityId, String uid, int roundNum, String dateStr) {
        return String.format("hotPostDailyGift:%s:%s:%s:%s", activityId, uid, roundNum, dateStr);
    }

    // 每日任务奖励设备去重
    private String getDailySetActivityId(String activityId, String taskKey, int roundNum, String dateStr) {
        return String.format("hotPostSet:%s:%s:%s:%s", activityId, taskKey, roundNum, dateStr);
    }

    // 任务对象去重
    private String getDailyTaskSetActivityId(String activityId, String taskKey, String uid, int roundNum, String dateStr) {
        return String.format("hotPostDailyTaskSet:%s:%s:%s:%s:%s", activityId, taskKey, uid, roundNum, dateStr);
    }

    // 任务奖励领取状态
    private String getTaskStatusKey(String taskKey) {
        return String.format("%s:status", taskKey);
    }

    /**
     * 周任务及状态
     */
    private String getWeekHashActivityId(String activityId, String uid, int roundNum) {
        return String.format("hotPost:%s:%s:%s", activityId, uid, roundNum);
    }

    private String getWeekTaskStatusKey(String key) {
        return String.format("%s:status", key);
    }

    // 周任务奖励设备去重
    private String getWeekFinishDaySetActivityId(String activityId, String taskKey, int roundNum) {
        return String.format("hotPostFinishDaySet:%s:%s:%s", activityId, taskKey, roundNum);
    }


    public List<MomentHotPostV2VO.HotPostRank> makePostRankingData(String rankKey) {
        List<MomentHotPostV2VO.HotPostRank> rankingList = new ArrayList<>();
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankKey, 50);
        Set<String> setData = new HashSet<>();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String momentId = entry.getKey();
            MomentActivityData momentData = momentActivityDao.findMomentById(momentId);
            if (momentData == null) {
                continue;
            }
            String uid = momentData.getUid();
            long score = entry.getValue();
            if (score <= 0) {
                continue;
            }

            if (NOT_SHOW_RANK_USER.contains(uid)) {
                continue;
            }

            if (setData.contains(uid)) {
                continue;
            }
            setData.add(uid);
            MomentHotPostV2VO.HotPostRank hotPostRank = new MomentHotPostV2VO.HotPostRank();
            ActorData rankActor = actorDao.getActorDataFromCache(uid);
            hotPostRank.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            hotPostRank.setUid(uid);
            hotPostRank.setMomentId(momentId);
            hotPostRank.setText(momentData.getText());
            hotPostRank.setQuote(momentData.getQuote());
            hotPostRank.setImgs(momentData.getImgs());
            hotPostRank.setC_time(momentData.getC_time());
            hotPostRank.setScore(entry.getValue());
            rankingList.add(hotPostRank);
            if (rankingList.size() >= 10) {
                break;
            }
        }
        return rankingList;
    }

    public List<MomentHotPostV2VO.HotTopicRank> makeTopicRankingData(String rankKey) {
        List<MomentHotPostV2VO.HotTopicRank> rankingList = new ArrayList<>();
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(rankKey, 20);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String topicIdStr = entry.getKey();
            MomentTopicData momentTopicData = momentTopicDao.selectByIdFromCache(Integer.valueOf(topicIdStr));
            if (momentTopicData == null) {
                continue;
            }

            String topicOwnerUid = momentTopicData.getOwnerUid();
            long score = entry.getValue();
            if (score <= 0) {
                continue;
            }

            if (NOT_SHOW_RANK_USER.contains(topicOwnerUid)) {
                continue;
            }

            MomentHotPostV2VO.HotTopicRank hotPostRank = new MomentHotPostV2VO.HotTopicRank();
            hotPostRank.setTopicId(momentTopicData.getRid());
            hotPostRank.setTopicName(momentTopicData.getName());
            hotPostRank.setAnnounce(momentTopicData.getAnnounce());
            hotPostRank.setTopicHead(ImageUrlGenerator.generateNormalUrl(momentTopicData.getHead()));
            hotPostRank.setScore(entry.getValue());
            rankingList.add(hotPostRank);
            if (rankingList.size() >= 10) {
                break;
            }
        }
        return rankingList;
    }

    private String getCurrentDay(String activityId) {
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }

    public MomentHotPostV2VO momentHotPostConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        MomentHotPostV2VO vo = new MomentHotPostV2VO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        int roundNum = activity.getRoundNum();
        int lastRoundNum = roundNum - 1;

        // 设置朋友圈点赞榜、收礼榜、任务
        fillMomentRankData(activityId, vo, roundNum, lastRoundNum);
        fillTopicRankData(activityId, vo, roundNum, lastRoundNum);
        fillHotTaskData(activityId, uid, vo, roundNum);
        return vo;
    }

    private void fillHotTaskData(String activityId, String uid, MomentHotPostV2VO vo, int roundNum) {
        // 设置每日任务
        String currentDate = getCurrentDay(activityId);
        String dailyHashActivityId = getDailyHashActivityId(activityId, uid, roundNum, currentDate);
        Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        List<TaskConfigVO> dailyMomentTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : MOMENT_DAILY_TASK_LIST) {
            TaskConfigVO taskConfigVO = new TaskConfigVO();
            BeanUtils.copyProperties(taskConfig, taskConfigVO);
            String taskKey = taskConfig.getTaskKey();
            int currentProcess = userDailyMap.getOrDefault(taskKey, 0);
            String dailyTaskStatusHashKey = getTaskStatusKey(taskKey);
            int taskStatus = userDailyMap.getOrDefault(dailyTaskStatusHashKey, 0);
            taskConfigVO.setStatus(taskStatus > 0 ? 2 : (currentProcess >= taskConfig.getTotalProcess() ? 1 : 0));
            taskConfigVO.setCurrentProcess(Math.min(taskConfig.getTotalProcess(), currentProcess));

            if (TASK_RECEIVE_GIFT_MOMENT.equals(taskConfig.getTaskKey()) && taskConfigVO.getCurrentProcess() > 0) {
                String receiveActivityId = getDailyHashGiftReceiveActivityId(activityId, uid, roundNum, currentDate);
                Map<String, String> receiveDailyMap = activityCommonRedis.getCommonHashAllMapStr(receiveActivityId);
                String giftFromUid = receiveDailyMap.get(TASK_RECEIVE_GIFT_UID);
                if (!StringUtils.isEmpty(giftFromUid)) {
                    taskConfigVO.setTargetId(giftFromUid);
                    ActorData fromActorData = actorDao.getActorDataFromCache(giftFromUid);
                    taskConfigVO.setTargetId(giftFromUid);
                    taskConfigVO.setTargetIcon(ImageUrlGenerator.generateRoomUserUrl(fromActorData.getHead()));
                }
                String giftIdStr = receiveDailyMap.get(TASK_RECEIVE_GIFT_ID);
                if (!StringUtils.isEmpty(giftIdStr)) {
                    GiftData giftData = giftDao.getGiftFromCache(Integer.parseInt(giftIdStr));
                    taskConfigVO.setGiftIcon(giftData.getGicon());
                }
            }
            dailyMomentTaskList.add(taskConfigVO);
        }
        vo.setMomentDailyTaskList(dailyMomentTaskList);

        // 设置周任务完成天数
        String weekTaskKey = getWeekHashActivityId(activityId, uid, roundNum);
        Map<String, Integer> userWeekFinishDayMap = activityCommonRedis.getCommonHashAll(weekTaskKey);
        int weekMomentFinishDayNum = userWeekFinishDayMap.getOrDefault(MOMENT_WEEK_TASK_FINISH_DAY, 0);
        List<TaskConfigVO> weekTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : MOMENT_WEEK_TASK_LIST) {
            TaskConfigVO dateVO = new TaskConfigVO();
            BeanUtils.copyProperties(taskConfig, dateVO);
            String weekFinishStatusHKey = getWeekTaskStatusKey(taskConfig.getTaskKey());
            int weekFinishStatus = userWeekFinishDayMap.getOrDefault(weekFinishStatusHKey, 0);
            dateVO.setCurrentProcess(Math.min(taskConfig.getTotalProcess(), weekMomentFinishDayNum));
            dateVO.setStatus(weekFinishStatus > 0 ? 2 : (weekMomentFinishDayNum >= taskConfig.getTotalProcess() ? 1 : 0));
            weekTaskList.add(dateVO);
        }
        vo.setMomentWeekTaskList(weekTaskList);


        // 设置话题每日任务
        List<TaskConfigVO> dailyTopicTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : TOPIC_DAILY_TASK_LIST) {
            TaskConfigVO taskConfigVO = new TaskConfigVO();
            BeanUtils.copyProperties(taskConfig, taskConfigVO);
            String taskKey = taskConfig.getTaskKey();
            int currentProcess = userDailyMap.getOrDefault(taskKey, 0);
            String dailyTaskStatusHashKey = getTaskStatusKey(taskKey);
            int taskStatus = userDailyMap.getOrDefault(dailyTaskStatusHashKey, 0);
            taskConfigVO.setStatus(taskStatus > 0 ? 2 : (currentProcess >= taskConfig.getTotalProcess() ? 1 : 0));
            taskConfigVO.setCurrentProcess(Math.min(taskConfig.getTotalProcess(), currentProcess));
            dailyTopicTaskList.add(taskConfigVO);
        }
        vo.setTopicDailyTaskList(dailyTopicTaskList);

        // 设置话题周任务完成天数
        int weekTopicFinishDayNum = userWeekFinishDayMap.getOrDefault(TOPIC_WEEK_TASK_FINISH_DAY, 0);
        List<TaskConfigVO> weekTopicTaskList = new ArrayList<>();
        for (TaskConfigVO taskConfig : TOPIC_WEEK_TASK_LIST) {
            TaskConfigVO dateVO = new TaskConfigVO();
            BeanUtils.copyProperties(taskConfig, dateVO);
            String weekFinishStatusHKey = getWeekTaskStatusKey(taskConfig.getTaskKey());
            int weekFinishStatus = userWeekFinishDayMap.getOrDefault(weekFinishStatusHKey, 0);
            dateVO.setCurrentProcess(Math.min(taskConfig.getTotalProcess(), weekTopicFinishDayNum));
            dateVO.setStatus(weekFinishStatus > 0 ? 2 : (weekTopicFinishDayNum >= taskConfig.getTotalProcess() ? 1 : 0));
            weekTopicTaskList.add(dateVO);
        }
        vo.setTopicWeekTaskList(weekTopicTaskList);
    }


    private void fillMomentRankData(String activityId, MomentHotPostV2VO vo, int roundNum, int lastRoundNum) {
        String lastLikeRankKey = getWeeklyLikeRankKey(activityId, lastRoundNum);
        List<MomentHotPostV2VO.HotPostRank> lastLikeRankList = this.makePostRankingData(lastLikeRankKey);
        vo.setMomentLikeLastWeekRank(lastLikeRankList.size() >= 3 ? lastLikeRankList.subList(0, 3) : lastLikeRankList);
        vo.setMomentLikeThisWeekRank(this.makePostRankingData(getWeeklyLikeRankKey(activityId, roundNum)));

        String lastGiftRankKey = getWeeklyGiftRankKey(activityId, lastRoundNum);
        List<MomentHotPostV2VO.HotPostRank> lastGiftRankList = this.makePostRankingData(lastGiftRankKey);
        vo.setMomentGiftLastWeekRank(lastGiftRankList.size() >= 3 ? lastGiftRankList.subList(0, 3) : lastGiftRankList);
        vo.setMomentGiftThisWeekRank(this.makePostRankingData(getWeeklyGiftRankKey(activityId, roundNum)));
    }


    private void fillTopicRankData(String activityId, MomentHotPostV2VO vo, int roundNum, int lastRoundNum) {
        for (String rankType : TOPIC_RANK_TYPE_LIST) {
            String lastRankKey = getTopicRankKey(activityId, rankType, lastRoundNum);
            List<MomentHotPostV2VO.HotTopicRank> lastRankList = this.makeTopicRankingData(lastRankKey);
            List<MomentHotPostV2VO.HotTopicRank> thisRankList = this.makeTopicRankingData(getTopicRankKey(activityId, rankType, roundNum));
            if (TOPIC_RANK_TYPE_LIST.get(0).equals(rankType)) {
                vo.setTopicPostLastWeekRank(lastRankList.size() >= 3 ? lastRankList.subList(0, 3) : lastRankList);
                vo.setTopicPostThisWeekRank(thisRankList);
            } else if (TOPIC_RANK_TYPE_LIST.get(1).equals(rankType)) {
                vo.setTopicLikeLastWeekRank(lastRankList.size() >= 3 ? lastRankList.subList(0, 3) : lastRankList);
                vo.setTopicLikeThisWeekRank(thisRankList);
            } else {
                vo.setTopicGiftLastWeekRank(lastRankList.size() >= 3 ? lastRankList.subList(0, 3) : lastRankList);
                vo.setTopicGiftThisWeekRank(thisRankList);
            }
        }
    }


    /**
     * 获取每日任务奖励
     */
    public void momentHotPostV2DailyGet(String activityId, String uid, String taskType, String taskKey) {
        if (!TASK_TYPE_LIST.contains(taskType)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = checkActivityTime(activityId);
        int roundNum = activityData.getRoundNum();
        synchronized (stringPool.intern("dailyGet" + uid)) {
            List<TaskConfigVO> dailyTaskList = TASK_TYPE_LIST.get(0).equals(taskType) ? MOMENT_DAILY_TASK_LIST : TOPIC_DAILY_TASK_LIST;
            Map<String, TaskConfigVO> taskConfigMap = dailyTaskList.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = taskConfigMap.get(taskKey);
            if (taskConfig == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            String currentDate = getCurrentDay(activityId);
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, roundNum, currentDate);
            Map<String, Integer> taskNumMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int currentNum = taskNumMap.getOrDefault(taskKey, 0);
            if (currentNum < taskConfig.getTotalProcess()) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }
            String dailyTaskStatusHashKey = getTaskStatusKey(taskKey);
            int taskStatus = taskNumMap.getOrDefault(dailyTaskStatusHashKey, 0);
            if (taskStatus >= 1) {
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null || StringUtils.isEmpty(actorData.getTn_id())) {
                logger.info("handleDailyTask not user uid:{}", uid);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            String tnId = actorData.getTn_id();
            String dailySetActivityId = getDailySetActivityId(ACTIVITY_ID, taskKey, roundNum, currentDate);
            if (activityCommonRedis.isCommonSetData(dailySetActivityId, tnId) > 0) {
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "تم استلام المكافآت لنفس الجهاز ولا يمكن استلامها مرة أخرى~");
            }
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, dailyTaskStatusHashKey, 2);
            activityCommonRedis.addCommonSetData(dailySetActivityId, tnId);
            String rewardDesc = TASK_TYPE_LIST.get(0).equals(taskType) ? "MomentActivity2-common task reward" : "MomentActivity2-topic task reward";
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, rewardDesc, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
        }
    }

    /**
     * 获取周任务奖励
     */
    public void momentHotPostV2WeeklyGet(String activityId, String uid, String taskType, String taskKey) {
        if (!TASK_TYPE_LIST.contains(taskType)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = checkActivityTime(activityId);
        int roundNum = activityData.getRoundNum();
        synchronized (stringPool.intern("weeklyGet" + uid)) {
            List<TaskConfigVO> weekTaskList = TASK_TYPE_LIST.get(0).equals(taskType) ? MOMENT_WEEK_TASK_LIST : TOPIC_WEEK_TASK_LIST;
            Map<String, TaskConfigVO> taskConfigMap = weekTaskList.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = taskConfigMap.get(taskKey);
            if (taskConfig == null) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            String weekFinishDayKey = getWeekHashActivityId(activityId, uid, activityData.getRoundNum());
            Map<String, Integer> userWeekFinishDayMap = activityCommonRedis.getCommonHashAll(weekFinishDayKey);

            String weekFinishDayHKey = TASK_TYPE_LIST.get(0).equals(taskType) ? MOMENT_WEEK_TASK_FINISH_DAY : TOPIC_WEEK_TASK_FINISH_DAY;
            int weekFinishDayNum = userWeekFinishDayMap.getOrDefault(weekFinishDayHKey, 0);
            if (weekFinishDayNum < taskConfig.getTotalProcess()) {
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }

            String weekFinishStatusKey = getWeekTaskStatusKey(taskKey);
            int weekFinishStatus = userWeekFinishDayMap.getOrDefault(weekFinishStatusKey, 0);
            if (weekFinishStatus > 0) {
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (actorData == null || StringUtils.isEmpty(actorData.getTn_id())) {
                logger.info("momentHotPostV2WeeklyGet not user uid:{}", uid);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            String tnId = actorData.getTn_id();
            String weekFinishDaySetKey = getWeekFinishDaySetActivityId(activityId, taskKey, roundNum);
            if (activityCommonRedis.isCommonSetData(weekFinishDaySetKey, tnId) > 0) {
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "تم استلام المكافآت لنفس الجهاز ولا يمكن استلامها مرة أخرى~");
            }
            activityCommonRedis.setCommonHashNum(weekFinishDayKey, weekFinishStatusKey, 1);
            activityCommonRedis.addCommonSetData(weekFinishDaySetKey, tnId);
            String rewardDesc = TASK_TYPE_LIST.get(0).equals(taskType) ? "MomentActivity2-common week reward" : "MomentActivity2-topic week reward";
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, rewardDesc, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
        }
    }

    // 处理奖励数据
    public void handleMomentHotTopRankingSend(OtherRankingActivityData activity) {
        try {
            String activityId = activity.get_id().toString();
            String acNameEn = activity.getAcNameEn();
            String acNameAr = activity.getAcNameAr();
            int roundNum = activity.getRoundNum();
            String likeRankKey = getWeeklyLikeRankKey(activityId, roundNum);
            List<MomentHotPostV2VO.HotPostRank> thisLikeRankList = this.makePostRankingData(likeRankKey);
            int likeRank = 1;
            for (MomentHotPostV2VO.HotPostRank hotPostRank : thisLikeRankList) {
                String resourceKey = null;
                if (likeRank == 1) {
                    resourceKey = MOMENT_LIKE_RANK_KEY.get(0);
                } else if (likeRank == 2) {
                    resourceKey = MOMENT_LIKE_RANK_KEY.get(1);
                } else if (likeRank == 3) {
                    resourceKey = MOMENT_LIKE_RANK_KEY.get(2);
                } else if (likeRank >= 4 && likeRank <= 5) {
                    resourceKey = MOMENT_LIKE_RANK_KEY.get(3);
                } else {
                    resourceKey = MOMENT_LIKE_RANK_KEY.get(4);
                }
                resourceKeyHandlerService.sendResourceData(hotPostRank.getUid(), resourceKey, acNameEn, acNameAr, acNameEn, ACTIVITY_URL, "");
                likeRank += 1;
            }

            String giftRankKey = getWeeklyGiftRankKey(activityId, roundNum);
            List<MomentHotPostV2VO.HotPostRank> thisGiftRankList = this.makePostRankingData(giftRankKey);
            int giftRank = 1;
            for (MomentHotPostV2VO.HotPostRank hotPostRank : thisGiftRankList) {
                String resourceKey = null;
                if (giftRank == 1) {
                    resourceKey = MOMENT_GIFT_RANK_KEY.get(0);
                } else if (giftRank == 2) {
                    resourceKey = MOMENT_GIFT_RANK_KEY.get(1);
                } else if (giftRank == 3) {
                    resourceKey = MOMENT_GIFT_RANK_KEY.get(2);
                } else if (giftRank >= 4 && giftRank <= 5) {
                    resourceKey = MOMENT_GIFT_RANK_KEY.get(3);
                } else {
                    resourceKey = MOMENT_GIFT_RANK_KEY.get(4);
                }
                resourceKeyHandlerService.sendResourceData(hotPostRank.getUid(), resourceKey, acNameEn, acNameAr, acNameEn, ACTIVITY_URL, "");
                giftRank += 1;
            }


            // 话题榜单奖励
            for (String rankType : TOPIC_RANK_TYPE_LIST) {
                List<MomentHotPostV2VO.HotTopicRank> thisTopicRankList = this.makeTopicRankingData(getTopicRankKey(activityId, rankType, roundNum));
                List<String> topicHostRankKey = TOPIC_POST_RANK_KEY;
                List<String> topicManagerRankKey = TOPIC_POST_MANAGER_RANK_KEY;
                int topicRank = 1;
                for (MomentHotPostV2VO.HotTopicRank hotTopicRank : thisTopicRankList) {
                    String hostResKey = null;
                    String managerResKey = null;
                    if (topicRank == 1) {
                        hostResKey = topicHostRankKey.get(0);
                        managerResKey = topicManagerRankKey.get(0);
                    } else if (topicRank == 2) {
                        hostResKey = topicHostRankKey.get(1);
                        managerResKey = topicManagerRankKey.get(1);
                    } else if (topicRank == 3) {
                        hostResKey = topicHostRankKey.get(2);
                        managerResKey = topicManagerRankKey.get(2);
                    }  else {
                        hostResKey = topicHostRankKey.get(3);
                        managerResKey = topicManagerRankKey.get(3);
                    }

                    int topicId = hotTopicRank.getTopicId();
                    MomentTopicData momentTopicData = momentTopicDao.selectByTopicRid(topicId);
                    String topicOwnerUid = momentTopicData.getOwnerUid();
                    resourceKeyHandlerService.sendResourceData(topicOwnerUid, hostResKey, acNameEn, acNameAr, acNameEn, ACTIVITY_URL, "");

                    Set<String> allAdminSet = momentTopicMemberDao.selectAllAdminList(topicId);
                    if(!CollectionUtils.isEmpty(allAdminSet)){
                        for (String adminUid : allAdminSet) {
                            if(adminUid.equals(topicOwnerUid)){
                                continue;
                            }
                            resourceKeyHandlerService.sendResourceData(adminUid, managerResKey, acNameEn, acNameAr, acNameEn, ACTIVITY_URL, "");
                        }
                    }
                    topicRank += 1;
                }
            }
        } catch (Exception e) {
            logger.error("handleMomentHotTOPRankingSend e={}", e.getMessage(), e);
        }
    }

    private void handleMomentDailyTask(String uid, String taskKey, int roundNum, int incNum, String jsonData, String fromUid, String handleId) {
        Map<String, TaskConfigVO> taskConfigMap = MOMENT_DAILY_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
        TaskConfigVO taskConfig = taskConfigMap.get(taskKey);
        if (taskConfig == null) {
            return;
        }
        synchronized (stringPool.intern("momentDaily" + uid)) {
            String currentDate = getCurrentDay(ACTIVITY_ID);
            String dailyHashActivityId = getDailyHashActivityId(ACTIVITY_ID, uid, roundNum, currentDate);
            Map<String, Integer> dailyTaskNumMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int taskCurrentNum = dailyTaskNumMap.getOrDefault(taskKey, 0);
            logger.info("handleDailyTask currentDate:{}, dailyHashActivityId:{}, taskNumMap:{}", currentDate, dailyHashActivityId, dailyTaskNumMap);
            if (taskCurrentNum >= taskConfig.getTotalProcess()) {
                return;
            }

            if (MOMENT_DAILY_TASK_LIST.get(0).getTaskKey().equals(taskKey) || MOMENT_DAILY_TASK_LIST.get(3).getTaskKey().equals(taskKey)) {
                String setDataId = MOMENT_DAILY_TASK_LIST.get(3).getTaskKey().equals(taskKey) ? fromUid : handleId;
                String dailyTaskSetKey = getDailyTaskSetActivityId(ACTIVITY_ID, taskKey, uid, roundNum, currentDate);
                if (activityCommonRedis.isCommonSetData(dailyTaskSetKey, setDataId) > 0) {
                    logger.info("dailyTaskSet not user uid:{}, setDataId:{}", uid, setDataId);
                    return;
                }
                activityCommonRedis.addCommonSetData(dailyTaskSetKey, setDataId);
            }

            int afterNum = activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskKey, incNum);
            dailyTaskNumMap.put(taskKey, afterNum);
            if (afterNum >= taskConfig.getTotalProcess()) {
                if (MOMENT_DAILY_TASK_LIST.get(3).getTaskKey().equals(taskKey) || MOMENT_DAILY_TASK_LIST.get(4).getTaskKey().equals(taskKey)) {
                    UserCommonPopupMessage userMsg = new UserCommonPopupMessage();
                    userMsg.setUid(uid);
                    userMsg.setIcon(TASK_RECEIVE_LIKE_MOMENT.equals(taskKey) ? "https://cdn3.qmovies.tv/gift/op_1694762495_coffee.png" : "https://cdn3.qmovies.tv/gift/op_sys_1659425236_cover_1_1.png");
                    userMsg.setTitleEn(ACTIVITY_TITLE_EN);
                    userMsg.setTitleAr(ACTIVITY_TITLE_AR);
                    userMsg.setTextEn("Activity rewards has already received.");
                    userMsg.setTextAr("المنشورات الرائجة الأسبوعية");
                    userMsg.setActionType(19);
                    userMsg.setActionValue(ACTIVITY_URL);
                    roomWebSender.sendPlayerWebMsg("", uid, uid, userMsg, false);
                }

                // 特殊展示礼物及发送者
                if (TASK_RECEIVE_GIFT_MOMENT.equals(taskKey)) {
                    String dailyHashGiftActivityId = getDailyHashGiftReceiveActivityId(ACTIVITY_ID, uid, roundNum, currentDate);
                    activityCommonRedis.setCommonHashData(dailyHashGiftActivityId, TASK_RECEIVE_GIFT_ID, jsonData);
                    activityCommonRedis.setCommonHashData(dailyHashGiftActivityId, TASK_RECEIVE_GIFT_UID, fromUid);
                }

                // 增加完成天数
                int finishNum = 0;
                for (TaskConfigVO task : MOMENT_DAILY_TASK_LIST) {
                    int currentProcess = dailyTaskNumMap.getOrDefault(task.getTaskKey(), 0);
                    if (currentProcess >= task.getTotalProcess()) {
                        finishNum += 1;
                    }
                }
                if (finishNum >= MOMENT_DAILY_TASK_LIST.size()) {
                    String weekTaskKey = getWeekHashActivityId(ACTIVITY_ID, uid, roundNum);
                    activityCommonRedis.incCommonHashNum(weekTaskKey, MOMENT_WEEK_TASK_FINISH_DAY, 1);
                }
            }
        }
    }

    private void handleTopicDailyTask(String uid, String taskKey, int roundNum, int incNum, String handleId, boolean setFlag) {
        Map<String, TaskConfigVO> taskConfigMap = TOPIC_DAILY_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
        TaskConfigVO taskConfig = taskConfigMap.get(taskKey);
        if (taskConfig == null) {
            return;
        }
        synchronized (stringPool.intern("topicDaily" + uid)) {
            String currentDate = getCurrentDay(ACTIVITY_ID);
            String dailyHashActivityId = getDailyHashActivityId(ACTIVITY_ID, uid, roundNum, currentDate);
            Map<String, Integer> dailyTopicTaskNumMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int taskCurrentNum = dailyTopicTaskNumMap.getOrDefault(taskKey, 0);
            if (taskCurrentNum >= taskConfig.getTotalProcess()) {
                return;
            }

            if (setFlag) {
                String dailyTaskSetKey = getDailyTaskSetActivityId(ACTIVITY_ID, taskKey, uid, roundNum, currentDate);
                if (activityCommonRedis.isCommonSetData(dailyTaskSetKey, handleId) > 0) {
                    return;
                }
                activityCommonRedis.addCommonSetData(dailyTaskSetKey, handleId);
            }

            int afterNum = activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskKey, incNum);
            dailyTopicTaskNumMap.put(taskKey, afterNum);
            if (afterNum >= taskConfig.getTotalProcess()) {
                // 增加完成天数

                int finishTopicNum = 0;
                for (TaskConfigVO task : TOPIC_DAILY_TASK_LIST) {
                    int currentProcess = dailyTopicTaskNumMap.getOrDefault(task.getTaskKey(), 0);
                    if (currentProcess >= task.getTotalProcess()) {
                        finishTopicNum += 1;
                    }
                }
                logger.info("handleTopicDailyTask uid:{}, currentDate:{}, taskKey:{}, finishTopicNum:{}, dailyTopicTaskNumMap:{}, taskConfig:{}", uid, currentDate, taskKey, finishTopicNum, dailyTopicTaskNumMap, JSONObject.toJSONString(taskConfig));
                if (finishTopicNum >= TOPIC_DAILY_TASK_LIST.size()) {
                    String weekTaskKey = getWeekHashActivityId(ACTIVITY_ID, uid, roundNum);
                    activityCommonRedis.incCommonHashNum(weekTaskKey, TOPIC_WEEK_TASK_FINISH_DAY, 1);
                }
            }
        }
    }

    private void handleMomentRankTask(String fromUid, String toUid, CommonMqTopicData data,  String handleId,  int startTime, int roundNum, String jsonData){
        String item = data.getItem();
        if (ObjectUtils.isEmpty(handleId) || handleId.length() != 24 || new ObjectId(handleId).getTimestamp() < startTime) {
            return;
        }
        switch (item) {
            case CommonMqTaskConstant.LIKE_MOMENT:
                // 帖子点赞统计
                activityCommonRedis.incrCommonZSetRankingScore(getWeeklyLikeRankKey(ACTIVITY_ID, roundNum), handleId, 1);
                if (!fromUid.equals(toUid)) {
                    this.handleMomentDailyTask(fromUid, MOMENT_DAILY_TASK_LIST.get(0).getTaskKey(), roundNum, 1, jsonData, null, handleId);
                    this.handleMomentDailyTask(toUid, MOMENT_DAILY_TASK_LIST.get(3).getTaskKey(), roundNum, 1, jsonData, fromUid, handleId);
                }
                break;
            case CommonMqTaskConstant.CANCEL_LIKE_MOMENT:
                // 帖子取消点赞统计
                activityCommonRedis.incrCommonZSetRankingScore(getWeeklyLikeRankKey(ACTIVITY_ID, roundNum), handleId, -1);
                break;
            case CommonMqTaskConstant.SEND_MOMENT_GIFT:
                // 帖子送礼统计
                int totalBeans = data.getRemainValue();
                activityCommonRedis.incrCommonZSetRankingScore(getWeeklyGiftRankKey(ACTIVITY_ID, roundNum), handleId, totalBeans);
                this.handleMomentDailyTask(fromUid, MOMENT_DAILY_TASK_LIST.get(1).getTaskKey(), roundNum, 1, jsonData, "", handleId);
                this.handleMomentDailyTask(toUid, MOMENT_DAILY_TASK_LIST.get(4).getTaskKey(), roundNum, 1, jsonData, fromUid, handleId);
                break;
            case CommonMqTaskConstant.POST_MOMENT:
                // 统计话题发帖数
                this.handleMomentDailyTask(fromUid, MOMENT_DAILY_TASK_LIST.get(2).getTaskKey(), roundNum, 1, jsonData, null, handleId);
                break;
        }
    }

    private void handleTopicRankTask(String fromUid, String toUid, CommonMqTopicData data,  String handleId, int roundNum, String currentDate){
        String item = data.getItem();
        MomentActivityData momentActivityData = momentActivityDao.findMomentById(handleId);
        String topicId = momentActivityData != null && momentActivityData.getTopicId() > 0 ? String.valueOf(momentActivityData.getTopicId()) : "";
        ActorData actorData = actorDao.getActorDataFromCache(fromUid);
        String tnId = actorData.getTn_id();
        switch (item) {
            case CommonMqTaskConstant.LIKE_MOMENT:
                // 话题点赞统计
                if (!ObjectUtils.isEmpty(topicId) && !ObjectUtils.isEmpty(tnId)) {
                    String topicTnLikeZSetKey = getTopicTnLikeZSetKey(ACTIVITY_ID, roundNum, currentDate);
                    int tnLikeNum = activityCommonRedis.getCommonZSetRankingScore(topicTnLikeZSetKey, tnId);
                    if(tnLikeNum < 10){
                        activityCommonRedis.incrCommonZSetRankingScore(topicTnLikeZSetKey, tnId, 1);
                        activityCommonRedis.incrCommonZSetRankingScore(getTopicRankKey(ACTIVITY_ID, TOPIC_RANK_TYPE_LIST.get(1), roundNum), topicId, 1);
                    }
                }

                if (!fromUid.equals(toUid) && !ObjectUtils.isEmpty(topicId)) {
                    this.handleTopicDailyTask(fromUid, TOPIC_DAILY_TASK_LIST.get(1).getTaskKey(), roundNum, 1, topicId, true);
                }
                break;
            case CommonMqTaskConstant.CANCEL_LIKE_MOMENT:
                // 话题取消点赞统计
                if (!ObjectUtils.isEmpty(topicId) && !ObjectUtils.isEmpty(tnId)) {
                    String topicTnLikeZSetKey = getTopicTnLikeZSetKey(ACTIVITY_ID, roundNum, currentDate);
                    int tnLikeNum = activityCommonRedis.getCommonZSetRankingScore(topicTnLikeZSetKey, tnId);
                    if (tnLikeNum > 0){
                        activityCommonRedis.incrCommonZSetRankingScore(topicTnLikeZSetKey, tnId, -1);
                        activityCommonRedis.incrCommonZSetRankingScore(getTopicRankKey(ACTIVITY_ID, TOPIC_RANK_TYPE_LIST.get(1), roundNum), topicId, -1);
                    }
                }
                break;
            case CommonMqTaskConstant.SEND_MOMENT_GIFT:
                int totalBeans = data.getRemainValue();
                // 话题送礼统计
                if (!ObjectUtils.isEmpty(topicId)) {
                    activityCommonRedis.incrCommonZSetRankingScore(getTopicRankKey(ACTIVITY_ID, TOPIC_RANK_TYPE_LIST.get(2), roundNum), topicId, totalBeans);
                    this.handleTopicDailyTask(fromUid, TOPIC_DAILY_TASK_LIST.get(2).getTaskKey(), roundNum, 1, topicId, false);
                }
                break;
            case CommonMqTaskConstant.POST_MOMENT:
                // 统计话题发帖数
                if (!ObjectUtils.isEmpty(topicId)) {
                    int afterPostNum = activityCommonRedis.incCommonHashNum(getDailyHashActivityId(ACTIVITY_ID, fromUid, roundNum, currentDate), "rank_post_moment", 1);
                    if (afterPostNum <= 3) {
                        activityCommonRedis.incrCommonZSetRankingScore(getTopicRankKey(ACTIVITY_ID, TOPIC_RANK_TYPE_LIST.get(0), roundNum), topicId, 1);
                    }
                    this.handleTopicDailyTask(fromUid, TOPIC_DAILY_TASK_LIST.get(4).getTaskKey(), roundNum, 1, topicId, false);
                }
                break;
            case CommonMqTaskConstant.FOLLOW_MOMENT_TOPIC:
                this.handleTopicDailyTask(fromUid, TOPIC_DAILY_TASK_LIST.get(0).getTaskKey(), roundNum, 1, handleId, true);
                break;
            case CommonMqTaskConstant.BECOME_MOMENT_TOPIC_ADMIN:
                this.handleTopicDailyTask(fromUid, TOPIC_DAILY_TASK_LIST.get(3).getTaskKey(), roundNum, 1, handleId, true);
                break;
        }
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String fromUid = data.getUid();
        String toUid = data.getAid();
        String handleId = data.getHandleId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item) || ObjectUtils.isEmpty(handleId)) {
            return;
        }

        int currentTime = DateHelper.getNowSeconds();
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            // logger.error("not find activity config");
            return;
        }

        int startTime = activityData.getStartTime();
        int endTime = activityData.getEndTime();
        if (currentTime < startTime || currentTime > endTime) {
            return;
        }

        // if (ServerConfig.isProduct() && !whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
        //     // 灰度测试
        //     return;
        // }


        String currentDate = getCurrentDay(ACTIVITY_ID);
        int roundNum = activityData.getRoundNum();
        String jsonData = data.getJsonData();
        handleMomentRankTask(fromUid, toUid, data, handleId, startTime, roundNum, jsonData);
        handleTopicRankTask(fromUid, toUid, data, handleId, roundNum, currentDate);

    }
}
