package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.data.vo.SuperPlayerVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomCommonScrollMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 超级玩家
 */
@Service
public class SuperPlayerService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(SuperPlayerService.class);
    private static final String ACTIVITY_TITLE_EN = "Super Gamer";
    private static final String ACTIVITY_TITLE_AR = "لاعب سوبر";
    private static final String ACTIVITY_DESC = "Super Gamer reward";
    private static final String ACTIVITY_ID = "66977cced72dc2a6db9192d5";
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/op_1717596913_FJRK.png";
    private static final List<Integer> REWARD_LEVEL_LIST = Arrays.asList(100, 2000, 6000, 20000, 40000, 80000, 160000, 360000, 720000, 1000000);
    // private static final List<Integer> REWARD_LEVEL_LIST = Arrays.asList(10, 20, 60, 200, 400, 800, 1600, 3600, 7200, 10000);
    private static final List<String> REWARD_LEVEL_KEY_LIST = Arrays.asList("superPlayerLevel100", "superPlayerLevel2000", "superPlayerLevel6000",
            "superPlayerLevel20000", "superPlayerLevel40000", "superPlayerLevel80000", "superPlayerLevel160000", "superPlayerLevel360000", "superPlayerLevel720000", "superPlayerLevel1000000");
    private static final List<String> DAILY_RANK_KEY_LIST = Arrays.asList("superPlayerDailyTop1", "superPlayerDailyTop2", "superPlayerDailyTop3", "superPlayerDailyTop4-5", "superPlayerDailyTop6-7", "superPlayerDailyTop8-10");
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/super_gamer202407/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/super_gamer202407/?activityId=%s", ACTIVITY_ID);
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private WhiteTestDao whiteTestDao;

    private String getDailyRankKey(String activityId, String queryDate){
        return String.format("dailyRank:%s:%s", activityId, queryDate);
    }

    private String getDailyDate(String activityId){
        return String.format("dailyDate:%s", activityId);
    }

    public SuperPlayerVO superPlayerConfig(String activityId, String uid, String queryDate) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        SuperPlayerVO vo = new SuperPlayerVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        // 设置日榜
        List<OtherRankingListVO> dailyRankingList = new ArrayList<>();
        OtherRankingListVO myDailyRank = new OtherRankingListVO();
        makeOtherRankingData(dailyRankingList, myDailyRank, getDailyRankKey(activityId, queryDate), uid, 10);
        vo.setDailyRankingList(dailyRankingList);
        vo.setMyDailyRank(myDailyRank);

        return vo;
    }

    // 日榜排行榜奖励
    public void distributionDailyRanking(){
        try{
            String currentDate = DateHelper.ARABIAN.getYesterdayStr(new Date());
            String dailyRankKey = getDailyRankKey(ACTIVITY_ID, currentDate);
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(dailyRankKey, 10);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                String rankUid = entry.getKey();
                String resourceKey = String.format("superPlayerDailyTop%s", rank);
                resourceKeyHandlerService.sendResourceData(rankUid, resourceKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distributionDailyRanking error: {}", e.getMessage(), e);
        }
    }


    public void handleMqMsg(CommonMqTopicData data) {
        String fromUid = data.getUid();
        if(!inActivityTime(ACTIVITY_ID)){
            return;
        }

        // if (ServerConfig.isProduct() && !whiteTestDao.isMemberByType(fromUid, WhiteTestDao.WHITE_TYPE_RID)) {
        //     // 灰度测试
        //     return;
        // }

        synchronized (stringPool.intern(fromUid)) {
            int value = data.getValue();
            // 日榜增加数值
            String currentDay = DateHelper.ARABIAN.formatDateInDay();
            // String currentDay = activityCommonRedis.getCommonStrValue(getDailyDate(ACTIVITY_ID));
            String dailyRankKey = getDailyRankKey(ACTIVITY_ID, currentDay);
            int currentNum = activityCommonRedis.getCommonZSetRankingScore(dailyRankKey, fromUid);
            while (value > 0){
                List<Integer> tempLevelNumList = new ArrayList<>(REWARD_LEVEL_LIST);
                int currentLevelIndex = 0;
                if(tempLevelNumList.contains(currentNum)){
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum);
                }else {
                    tempLevelNumList.add(currentNum);
                    tempLevelNumList.sort(Integer::compare);
                    currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
                }

                int upLevelIndex = currentLevelIndex + 1;
                if(upLevelIndex >= REWARD_LEVEL_LIST.size()){
                    activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, fromUid, value);
                    value = 0;
                }else {
                    int upLevelNum = REWARD_LEVEL_LIST.get(upLevelIndex);     // 下一级的数量
                    int needUpNum = upLevelNum - currentNum;                     // 需要升级到下一级的数量
                    if(value >= needUpNum){                                      // 如果【增加的数量】大于等于【需要升级到下一级的数量】则升级, 否则不升级直接增加数量
                        currentNum = currentNum + needUpNum;
                        value  = value - needUpNum;
                        activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, fromUid, needUpNum);
                        String resKey = REWARD_LEVEL_KEY_LIST.get(upLevelIndex);
                        resourceKeyHandlerService.sendResourceData(fromUid, resKey, ACTIVITY_TITLE_EN, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                    }else {
                        activityCommonRedis.incrCommonZSetRankingScore(dailyRankKey, fromUid, value);
                        value = 0;
                    }
                }
            }
        }
    }
}
