package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.MomentConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.data.dto.QuizActivityDTO;
import com.quhong.data.vo.MBTIQuizQuestionVO;
import com.quhong.data.vo.MBTIVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.QuestionDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.QuestionData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
public class MBTIService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(MBTIService.class);
    private static final String ACTIVITY_TITLE_EN = "The world's most famous personality";
    private static final String ACTIVITY_TITLE_AR = "أشهر اختبار شخصية في العالم";
    public static final String MOMENT_MBTI_ORIGIN = "mbtiPicture";
    public static String ACTIVITY_ID = "68664dbb38cbb65e074edb9f";//
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/mbti_quiz/?activityId=%s&shareId=63", ACTIVITY_ID);
    private static final String ACTIVITY_ICON = "https://cdn3.qmovies.tv/youstar/op_1751279038_pyq.jpg";
    private static int TOPIC_RID = 16132;
    /**
     * 答题选项常量
     */
    private static final String OPTION_A = "A";
    private static final String OPTION_B = "B";

    private static final String EVENT_TITLE = "MBTI-reward";

    /**
     * 性格对应颜色
     */
    public static final Map<String, Integer> LABEL_COLOR_MAP = new HashMap<String, Integer>() {
        {
            put("ENFP", 1); // 绿人
            put("ENFJ", 1);
            put("INFP", 1);
            put("INFJ", 1);

            put("INTP", 2); // 紫人
            put("INTJ", 2);
            put("ENTP", 2);
            put("ENTJ", 2);

            put("ESTJ", 3); // 蓝人
            put("ISTJ", 3);
            put("ESFJ", 3);
            put("ISFJ", 3);

            put("ISFP", 4); // 黄人
            put("ESFP", 4);
            put("ISTP", 4);
            put("ESTP", 4);
        }
    };


    /**
     * 性格对应奖励
     */
    public static final Map<String, String> MBTI_RESOURCE_KEY_MAP = new HashMap<String, String>() {
        {
            put("ENFP", "mbtiENFP"); // 绿人
            put("ENFJ", "mbtiENFJ");
            put("INFP", "mbtiINFP");
            put("INFJ", "mbtiINFJ");

            put("INTP", "mbtiINTP"); // 紫人
            put("INTJ", "mbtiINTJ");
            put("ENTP", "mbtiENTP");
            put("ENTJ", "mbtiENTJ");

            put("ESTJ", "mbtiESTJ"); // 蓝人
            put("ISTJ", "mbtiISTJ");
            put("ESFJ", "mbtiESFJ");
            put("ISFJ", "mbtiISFJ");

            put("ISFP", "mbtiISFP"); // 黄人
            put("ESFP", "mbtiESFP");
            put("ISTP", "mbtiISTP");
            put("ESTP", "mbtiESTP");
        }
    };

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static String GID = "123"; // 题库id

    /**
     * MBTI计算结果内部类
     */
    private static class MBTICalculationResult {
        private String mbtiType;
        private List<Integer> mbtiRotateList; // 依次为E,S,T,J在当前组中的占比

        public MBTICalculationResult(String mbtiType, List<Integer> mbtiRotateList) {
            this.mbtiType = mbtiType;
            this.mbtiRotateList = mbtiRotateList;
        }

        public String getMbtiType() {
            return mbtiType;
        }

        public List<Integer> getMbtiRotateList() {
            return mbtiRotateList;
        }
    }

    /**
     * MBTI测试结果数据类（用于Redis存储）
     */
    private static class MBTIResultData {
        private String mbtiType;
        private Integer colorType;
        private List<Integer> mbtiRotateList;
        private int isShareMoment;

        public MBTIResultData() {
        }

        public MBTIResultData(String mbtiType, Integer colorType, List<Integer> mbtiRotateList, int isShareMoment) {
            this.mbtiType = mbtiType;
            this.colorType = colorType;
            this.mbtiRotateList = mbtiRotateList;
            this.isShareMoment = isShareMoment;
        }

        public String getMbtiType() {
            return mbtiType;
        }

        public void setMbtiType(String mbtiType) {
            this.mbtiType = mbtiType;
        }

        public Integer getColorType() {
            return colorType;
        }

        public void setColorType(Integer colorType) {
            this.colorType = colorType;
        }

        public List<Integer> getMbtiRotateList() {
            return mbtiRotateList;
        }

        public void setMbtiRotateList(List<Integer> mbtiRotateList) {
            this.mbtiRotateList = mbtiRotateList;
        }

        public int getIsShareMoment() {
            return isShareMoment;
        }

        public void setIsShareMoment(int isShareMoment) {
            this.isShareMoment = isShareMoment;
        }
    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private QuestionDao questionDao;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private ActivityCommonRedis activityCommonRedis;


    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            GID = "0122";
            TOPIC_RID = 10132;
            ACTIVITY_ID = "685cc0c898e174029ac33ed0";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/mbti_quiz/?activityId=%s", ACTIVITY_ID);
        }
    }

    private String getLocalPublishUserKey(String uid) {
        return "lock:mbti:publish:uid:" + uid;
    }


    public void pictureMBTIPush(String activityId, String uid, PainterPictureDTO dto) {
        checkActivityTime(activityId);
        String picture = dto.getPicture();
//        int slang = dto.getSlang();
        if (StringUtils.isEmpty(picture)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        String nowDay = getDayByBase(ACTIVITY_ID, uid);
        int startTime = DateHelper.ARABIAN.stringDateToStampSecond(nowDay);
        int endTime = (int) (startTime + TimeUnit.DAYS.toSeconds(1));
        MomentActivityData momentActivityData = momentActivityDao.findMomentOneByTime(uid, MOMENT_MBTI_ORIGIN, startTime, endTime);
        if (momentActivityData != null) {
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }
        synchronized (stringPool.intern(getLocalPublishUserKey(uid))) {
            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText(ACTIVITY_TITLE_AR);
            publishMomentDTO.setShow(MomentConstant.MOMENT_PUBLIC);
            publishMomentDTO.setActiveId(ACTIVITY_ID);
            publishMomentDTO.setLocation(MOMENT_MBTI_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("2000");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));

            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setType(6);
            quote.setIcon(ACTIVITY_ICON);
            quote.setContent(ACTIVITY_TITLE_AR);
            quote.setAction(ACTIVITY_URL);

            publishMomentDTO.setQuote(quote);

            publishMomentDTO.setTopicRid(TOPIC_RID);

            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if (result.getCode() == 20) {
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            String mid = result.getData();
            logger.info("mbti publish moment success. uid={} mid={}", uid, mid);
        }
        // 从Redis获取用户的MBTI测试结果
        MBTIResultData resultData = getMBTIResultFromRedis(activityId, uid);
        if (resultData != null && resultData.getIsShareMoment() == 0) {
            String mbtiType = resultData.getMbtiType();
            String resKey = MBTI_RESOURCE_KEY_MAP.get(mbtiType);
            if (!StringUtils.isEmpty(resKey)) {
                resourceKeyHandlerService.sendResourceData(uid, resKey,
                        EVENT_TITLE, EVENT_TITLE, EVENT_TITLE, ACTIVITY_URL, "");
                resultData.setIsShareMoment(1);
                saveMBTIResultToRedis(activityId, uid, resultData);
            }
        }
    }

    /**
     * 获取题目列表
     */
    public MBTIQuizQuestionVO getMBTIQuestionList(String activityId, String uid) {
        MBTIQuizQuestionVO vo = new MBTIQuizQuestionVO();
        List<MBTIQuizQuestionVO.QuestionVO> list = new ArrayList<>();
        int page = 1;
        List<QuestionData> questionList = questionDao.selectListPage(GID, page, 30);
        if (!CollectionUtils.isEmpty(questionList)) {
            for (QuestionData item : questionList) {
                MBTIQuizQuestionVO.QuestionVO questionVO = new MBTIQuizQuestionVO.QuestionVO();
                questionVO.setId(item.getId());
                questionVO.setPictureUrl(item.getPictureUrl());
                questionVO.setContent(item.getContent());
                Map<String, String> optionContentMap;
                String optionContent = item.getOptionContent();
                if (!StringUtils.isEmpty(optionContent)) {
                    optionContentMap = JSON.parseObject(optionContent, new TypeReference<HashMap<String, String>>() {
                    });
                } else {
                    optionContentMap = new HashMap<>(4);
                }
                questionVO.setOptionContent(optionContentMap);
                questionVO.setCorrectOption(item.getCorrectOption());
                list.add(questionVO);
            }
        }
        vo.setList(list);

        // 从Redis获取用户的MBTI测试结果
        MBTIResultData resultData = getMBTIResultFromRedis(activityId, uid);
        if (resultData != null) {
            vo.setColorType(resultData.getColorType());
            vo.setMbtiType(resultData.getMbtiType());
            vo.setMbtiRotateList(resultData.getMbtiRotateList());
            vo.setIsShareMoment(resultData.getIsShareMoment());
        } else {
            // 如果没有测试结果，设置默认值
            vo.setColorType(-1);
            vo.setMbtiType(null);
            vo.setMbtiRotateList(null);
            vo.setIsShareMoment(0);
        }

        return vo;
    }

    /**
     * 提交答题回答
     */
    public MBTIVO submitAnswer(String activityId, String uid, QuizActivityDTO dto) {
        OtherRankingActivityData activityData = otherActivityService.checkActivityTime(activityId);
        Map<Integer, String> quizAnswer = dto.getQuizAnswer();
        if (CollectionUtils.isEmpty(quizAnswer) || quizAnswer.size() != 28) {
            logger.error("submit answer param error. quizAnswer={}", quizAnswer);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        // 计算MBTI性格类型和各维度占比
        MBTICalculationResult result = calculateMBTITypeWithPercentages(quizAnswer);
        String mbtiType = result.getMbtiType();
        Integer colorType = LABEL_COLOR_MAP.get(mbtiType);
        List<Integer> mbtiRotateList = result.getMbtiRotateList();

        logger.info("用户 {} MBTI测试结果: mbtiType={}, colorType={}, mbtiRotateList={}",
                uid, mbtiType, colorType, mbtiRotateList);

        // 将结果保存到Redis
        saveMBTIResultToRedis(activityId, uid, mbtiType, colorType, mbtiRotateList, 0);

        MBTIVO vo = new MBTIVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        vo.setMbtiType(mbtiType);
        vo.setColorType(colorType);
        vo.setMbtiRotateList(mbtiRotateList);

        return vo;
    }


    /**
     * 根据答题结果计算MBTI性格类型和各维度占比
     *
     * @param quizAnswer 答题结果，key为题号，value为选项(A或B)
     * @return MBTI计算结果，包含类型和占比列表
     */
    private MBTICalculationResult calculateMBTITypeWithPercentages(Map<Integer, String> quizAnswer) {
        // 统计各维度的得分
        int eCount = 0, iCount = 0; // 外向/内向
        int sCount = 0, nCount = 0; // 感觉/直觉
        int tCount = 0, fCount = 0; // 思考/情感
        int jCount = 0, pCount = 0; // 判断/知觉

        // 获取所有题号并按升序排列
        List<Integer> sortedKeys = new ArrayList<>(quizAnswer.keySet());
        Collections.sort(sortedKeys);

        if (sortedKeys.size() != 28) {
            logger.error("题目数量不正确，期望28题，实际{}题", sortedKeys.size());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        // 按升序处理题目
        for (int i = 0; i < sortedKeys.size(); i++) {
            Integer questionKey = sortedKeys.get(i);
            String answer = quizAnswer.get(questionKey);

            // 第1-7题：选A为E，选B为I
            if (i < 7) {
                if (OPTION_A.equals(answer)) {
                    eCount++;
                } else if (OPTION_B.equals(answer)) {
                    iCount++;
                }
            }
            // 第8-14题：选A为S，选B为N
            else if (i < 14) {
                if (OPTION_A.equals(answer)) {
                    sCount++;
                } else if (OPTION_B.equals(answer)) {
                    nCount++;
                }
            }
            // 第15-21题：选A为T，选B为F
            else if (i < 21) {
                if (OPTION_A.equals(answer)) {
                    tCount++;
                } else if (OPTION_B.equals(answer)) {
                    fCount++;
                }
            }
            // 第22-28题：选A为J，选B为P
            else {
                if (OPTION_A.equals(answer)) {
                    jCount++;
                } else if (OPTION_B.equals(answer)) {
                    pCount++;
                }
            }
        }

        // 计算各维度的占比（E, S, T, J在各自组中的占比）
        int ePercentage = calculatePercentage(eCount, eCount + iCount);
        int sPercentage = calculatePercentage(sCount, sCount + nCount);
        int tPercentage = calculatePercentage(tCount, tCount + fCount);
        int jPercentage = calculatePercentage(jCount, jCount + pCount);

        // 创建占比列表：依次为E,S,T,J在当前组中的占比
        List<Integer> mbtiRotateList = Arrays.asList(ePercentage, sPercentage, tPercentage, jPercentage);

        // 判定各维度的最终类型
        char dimension1 = determineDimension(eCount, iCount, 'E', 'I');
        char dimension2 = determineDimension(sCount, nCount, 'S', 'N');
        char dimension3 = determineDimension(tCount, fCount, 'T', 'F');
        char dimension4 = determineDimension(jCount, pCount, 'J', 'P');

        String mbtiType = "" + dimension1 + dimension2 + dimension3 + dimension4;

        logger.info("MBTI计算结果: E={}, I={}, S={}, N={}, T={}, F={}, J={}, P={}, 最终类型={}, 占比列表={}",
                eCount, iCount, sCount, nCount, tCount, fCount, jCount, pCount, mbtiType, mbtiRotateList);

        return new MBTICalculationResult(mbtiType, mbtiRotateList);
    }

    /**
     * 计算百分比
     *
     * @param count 选择该选项的数量
     * @param total 总数量
     * @return 百分比（0-100）
     */
    private int calculatePercentage(int count, int total) {
        if (total == 0) {
            return 0;
        }
        return Math.round((float) count * 100 / total);
    }

    /**
     * 判定单个维度的性格类型
     *
     * @param count1 第一个选项的计数
     * @param count2 第二个选项的计数
     * @param type1  第一个性格类型
     * @param type2  第二个性格类型
     * @return 最终确定的性格类型
     */
    private char determineDimension(int count1, int count2, char type1, char type2) {
        if (count1 > count2) {
            return type1;
        } else if (count2 > count1) {
            return type2;
        } else {
            // 占比相同时随机选择
            Random random = new Random();
            return random.nextBoolean() ? type1 : type2;
        }
    }

    /**
     * 将MBTI测试结果保存到Redis
     */
    private void saveMBTIResultToRedis(String activityId, String uid, MBTIResultData resultData) {
        try {
            String jsonData = JSON.toJSONString(resultData);
            activityCommonRedis.setCommonHashData(getMBTIHashKey(activityId), uid, jsonData);
            logger.info("保存MBTI结果到Redis成功: activityId={}, uid={}, resultData={}", activityId, uid, jsonData);
        } catch (Exception e) {
            logger.error("保存MBTI结果到Redis失败: activityId={}, uid={}, resultData={}", activityId, uid, JSON.toJSONString(resultData), e);
        }
    }

    /**
     * 将MBTI测试结果保存到Redis
     */
    private void saveMBTIResultToRedis(String activityId, String uid, String mbtiType, Integer colorType, List<Integer> mbtiRotateList, int isShareMoment) {
        try {
            MBTIResultData resultData = new MBTIResultData(mbtiType, colorType, mbtiRotateList, isShareMoment);
            String jsonData = JSON.toJSONString(resultData);
            activityCommonRedis.setCommonHashData(getMBTIHashKey(activityId), uid, jsonData);
            logger.info("保存MBTI结果到Redis成功: activityId={}, uid={}, mbtiType={}", activityId, uid, mbtiType);
        } catch (Exception e) {
            logger.error("保存MBTI结果到Redis失败: activityId={}, uid={}, mbtiType={}", activityId, uid, mbtiType, e);
        }
    }

    /**
     * 从Redis获取MBTI测试结果
     */
    private MBTIResultData getMBTIResultFromRedis(String activityId, String uid) {
        try {
            String jsonData = activityCommonRedis.getCommonHashStrValue(getMBTIHashKey(activityId), uid);
            if (StringUtils.hasLength(jsonData)) {
                return JSON.parseObject(jsonData, MBTIResultData.class);
            }
        } catch (Exception e) {
            logger.error("从Redis获取MBTI结果失败: activityId={}, uid={}", activityId, uid, e);
        }
        return null;
    }

    /**
     * 获取MBTI结果的Redis Hash Key
     */
    private String getMBTIHashKey(String activityId) {
        return "mbti_result_" + activityId;
    }

}
