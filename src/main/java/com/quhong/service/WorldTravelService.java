package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.msg.obj.LuckyGiftRewardObject;
import com.quhong.msg.room.RoomLuckGiftRewardMsg;
import com.quhong.mysql.dao.WorldTravelDao;
import com.quhong.mysql.dao.WorldTravelRecordDao;
import com.quhong.mysql.data.WorldTravelData;
import com.quhong.mysql.data.WorldTravelRecordData;
import com.quhong.redis.WorldTravelRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class WorldTravelService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(WorldTravelService.class);
    private static final String RATE1 = "rate1";
    private static final String RATE2 = "rate2";
    private static final int LIMIT_INIT_POOL = 30;
    private static final int ZERO_INIT_POOL = 0;
    private static final String ACTIVITY_NAME = "WorldTravel";
    private static final String MOMENT_ORIGIN = "worldTravel";
    private static final String AWARD_STATUS = "awardStatus_%s";
    private static final String FULL_AWARD_STATUS = "awardFullStatus";

    private static final String CLICK_STATUS = "clickStatus_%s";
    private static final String SHARE_DESC_EN = "I have landmark %s , click to join the activity with me, travelling around the world.";
    private static final String SHARE_DESC_AR = "لدي نقطة النزول %s ، انقر للانضمام إلى النشاط معي ، والسفر حول العالم.";
    private static final String SHARE_CONTENT_EN = "Shared an interesting activity “The wonders of the world” to you";
    private static final String SHARE_CONTENT_AR = "تم شارك النشاط رائع لك ،و هو “عجائب الدنيا”";
    private static final String SHARE_ICON_EN = "https://cloudcdn.qmovies.tv/activity/op_1688179108_room_en.png";
    private static final String SHARE_ICON_AR = "https://cloudcdn.qmovies.tv/activity/op_1688179113_room_ar.png";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? "https://static.youstar.live/punch_plan/?activityId=649f87edc228a354834df444&shareId=7" : "https://test2.qmovies.tv/punch_plan/?activityId=64913c838d9a849497415757&shareId=5";
    private static final Integer RATE2_TIME =  ServerConfig.isProduct() ? 1688590800 : 1688029200;
    private static final Integer GIFT_ID =  ServerConfig.isProduct() ? 601 : 742;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<Integer> BADGE_AWARD_LIST = ServerConfig.isProduct() ? Arrays.asList(2140, 2141, 2142) : Arrays.asList(1702, 1703, 1704);
    private static final List<Integer> AWARD_DAY = Arrays.asList(15, 7, 3);
    private static final Integer MIC_AWARD_ID = 469;
    private static final Integer BUBBLE_AWARD_ID = 135;
    private static final Integer FLOAT_AWARD_ID = 30;
    private static final Integer RIDE_AWARD_ID = 145;
    private static final Integer FULL_AWARD_RIDE = 144;

    @Resource
    private WorldTravelRedis worldTravelRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private WorldTravelDao worldTravelDao;
    @Resource
    private WorldTravelService worldTravelService;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;
    @Resource
    private WorldTravelRecordDao worldTravelRecordDao;
    @Resource
    private IMomentService iMomentService;


    @Cacheable(value = "getWorldTravelData", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<WorldTravelData> getWorldTravelDataFromCache(){
        return worldTravelDao.selectList();

    }

    public WorldTravelActivityVO worldTravelConfig(String uid, String activityId) {

        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivity(activityId);
        // 设置时间
        WorldTravelActivityVO vo = new WorldTravelActivityVO();
        vo.setStartTime(otherRankingActivityData.getStartTime());
        vo.setEndTime(otherRankingActivityData.getEndTime());

        // 设置坐标值
        Map<String, Integer> taskNumMap =  worldTravelRedis.getAllHashValue(uid);
        List<WorldTravelData> worldTravelDataList = worldTravelService.getWorldTravelDataFromCache();
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        List<WorldTravelActivityVO.WorldConfig> worldConfigList = new ArrayList<>();
        for (WorldTravelData data: worldTravelDataList) {
            WorldTravelActivityVO.WorldConfig worldConfig = new WorldTravelActivityVO.WorldConfig();
            worldConfig.setWorldKey(data.getWorldKey());
            worldConfig.setIcon(data.getIcon());
            worldConfig.setNameEn(data.getNameEn());
            worldConfig.setNameAr(data.getNameAr());
            worldConfig.setStatus(taskNumMap.getOrDefault(data.getWorldKey(), 0));
            worldConfig.setAward(taskNumMap.getOrDefault(String.format(AWARD_STATUS, data.getWorldKey()), 0));
            worldConfig.setClick(taskNumMap.getOrDefault(String.format(CLICK_STATUS, data.getWorldKey()), 0));
            worldConfig.setLikes(worldTravelRedis.getLikeSize(data.getWorldKey()));
            worldConfig.setLikeStatus(worldTravelRedis.isLike(uid, data.getWorldKey()));
            worldConfigList.add(worldConfig);
        }
        vo.setWorldList(worldConfigList);

        // 发送礼物排行榜
        OtherRankingVO otherRankingVO = otherRanking(uid, activityId, ActivityConstant.SEND_RANK, 10, 0);
        vo.setCollectRankingList(otherRankingVO.getRankingList());
        vo.setMyCollectRank(otherRankingVO.getMyRank());

        // 打卡排行榜
        OtherMyRankVO myTravelRank = new OtherMyRankVO();
        List<OtherRankingListVO> rankingVOList = new ArrayList<>();
        Map<String, Integer> rankingMap = worldTravelRedis.getRankingMap(10);
        ActorData rankActor = null;
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO otherRankingListVO = new OtherRankingListVO();
            String aid = entry.getKey();
            rankActor = actorDao.getActorDataFromCache(aid);

            otherRankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            otherRankingListVO.setName(rankActor.getName());
            otherRankingListVO.setScore(entry.getValue());
            if(uid.equals(entry.getKey())){
                myTravelRank.setRank(rank);
                myTravelRank.setSendingScore(entry.getValue());
            }
            rankingVOList.add(otherRankingListVO);
            rank += 1;
        }

        vo.setTravelRankingList(rankingVOList);

        if(myTravelRank.getRank() == 0){
            myTravelRank.setSendingScore(worldTravelRedis.getRankingScore(uid));
            myTravelRank.setRank(-1);
        }

        myTravelRank.setName(actorData.getName());
        myTravelRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setMyTravelRank(myTravelRank);

        return vo;
    }

    private void onActivityTime(String activityId){
        OtherRankingActivityData otherRankingActivityData = otherActivityService.getOtherRankingActivity(activityId);

        int curTime = DateHelper.getNowSeconds();
        if(curTime < otherRankingActivityData.getStartTime() || curTime > otherRankingActivityData.getEndTime()){
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }

    // 点赞
    public LikeVO likeWorld(String uid, String activityId, String worldKey, Integer slang) {
        LikeVO likeVO = new LikeVO();
        onActivityTime(activityId);
        Map<String, WorldTravelData> rewardConfigMap = worldTravelService.getWorldTravelDataFromCache().stream().collect(Collectors.toMap(WorldTravelData::getWorldKey, Function.identity()));
        WorldTravelData worldTravelData = rewardConfigMap.get(worldKey);
        if(worldTravelData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(uid)) {
            int isLike = worldTravelRedis.isLike(uid, worldKey);
            if(isLike == 1){
                throw new CommonH5Exception(ActivityHttpCode.QUEEN_ALREADY_LIKE);
            }
            likeVO.setLikeStatus(worldTravelRedis.addLike(uid, worldKey));
            likeVO.setLikes(worldTravelRedis.getLikeSize(worldKey));

            // 发布朋友圈
            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            String momentText = slang == SLangType.ARABIC ? SHARE_DESC_AR : SHARE_DESC_EN;
            String addressName = slang == SLangType.ARABIC ? worldTravelData.getNameAr() : worldTravelData.getNameEn();
            momentText = String.format(momentText, addressName);
            publishMomentDTO.setText(momentText);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(MOMENT_ORIGIN);
            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setAction(ACTIVITY_URL);
            quote.setIcon(slang == SLangType.ARABIC ? SHARE_ICON_AR : SHARE_ICON_EN);
            quote.setContent(slang == SLangType.ARABIC ? SHARE_CONTENT_AR : SHARE_CONTENT_EN);
            quote.setType(6);
            publishMomentDTO.setQuote(quote);

            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if(result.getCode() == 20){
                logger.info("newYearExpect level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("newYearExpect error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            return likeVO;
        }
    }

    // 点击世界
    public void clickWorldKey(String uid, String activityId, String worldKey) {
        worldTravelRedis.setHashNum(uid, String.format(CLICK_STATUS, worldKey), 1);
    }

    public void getWorldAward(String uid, String activityId, String worldKey) {
        // onActivityTime(activityId);
        Map<String, WorldTravelData> rewardConfigMap = worldTravelService.getWorldTravelDataFromCache().stream().collect(Collectors.toMap(WorldTravelData::getWorldKey, Function.identity()));
        WorldTravelData worldTravelData = rewardConfigMap.get(worldKey);
        if(worldTravelData == null){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        Map<String, Integer> taskNumMap =  worldTravelRedis.getAllHashValue(uid);
        int worldKeyAwardStatus = taskNumMap.getOrDefault(String.format(AWARD_STATUS, worldKey), 0);
        if(worldKeyAwardStatus >= 1){
            throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
        }

        int worldKeyStatus = taskNumMap.getOrDefault(worldKey, 0);
        if(worldKeyStatus >= 1){
            distributionService.sendRewardResource(uid, GIFT_ID, ActivityRewardTypeEnum.getEnumByName("gift"), 0, 1, ACTIVITY_NAME, ACTIVITY_NAME, 0);
            worldTravelRedis.setHashNum(uid, String.format(AWARD_STATUS, worldKey), 1);
        }
    }


    public NewYearHallVO myTravelRecord(int page, String uid) {
        int size = 20;
        int start = (page - 1) * size;

        List<MomentActivityData> momentList = momentActivityDao.findUserMsgPageList(uid, MOMENT_ORIGIN, start, size);
        NewYearHallVO vo = new NewYearHallVO();
        List<NewYearHallVO.Message> momentIds = new ArrayList<>();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        for (MomentActivityData data : momentList) {
            NewYearHallVO.Message otherMessage = new NewYearHallVO.Message();
            otherMessage.setMessage(data.getText());
            otherMessage.setName(actorData.getName());
            otherMessage.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            otherMessage.setRepost(data.getRepost());
            otherMessage.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
            otherMessage.setComments(data.getComments());
            momentIds.add(otherMessage);
        }

        vo.setMessageList(momentIds);
        vo.setNext(momentIds.size() < size ? -1 : page + 1);
        return vo;

    }

    public NewYearHallVO worldTravelRanking(int page, String uid) {
        int size = 20;
        int start = (page - 1) * size;
        List<MomentActivityData> momentList = momentActivityDao.momentRanking(MOMENT_ORIGIN, 0,0, start, size);
        NewYearHallVO vo = new NewYearHallVO();
        List<NewYearHallVO.Message> momentIds = new ArrayList<>();

        for (MomentActivityData data : momentList) {
            NewYearHallVO.Message otherMessage = new NewYearHallVO.Message();
            ActorData actorData = actorDao.getActorDataFromCache(data.getUid());
            otherMessage.setMessage(data.getText());
            otherMessage.setName(actorData.getName());
            otherMessage.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            otherMessage.setRepost(data.getRepost());
            otherMessage.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
            otherMessage.setComments(data.getComments());
            momentIds.add(otherMessage);
        }

        vo.setMessageList(momentIds);
        vo.setNext(momentIds.size() < size ? -1 : page + 1);
        return vo;
    }

    public WorldTravelRecordVO worldTravelRecord(String uid, int page){
        WorldTravelRecordVO vo = new WorldTravelRecordVO();
        int pageSize = 20;
        List<WorldTravelRecordData> dataList = worldTravelRecordDao.selectPageList(uid, page, pageSize);
        Map<String, WorldTravelData> rewardConfigMap = worldTravelService.getWorldTravelDataFromCache().stream().collect(Collectors.toMap(WorldTravelData::getWorldKey, Function.identity()));
        List<WorldTravelRecordVO.DrawRecord> recordList = new ArrayList<>();
        for (WorldTravelRecordData data : dataList) {
            WorldTravelData worldTravelData = rewardConfigMap.get(data.getWorldKey());
            if(worldTravelData != null){
                WorldTravelRecordVO.DrawRecord record = new WorldTravelRecordVO.DrawRecord();
                record.setNameEn(worldTravelData.getNameEn());
                record.setNameAr(worldTravelData.getNameAr());
                record.setAmount(data.getAmount());
                record.setIcon(worldTravelData.getIcon());
                record.setMtime(data.getMtime());
                recordList.add(record);
            }
        }

        vo.setDrawRecordList(recordList);
        if (dataList.size() < pageSize) {
            vo.setNextUrl(-1);
        } else {
            vo.setNextUrl(page + 1);
        }
        return vo;
    }


    // 旅行打卡排行榜奖励
    public void distributionSpecialAward(){
        try{
            Map<String, Integer> rankingMap = worldTravelRedis.getRankingMap(3);
            int rank = 0;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String rankUid = entry.getKey();

                Integer badgeId = BADGE_AWARD_LIST.get(rank);
                Integer day = AWARD_DAY.get(rank);
                if(rank == 0){
                    distributionService.sendRewardResource(rankUid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, MIC_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("mic"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, BUBBLE_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, FLOAT_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("float_screen"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, RIDE_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("ride"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                } else if (rank == 1) {
                    distributionService.sendRewardResource(rankUid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, MIC_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("mic"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, BUBBLE_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, FLOAT_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("float_screen"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }else {
                    distributionService.sendRewardResource(rankUid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, MIC_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("mic"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, BUBBLE_AWARD_ID, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distributionLionRankingAward error: {}", e.getMessage(), e);
        }
    }

    public void distributeRidePack(String uid) {

        try {
            Map<String, Integer> taskNumMap =  worldTravelRedis.getAllHashValue(uid);
            Integer fullAwardStatus = taskNumMap.get(FULL_AWARD_STATUS);
            if(fullAwardStatus == null){
                int totalNum = 0;
                List<WorldTravelData> worldTravelDataList = worldTravelService.getWorldTravelDataFromCache();
                for (WorldTravelData data : worldTravelDataList) {
                    totalNum += taskNumMap.getOrDefault(data.getWorldKey(), 0);
                }
                if(totalNum >= 10){
                    distributionService.sendRewardResource(uid, FULL_AWARD_RIDE, ActivityRewardTypeEnum.getEnumByName("ride"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    worldTravelRedis.setHashNum(uid, FULL_AWARD_STATUS, 1);
                }
            }

        }catch (Exception e) {
            logger.error("distributeRidePack error uid={} e={}", uid, e);
        }
    }



    // 初始化奖池
    public void commonInitPoolSize(String rateKey){
        List<String> poolList = new ArrayList<>();
        List<WorldTravelData> rewardConfigList = worldTravelService.getWorldTravelDataFromCache();
        for (WorldTravelData worldTravelData : rewardConfigList) {
            int awardSize = RATE1.equals(rateKey)? worldTravelData.getRate1() : worldTravelData.getRate2();
            String poolKey = worldTravelData.getWorldKey();
            for (int i = 0; i < awardSize; i++) {
                poolList.add(poolKey);
            }
        }

        Collections.shuffle(poolList);
        worldTravelRedis.initPoolSize(poolList, rateKey);
    }


    private void initPoolSize(String rateKey){
        int poolSize = worldTravelRedis.getPoolSize(rateKey);
        if (poolSize > ZERO_INIT_POOL && poolSize <= LIMIT_INIT_POOL){
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    commonInitPoolSize(rateKey);
                }
            });
        }else if(poolSize <= ZERO_INIT_POOL){
            commonInitPoolSize(rateKey);
        }
    }


    public void handleSendGift(SendGiftData giftData) {
        int totalNum = giftData.getNumber() * giftData.getAid_list().size();
        Map<String, WorldTravelData> rewardConfigMap = worldTravelService.getWorldTravelDataFromCache().stream().collect(Collectors.toMap(WorldTravelData::getWorldKey, Function.identity()));

        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();

        Map<String, Integer> rewardMap = new HashMap<>();
        ActorData actorData = actorDao.getActorDataFromCache(fromUid);
        int currentTime = DateHelper.getNowSeconds();
        String rateKey = currentTime < RATE2_TIME ? RATE1 : RATE2;

        // 抽奖及合并奖励
        for (int i=0; i < totalNum; i++){
            initPoolSize(rateKey);
            String cardKey = worldTravelRedis.drawPoolSizeKey(rateKey);
            logger.info("worldTravel fromUid:{} drawPoolSizeKey: {}", fromUid, cardKey);
            rewardMap.compute(cardKey, (k, v) -> {
                if (null == v) {
                    v = 1;
                }else {
                    v += 1;
                }
                return v;
            });
        }

        if (!rewardMap.isEmpty()){
            RoomLuckGiftRewardMsg rewardMsg = new RoomLuckGiftRewardMsg();
            List<LuckyGiftRewardObject> luckyGiftRewardList = new ArrayList<>();

            for (String key : rewardMap.keySet()) {
                WorldTravelData cardConfig = rewardConfigMap.get(key);
                LuckyGiftRewardObject rewardObject = new LuckyGiftRewardObject();
                rewardObject.setIcon(cardConfig.getIcon());
                rewardObject.setName(actorData.getSlang() == SLangType.ENGLISH? cardConfig.getNameEn() : cardConfig.getNameAr());
                rewardObject.setType(2);
                rewardObject.setValue(rewardMap.get(key));
                luckyGiftRewardList.add(rewardObject);


                int value = worldTravelRedis.getHashValue(fromUid, key);
                if(value == 0){
                    worldTravelRedis.setHashNum(fromUid, key, 1);
                    worldTravelRedis.incrRankingScore(fromUid, 1);
                }

                WorldTravelRecordData worldTravelRecordData = worldTravelRecordDao.selectOneByKey(fromUid, key);
                if(worldTravelRecordData == null){
                    worldTravelRecordData = new WorldTravelRecordData();
                    worldTravelRecordData.setUid(fromUid);
                    worldTravelRecordData.setWorldKey(key);
                    worldTravelRecordData.setAmount(rewardMap.get(key));
                    worldTravelRecordData.setMtime(currentTime);
                    worldTravelRecordData.setCtime(currentTime);
                    worldTravelRecordDao.insertData(worldTravelRecordData);
                }else {
                    worldTravelRecordData.setAmount(worldTravelRecordData.getAmount() + rewardMap.get(key));
                    worldTravelRecordData.setMtime(currentTime);
                    worldTravelRecordDao.updateData(worldTravelRecordData);
                }
            }

            rewardMsg.setAid(fromUid);
            rewardMsg.setA_type(0);
            rewardMsg.setType(2);
            rewardMsg.setLucky_gift_reward(luckyGiftRewardList);
            marsMsgActivityService.asyncSendPlayerMsg(roomId, fromUid, fromUid, rewardMsg, false);
            distributeRidePack(fromUid); // 坐骑奖励
        }

    }

}
