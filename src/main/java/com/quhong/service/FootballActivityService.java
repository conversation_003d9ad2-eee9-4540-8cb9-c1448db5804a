package com.quhong.service;


import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.FBCompetitionVO;
import com.quhong.data.vo.FightConfigVO;
import com.quhong.data.vo.FootballConfigVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.dao.FootballDao;
import com.quhong.mongo.data.BadgeData;
import com.quhong.mongo.data.FootballData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.FootballRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FootballActivityService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(FootballActivityService.class);
    private static final String ACTIVITY_NAME = "football";
    private static final List<Integer> BADGE_LEVEL_LIST = Arrays.asList(5000, 15000);
    private static final Map<Integer, List<Integer>> GIFT_BADGE_ID_LIST = new HashMap<>();
    public static final String ACTIVITY_ID = "678a1a7d7cf72d2e589da8a8";

    static {
        GIFT_BADGE_ID_LIST.put(147, Arrays.asList(2975, 2976));
        GIFT_BADGE_ID_LIST.put(149, Arrays.asList(2977, 2978));
        GIFT_BADGE_ID_LIST.put(150, Arrays.asList(2979, 2980));
        GIFT_BADGE_ID_LIST.put(151, Arrays.asList(2981, 2982));
        GIFT_BADGE_ID_LIST.put(152, Arrays.asList(2983, 2984));
        GIFT_BADGE_ID_LIST.put(229, Arrays.asList(2985, 2986));
        GIFT_BADGE_ID_LIST.put(153, Arrays.asList(2987, 2988));
        GIFT_BADGE_ID_LIST.put(292, Arrays.asList(2989, 2990));
        GIFT_BADGE_ID_LIST.put(154, Arrays.asList(2991, 2992));
        GIFT_BADGE_ID_LIST.put(155, Arrays.asList(2993, 2994));
        GIFT_BADGE_ID_LIST.put(221, Arrays.asList(2995, 2996));
        GIFT_BADGE_ID_LIST.put(225, Arrays.asList(2997, 2998));
        GIFT_BADGE_ID_LIST.put(156, Arrays.asList(2999, 3000));
        GIFT_BADGE_ID_LIST.put(186, Arrays.asList(3001, 3002));
        GIFT_BADGE_ID_LIST.put(224, Arrays.asList(3003, 3004));
        GIFT_BADGE_ID_LIST.put(157, Arrays.asList(3005, 3006));
        GIFT_BADGE_ID_LIST.put(223, Arrays.asList(3007, 3008));
        GIFT_BADGE_ID_LIST.put(184, Arrays.asList(3009, 3010));
        GIFT_BADGE_ID_LIST.put(158, Arrays.asList(3011, 3012));
        GIFT_BADGE_ID_LIST.put(293, Arrays.asList(3013, 3014));
        GIFT_BADGE_ID_LIST.put(159, Arrays.asList(3015, 3016));
        GIFT_BADGE_ID_LIST.put(160, Arrays.asList(3017, 3018));
        GIFT_BADGE_ID_LIST.put(161, Arrays.asList(3019, 3020));
        GIFT_BADGE_ID_LIST.put(162, Arrays.asList(3021, 3022));
        GIFT_BADGE_ID_LIST.put(163, Arrays.asList(3023, 3024));
        GIFT_BADGE_ID_LIST.put(164, Arrays.asList(3025, 3026));
        GIFT_BADGE_ID_LIST.put(165, Arrays.asList(3027, 3028));
        GIFT_BADGE_ID_LIST.put(166, Arrays.asList(3029, 3030));
        GIFT_BADGE_ID_LIST.put(167, Arrays.asList(3031, 3032));
        GIFT_BADGE_ID_LIST.put(168, Arrays.asList(3033, 3034));
        GIFT_BADGE_ID_LIST.put(227, Arrays.asList(3035, 3036));
        GIFT_BADGE_ID_LIST.put(169, Arrays.asList(3037, 3038));
        GIFT_BADGE_ID_LIST.put(228, Arrays.asList(3039, 3040));
        GIFT_BADGE_ID_LIST.put(170, Arrays.asList(3041, 3042));
        GIFT_BADGE_ID_LIST.put(219, Arrays.asList(3043, 3044));
        GIFT_BADGE_ID_LIST.put(172, Arrays.asList(3045, 3046));
        GIFT_BADGE_ID_LIST.put(173, Arrays.asList(3047, 3048));
        GIFT_BADGE_ID_LIST.put(174, Arrays.asList(3049, 3050));
        GIFT_BADGE_ID_LIST.put(175, Arrays.asList(3051, 3052));
        GIFT_BADGE_ID_LIST.put(176, Arrays.asList(3053, 3054));
        GIFT_BADGE_ID_LIST.put(177, Arrays.asList(3055, 3056));
        GIFT_BADGE_ID_LIST.put(178, Arrays.asList(3057, 3058));
        GIFT_BADGE_ID_LIST.put(179, Arrays.asList(3059, 3060));
        GIFT_BADGE_ID_LIST.put(222, Arrays.asList(3061, 3062));
        GIFT_BADGE_ID_LIST.put(226, Arrays.asList(3063, 3064));
        GIFT_BADGE_ID_LIST.put(180, Arrays.asList(3065, 3066));
        GIFT_BADGE_ID_LIST.put(181, Arrays.asList(3067, 3068));
        GIFT_BADGE_ID_LIST.put(182, Arrays.asList(3069, 3070));
        GIFT_BADGE_ID_LIST.put(183, Arrays.asList(3071, 3072));
        GIFT_BADGE_ID_LIST.put(649, Arrays.asList(3073, 3074));
        GIFT_BADGE_ID_LIST.put(652, Arrays.asList(3075, 3076));
        GIFT_BADGE_ID_LIST.put(654, Arrays.asList(3077, 3078));
        GIFT_BADGE_ID_LIST.put(655, Arrays.asList(3079, 3080));
        // 新增id 224
    }

    @Resource
    private FootballRedis footballRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private FootballDao footballDao;
    @Resource
    private FootballActivityService footballActivityService;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            GIFT_BADGE_ID_LIST.put(147, Arrays.asList(2227, 2228));
            GIFT_BADGE_ID_LIST.put(149, Arrays.asList(2231, 2232));
            GIFT_BADGE_ID_LIST.put(150, Arrays.asList(2233, 2234));
        }
    }

    // 足球等级勋章活动
    @Cacheable(value = "getAllFootballData", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<FootballData> getAllFootballData() {
        return footballDao.findAll();
    }

    public FootballConfigVO footballConfig(String uid, String activityId) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        FootballConfigVO vo = new FootballConfigVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        vo.setCount(footballRedis.getTotalFootballRankingScore(uid));

        List<FootballData> footballDataList = footballActivityService.getAllFootballData();
        List<FootballConfigVO.Team> teamList = new ArrayList<>();
        List<FootballConfigVO.Team> sendTeamList = new ArrayList<>();

        for (FootballData data : footballDataList) {
            FootballConfigVO.Team team = new FootballConfigVO.Team();
            team.setName(data.getName());
            team.setIcon(data.getIcon());
            teamList.add(team);

            int giftIdNum = footballRedis.getGiftFootballRankingScore(uid, data.getGiftId());
            if (giftIdNum > 0) {
                FootballConfigVO.Team sendTeam = new FootballConfigVO.Team();
                sendTeam.setName(data.getName());
                sendTeam.setIcon(data.getIcon());
                sendTeam.setSend(giftIdNum);
                sendTeamList.add(sendTeam);
            }
        }

        vo.setTeamList(teamList);
        vo.setSendTeamList(sendTeamList);
        return vo;
    }


    public void distributionLevelBadge(String uid, int currentNum, List<Integer> levelNumList, List<Integer> badgeList) {
        try {

            List<Integer> tempLevelNumList = new ArrayList<>(levelNumList);
            int currentLevelIndex = 0;

            if (tempLevelNumList.contains(currentNum)) {
                currentLevelIndex = tempLevelNumList.indexOf(currentNum);
            } else {
                tempLevelNumList.add(currentNum);
                tempLevelNumList.sort(Integer::compare);
                currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
            }

            if (currentLevelIndex < 0) {
                return;
            }

            Integer badgeId = badgeList.get(currentLevelIndex);
            if (badgeId == null) {
                return;
            }

            BadgeData badgeData = badgeDao.getBadgeData(uid, badgeId);
            if (badgeData == null) {
                distributionService.sendRewardResource(uid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
            }

            // 移除低等级勋章
            List<Integer> removeBadgeList = new ArrayList<>();
            for (int i = 0; i < currentLevelIndex; i++) {
                removeBadgeList.add(badgeList.get(i));
            }

            if (!removeBadgeList.isEmpty()) {
                badgeDao.removeBadges(uid, removeBadgeList);
            }
        } catch (Exception e) {
            logger.error("distributionLevelBadge error:{}", e.getMessage(), e);
        }
    }

    public void handleFootballGift(SendGiftData giftData) {
        try {
            int giftId = giftData.getGid();
            if (!GIFT_BADGE_ID_LIST.containsKey(giftId)) {
                return;
            }
            String fromUid = giftData.getFrom_uid();
            int totalNumber = giftData.getNumber() * giftData.getAid_list().size();
            footballRedis.incTotalFootballRankingScore(fromUid, totalNumber);
            footballRedis.incGiftFootballRankingScore(fromUid, giftId, totalNumber);

            int currentNum = footballRedis.getGiftFootballRankingScore(fromUid, giftId);
            List<Integer> badgeIdList = GIFT_BADGE_ID_LIST.get(giftId);
            if (badgeIdList != null) {
                distributionLevelBadge(fromUid, currentNum, BADGE_LEVEL_LIST, badgeIdList);
            }

        } catch (Exception e) {
            logger.error("handleFootballGift error :{}", e.getMessage(), e);
        }

    }


    // 足球排行榜活动
    public void handleSoccerTeamGift(SendGiftData giftData, String activityId) {
        try {
            int giftId = giftData.getGid();
            String fromUid = giftData.getFrom_uid();
            int totalNumber = giftData.getNumber() * giftData.getAid_list().size();
            footballRedis.incTotalTeamRankingScore(activityId, String.valueOf(giftId), totalNumber);
            footballRedis.incGiftTeamRankingScore(activityId, giftId, fromUid, totalNumber);

        } catch (Exception e) {
            logger.error("handleSoccerTeamGift error :{}", e.getMessage(), e);
        }

    }


    public FBCompetitionVO fbCompetitionConfig(String uid, String activityId) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);


        FBCompetitionVO vo = new FBCompetitionVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        vo.setInvited(footballRedis.isInvited(uid));

        // 设置team榜
        Map<String, FootballData> teamMap = footballActivityService.getAllFootballData().stream().collect(Collectors.toMap(k -> String.valueOf(k.getGiftId()), Function.identity()));
        List<FBCompetitionVO.TeamVO> teamRankList = new ArrayList<>();
        Map<String, Integer> totalTeamMap = footballRedis.getTotalTeamRankingMap(activityId);
        for (Map.Entry<String, Integer> entry : totalTeamMap.entrySet()) {
            FootballData footballData = teamMap.get(entry.getKey());
            if (footballData == null) {
                continue;
            }

            FBCompetitionVO.TeamVO teamVO = new FBCompetitionVO.TeamVO();
            teamVO.setTeamName(footballData.getName());
            teamVO.setTeamIcon(footballData.getIcon());
            teamVO.setCount(entry.getValue());


            List<OtherRankingListVO> userList = new ArrayList<>();
            Map<String, Integer> giftSendMap = footballRedis.getGiftTeamRankingMap(activityId, footballData.getGiftId(), 3);
            for (Map.Entry<String, Integer> sendEntry : giftSendMap.entrySet()) {
                OtherRankingListVO giftSendVO = new OtherRankingListVO();
                String sendUid = sendEntry.getKey();
                ActorData sendActorData = actorDao.getActorDataFromCache(sendUid);
                giftSendVO.setName(sendActorData.getName());
                giftSendVO.setHead(ImageUrlGenerator.generateRoomUserUrl(sendActorData.getHead()));
                giftSendVO.setScore(sendEntry.getValue());
                userList.add(giftSendVO);
            }
            teamVO.setUserList(userList);
            teamRankList.add(teamVO);
        }
        vo.setTeamRankList(teamRankList);


        // 设置support排行榜
        int roundNum = activityData.getRoundNum();
        FBCompetitionVO.SupportRankVO mySupportVO = new FBCompetitionVO.SupportRankVO();
        List<FBCompetitionVO.SupportRankVO> supportVOList = new ArrayList<>();
        Map<String, Integer> supportRankingMap = activityOtherRedis.getOtherRankingMap(activityId, ActivityConstant.SEND_RANK, 10, roundNum);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : supportRankingMap.entrySet()) {
            FBCompetitionVO.SupportRankVO supportRankVO = new FBCompetitionVO.SupportRankVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            supportRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            supportRankVO.setName(rankActor.getName());
            supportRankVO.setScore(entry.getValue());
            supportRankVO.setRank(rank);
            if (uid.equals(entry.getKey())) {
                BeanUtils.copyProperties(supportRankVO, mySupportVO);
            }
            supportVOList.add(supportRankVO);
            rank += 1;
        }

        vo.setSupportRankList(supportVOList);

        if (StringUtils.isEmpty(mySupportVO.getHead())) {
            mySupportVO.setName(actorData.getName());
            mySupportVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            mySupportVO.setScore(activityOtherRedis.getOtherRankingScore(activityId, uid, ActivityConstant.SEND_RANK, roundNum));
        }
        vo.setMySupportRank(mySupportVO);
        return vo;
    }


}
