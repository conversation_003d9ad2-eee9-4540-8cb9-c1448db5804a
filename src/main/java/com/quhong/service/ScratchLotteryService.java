package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.DrawPrizeRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.config.AsyncConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.*;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.RechargedUserDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.ScratchLotteryRedis;
import com.quhong.utils.CollectionUtil;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 狮子刮刮乐
 *
 * <AUTHOR>
 * @date 2025/2/11
 */
@Service
public class ScratchLotteryService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(ScratchLotteryService.class);

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    public static final String ACTIVITY_ID = "6800e2d535a7dac5ef56f6f3";

    public static final String ACTIVITY_NAME = "Lion Scratch";
    private static final String DRAW_NUM = "draw_num"; // 抽奖次数
    private static final String DRAW_POOL_2_KEY = "LionScratchDraw2"; //  非付费用户资源key
    private static final String DRAW_POOL_1_KEY = "LionScratchDraw1"; // 付费用户资源key

    private static final String RANKING_REWARD_KEY = "LionTop%s";
    private static final String DRAW_TITLE_2 = "Lion Scratch-draw reward2";
    private static final String DRAW_TITLE_1 = "Lion Scratch-draw reward1";
    private static final String LUCKY_REWARD_KEY = "LionLuckyOne"; // 幸运奖资源key
    private static final String BADGE_REWARD_KEY = "LionBadge";

    private static final int GIFT_DRAW_BEANS = 100;
    private static final String GIFT_EXTRA_POINTS = "gift_extra_points"; // 礼物多余积分
    private static final int GAME_DRAW_BEANS = 100000;
    private static final String GAME_EXTRA_POINTS = "game_extra_points"; // 游戏多余积分

    private static final int LIMIT_INIT_POOL = 100;
    private static final Integer INIT_POOL_SIZE = 1000;
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final String SPECIAL_RESOURCE = "special_res_%s"; // 特殊资源 （用于合成的奖励）
    private static final String DRAW_REWARD = "drawReward";
    private static final String EXCHANGE_RECORD = "exchangeReward%s";
    //    private static final String GAME_URL = ServerConfig.isProduct() ? "https://h5.bigluckygame.com/game_greedy_six/?appId=666666&gameName=greedy" : "https://api.springluckygame.com/appw/greedy/?appId=666666&gameName=greedy";
    private static final String GAME_URL = "";

    private static final List<ScratchLotteryVO.Task> TASK_LIST = Arrays.asList(
            new ScratchLotteryVO.Task("task1", Arrays.asList("1", "2"), "Lion1And2", "Lion Scratch-exchange1 reward", ""),
            new ScratchLotteryVO.Task("task2", Arrays.asList("3", "4"), "Lion3And4", "Lion Scratch-exchange2 reward", ""),
            new ScratchLotteryVO.Task("task3", Arrays.asList("1", "2", "3", "4"), "Lion1And2And3And4", "Lion Scratch-exchange3 reward", BADGE_REWARD_KEY)
    );

    @Resource
    private ScratchLotteryRedis scratchLotteryRedis;
    @Resource
    private ScratchLotteryService scratchLotteryService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;

    public ScratchLotteryVO getInfo(String activityId, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = getActivityData(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ScratchLotteryVO vo = new ScratchLotteryVO();
        Map<String, Long> userDataMap = scratchLotteryRedis.getUserData(activityId, uid);
        fillScratchLotteryVO(vo, activityId, uid, userDataMap, activityData, actorData);
        return vo;
    }

    /**
     * 抽奖
     */
    public ScratchLotteryVO userDraw(String activityId, String uid, int num) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = getActivityData(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        if (num != 1 && num != 10 && num != 50) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList;
        boolean isRecharge = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30) > 0;
        synchronized (stringPool.intern(uid)) {
            long curBallNum = scratchLotteryRedis.getUserData(activityId, uid, DRAW_NUM);
            if (curBallNum - num < 0) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_CHANCES_TO_DRAW);
            }
            // 扣减用户抽奖次数
            scratchLotteryRedis.incUserData(activityId, uid, DRAW_NUM, -num);
            doReportItemsChangeEvent(uid, 2, "0", num, 0, "");
            // 抽奖
            resourceMetaList = draw(activityId, uid, num, isRecharge);

            // 抽中钻石加排行榜分数
            int incNum = 0;
            boolean notGetLuckyReward = scratchLotteryRedis.getUserData(activityId, uid, LUCKY_REWARD_KEY) == 0;
            for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetaList) {
                if (resourceMeta.getResourceType() == -2) {
                    incNum += resourceMeta.getResourceNumber();
                    if (resourceMeta.getResourceNumber() == 8888 && notGetLuckyReward) {
                        // 抽中10000钻石的幸运奖
                        resourceKeyHandlerService.sendResourceData(uid, LUCKY_REWARD_KEY, "Lion Scratch-lucky reward", "Lion Scratch-lucky reward");
                        scratchLotteryRedis.incUserData(activityId, uid, LUCKY_REWARD_KEY, 1);
                        notGetLuckyReward = true;
                    }
                }
            }
//            if (incNum > 0) {
//                scratchLotteryRedis.incrWinnerRankingScore(activityId, uid, incNum);
//            }
            // 卡片奖励
            Map<String, Long> specialResMap = resourceMetaList.stream().filter(k -> k.getResourceType() == -1)
                    .collect(Collectors.groupingBy(ResourceKeyConfigData.ResourceMeta::getMetaId, Collectors.counting()));
            for (Map.Entry<String, Long> entry : specialResMap.entrySet()) {
                scratchLotteryRedis.incUserData(activityId, uid, String.format(SPECIAL_RESOURCE, entry.getKey()), entry.getValue().intValue());
                doReportItemsChangeEvent(uid, 1, entry.getKey(), entry.getValue().intValue(), 3, "");
            }
        }
        List<ResourceKeyConfigData.ResourceMeta> finalResourceMetaList = resourceMetaList;
        // 异步下发抽奖奖励
        executor.execute(() -> asyncSendReward(activityId, uid, finalResourceMetaList, isRecharge));
        ScratchLotteryVO vo = new ScratchLotteryVO();
        List<PrizeConfigVO> list = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            PrizeConfigVO drawRecord = new PrizeConfigVO();
            drawRecord.setUid(uid);
            drawRecord.setDrawType(resourceMeta.getMetaId());
            drawRecord.setKey(resourceMeta.getMetaId());
            drawRecord.setRewardType(String.valueOf(resourceMeta.getResourceType()));
            ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceMeta.getResourceType());
            if (typeEnum != null && typeEnum != ResTypeEnum.BAG_GIFT) {
                drawRecord.setNameEn(typeEnum.formatTag(SLangType.ENGLISH, resourceMeta.getResourceTime()));
                drawRecord.setNameAr(typeEnum.formatTag(SLangType.ARABIC, resourceMeta.getResourceTime()));
            } else {
                drawRecord.setNameEn(ResTypeEnum.DIAMONDS.formatTag(SLangType.ENGLISH, resourceMeta.getResourceNumber()));
                drawRecord.setNameAr(ResTypeEnum.DIAMONDS.formatTag(SLangType.ARABIC, resourceMeta.getResourceNumber()));
            }
            drawRecord.setIconEn(resourceMeta.getResourceIcon());
            drawRecord.setRewardTime(resourceMeta.getResourceTime());
            drawRecord.setRewardNum(resourceMeta.getResourceNumber());
            drawRecord.setRewardPrice(resourceMeta.getResourcePrice());
            drawRecord.setCtime(DateHelper.getNowSeconds());
            ScratchLotteryRecordVO record = new ScratchLotteryRecordVO(Collections.singletonList(drawRecord), DateHelper.getNowSeconds());
            scratchLotteryRedis.saveRecord(activityId, DRAW_REWARD, uid, JSONObject.toJSONString(record));
            list.add(drawRecord);
        }
        vo.setRewardList(getShowPrizeList(list));
        Map<String, Long> userDataMap = scratchLotteryRedis.getUserData(activityId, uid);
        fillScratchLotteryVO(vo, activityId, uid, userDataMap, activityData, actorData);
        return vo;
    }

    /**
     * 兑换奖励
     */
    public ScratchLotteryVO exchange(String activityId, String uid, String taskKey) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = getActivityData(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        Map<String, ScratchLotteryVO.Task> taskMap = CollectionUtil.listToKeyMap(TASK_LIST, ScratchLotteryVO.Task::getKey);
        ScratchLotteryVO.Task task = taskMap.get(taskKey);
        if (task == null || CollectionUtils.isEmpty(task.getTargets())) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        synchronized (stringPool.intern(uid)) {
            Map<String, Long> userDataMap = scratchLotteryRedis.getUserData(activityId, uid);
            if (!exchangeCheck(task, userDataMap)) {
                throw new CommonH5Exception(ActivityHttpCode.CORRESPONDING_PATTERN_IS_MISSING);
            }
            // 扣减用户图案的数量
            for (String target : task.getTargets()) {
                scratchLotteryRedis.incUserData(activityId, uid, String.format(SPECIAL_RESOURCE, target), -1);
                doReportItemsChangeEvent(uid, 2, target, 1, 4, task.getKey().replace("task", ""));
            }
            // 下发合成奖励
            resourceKeyHandlerService.sendResourceData(uid, task.getRewardKey(), task.getRewardTitle(), task.getRewardTitle());
            if (StringUtils.hasLength(task.getBadgeRewardKey()) && userDataMap.getOrDefault(task.getBadgeRewardKey(), 0L) == 0) {
                // 勋章奖励
                resourceKeyHandlerService.sendResourceData(uid, task.getBadgeRewardKey(), "Lion Scratch-badge reward", "Lion Scratch-badge reward");
                scratchLotteryRedis.incUserData(activityId, uid, task.getBadgeRewardKey(), 1);
            }
        }
        saveExchangeRecord(activityId, uid, task);
        ScratchLotteryVO vo = new ScratchLotteryVO();
        Map<String, Long> userDataMap = scratchLotteryRedis.getUserData(activityId, uid);
        fillScratchLotteryVO(vo, activityId, uid, userDataMap, activityData, actorData);
        return vo;
    }

    public PageVO<ScratchLotteryRecordVO> getRewardRecord(String activityId, String uid, int type, int page) {
        List<ScratchLotteryRecordVO> list = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        List<String> drawRecordList = scratchLotteryRedis.getRecordList(activityId, type == 1 ? EXCHANGE_RECORD : DRAW_REWARD, uid, start, end);
        if (!CollectionUtils.isEmpty(drawRecordList)) {
            for (String strDrawRecord : drawRecordList) {
                list.add(JSONObject.parseObject(strDrawRecord, ScratchLotteryRecordVO.class));
            }
        }
        return new PageVO<>(list, list.size() >= RECORD_PAGE_SIZE ? page + 1 + "" : "");
    }

    private void fillScratchLotteryVO(ScratchLotteryVO vo, String activityId, String uid, Map<String, Long> userDataMap, OtherRankingActivityData activityData, ActorData actorData) {
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        vo.setRankingList(getRankingList(activityId));
        vo.setMyRank(getMyRanking(activityId, uid, actorData));
        vo.setDrawNum(userDataMap.getOrDefault(DRAW_NUM, 0L).intValue());
        vo.setNeedGiftBeans(GIFT_DRAW_BEANS - userDataMap.getOrDefault(GIFT_EXTRA_POINTS, 0L).intValue());
        vo.setNeedGameBeans(GAME_DRAW_BEANS - userDataMap.getOrDefault(GAME_EXTRA_POINTS, 0L).intValue());
        vo.setGameUrl(GAME_URL);
        vo.setRoomId(getPopularRoomId());
        vo.setLuckyReward(userDataMap.getOrDefault(LUCKY_REWARD_KEY, 0L).intValue());
        vo.setBadgeReward(userDataMap.getOrDefault(BADGE_REWARD_KEY, 0L).intValue());
        vo.setTaskList(getTaskList(userDataMap));
    }

    private List<ScratchLotteryVO.Task> getTaskList(Map<String, Long> userDataMap) {
        Map<String, Integer> targetMap = new HashMap<>();
        for (int i = 1; i <= 4; i++) {
            targetMap.put(i + "", userDataMap.getOrDefault(String.format(SPECIAL_RESOURCE, i), 0L).intValue());
        }
        List<ScratchLotteryVO.Task> taskList = new ArrayList<>();
        for (ScratchLotteryVO.Task task : TASK_LIST) {
            ScratchLotteryVO.Task newTask = new ScratchLotteryVO.Task();
            BeanUtils.copyProperties(task, newTask);
            newTask.setTargetMap(targetMap);
            taskList.add(newTask);
        }
        return taskList;
    }

    private void saveExchangeRecord(String activityId, String uid, ScratchLotteryVO.Task task) {
        ResourceKeyConfigData rewardConfigData = resourceKeyHandlerService.getConfigData(task.getRewardKey());
        if (rewardConfigData == null) {
            return;
        }
        List<PrizeConfigVO> drawRecordList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rewardConfigData.getResourceMetaList())) {
            for (ResourceKeyConfigData.ResourceMeta resourceMeta : rewardConfigData.getResourceMetaList()) {
                PrizeConfigVO record = new PrizeConfigVO();
                record.setRewardType(String.valueOf(resourceMeta.getResourceType()));
                ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceMeta.getResourceType());
                if (typeEnum != null && typeEnum != ResTypeEnum.BAG_GIFT) {
                    record.setNameEn(typeEnum.formatTag(SLangType.ENGLISH, resourceMeta.getResourceTime()));
                    record.setNameAr(typeEnum.formatTag(SLangType.ARABIC, resourceMeta.getResourceTime()));
                } else {
                    record.setNameEn(ResTypeEnum.DIAMONDS.formatTag(SLangType.ENGLISH, resourceMeta.getResourceNumber()));
                    record.setNameAr(ResTypeEnum.DIAMONDS.formatTag(SLangType.ARABIC, resourceMeta.getResourceNumber()));
                }
                record.setIconEn(resourceMeta.getResourceIcon());
                record.setRewardTime(resourceMeta.getResourceTime());
                record.setRewardNum(resourceMeta.getResourceNumber());
                record.setRewardPrice(resourceMeta.getResourcePrice());
                record.setCtime(DateHelper.getNowSeconds());
                drawRecordList.add(record);
            }
        }

        List<String> consumeList = scratchLotteryService.getConsumeList(task.getTargets());
        scratchLotteryRedis.saveRecord(activityId, EXCHANGE_RECORD, uid, JSONObject.toJSONString(new ScratchLotteryRecordVO(drawRecordList, consumeList, DateHelper.getNowSeconds())));
    }


    public List<String> getConsumeList(List<String> metaIdList) {
        ResourceKeyConfigData configData = resourceKeyHandlerService.getConfigData(DRAW_POOL_2_KEY);
        if (configData == null || CollectionUtils.isEmpty(configData.getResourceMetaList())) {
            return Collections.emptyList();
        }
        Map<String, ResourceKeyConfigData.ResourceMeta> map = CollectionUtil.listToKeyMap(configData.getResourceMetaList(), ResourceKeyConfigData.ResourceMeta::getMetaId);
        List<String> list = new ArrayList<>();
        metaIdList.forEach(k -> {
            ResourceKeyConfigData.ResourceMeta meta = map.get(k);
            if (StringUtils.hasLength(meta.getResourceIcon())) {
                list.add(meta.getResourceIcon());
            }
        });
        return list;
    }

    private boolean exchangeCheck(ScratchLotteryVO.Task task, Map<String, Long> userDataMap) {
        for (String target : task.getTargets()) {
            if (userDataMap.getOrDefault(String.format(SPECIAL_RESOURCE, target), 0L) <= 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理礼物消息
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String uid = giftData.getFrom_uid();
        synchronized (stringPool.intern(uid)) {
            int sendBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
            Map<String, Long> userMapData = scratchLotteryRedis.getUserData(activityId, uid);
            long extraPoints = userMapData.getOrDefault(GIFT_EXTRA_POINTS, 0L);
            long totalBeans = extraPoints + sendBeans;
            int incNum = (int) (totalBeans / GIFT_DRAW_BEANS);
            int modNum = (int) (totalBeans % GIFT_DRAW_BEANS);
            if (incNum != 0) {
                scratchLotteryRedis.incUserData(activityId, uid, DRAW_NUM, incNum);
                doReportItemsChangeEvent(uid, 1, "0", incNum, 1, "");
            }
            scratchLotteryRedis.setUserData(activityId, uid, GIFT_EXTRA_POINTS, modNum);
            scratchLotteryRedis.incrWinnerRankingScore(activityId, uid, (int) totalBeans);
        }
    }

    /**
     * 处理玩mq消息
     */
    public void handleMqMsg(CommonMqTopicData data) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        String uid = data.getUid();
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTestUser = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTestUser) {
                // 灰度测试,只统计测试用户的
                return;
            }
        }
        synchronized (stringPool.intern(uid)) {
            int value = Math.abs(data.getValue());
            Map<String, Long> userMapData = scratchLotteryRedis.getUserData(ACTIVITY_ID, uid);
            long extraPoints = userMapData.getOrDefault(GAME_EXTRA_POINTS, 0L);
            long totalBeans = extraPoints + value;
            int incNum = (int) (totalBeans / GAME_DRAW_BEANS);
            int modNum = (int) (totalBeans % GAME_DRAW_BEANS);
            if (incNum != 0) {
                scratchLotteryRedis.incUserData(ACTIVITY_ID, uid, DRAW_NUM, incNum);
                doReportItemsChangeEvent(uid, 1, "0", incNum, 2, "");
            }
            scratchLotteryRedis.setUserData(ACTIVITY_ID, uid, GAME_EXTRA_POINTS, modNum);
        }
    }

    public List<PrizeConfigVO> getShowPrizeList(List<PrizeConfigVO> prizeConfigList) {
        Map<String, PrizeConfigVO> map = new HashMap<>(prizeConfigList.size());
        for (PrizeConfigVO prizeConfig : prizeConfigList) {
            if (map.containsKey(prizeConfig.getKey())) {
                map.get(prizeConfig.getKey()).setRewardNum(map.get(prizeConfig.getKey()).getRewardNum() + 1);
            } else {
                PrizeConfigVO newPrizeConfig = new PrizeConfigVO();
                BeanUtils.copyProperties(prizeConfig, newPrizeConfig);
                newPrizeConfig.setRewardNum(1);
                map.put(newPrizeConfig.getKey(), newPrizeConfig);
            }
        }
        return new ArrayList<>(map.values());
    }

    private void asyncSendReward(String activityId, String uid, List<ResourceKeyConfigData.ResourceMeta> resourceMetaList, boolean isRecharge) {
        String drawTitle = isRecharge ? DRAW_TITLE_1 : DRAW_TITLE_2;
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            resourceKeyHandlerService.sendOneResourceData(uid, resourceMeta, 905, drawTitle, drawTitle, drawTitle, "", "", 1);
        }
    }

    private List<ResourceKeyConfigData.ResourceMeta> draw(String activityId, String uid, int num, boolean isRecharge) {
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = new ArrayList<>();
        String drawPoolKey = isRecharge ? DRAW_POOL_1_KEY : DRAW_POOL_2_KEY;
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(drawPoolKey);
        if (resourceKeyConfigData != null) {
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            initDrawPrizePool(activityId, drawPoolKey, resourceMetaMap);
            List<String> metaIdList = scratchLotteryRedis.popRewardFromPool(activityId, drawPoolKey, num);
            if (!CollectionUtils.isEmpty(metaIdList)) {
                metaIdList.forEach(k -> {
                    ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(k);
                    if (resourceMeta != null) {
                        resourceMetaList.add(resourceMeta);
                    }
                });
            }
        }
        doDrawPrizeRecordEvent(uid, num, isRecharge, resourceMetaList);
        return resourceMetaList;
    }

    private void initDrawPrizePool(String activityId, String poolName, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap) {
        int poolSize = scratchLotteryRedis.getPoolSize(activityId, poolName);
        if (poolSize <= 0) {
            expandPrizePool(activityId, poolName, resourceMetaMap.values());
        } else if (poolSize <= LIMIT_INIT_POOL) {
            executor.execute(() -> expandPrizePool(activityId, poolName, resourceMetaMap.values()));
        }
    }

    private void expandPrizePool(String activityId, String poolName, Collection<ResourceKeyConfigData.ResourceMeta> resourceMetas) {
        List<String> prizePoolList = new ArrayList<>(INIT_POOL_SIZE);
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetas) {
            // 添加元素到奖池
            int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
            prizePoolList.addAll(Collections.nCopies(rateNumber, resourceMeta.getMetaId()));
        }
        // 打乱奖池顺序
        Collections.shuffle(prizePoolList);
        scratchLotteryRedis.pushRewardInPool(activityId, poolName, prizePoolList);
    }

    private List<OtherRankingListVO> getRankingList(String activityId) {
        Map<String, Long> rankingMap = scratchLotteryRedis.getWinnerRankingMap(activityId, 10);
        List<OtherRankingListVO> list = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
            OtherRankingListVO vo = new OtherRankingListVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            vo.setName(rankActor.getName());
            vo.setScore(entry.getValue().intValue());
            vo.setRank(rank);
            vo.setBadgeList(badgeDao.getBadgeList(aid));
            vo.setVipLevel(vipInfoDao.getIntVipLevel(aid));
            vo.setUid(aid);
            list.add(vo);
            rank += 1;
        }
        return list;
    }

    private OtherMyRankVO getMyRanking(String activityId, String aid, ActorData actorData) {
        long score = scratchLotteryRedis.getWinnerRankingScore(activityId, aid);
        int rank = scratchLotteryRedis.getWinnerRankingRank(activityId, aid);
        OtherMyRankVO vo = new OtherMyRankVO();
        vo.setScore((int) score);
        vo.setRank(rank == 0 || rank > 99 ? -1 : rank);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setBadgeList(badgeDao.getBadgeList(aid));
        vo.setVipLevel(vipInfoDao.getIntVipLevel(aid));
        return vo;
    }

    /**
     * 发送榜单奖励
     */
    public void sendRankingReward(String activityId) {
        try {
            String title = "Lion Scratch-rank reward";
            Map<String, Long> rankingMap = scratchLotteryRedis.getWinnerRankingMap(activityId, 10);
            int rank = 1;
            for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
                String rewardKey = rank <= 5 ? String.format(RANKING_REWARD_KEY, rank) : String.format(RANKING_REWARD_KEY, "6-10");
                String aid = entry.getKey();
                logger.info("sendRankingReward. aid={} score={} rank={} rewardKey={}", aid, entry.getValue(), rank, rewardKey);
                ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(rewardKey);
                if (resourceKeyConfigData == null || CollectionUtils.isEmpty(resourceKeyConfigData.getResourceMetaList())) {
                    logger.error("sendResourceData not find; resourceKey={}", rewardKey);
                    return;
                }
                resourceKeyHandlerService.sendResourceData(aid, rewardKey, title, title);
                rank++;
            }
        } catch (Exception e) {
            logger.error("sendRankingReward error. activityId={} {}", activityId, e.getMessage(), e);
        }
    }

    private void doReportItemsChangeEvent(String uid, int action, String itemId, int num, int source, String desc) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setActivity_name(ACTIVITY_NAME);
        event.setActive_id(ACTIVITY_ID);
        event.setChange_action(action);
        event.setActivity_special_items_id(itemId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc(desc);
        event.setChange_nums(num);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void doDrawPrizeRecordEvent(String uid, int costNum, boolean isRecharge, List<ResourceKeyConfigData.ResourceMeta> resourceMetaList) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_NAME);
        event.setSence_detail(isRecharge ? 1 : 2);
        event.setCost_ticket(costNum);
        event.setDraw_nums(costNum);
        event.setDraw_success_nums(resourceMetaList.size());
        event.setTicket_type(0);
        event.setDraw_detail("");
        event.setDraw_result(JSONObject.toJSONString(resourceMetaList));
        eventReport.track(new EventDTO(event));
    }
}
