package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.ShareData;
import com.quhong.data.SociaEntryData;
import com.quhong.data.bo.ShareRoomEventBO;
import com.quhong.data.dto.ShareDTO;
import com.quhong.data.vo.ActivityDownloadUrlVO;
import com.quhong.data.vo.H5ShareVO;
import com.quhong.data.vo.ShareActivityVO;
import com.quhong.data.vo.ShareRoomEventVO;
import com.quhong.enums.LogType;
import com.quhong.enums.PKGConstant;
import com.quhong.enums.RoomConstant;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mysql.dao.ActivityShareConfigDao;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.dao.ShareParamDao;
import com.quhong.mysql.data.ActivityShareConfigData;
import com.quhong.mysql.data.RoomEventData;
import com.quhong.mysql.data.ShareParamData;
import com.quhong.mysql.data.SmashEggConfigData;
import com.quhong.redis.SudGameRedis;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/9
 */
@Service
public class ShareService {

    private static final Logger logger = LoggerFactory.getLogger(ShareService.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);

    private static final String DEBUG_SHARE_H5_LINK = "https://testv2.qmovies.tv/activity/share_activity_page/";
    private static final String PRO_SHARE_H5_LINK = "https://share.youstar.live/activity/share_activity_page/";

    private static final String DEBUG_SHARE_H5_LINK_NEW = "https://testv2.qmovies.tv/share/shareActivity/";
    private static final String PRO_SHARE_H5_LINK_NEW = "https://share.youstar.live/share/shareActivity/";

    private static final String ANDROID_APPSTORE = "https://play.google.com/store/apps/details?id=in.dradhanus.liveher";
    private static final String IOS_APPSTORE = "https://apps.apple.com/us/app/id1377028992";
    public static final String ENTER_ROOM_ACTION = "com.youstar.live://enterroom?roomId=";
    public static final String ENTER_USER_PROFILE_ACTION = "com.youstar.live://enterprofile?aid=";
    private static final String ENTER_ACTIVITY_ACTION = "com.youstar.live://enteractivity?activityUrl=";
    private static final String ENTER_FAMILY_ACTION = "com.youstar.live://enterfamily?rid=";
    private static final String ENTER_TASK_ACTION = "com.youstar.live://enterdailytask";
    private static final String ENTER_FAMILY_HALL = "com.youstar.live://enterfamilyhall";
    private static final String ENTER_ROOM_EVENT_ACTION = "com.youstar.live://enterevent?eventId=";     // 进入房间活动详情页
    private static final String ENTER_ROOM_EVENT_HALL_ACTION = "com.youstar.live://entereventhall";     // 进入房间活动广场
    private static final Integer RECORD_PAGE_SIZE = 10;

    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private ActivityShareConfigDao activityShareConfigDao;
    @Resource
    private ShareParamDao shareParamDao;
    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private SudGameRedis sudGameRedis;

    /**
     * 生成分享链接 h5
     */
    public H5ShareVO shareH5(ShareDTO dto) {
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            logger.error("user not exist. uid={}", dto.getUid());
            throw new CommonException(ActivityHttpCode.USER_NOT_EXIST);
        }
        String mid = dto.getMid();
        if (mid == null || mid.length() != 26 || !mid.startsWith("r:")) {
            logger.error("mid param error. mid={}", mid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        MongoRoomData roomData = roomDao.getDataFromCache(mid);
        String aid = RoomUtils.getRoomHostId(mid);
        ActorData hostActorData = actorDao.getActorDataFromCache(aid);
        if (roomData == null || hostActorData == null) {
            logger.error("room data not exist. mid={}", mid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        String resultUrl = createLink(dto.getUid(), dto.getMid(), roomData);
        return new H5ShareVO(resultUrl, resultUrl, ImageUrlGenerator.generateNormalUrl(roomData.getHead()), roomData.getName(), hostActorData.getStrRid());
    }

    /**
     * 生成h5页面
     */
    public ModelAndView sharePage(String param, Model model) {
        JSONObject jsonObject = getShareParam(param);
        String roomId = jsonObject != null ? jsonObject.getString("roomId") : "";
        String uid = jsonObject != null ? jsonObject.getString("uid") : "";
        if (roomId == null || roomId.length() != 26 || !roomId.startsWith("r:")) {
            logger.error("roomId param error. roomId={}", roomId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("user not exist. uid={}", uid);
            throw new CommonException(ActivityHttpCode.USER_NOT_EXIST);
        }
        MongoRoomData roomData = roomDao.getDataFromCache(roomId);
        String aid = RoomUtils.getRoomHostId(roomId);
        ActorData hostActorData = actorDao.getActorDataFromCache(aid);
        if (roomData == null || hostActorData == null) {
            logger.error("room data not exist. roomId={}", roomId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        String resultUrl = createLink(uid, roomId, roomData);
        model.addAttribute("android_link", resultUrl);
        model.addAttribute("ios_link", resultUrl);
        model.addAttribute("icon", ImageUrlGenerator.generateNormalUrl(roomData.getHead()));
        model.addAttribute("name", roomData.getName());
        model.addAttribute("rid", hostActorData.getStrRid());
        model.addAttribute("action_url", getActionUrl(uid, roomId));
        model.addAttribute("room_id", roomId);
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("share");
        return modelAndView;
    }


    public ShareActivityVO shareActivity(String uid, Integer shareId, Integer slang, Integer containsAid) {
        ActivityShareConfigData shareConfigData = activityShareConfigDao.selectOne(shareId);
        if (shareConfigData == null) {
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        slang = slang != null ? slang : SLangType.ENGLISH;
        containsAid = containsAid != null ? containsAid : 0;
        ShareActivityVO vo = new ShareActivityVO();
        vo.setActivityId(shareConfigData.getActivityId());
        vo.setName(slang == SLangType.ARABIC ? shareConfigData.getNameAr() : shareConfigData.getName());
        vo.setDescription(slang == SLangType.ARABIC ? shareConfigData.getDescriptionAr() : shareConfigData.getDescription());
        vo.setBanner(slang == SLangType.ARABIC ? shareConfigData.getBannerAr() : shareConfigData.getBanner());
        vo.setIcon(slang == SLangType.ARABIC ? shareConfigData.getIconAr() : shareConfigData.getIcon());
        vo.setUrl(shareConfigData.getUrl());
        vo.setActionUrl(getActionUrl(uid, shareId, slang, containsAid));
        return vo;
    }

    public ModelAndView shareActivityPage(String param, Model model) {
        JSONObject jsonObject = getShareParam(param);
        int shareId = jsonObject != null ? jsonObject.getIntValue("shareId") : 0;
        int slang = jsonObject != null ? jsonObject.getIntValue("slang") : 0;
        ActivityShareConfigData configData = activityShareConfigDao.selectOne(shareId);
        if (configData == null) {
            logger.error("can not find activity share config data. shareId={}", shareId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        String resultUrl = createLink();
        model.addAttribute("android_link", resultUrl);
        model.addAttribute("ios_link", resultUrl);
        String actionUrl = configData.getUrl();
        Map<String, String> paramMap = new HashMap<>(2);
        // 控制H5页面是否显示下载按钮
        paramMap.put("showDownloadBtn", "1");
        if (!StringUtils.isEmpty(configData.getActivityId())) {
            paramMap.put("activityId", configData.getActivityId());
        }
        actionUrl = getUrlByParamMap(actionUrl, paramMap);
        model.addAttribute("action_url", actionUrl);
        model.addAttribute("icon", slang == SLangType.ENGLISH ? ImageUrlGenerator.generateUrl(configData.getIcon(), 50, 50) : ImageUrlGenerator.generateUrl(configData.getIconAr(), 50, 50));
        model.addAttribute("name", slang == SLangType.ENGLISH ? configData.getName() : configData.getNameAr());
        model.addAttribute("desc", slang == SLangType.ENGLISH ? configData.getDescription() : configData.getDescriptionAr());
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("share_activity");
        return modelAndView;
    }

    public ActivityDownloadUrlVO getActivityDownloadUrl(Integer shareId) {
        ActivityShareConfigData shareConfigData = activityShareConfigDao.selectOne(shareId);
        if (shareConfigData == null) {
            logger.error("shareId error. shareId={}", shareId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        ShareData data = new ShareData();
        String baseUrl = "https://h8fty.app.goo.gl?";
        String officialUrl = "http://www.youstar.in/";
        data.setApn(PKGConstant.ANDROID_YOUSTAR);
        data.setIbi(PKGConstant.IOS_MAIN);
        data.setIsi("1377028992");
        data.setAmv("45");
        data.setLink(officialUrl + "room?mode=6&activityUrl=" + shareConfigData.getUrl());
        String resultUrl = getResultUrl(baseUrl, data);
        return new ActivityDownloadUrlVO(resultUrl, resultUrl);
    }

    public void cleanShareParamData() {
        int expireTime = DateHelper.getNowSeconds() - 30 * 24 * 60 * 60;
        shareParamDao.deleteExpireData(expireTime);
    }

    private JSONObject getShareParam(String param) {
        int paramId = strToInt(param);
        if (paramId == 0) {
            logger.error("share page param error. param={}", param);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        ShareParamData paramData = shareParamDao.selectOneById(paramId);
        if (paramData == null) {
            logger.error("can not find share param data. paramId={}", paramId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        return JSONObject.parseObject(paramData.getParams());
    }

    private String getActionUrl(String uid, int shareId, int slang, int containsAid) {
        String baseUrl = ServerConfig.isNotProduct() ? DEBUG_SHARE_H5_LINK_NEW : PRO_SHARE_H5_LINK_NEW;
        Map<String, String> paramMap = new HashMap<>(4);
        paramMap.put("shareId", shareId + "");
        paramMap.put("slang", slang + "");
        paramMap.put("uid", uid);
        if (containsAid == 1) {
            paramMap.put("containsAid", "1");
        }
        ShareParamData paramData = new ShareParamData();
        paramData.setUid(uid);
        paramData.setSource(1);
        paramData.setParams(JSON.toJSONString(paramMap));
        paramData.setCtime(DateHelper.getNowSeconds());
        shareParamDao.insert(paramData);
        return baseUrl + paramData.getId();
    }

    private String getUrlByParamMap(String url, Map<String, String> paramMap) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        if (!CollectionUtils.isEmpty(paramMap)) {
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                urlBuilder.queryParam(entry.getKey(), entry.getValue());
            }
        }
        return urlBuilder.build(false).encode().toUriString();
    }

    private String getActionUrl(String uid, String mid) {
        String baseUrl = "https://h8fty.app.goo.gl/?apn=in.dradhanus.liveher&ibi=com.stonemobile.youstar&isi=1377028992&amv=45&";
        ShareData data = new ShareData();
        data.setLink(String.format("https://www.youstar.in/room?mode=5&mid=%s&roomId=%s", uid, mid));
        return getResultUrl(baseUrl, data);
    }

    private String createLink(String uid, String mid, MongoRoomData roomData) {
        ShareData data = new ShareData();
        String baseUrl = "https://h8fty.app.goo.gl?";
        String officialUrl = "http://www.youstar.in/";
        data.setApn(PKGConstant.ANDROID_YOUSTAR);
        data.setIbi(PKGConstant.IOS_MAIN);
        data.setIsi("1377028992");
        data.setAmv("45");
        data.setLink(officialUrl + "room?mode=5&mid=" + uid + "&roomId=" + mid);
        data.setSt(roomData.getName());
        data.setSd(roomData.getAnnounce() != null ? roomData.getAnnounce() : "");
        data.setSi(roomData.getHead());
        return getResultUrl(baseUrl, data);
    }

    private String createLink() {
        ShareData data = new ShareData();
        String baseUrl = "https://h8fty.app.goo.gl?";
        String officialUrl = "http://www.youstar.in/";
        data.setApn(PKGConstant.ANDROID_YOUSTAR);
        data.setIbi(PKGConstant.IOS_MAIN);
        data.setIsi("1377028992");
        data.setAmv("45");
        data.setLink(officialUrl);
        return getResultUrl(baseUrl, data);
    }

    private String getResultUrl(String url, ShareData data) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromHttpUrl(url);
        Map<String, String> paramMap = JSON.parseObject(JSON.toJSONString(data), new TypeReference<Map<String, String>>() {
        });
        if (!CollectionUtils.isEmpty(paramMap)) {
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                String value = "";
                try {
                    value = URLEncoder.encode(entry.getValue(), "utf-8");
                } catch (Exception e) {
                    logger.error("url encode error. url={} {}", entry.getValue(), e.getMessage(), e);
                }
                urlBuilder.queryParam(entry.getKey(), value);
            }
        }
        return urlBuilder.build().toString();
    }


    private int strToInt(String strValue) {
        try {
            return Integer.parseInt(strValue);
        } catch (Exception e) {
            logger.info("string to integer error. strValue={}", strValue);
            return 0;
        }
    }


    /**
     * 生成分享房间h5页面
     */
    public ModelAndView shareRoom(String param, Model model) {
        ActorData actorData = actorDao.getActorByStrRid(param);
        if (actorData == null) {
            logger.error("user not exist. rid={}", param);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        MongoRoomData roomData = roomDao.getDataFromCache(RoomUtils.formatRoomId(actorData.getUid()));
        if (roomData == null) {
            msgLogger.error("room data not exist param:{} roomId={}", param, RoomUtils.formatRoomId(actorData.getUid()));
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        model.addAttribute("android_link", ANDROID_APPSTORE);
        model.addAttribute("ios_link", IOS_APPSTORE);
        model.addAttribute("icon", ImageUrlGenerator.generateNormalUrl(roomData.getHead()));
        model.addAttribute("name", roomData.getName());
        model.addAttribute("rid", actorData.getStrRid());
        model.addAttribute("action_url", ENTER_ROOM_ACTION + roomData.getRid());
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("share");
        return modelAndView;
    }

    /**
     * 生成分享游戏房间h5页面
     */
    public ModelAndView shareGameRoom(String param, Model model) {
        ActorData actorData = actorDao.getActorByStrRid(param);
        if (actorData == null) {
            logger.info("user not exist. rid={}", param);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        String gameRoomId = RoomUtils.formatGameRoomId(actorData.getUid());
        MongoRoomData roomData = roomDao.getDataFromCache(gameRoomId);
        if (roomData == null) {
            logger.info("live room data not exist. roomId={}", RoomUtils.formatGameRoomId(actorData.getUid()));
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        int runGameType = sudGameRedis.getGameTypeByRoomId(gameRoomId);
        String icon = ImageUrlGenerator.generateNormalUrl(roomData.getHead());
        if (runGameType > 0) {
            Map<Integer, SociaEntryData> sociaEntryDataMap = RoomConstant.SOCIAL_ITEM_LIST.stream().filter(item -> item.getGameType() > 0).collect(Collectors.toMap(SociaEntryData::getGameType, Function.identity(), (key1, key2) -> key2));
            SociaEntryData sociaEntryData = sociaEntryDataMap.get(runGameType);
            icon = sociaEntryData != null ? sociaEntryData.getSmallIcon() : icon;
        }
        model.addAttribute("android_link", ANDROID_APPSTORE);
        model.addAttribute("ios_link", IOS_APPSTORE);
        model.addAttribute("icon", icon);
        model.addAttribute("name", roomData.getName());
        model.addAttribute("rid", actorData.getRid());
        model.addAttribute("action_url", ENTER_ROOM_ACTION + roomData.getRid());
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("share");
        return modelAndView;
    }

    /**
     * 分享个人主页页面
     */
    public ModelAndView shareUser(String param, Model model) {
        ActorData actorData = actorDao.getActorByStrRid(param);
        if (actorData == null) {
            logger.error("user not exist. rid={}", param);
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_EXIST);
        }
        model.addAttribute("android_link", ANDROID_APPSTORE);
        model.addAttribute("ios_link", IOS_APPSTORE);
        model.addAttribute("action_url", ENTER_USER_PROFILE_ACTION + actorData.getUid());
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("shareUser");
        return modelAndView;
    }

    /**
     * 分享活动
     */
    public ModelAndView shareActivity(String param, Model model) {
        JSONObject jsonObject = getShareParam(param);
        int shareId = jsonObject != null ? jsonObject.getIntValue("shareId") : 0;
        int slang = jsonObject != null ? jsonObject.getIntValue("slang") : 0;
        String uid = jsonObject != null ? jsonObject.getString("uid") : "";
        int containsAid = jsonObject != null ? jsonObject.getIntValue("containsAid") : 0;
        ActivityShareConfigData configData = activityShareConfigDao.selectOne(shareId);
        if (configData == null) {
            logger.error("can not find activity share config data. shareId={}", shareId);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }
        String actionUrl = configData.getUrl();
        if (containsAid == 1) {
            ActorData hostActorData = actorDao.getActorDataFromCache(uid);
            if (hostActorData == null) {
                logger.error("uid data not exist. uid={}", uid);
                throw new CommonException(ActivityHttpCode.PARAM_ERROR);
            }
            actionUrl = actionUrl + "&shareAid=" + uid;
            model.addAttribute("icon", ImageUrlGenerator.generateNormalUrl(hostActorData.getHead()));
        }
        model.addAttribute("android_link", ANDROID_APPSTORE);
        model.addAttribute("ios_link", IOS_APPSTORE);
        model.addAttribute("action_url", ENTER_ACTIVITY_ACTION + actionUrl);
        ModelAndView modelAndView = new ModelAndView();
        if (containsAid == 1) {
            modelAndView.setViewName("shareActivityAid");
        } else {
            modelAndView.setViewName("shareActivity");
        }
        return modelAndView;
    }

    /**
     * 分享房间活动
     */
    public ModelAndView shareRoomEvent(String param, Model model) {
        int paramId = strToInt(param);
        RoomEventData roomEventData = roomEventDao.selectById(paramId);
        ShareRoomEventBO bo = new ShareRoomEventBO();
        if (roomEventData != null) {
            BeanUtils.copyProperties(roomEventData, bo);
            MongoRoomData roomData = roomDao.getDataFromCache(roomEventData.getRoomId());

            if (roomData != null) {
                bo.setRoomRid(roomData.getRid());
                bo.setRoomName(roomData.getName());
                ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomEventData.getRoomId()));

                model.addAttribute("eventCoverUrl", roomEventData.getEventCoverUrl());
                model.addAttribute("eventName", roomEventData.getName());
                model.addAttribute("eventDescription", roomEventData.getDescription());
                model.addAttribute("eventStartTime", roomEventData.getStartTime());
                model.addAttribute("eventEndTime", roomEventData.getEndTime());
                model.addAttribute("roomName", roomData.getName());
                model.addAttribute("roomId", actorData.getStrRid());
            }
        }
        model.addAttribute("android_link", ANDROID_APPSTORE);
        model.addAttribute("ios_link", IOS_APPSTORE);
        model.addAttribute("action_url", ENTER_ROOM_EVENT_ACTION + paramId);
        model.addAttribute("action_hall_url", ENTER_ROOM_EVENT_HALL_ACTION);
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("shareRoomEvent");
        return modelAndView;
    }

    public ShareRoomEventVO shareRoomEventConfig() {
        int now = DateHelper.getNowSeconds();
        ShareRoomEventVO vo = new ShareRoomEventVO();
        List<ShareRoomEventBO> roomEventConfigList = new ArrayList<>();
        List<RoomEventData> roomEventDataList = roomEventDao.getRoomEventList(now);
        for (RoomEventData item : roomEventDataList) {
            ShareRoomEventBO bo = new ShareRoomEventBO();
            BeanUtils.copyProperties(item, bo);
            String roomId = item.getRoomId();
            MongoRoomData roomData = roomDao.getDataFromCache(roomId);
            if (roomData == null) {
                continue;
            }
            bo.setRoomRid(roomData.getRid());
            bo.setRoomName(roomData.getName());
            roomEventConfigList.add(bo);
        }
        vo.setRoomEventConfigList(roomEventConfigList);
        return vo;
    }



}
