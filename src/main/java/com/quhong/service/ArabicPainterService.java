package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.data.vo.ArabicPainterVO;
import com.quhong.data.vo.OtherRankConfigVO;
import com.quhong.datas.DayTimeData;
import com.quhong.datas.HttpResult;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.PainterRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ArabicPainterService extends OtherActivityService implements TaskMsgHandler {
    /**
     * 阿拉伯书法家
     */

    private static final Logger logger = LoggerFactory.getLogger(ArabicPainterService.class);
    private static final String MOMENT_PAINTER_ORIGIN = "arabicPainterPicture";
    public static final String ACTIVITY_ID = "67e3f0942396cd6738d34912";
    private static final String CDN_DOMAIN = "https://cloudcdn.qmovies.tv/";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/arabic_calligrapher/?activityId=%s", ACTIVITY_ID) : String.format("https://test2.qmovies.tv/arabic_calligrapher/?activityId=%s", ACTIVITY_ID);
    //    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? "https://static.youstar.live/Calligrapher/?activityId=6417c55b49490641c0f8159c&shareId=2" : "https://test2.qmovies.tv/Calligrapher/?activityId=6417c55b49490641c0f8159c&shareId=2";
    private static final String ACTIVITY_ICON = "https://cdn3.qmovies.tv/youstar/op_1742805228_albsfj_.jpg";
    private static final String ACTIVITY_TITLE_AR = "الخطاط العربي";
    private static final String ACTIVITY_TITLE_EN = "Calligrapher";
    private static final List<String> TITLE_TEXT_AR = Arrays.asList(
            "\"ووجدك ضالا فهدى.\"",
            "\"و هو معكم أين ما كنتم.\"",
            "\"العلم نور والجهل ظلام.\"",
            " \"الكلمة الطيبة صدقة.\"",
            "\"الحرية أثمن من الذهب.\"",
            " \"إذا أحببت شخصًا فأخبره، فالحب لا يعرف الصمت.\"",
            "\"الكتاب خير جليس.\"",
            "\"الدنيا ساعة فاجعلها طاعة.\""
    );
    private static final List<String> COMMENT_TEXT_AR = Arrays.asList(
            "أقيّم 1/10 \uD83E\uDEE3 ما هذا؟ الخط يبدو عشوائيًا جدًا، الحروف غير متناسقة، والتركيب غير واضح. من الصعب قراءته!",
            "أقيّم 2/10 \uD83E\uDD14 يمكن التعرف على الحروف، لكنها غير متوازنة. الخط ضعيف ويفتقر إلى القوة، ويحتاج إلى مزيد من الممارسة.",
            "أقيّم 3/10 \uD83D\uDE2C الكتابة واضحة، لكن لا يوجد تناغم. الحروف متباعدة، والخطوط غير متسقة. الأساسيات بحاجة إلى تحسين!",
            "أقيّم 4/10 ✍ هناك مستوى جيد، لكن الخط يفتقر إلى الانسيابية والجمالية. التوازن ضعيف، لكنه بداية جيدة تحتاج إلى تطوير.",
            "أقيّم 5/10 \uD83D\uDE42 الخط جميل ومنظم، لكنه لا يزال يفتقر إلى الإبداع والقوة. يمكن تحسين توزيع الحروف وقوة الضغط على القلم.",
            "أقيّم 6/10 \uD83D\uDC4D الخط ثابت ومتناسق، وهناك سيطرة على القلم، لكنه بحاجة إلى مزيد من التدرب على التوازن والإيقاع.",
            "أقيّم 7/10 \uD83C\uDF3F الخط واضح وأنيق، وهناك لمسة شخصية مميزة. إذا تم تحسين بعض تفاصيل الحروف، فسيصبح أكثر جاذبية.",
            "أقيّم 8/10 \uD83C\uDFA8 الخط متقن، الحروف متناسقة، والتناغم واضح. لمسة فنية جميلة جدًا! بحاجة فقط لبعض التحسينات الطفيفة.",
            "أقيّم 9/10 \uD83D\uDD25 إبداع حقيقي! الخط قوي ومتوازن، يحمل روحًا وانسيابية رائعة. يظهر فيه مستوى عالٍ من الاحترافية.",
            "أقيّم 10/10 \uD83C\uDF1F تحفة فنية! الخط مليء بالحياة، الحروف نابضة بالإحساس والقوة، والتناسق مثالي! هذا هو الإبداع في أبهى صوره! \uD83D\uDC4F\uD83D\uDC4F"
    );
    private static final List<String> COMMENT_TEXT_MD5_LIST = Arrays.asList(
            "a5768c7831e300e6281385d71dcf6e9f",
            "625022cbbfa7fd394c11a1fea4046c63",
            "5e46530f7d35c947bf7611d0ee33de2b",
            "53bedfd6ab9699bf2aebf05630d98ad8",
            "dd89f30c6f16f021e346b4d14783407b",
            "cb32beb1b725e0213e98430adc6a8aa3",
            "d1cc3d4fbe325f6576ed15b9359492bb",
            "6c8086b0fb2c5f72a4506dda14e8b2cd",
            "18817ffb4751a696593c01357f4c812e",
            "43a46faf4fa0b86945dbea947a70dac4"
    );

    /**
     * 榜单前3名奖励
     */
    public static final List<String> RANK_KEY_LIST = Arrays.asList(
            "ArabicPainterTop1", "ArabicPainterTop2", "ArabicPainterTop3");

    private static final String COMMENT_REWARD_KEY = "ArabicPainterCommentRewardKey";

    private static final int MAX_COMMENT_LIMIT = 5;

    private static int TOPIC_RID = 13493;
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.COMMENT_MOMENT, CommonMqTaskConstant.POST_MOMENT);

    /**
     * 埋点事件
     */
    public static final Map<Integer, String> EVENT_TITLE_MAP = new HashMap<Integer, String>() {
        {
            put(1, "Calligrapher-task reward");
            put(2, "Calligrapher-rank reward");
        }
    };
    @Resource
    private IMomentService iMomentService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MomentActivityDao momentActivityDao;
    @Resource
    private IDetectService idetectService;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private PainterRedis painterRedis;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private WhiteTestDao whiteTestDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            TOPIC_RID = 10132;
            for (String text : COMMENT_TEXT_AR) {
                String textMd5 = DigestUtils.md5DigestAsHex(text.getBytes(StandardCharsets.UTF_8));
                logger.info("textMd5:{}", textMd5);
            }
        }
    }

    public ArabicPainterVO painterConfig(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        ArabicPainterVO vo = new ArabicPainterVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        int enterType = 4;
        int nowTime = DateHelper.getNowSeconds();
        String now = getDay(uid);
        String totalInfoKey = getHashTotalKey(activityId);
        ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
        Map<String, Integer> dayMapLikeState = painterInfo.getDayMapLikeState();
        if (nowTime < activityData.getEndTime()) {
            if (painterInfo.getFirstEntryTime() == 0) {
                synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                    painterInfo.setFirstEntryTime(nowTime);
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
                }
                enterType = 1;
            } else {
                enterType = painterInfo.getFirstWriteTime() > 0 ? 2 : 3;
            }
        }
        vo.setEnterType(enterType);

        int pos = getPositionByDay(activityData.getStartTime(), Math.min(DateHelper.getNowSeconds(), activityData.getEndTime() - 10));
        int maxPos = TITLE_TEXT_AR.size() - 1;
        pos = Math.min(pos, maxPos);
        vo.setRecommendText(TITLE_TEXT_AR.get(pos));
        vo.setRecommendTextState(dayMapLikeState.getOrDefault(now, 0));

        List<ArabicPainterVO.ArabicPainterMomentInfoVO> myMomentListVO = new ArrayList<>();
        List<ArabicPainterVO.ArabicPainterMomentInfoVO> rankMomentListVO = new ArrayList<>();
        ArabicPainterVO.ArabicPainterMomentInfoVO myRankVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
        int uploadCount = 0;
        int myRank = -1;
        if (nowTime < activityData.getStartTime()) {
            String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
            ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
            itemVO.setDayStr(acStart);
            myMomentListVO.add(itemVO);
        } else if (nowTime >= activityData.getStartTime()) {
            List<MomentActivityData> myMomentActivities = momentActivityDao.findMomentList(uid, MOMENT_PAINTER_ORIGIN);
            if (!CollectionUtils.isEmpty(myMomentActivities)) {
                Map<String, MomentActivityData> midMap = myMomentActivities.stream()
                        .collect(Collectors.toMap(k -> k.get_id().toString(), Function.identity()));
                int endTime = Math.min(activityData.getEndTime() - 10, DateHelper.getNowSeconds()); // 偏移10秒为活动的最后一天
                endTime = Math.max(endTime, DateHelper.ARABIAN.stringDateToStampSecond(now)); // 这里可能按测试偏移天数转换，用于测试环境
                String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
                String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
                List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
                Map<String, String> dayMapMomentId = painterInfo.getDayMapMomentId();
                uploadCount = dayMapMomentId.size();
                for (DayTimeData item : dayTimeDataList) {
                    String day = item.getDate();
                    String mid = dayMapMomentId.get(day);
                    if (!StringUtils.isEmpty(mid)) {
                        MomentActivityData data = midMap.get(mid);
                        if (data != null) {
                            ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                            itemVO.setDayStr(day);
                            itemVO.setMomentId(mid);
                            itemVO.setPicture(data.getImgs().get(0).getThumbnail());
                            itemVO.setTitle(data.getText());
                            itemVO.setGiftTotalPrice(data.getGiftTotalPrice());
                            itemVO.setComments(data.getComments());
                            itemVO.setLikes(data.getLikes() != null ? data.getLikes().size() : 0);
                            if (data.getLikes() != null && data.getLikes().contains(uid)) {
                                itemVO.setLikeStatus(1);
                            } else {
                                itemVO.setLikeStatus(0);
                            }
                            myMomentListVO.add(itemVO);
                        }
                    } else if (day.equals(now)) {
                        // 之前有上传作品，当天没有上传的作品，构造一个空数据
                        ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                        itemVO.setDayStr(day);
                        myMomentListVO.add(itemVO);
                    }
                }
            } else {
                // 如果没有上传任何作品，只构造一个当天的数据
                ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                itemVO.setDayStr(now);
                myMomentListVO.add(itemVO);
            }
        }

        List<MomentActivityData> mMomentList = momentActivityDao.momentRanking(MOMENT_PAINTER_ORIGIN, activityData.getStartTime(), activityData.getEndTime(), 50);
        if (!CollectionUtils.isEmpty(mMomentList)) {
            List<String> uidList = mMomentList.stream().map(MomentActivityData::getUid).collect(Collectors.toList());
            myRank = uidList.indexOf(uid);
            myRank = myRank == -1 ? -1 : myRank + 1;
            int rank = 0;
            for (MomentActivityData item : mMomentList) {
                rank++;
                if (rank > 10) {
                    break;
                }
                ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                itemVO.setRank(rank);
                itemVO.setMomentId(item.get_id().toString());
                itemVO.setPicture(item.getImgs().get(0).getThumbnail());
                itemVO.setTitle(item.getText());
                itemVO.setGiftTotalPrice(item.getGiftTotalPrice());
                itemVO.setComments(item.getComments());
                itemVO.setLikes(item.getLikes() != null ? item.getLikes().size() : 0);
                if (item.getLikes() != null && item.getLikes().contains(uid)) {
                    itemVO.setLikeStatus(1);
                } else {
                    itemVO.setLikeStatus(0);
                }

                ActorData actorData = actorDao.getActorDataFromCache(item.getUid());
                itemVO.setName(actorData.getName());
                itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                itemVO.setAid(item.getUid());
                rankMomentListVO.add(itemVO);
            }

        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        myRankVO.setUploadCount(uploadCount);
        myRankVO.setName(actorData.getName());
        myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        myRankVO.setRank(myRank);

        vo.setMyMomentListVO(myMomentListVO);
        vo.setMyRankVO(myRankVO);
        vo.setRankMomentListVO(rankMomentListVO);

        return vo;

    }

    /**
     * 开始鉴赏
     *
     * @param activityId
     * @param uid
     * @return
     */
    public ArabicPainterVO recommendDayCreateList(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ArabicPainterVO vo = new ArabicPainterVO();
        List<ArabicPainterVO.ArabicPainterDayVO> arabicPainterDayVOList = new ArrayList<>();
        int nowTime = DateHelper.getNowSeconds();

        if (nowTime >= activityData.getStartTime()) {
            int endTime = Math.min(activityData.getEndTime() - 10, DateHelper.getNowSeconds()); // 偏移10秒为活动的最后一天
            String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
            String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
            List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
            for (DayTimeData item : dayTimeDataList) {
                String day = item.getDate();
                int s = DateHelper.ARABIAN.stringDateToStampSecond(day);
                int e = (int) (s + TimeUnit.DAYS.toSeconds(1));
                List<MomentActivityData> rankList = momentActivityDao.momentRankingCache(MOMENT_PAINTER_ORIGIN, s, e, 20,0);
                if (!CollectionUtils.isEmpty(rankList)) {
                    List<ArabicPainterVO.ArabicPainterMomentInfoVO> recommendMomentList = new ArrayList<>();
                    for (MomentActivityData top1Data : rankList) {
                        ArabicPainterVO.ArabicPainterMomentInfoVO itemVO = new ArabicPainterVO.ArabicPainterMomentInfoVO();
                        itemVO.setDayStr(day);
                        itemVO.setMomentId(top1Data.get_id().toString());
                        itemVO.setPicture(top1Data.getImgs().get(0).getThumbnail());
                        itemVO.setTitle(top1Data.getText());
                        itemVO.setGiftTotalPrice(top1Data.getGiftTotalPrice());
                        itemVO.setComments(top1Data.getComments());
                        itemVO.setLikes(top1Data.getLikes() != null ? top1Data.getLikes().size() : 0);
                        if (top1Data.getLikes() != null && top1Data.getLikes().contains(uid)) {
                            itemVO.setLikeStatus(1);
                        } else {
                            itemVO.setLikeStatus(0);
                        }
                        ActorData actorData = actorDao.getActorDataFromCache(top1Data.getUid());
                        itemVO.setName(actorData.getName());
                        itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                        itemVO.setAid(top1Data.getUid());
                        recommendMomentList.add(itemVO);
                    }
                    ArabicPainterVO.ArabicPainterDayVO dayVO = new ArabicPainterVO.ArabicPainterDayVO();
                    dayVO.setDayStr(day);
                    dayVO.setRecommendMomentListVOList(recommendMomentList);
                    arabicPainterDayVOList.add(dayVO);
                }
            }
        }

        if (CollectionUtils.isEmpty(arabicPainterDayVOList)) {
            ArabicPainterVO.ArabicPainterDayVO dayVO = new ArabicPainterVO.ArabicPainterDayVO();
            String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activityData.getStartTime() * 1000L));
            dayVO.setDayStr(acStart);
            dayVO.setRecommendMomentListVOList(Collections.emptyList());
            arabicPainterDayVOList.add(dayVO);
        }

        vo.setArabicPainterDayVOList(arabicPainterDayVOList);
        vo.setRecommendCommentList(COMMENT_TEXT_AR);
        vo.setTopicRid(TOPIC_RID);

        String nowDay = getDay(uid);
        String totalInfoKey = getHashTotalKey(activityId);
        ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
        Map<String, Set<String>> dayMapCommentAidList = painterInfo.getDayMapCommentAidList();
        Set<String> dayAidList = dayMapCommentAidList.getOrDefault(nowDay, new HashSet<>());
        vo.setCommentCount(dayAidList.size());
        return vo;
    }

    public void painterPicturePush(String activityId, String uid, PainterPictureDTO dto) {
        checkActivityTime(activityId);
        String picture = dto.getPicture();
        String momentText = dto.getMomentText();
        int slang = dto.getSlang();

        if (StringUtils.isEmpty(picture)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (StringUtils.isEmpty(momentText)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (momentText.length() > 50) {
            logger.info("length too long momentText length:{}", momentText.length());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        String nowDay = getDay(uid);
        int startTime = DateHelper.ARABIAN.stringDateToStampSecond(nowDay);
        int endTime = (int) (startTime + TimeUnit.DAYS.toSeconds(1));
        MomentActivityData momentActivityData = momentActivityDao.findMomentOneByTime(uid, MOMENT_PAINTER_ORIGIN, startTime, endTime);
        if (momentActivityData != null) {
            throw new CommonH5Exception(ActivityHttpCode.ARABIC_PAINTER_ALREADY_JOIN);
        }
        if (idetectService.detectImage(new ImageDTO(CDN_DOMAIN + picture, "activity")).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_IMAGE);
        }
        if (idetectService.detectText(new TextDTO(momentText, DetectOriginConstant.ACTIVITY_RELATED,uid)).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }

        synchronized (stringPool.intern(getLocalPublishUserKey(uid))) {
            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText(momentText);
            publishMomentDTO.setShow(1);
            publishMomentDTO.setActiveId(ACTIVITY_ID);
            publishMomentDTO.setLocation(MOMENT_PAINTER_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("2000");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));

            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setType(6);
            quote.setIcon(ACTIVITY_ICON);
            quote.setContent(ACTIVITY_TITLE_AR);
            quote.setAction(ACTIVITY_URL);

            publishMomentDTO.setQuote(quote);

            publishMomentDTO.setTopicRid(TOPIC_RID);

            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if (result.getCode() == 20) {
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            String mid = result.getData();
            if (!StringUtils.isEmpty(mid)) {
                String now = getDay(uid);
                String totalInfoKey = getHashTotalKey(activityId);
                ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
                synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                    Map<String, String> dayMapMomentId = painterInfo.getDayMapMomentId();
                    dayMapMomentId.put(now, mid);
                    if (painterInfo.getFirstWriteTime() == 0) {
                        painterInfo.setFirstWriteTime(DateHelper.getNowSeconds());
                    }
                    doCalligrapherEvent(ACTIVITY_ID, uid, 1);
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
                }
            }
        }
    }


    /**
     * 给今日推荐文案点赞
     *
     * @param activityId
     * @param uid
     */
    public void painterLikeMomentText(String activityId, String uid) {
        checkActivityTime(activityId);
        String now = getDay(uid);
        String totalInfoKey = getHashTotalKey(activityId);
        ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
        Map<String, Integer> dayMapLikeState = painterInfo.getDayMapLikeState();
        if (dayMapLikeState.getOrDefault(now, 0) == 0) {
            synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                dayMapLikeState.put(now, 1);
                activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
            }
        }
    }


    public void painterLike(String activityId, String uid, String momentId) {
        checkActivityTime(activityId);
        if (StringUtils.isEmpty(momentId)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        ApiResult<HttpCode> result = iMomentService.likeMoment(uid, momentId);
        if (result.getCode().getCode() == 20) {
            logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        if (item.equals(CommonMqTaskConstant.COMMENT_MOMENT)) {
            syncHandle(uid, data);
        }

    }

    private void syncHandle(String uid, CommonMqTopicData mqData) {
        String eventId = checkAc(uid, mqData);
        if (eventId == null) {
            return;
        }
        String now = getDay(uid);
        String totalInfoKey = getHashTotalKey(null);
        synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
            ArabicPainterVO.ArabicPainterInfo painterInfo = cacheDataService.getArabicPainterInfo(totalInfoKey, uid);
            if (mqData.getItem().equals(CommonMqTaskConstant.COMMENT_MOMENT)) {
                String aid = mqData.getAid();
                String mid = mqData.getHandleId();
                Map<String, Set<String>> dayMapCommentAidList = painterInfo.getDayMapCommentAidList();
                Set<String> levelAidList = dayMapCommentAidList.getOrDefault(now, new HashSet<>());
                MomentActivityData momentActivityData = momentActivityDao.findMomentById(mid);
                if (momentActivityData != null && MOMENT_PAINTER_ORIGIN.equals(momentActivityData.getLocation())
                        && levelAidList.size() < MAX_COMMENT_LIMIT && !levelAidList.contains(aid)) {
                    levelAidList.add(aid);
                    dayMapCommentAidList.put(now, levelAidList);
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(painterInfo));
                    if (levelAidList.size() == MAX_COMMENT_LIMIT) {
                        handleRes(uid, COMMENT_REWARD_KEY, 1);
                        doCalligrapherEvent(ACTIVITY_ID, uid, 2);
                    }
                }
            }

        }
    }

    private String checkAc(String uid, CommonMqTopicData mqData) {
        String eventId = null;
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return null;
            }
        }
        if (mqData.getItem().equals(CommonMqTaskConstant.COMMENT_MOMENT)) {
            logger.info("uid:{} aid:{} mid:{} textMD5:{}", uid, mqData.getAid(), mqData.getHandleId(), mqData.getJsonData());
            if (StringUtils.isEmpty(mqData.getJsonData()) || !COMMENT_TEXT_MD5_LIST.contains(mqData.getJsonData())) {
                return null;
            }

        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return null;
        }
        return "ok";
    }


    // 下发榜单奖励
    public void distributionRanking() {
        try {
            OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
            int length = 3;
            List<MomentActivityData> mMomentList = momentActivityDao.momentRanking(MOMENT_PAINTER_ORIGIN,
                    activityData.getStartTime(), activityData.getEndTime(), length);
            int rank = 1;
            for (MomentActivityData item : mMomentList) {
                if (rank > length) {
                    continue;
                }
                String aid = item.getUid();
                String resKey = RANK_KEY_LIST.get(rank - 1);
                handleRes(aid, resKey, 2);
                rank += 1;
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, int atype) {
        String eventTitle = EVENT_TITLE_MAP.getOrDefault(atype, "");
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    public OtherRankConfigVO testUidDay(String activityId, int cmd, String uid, int addDays) {
        if (ServerConfig.isProduct()) {
            throw new CommonH5Exception(ActivityHttpCode.AUTH_ERROR);
        }
        OtherRankConfigVO otherRankConfigVO = new OtherRankConfigVO();
        if (cmd == 1) {
            // 设置偏移天数
            activityCommonRedis.setCommonHashData(getHashTestDayKey(activityId), uid, String.valueOf(addDays));
            otherRankConfigVO.setScore(addDays);
        } else if (cmd == 2) {
            // 查询偏移天数
            int add = activityCommonRedis.getCommonHashValue(getHashTestDayKey(activityId), uid);
            otherRankConfigVO.setScore(add);
        }
        return otherRankConfigVO;
    }

    private String getDay(String uid) {
        return getDay(uid, true);
    }

    private String getDay(String uid, boolean baseIsToday) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isNotProduct() || activityData.getAcNameEn().startsWith("test")) {
            return getTestDays(uid, baseIsToday);
        }
        return baseIsToday ? DateHelper.ARABIAN.formatDateInDay()
                : DateHelper.ARABIAN.getYesterdayStr(new Date());
    }


    private String getTestDays(String uid, boolean baseIsToday) {
        int addDays = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
        logger.info("test add uid:{} days:{}", uid, addDays);
        addDays = baseIsToday ? addDays : addDays - 1;
        return todayMinusDays(addDays);
    }

    /**
     * 书法家活动
     *
     * @param activityStage 1 上传作品  2 完成评论任务
     */
    private void doCalligrapherEvent(String activityId, String uid, int activityStage) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_stage(activityStage);
        eventReport.track(new EventDTO(event));
    }


    /**
     * @param days
     * @return
     */
    private String todayMinusDays(int days) {
        days = -days; // -1为明天  1为昨天
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }

    private int getPositionByDay(int startTime, int endTime) {
        if (startTime >= endTime) {
            return 0;
        }
        String before = DateHelper.ARABIAN.formatDateInDay
                (new Date(startTime * 1000L));
        String after = DateHelper.ARABIAN.formatDateInDay
                (new Date(endTime * 1000L));
        int d = (int) (DateSupport.calculateDaysBetween(before, after));
        return d;
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:arabic_painter:uid:" + uid;
    }

    private String getLocalPublishUserKey(String uid) {
        return "lock:arabic_painter:publish:uid:" + uid;
    }

    private String getHashTotalKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":arabic_painter:total";
    }

    private String getHashTestDayKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":test:uid:day";
    }
}
