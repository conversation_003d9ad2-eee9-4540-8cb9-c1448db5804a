package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.vo.ActiveBadgeVO;
import com.quhong.data.vo.ForYouListV2VO;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户活跃等级勋章
 */
@Service
public class ActiveBadgeService extends OtherActivityService implements TaskMsgHandler {

    private static final Logger logger = LoggerFactory.getLogger(ActiveBadgeService.class);
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    public static final String ACTIVITY_NAME = "Active Badge Activity";
    public static final String ACTIVITY_ID = "6849240b052459ae91754af5";
    public static final String ACTIVE_BADGE_DAY = "activeBadgeDay";  // 活跃勋章天数
    public static final String ACTIVE_DATE_KEY = "activeDate";  // 活跃日期key

    // 每日key
    public static final String ACTIVE_DAILY_TASK_DAY = "activeTaskDay";  // 需要任务完成天数
    public static final String ACTIVE_DAILY_TASK_ON_MIC_TIME_KEY = "activeTaskOnMicTime";  // 需要任务完成的麦上时长
    public static final String ACTIVE_ON_MIC_TIME_KEY = "activeOnMicTime";  // 麦上时长key
    public static final String DAILY_STATUS_KEY = "status";  // 每日任务状态 0未完成 1已完成 2:连续中断检查
    private static final List<ActiveBadgeVO.BadgeInfo> BADGE_INFO_LIST = new ArrayList<>();

    static {
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(3, 3315, "https://cdn3.qmovies.tv/badge/op_1748599083_06_600.png", 15, 0, "النشاط لمدة 3 يوما"));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(7, 3316, "https://cdn3.qmovies.tv/badge/op_1748599164_05_600.png", 20, 0, "النشاط لمدة 7 يوما"));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(15, 3317, "https://cdn3.qmovies.tv/badge/op_1748599198_04_600.png", 25, 0, "النشاط لمدة 15 يوما"));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(30, 3318, "https://cdn3.qmovies.tv/badge/op_1748599233_03_600.png", 30, 0, "النشاط لمدة 30 يوما"));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(60, 3319, "https://cdn3.qmovies.tv/badge/op_1748599266_02_600.png", 30, 0, "النشاط لمدة 60 يوما"));
        BADGE_INFO_LIST.add(new ActiveBadgeVO.BadgeInfo(90, 3320, "https://cdn3.qmovies.tv/badge/op_1748599293_01_600.png", 30, 0, "النشاط لمدة 90 يوما"));
    }

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;

    /**
     * 总状态信息
     * @param uid
     * @return
     */
    private String getHashActiveBadgeKey(String uid) {
        return String.format("activeBadge:%s:%s", ACTIVITY_ID, uid);
    }

    /**
     * 每日任务状态信息
     */
    private String getDailyHashActiveBadgeKey(String uid, String dateStr) {
        return String.format("dailyActiveBadge:%s:%s:%s", ACTIVITY_ID, uid, dateStr);
    }

    private String getCurrentDay() {
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue("activeBadgeDate");
    }


    public ActiveBadgeVO getActiveBadgeInfo(String uid) {
        ActiveBadgeVO vo = new ActiveBadgeVO();
        reviseActiveDay(uid);
        Map<String, String> userActiveInfoMap = activityCommonRedis.getCommonHashAllMapStr(getHashActiveBadgeKey(uid));
        int activeDay = Integer.parseInt(userActiveInfoMap.getOrDefault(ACTIVE_BADGE_DAY, "0"));
        vo.setActiveDay(activeDay);

        String currentDay = getCurrentDay();
        String dailyHashActiveBadgeKey = getDailyHashActiveBadgeKey(uid, currentDay);
        Map<String, String> userDailyActiveInfoMap = activityCommonRedis.getCommonHashAllMapStr(dailyHashActiveBadgeKey);

        int nextBadgeDay = Integer.parseInt(userDailyActiveInfoMap.getOrDefault(ACTIVE_DAILY_TASK_DAY, "0"));
        vo.setNextBadgeDay(nextBadgeDay);
        vo.setNeedActiveDay(Math.max(nextBadgeDay - activeDay, 0));

        // 获取勋章信息
        Map<Integer, ActiveBadgeVO.BadgeInfo> badgeInfoMap = BADGE_INFO_LIST.stream().collect(Collectors.toMap(ActiveBadgeVO.BadgeInfo::getNeedActiveDay, Function.identity()));
        ActiveBadgeVO.BadgeInfo badgeInfo = badgeInfoMap.get(nextBadgeDay);
        vo.setNeedOnMicMinute(badgeInfo.getNeedOnMicMinute());
        vo.setOnMicMinute(Integer.parseInt(userDailyActiveInfoMap.getOrDefault(ACTIVE_ON_MIC_TIME_KEY, "0")));

        List<ActiveBadgeVO.BadgeInfo> activebadgeList = new ArrayList<>();
        for (ActiveBadgeVO.BadgeInfo badgeConfig : BADGE_INFO_LIST) {
            ActiveBadgeVO.BadgeInfo badgeInfoVO = new ActiveBadgeVO.BadgeInfo();
            BeanUtils.copyProperties(badgeConfig, badgeInfoVO);
            int badgeGetTime = Integer.parseInt(userActiveInfoMap.getOrDefault(String.valueOf(badgeConfig.getBadgeId()), "0"));
            badgeInfoVO.setBadgeGetTime(badgeGetTime);
            activebadgeList.add(badgeInfoVO);
        }
        vo.setActivebadgeList(activebadgeList);

        // 设置同国家房间
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setSameCountryRoomId("");
        List<ForYouListV2VO>  countryList = activityCommonRedis.getCountryList(ActorUtils.getCountryCode(actorDao.getActorDataFromCache(uid).getCountry()));
        for (ForYouListV2VO item : countryList) {
            ActorData hostData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(item.getRoomId()));
            if (actorData.getFb_gender() != hostData.getFb_gender()) {
                vo.setSameCountryRoomId(item.getRoomId());
                break;
            }
        }
        return vo;
    }

    /**
     * 修正活跃天数
     */
    private void reviseActiveDay(String uid) {
        synchronized (stringPool.intern("reviseBadge" + uid)) {

            String currentDay = getCurrentDay();
            String hashActiveBadgeKey = getHashActiveBadgeKey(uid);

            // 查询昨天任务是否完成
            String yesterdayStr = DateHelper.ARABIAN.getYesterdayStr(DateHelper.ARABIAN.parseDate(currentDay));
            String dailyHashActiveBadgeKey = getDailyHashActiveBadgeKey(uid, yesterdayStr);
            Map<String, String> userDailyActiveInfoMap = activityCommonRedis.getCommonHashAllMapStr(dailyHashActiveBadgeKey);
            int status = Integer.parseInt(userDailyActiveInfoMap.getOrDefault(DAILY_STATUS_KEY, "0"));
            if (status <= 0){
                activityCommonRedis.setCommonHashNum(hashActiveBadgeKey, ACTIVE_BADGE_DAY, 0);
                activityCommonRedis.setCommonHashNum(dailyHashActiveBadgeKey, DAILY_STATUS_KEY, 2);
            }

            Map<String, String> userActiveInfoMap = activityCommonRedis.getCommonHashAllMapStr(hashActiveBadgeKey);
            int activeDay = Integer.parseInt(userActiveInfoMap.getOrDefault(ACTIVE_BADGE_DAY, "0"));
            String activeDate = userActiveInfoMap.get(ACTIVE_DATE_KEY);

            // 判断是否是同一天
            if (ObjectUtils.isEmpty(activeDate) || !currentDay.equals(activeDate)) {
                activityCommonRedis.setCommonHashData(hashActiveBadgeKey, ACTIVE_DATE_KEY, currentDay);
                // 今天需要完成的任务天数
                String dailyCurrentHashActiveBadgeKey = getDailyHashActiveBadgeKey(uid, currentDay);
                ActiveBadgeVO.BadgeInfo badgeInfo = getCurrentBadgeDayByActiveDay(activeDay);
                if (badgeInfo != null){
                    activityCommonRedis.setCommonHashNum(dailyCurrentHashActiveBadgeKey, ACTIVE_DAILY_TASK_DAY, badgeInfo.getNeedActiveDay());
                    activityCommonRedis.setCommonHashNum(dailyCurrentHashActiveBadgeKey, ACTIVE_DAILY_TASK_ON_MIC_TIME_KEY, badgeInfo.getNeedOnMicMinute());
                }
            }

            // 已经累积活跃90天，则次日从第一天开始重新累积天数
            int lastActiveDay = BADGE_INFO_LIST.get(BADGE_INFO_LIST.size() - 1).getNeedActiveDay();
            if (activeDay >= lastActiveDay && !currentDay.equals(activeDate)) {
                activityCommonRedis.setCommonHashNum(hashActiveBadgeKey, ACTIVE_BADGE_DAY, 0);
            }

        }
    }


    public ActiveBadgeVO.BadgeInfo getCurrentBadgeDayByActiveDay(int activeDay) {
        // 获取勋章活跃天数
        for (ActiveBadgeVO.BadgeInfo badgeInfo : BADGE_INFO_LIST) {
            if (activeDay < badgeInfo.getNeedActiveDay()) {
                return badgeInfo;
            }
        }
        return BADGE_INFO_LIST.get(0);
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {

        String item = data.getItem();
        if (!CommonMqTaskConstant.ON_MIC_TIME.equals(item)) {
            return;
        }

        String uid = data.getUid();
        // if (ServerConfig.isProduct() && !whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID)) {
        //     // 灰度测试
        //     return;
        // }
        synchronized (stringPool.intern("activeBadge" + uid)) {
            // 修正活跃天数
            reviseActiveDay(uid);

            String currentDay = getCurrentDay();
            String hashActiveBadgeKey = getHashActiveBadgeKey(uid);
            String dailyHashActiveBadgeKey = getDailyHashActiveBadgeKey(uid, currentDay);
            Map<String, String> userDailyActiveInfoMap = activityCommonRedis.getCommonHashAllMapStr(dailyHashActiveBadgeKey);

            // 当天上麦分钟数
            int activeOnMicTime = Integer.parseInt(userDailyActiveInfoMap.getOrDefault(ACTIVE_ON_MIC_TIME_KEY, "0"));

            // 获取下一等级天数
            int nextBadgeDay = Integer.parseInt(userDailyActiveInfoMap.getOrDefault(ACTIVE_DAILY_TASK_DAY, "0"));
            Map<Integer, ActiveBadgeVO.BadgeInfo> badgeInfoMap = BADGE_INFO_LIST.stream().collect(Collectors.toMap(ActiveBadgeVO.BadgeInfo::getNeedActiveDay, Function.identity()));
            ActiveBadgeVO.BadgeInfo badgeInfo = badgeInfoMap.get(nextBadgeDay);
            if (badgeInfo == null){
                return;
            }

            // 需要的上麦分钟数
            int needOnMicMinute = badgeInfo.getNeedOnMicMinute();
            if (activeOnMicTime >= needOnMicMinute) {
                return;
            }

            int afterMicTime = activityCommonRedis.incCommonHashNum(dailyHashActiveBadgeKey, ACTIVE_ON_MIC_TIME_KEY, data.getValue());
            if (afterMicTime < needOnMicMinute) {
                return;
            }

            activityCommonRedis.setCommonHashNum(dailyHashActiveBadgeKey, DAILY_STATUS_KEY, 1);
            int afterActiveDay = activityCommonRedis.incCommonHashNum(hashActiveBadgeKey, ACTIVE_BADGE_DAY, 1);
            if (afterActiveDay == nextBadgeDay){
                // 发放勋章资源
                ResourceKeyConfigData.ResourceMeta resourceMeta = new ResourceKeyConfigData.ResourceMeta();
                resourceMeta.setMetaId(new ObjectId().toString());
                resourceMeta.setResourceType(BaseDataResourcesConstant.TYPE_BADGE);
                resourceMeta.setResourceId(badgeInfo.getBadgeId());
                resourceMeta.setResourceNumber(1);
                resourceMeta.setResourceTime(30); // 永久
                resourceKeyHandlerService.sendOneResourceData(uid, resourceMeta, 905, ACTIVITY_NAME, ACTIVITY_NAME, ACTIVITY_NAME, "", "", 1);
                activityCommonRedis.setCommonHashNum(hashActiveBadgeKey, String.valueOf(badgeInfo.getBadgeId()), DateHelper.getNowSeconds());
            }
        }
    }
}
