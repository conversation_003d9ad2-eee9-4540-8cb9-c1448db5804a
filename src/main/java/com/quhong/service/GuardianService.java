package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.DrawPrizeRecordEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.enums.ApiResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.FamilyMemberDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.redis.GuardianRedis;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 守护摩天轮
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Service
public class GuardianService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(GuardianService.class);

    private final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String DRAW_NUM = "draw_num"; // 抽奖次数
    private static final String EXTRA_POINTS = "extra_points"; // 多余积分
    private static final String DRAW_POOL_KEY = "GuardNoPayDraw";   // 抽奖池
    private static final String DRAW_POOL_PAY_KEY = "GuardPayDraw";   // 付费用户抽奖池

    private static final String GUARD_RANKING = "guard_ranking_%s"; // 守护榜
    private static final String ANCHOR_RANKING = "anchor_ranking";
    private static final String DRAW_RANKING = "draw_ranking"; // 抽奖榜

    private static final String EXCHANGE_KEY = "HI2025GuardExchangeShop";

    private static final String RING_NUM = "ring"; // 戒指数
    private static final String ENVELOPE_NUM = "envelope"; // 信封数

    private static final int DRAW_RECORD = 0; // 抽奖记录
    private static final int EXCHANGE_RECORD = 1; // 兑换记录
    private static final int ENVELOPE_RECORD = 2; // 信封记录

    private static final int BUY_DRAW_NUM_PRICE = 400;

    private static final int SHARE_ID = ServerConfig.isProduct() ? 14 : 13;
    private static final int GUARD_GIFT_ID = ServerConfig.isProduct() ? 1077 : 899;


    private static final int LIMIT_INIT_POOL = 100;
    private static final Integer INIT_POOL_SIZE = 1000;
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final String ACTIVITY_NAME = "FerrisWheel";

    @Resource
    private GuardianRedis guardianRedis;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;

    /**
     * 获取活动信息
     */
    public GuardianActivityVO getInfo(String activityId, String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        OtherRankingActivityData activityData = getActivityData(activityId);
        if (activityData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        GuardianActivityVO vo = new GuardianActivityVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        Map<String, Long> userDataMap = guardianRedis.getUserData(activityId, uid);
        vo.setDrawNum(userDataMap.getOrDefault(DRAW_NUM, 0L).intValue());
        vo.setRangNum(userDataMap.getOrDefault(RING_NUM, 0L).intValue());
        vo.setEnvelopeNum(userDataMap.getOrDefault(ENVELOPE_NUM, 0L).intValue());
        // 抽奖榜
        Map<String, Long> drawRankingMap = guardianRedis.getRankingMap(activityId, DRAW_RANKING, 10);
        vo.setDrawRankingList(getRankingList(activityId, drawRankingMap, false));
        vo.setMyDrawRank(getMyRanking(activityId, uid, DRAW_RANKING, actorData, false));
        // 守护榜
        Map<String, Long> anchorRankingMap = guardianRedis.getRankingMap(activityId, ANCHOR_RANKING, 10);
        vo.setGuardRankingList(getRankingList(activityId, anchorRankingMap, true));
        vo.setMyGuardRank(getMyRanking(activityId, uid, ANCHOR_RANKING, actorData, true));
        vo.setRewardNotifyList(getRewardNotifyList(activityId));
        return vo;
    }

    /**
     * 抽奖
     */
    public GuardianDrawVO draw(String activityId, String uid, int num) {
        if (num != 1 && num != 10 && num != 50) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList;
        synchronized (stringPool.intern(uid)) {
            long curBallNum = guardianRedis.getUserData(activityId, uid, DRAW_NUM);
            if (curBallNum - num < 0) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_CHANCES_TO_DRAW);
            }
            // 扣减用户抽奖次数
            guardianRedis.incUserData(activityId, uid, DRAW_NUM, -num);
            doReportItemsChangeEvent(activityId, uid, 2, "0", num, 3, "");
            // 增加抽奖排行榜分数
            guardianRedis.incrRankingScore(activityId, DRAW_RANKING, uid, num);
            // 抽奖
            resourceMetaList = drawPrize(activityId, uid, num);
            // 特殊奖励
            Map<String, Long> specialResMap = resourceMetaList.stream().filter(k -> k.getResourceType() == -1)
                    .collect(Collectors.groupingBy(ResourceKeyConfigData.ResourceMeta::getMetaId, Collectors.counting()));
            for (Map.Entry<String, Long> entry : specialResMap.entrySet()) {
                guardianRedis.incUserData(activityId, uid, entry.getKey(), entry.getValue().intValue());
                doReportItemsChangeEvent(activityId, uid, 1, entry.getKey().equals(RING_NUM) ? "1" : "2", entry.getValue().intValue(), 1, "");
            }
        }
        asyncSendRewardAndSaveRecord(activityId, uid, num, ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()), resourceMetaList);
        GuardianDrawVO vo = new GuardianDrawVO();
        List<GuardianRecordVO> list = new ArrayList<>();
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            GuardianRecordVO drawRecord = new GuardianRecordVO();
            drawRecord.setKey(resourceMeta.getMetaId());
            drawRecord.setRewardType(String.valueOf(resourceMeta.getResourceType()));
            ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceMeta.getResourceType());
            if (typeEnum != null && typeEnum != ResTypeEnum.BAG_GIFT) {
                drawRecord.setNameEn(typeEnum.formatTag(SLangType.ENGLISH, resourceMeta.getResourceTime()));
                drawRecord.setNameAr(typeEnum.formatTag(SLangType.ARABIC, resourceMeta.getResourceTime()));
            } else {
                drawRecord.setNameEn(ResTypeEnum.DIAMONDS.formatTag(SLangType.ENGLISH, resourceMeta.getResourceNumber()));
                drawRecord.setNameAr(ResTypeEnum.DIAMONDS.formatTag(SLangType.ARABIC, resourceMeta.getResourceNumber()));
            }
            drawRecord.setIconEn(resourceMeta.getResourceIcon());
            drawRecord.setRewardTime(resourceMeta.getResourceTime());
            drawRecord.setRewardNum(resourceMeta.getResourceNumber());
            drawRecord.setRewardPrice(resourceMeta.getResourcePrice());
            drawRecord.setCtime(DateHelper.getNowSeconds());
            guardianRedis.saveRecord(activityId, DRAW_RECORD, uid, JSONObject.toJSONString(drawRecord));
            list.add(drawRecord);
        }
        vo.setRewardList(getShowPrizeList(list));
        Map<String, Long> userDataMap = guardianRedis.getUserData(activityId, uid);
        vo.setDrawNum(userDataMap.getOrDefault(DRAW_NUM, 0L).intValue());
        vo.setRangNum(userDataMap.getOrDefault(RING_NUM, 0L).intValue());
        vo.setEnvelopeNum(userDataMap.getOrDefault(ENVELOPE_NUM, 0L).intValue());
        // 抽奖榜
        Map<String, Long> drawRankingMap = guardianRedis.getRankingMap(activityId, DRAW_RANKING, 10);
        vo.setDrawRankingList(getRankingList(activityId, drawRankingMap, false));
        vo.setMyDrawRank(getMyRanking(activityId, uid, DRAW_RANKING, actorData, false));
        // 守护榜
        Map<String, Long> anchorRankingMap = guardianRedis.getRankingMap(activityId, ANCHOR_RANKING, 10);
        vo.setGuardRankingList(getRankingList(activityId, anchorRankingMap, true));
        vo.setMyGuardRank(getMyRanking(activityId, uid, ANCHOR_RANKING, actorData, true));
        return vo;
    }

    /**
     * 购买抽奖次数
     */
    public HttpResult<GuardianActivityVO> buyDrawNum(String activityId, String uid, int num, int slang) {
        if (num != 1 && num != 10 && num != 50) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        int costBeans = num * BUY_DRAW_NUM_PRICE;
        synchronized (stringPool.intern(uid)) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(906);
            moneyDetailReq.setChanged(-costBeans);
            moneyDetailReq.setTitle(ACTIVITY_NAME);
            moneyDetailReq.setDesc(ACTIVITY_NAME);
            ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
            if (!result.isOk()) {
                if (1 == result.getCode().getCode()) {
                    throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_DIAMOND);
                }
                logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
                throw new CommonH5Exception(HttpCode.SERVER_ERROR);
            }
            // 增加抽奖次数
            guardianRedis.incUserData(activityId, uid, DRAW_NUM, num);
            doReportItemsChangeEvent(activityId, uid, 1, "0", num, 2, "");
        }
        GuardianActivityVO vo = new GuardianActivityVO();
        Map<String, Long> userDataMap = guardianRedis.getUserData(activityId, uid);
        vo.setDrawNum(userDataMap.getOrDefault(DRAW_NUM, 0L).intValue());
        vo.setRangNum(userDataMap.getOrDefault(RING_NUM, 0L).intValue());
        vo.setEnvelopeNum(userDataMap.getOrDefault(ENVELOPE_NUM, 0L).intValue());
        String msg = SLangType.ARABIC == slang ? "لقد قمت بالشراء %d مرات، اذهب إلى السحب المحظوظ" : "You have purchased %d time, go to the lucky draw";
        return new HttpResult<>(0, String.format(msg, num), vo);
    }

    /**
     * 兑换奖励
     */
    public GuardianActivityVO exchange(String activityId, String uid, String rewardKey) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(EXCHANGE_KEY);
        if (resourceKeyConfigData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "Not Find Exchange Item");
        }
        Map<String, ResourceKeyConfigData.ResourceMeta> metaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
        ResourceKeyConfigData.ResourceMeta resourceMeta = metaMap.get(rewardKey);
        if (resourceMeta == null) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        int costRingNum;
        synchronized (stringPool.intern(uid)) {
            long curRingNum = guardianRedis.getUserData(activityId, uid, RING_NUM);
            costRingNum = Integer.parseInt(resourceMeta.getRateNumber());
            if (curRingNum - costRingNum < 0) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_RING);
            }
            // 扣减用户戒指的数量
            guardianRedis.incUserData(activityId, uid, RING_NUM, -costRingNum);
            doReportItemsChangeEvent(activityId, uid, 2, "1", costRingNum, 2, "");
        }
        // 下发兑换商品
        resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, "FerrisWheel-exchange", "FerrisWheel-exchange", "FerrisWheel-exchange", "", "", 1);
        GuardianRecordVO record = new GuardianRecordVO();
        record.setCostRangNum(costRingNum);
        record.setRewardType(String.valueOf(resourceMeta.getResourceType()));
        ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceMeta.getResourceType());
        if (typeEnum != null && typeEnum != ResTypeEnum.BAG_GIFT) {
            record.setNameEn(typeEnum.formatTag(SLangType.ENGLISH, resourceMeta.getResourceTime()));
            record.setNameAr(typeEnum.formatTag(SLangType.ARABIC, resourceMeta.getResourceTime()));
        } else {
            record.setNameEn(ResTypeEnum.DIAMONDS.formatTag(SLangType.ENGLISH, resourceMeta.getResourceNumber()));
            record.setNameAr(ResTypeEnum.DIAMONDS.formatTag(SLangType.ARABIC, resourceMeta.getResourceNumber()));
        }
        record.setIconEn(resourceMeta.getResourceIcon());
        record.setRewardTime(resourceMeta.getResourceTime());
        record.setRewardNum(resourceMeta.getResourceNumber());
        record.setRewardPrice(resourceMeta.getResourcePrice());
        record.setCtime(DateHelper.getNowSeconds());
        guardianRedis.saveRecord(activityId, EXCHANGE_RECORD, uid, JSONObject.toJSONString(record));
        GuardianActivityVO vo = new GuardianActivityVO();
        Map<String, Long> userDataMap = guardianRedis.getUserData(activityId, uid);
        vo.setDrawNum(userDataMap.getOrDefault(DRAW_NUM, 0L).intValue());
        vo.setRangNum(userDataMap.getOrDefault(RING_NUM, 0L).intValue());
        vo.setEnvelopeNum(userDataMap.getOrDefault(ENVELOPE_NUM, 0L).intValue());
        return vo;
    }

    /**
     * 赠送信封
     */
    public HttpResult<Object> sendEnvelope(String activityId, String uid, String rid, int slang) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        ActorData receiver = actorDao.getActorByStrRid(rid);
        if (receiver == null) {
            throw new CommonH5Exception(ActivityHttpCode.INCORRECT_INPUT_ID);
        }
        String aid = receiver.getUid();
        synchronized (stringPool.intern(uid)) {
            long envelopeNum = guardianRedis.getUserData(activityId, uid, ENVELOPE_NUM);
            if (envelopeNum <= 0) {
                throw new CommonH5Exception(ActivityHttpCode.NOT_ENOUGH_ENVELOPE);
            }
            guardianRedis.incUserData(activityId, uid, ENVELOPE_NUM, -1);
            doReportItemsChangeEvent(activityId, uid, 2, "2", 1, 2, receiver.getUid());
        }
        // 发送奖励
        resourceKeyHandlerService.sendResourceData(aid, "HI2025GuardEnvelopeReward", "FerrisWheel-guard gift", "FerrisWheel-guard gift");
        boolean sendSelf = uid.equals(aid);
        String msg = SLangType.ARABIC == slang ? "أتمنى لك سنة جديدة سعيدة~" : "Wish you a happy new year~";
        // 发送私信
        if (!sendSelf) {
            sendActivityShareMsg(uid, aid, SHARE_ID);
            msg = SLangType.ARABIC == slang ? "تم إعطاؤه لصديقك بالفعل" : "Already given to your friend";
        }
        // 保存记录
        GuardianRecordVO record = new GuardianRecordVO();
        record.setSender(new GuardianRecordVO.UserInfo(actorData));
        record.setReceiver(new GuardianRecordVO.UserInfo(receiver));
        record.setCtime(DateHelper.getNowSeconds());
        guardianRedis.saveRecord(activityId, ENVELOPE_RECORD, uid, JSONObject.toJSONString(record));
        if (!sendSelf) {
            guardianRedis.saveRecord(activityId, ENVELOPE_RECORD, receiver.getUid(), JSONObject.toJSONString(record));
        }
        return new HttpResult<>(0, msg, null);
    }

    /**
     * 记录
     */
    public PageVO<GuardianRecordVO> record(String activityId, String uid, int type, int page) {
        List<GuardianRecordVO> list = new ArrayList<>();
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        List<String> drawRecordList = guardianRedis.getRecordList(activityId, type, uid, start, end);
        if (!CollectionUtils.isEmpty(drawRecordList)) {
            for (String strDrawRecord : drawRecordList) {
                GuardianRecordVO vo = JSONObject.parseObject(strDrawRecord, GuardianRecordVO.class);
                vo.setUid(uid);
                list.add(vo);
            }
        }
        return new PageVO<>(list, list.size() >= RECORD_PAGE_SIZE ? page + 1 + "" : "");
    }

    private List<OtherRankingListVO> getRankingList(String activityId, Map<String, Long> rankingMap, boolean showGuardian) {
        List<OtherRankingListVO> list = new ArrayList<>();
        int rank = 1;
        for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            if (null == rankActor) {
                logger.error("can not find actor data. aid={}", entry.getKey());
                continue;
            }
            OtherRankingListVO vo = new OtherRankingListVO();
            vo.setUid(aid);
            vo.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            vo.setName(rankActor.getName());
            vo.setScore(entry.getValue().intValue());
            vo.setRank(rank);
            vo.setBadgeList(badgeDao.getBadgeList(aid));
            vo.setVipLevel(vipInfoDao.getIntVipLevelFromCache(aid));
            if (showGuardian) {
                vo.setSupportUserList(getSupportUserList(activityId, aid, rankActor));
            }
            list.add(vo);
            rank += 1;
        }
        return list;
    }

    private OtherMyRankVO getMyRanking(String activityId, String aid, String rankType, ActorData actorData, boolean showGuardian) {
        long score = guardianRedis.getRankingScore(activityId, rankType, aid);
        int rank = guardianRedis.getRankingRank(activityId, rankType, aid);
        OtherMyRankVO vo = new OtherMyRankVO();
        vo.setScore((int) score);
        vo.setRank(rank == 0 || rank > 99 ? -1 : rank);
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setBadgeList(badgeDao.getBadgeList(aid));
        vo.setVipLevel(vipInfoDao.getIntVipLevelFromCache(aid));
        vo.setUid(aid);
        if (showGuardian) {
            vo.setSupportUserList(getSupportUserList(activityId, aid, actorData));
        }
        return vo;
    }

    private List<OtherSupportUserVO> getSupportUserList(String activityId, String aid, ActorData actorData) {
        List<OtherSupportUserVO> supportUserList = new ArrayList<>();
        Map<String, Long> guardRankingMap = guardianRedis.getRankingMap(activityId, String.format(GUARD_RANKING, aid), 0);
        int aidSex = actorData.getFb_gender();
        for (Map.Entry<String, Long> guardRankingEntry : guardRankingMap.entrySet()) {
            ActorData guardian = actorDao.getActorDataFromCache(guardRankingEntry.getKey());
            if (null == guardian) {
                logger.error("can not find actor data. aid={}", guardRankingEntry.getKey());
                continue;
            }
            int guardSex = guardian.getFb_gender();
            if(aidSex == guardSex){
                continue;
            }
            OtherSupportUserVO supportUserVO = new OtherSupportUserVO();
            supportUserVO.setHead(ImageUrlGenerator.generateRoomUserUrl(guardian.getHead()));
            supportUserVO.setName(guardian.getName());
            supportUserVO.setUid(guardRankingEntry.getKey());
            supportUserVO.setScore(guardRankingEntry.getValue().intValue());
            supportUserList.add(supportUserVO);
            break;
        }
        return supportUserList;
    }

    /**
     * 处理礼物消息
     */
    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String uid = giftData.getFrom_uid();
        int sendBeans = giftData.getNumber() * giftData.getPrice() * giftData.getAid_list().size();
        synchronized (stringPool.intern(uid)) {
            Map<String, Long> userMapData = guardianRedis.getUserData(activityId, uid);
            long extraPoints = userMapData.getOrDefault(EXTRA_POINTS, 0L);
            long totalBeans = extraPoints + sendBeans;
            int incNum = (int)(totalBeans / 100);
            int modNum = (int)(totalBeans % 100);
            if (incNum != 0) {
                // 获取抽奖次数
                guardianRedis.incUserData(activityId, uid, DRAW_NUM, incNum);
                doReportItemsChangeEvent(activityId, uid, 1, "0", incNum, 1, "");
            }
            guardianRedis.setUserData(activityId, uid, EXTRA_POINTS, modNum);
        }
        // 统计守护榜单
        if (GUARD_GIFT_ID == giftData.getGid()) {
            ActorData sendActorData = actorDao.getActorDataFromCache(uid);
            int sendSex = sendActorData.getFb_gender();
            for (String aid : giftData.getAid_list()) {
                ActorData aidActorData = actorDao.getActorDataFromCache(aid);
                int aidSex = aidActorData.getFb_gender();
                if (sendSex != aidSex){
                    guardianRedis.incrRankingScore(activityId, ANCHOR_RANKING, aid, giftData.getNumber());
                    guardianRedis.incrRankingScore(activityId, String.format(GUARD_RANKING, aid), uid, giftData.getNumber());
                    // 发送守护礼物奖励
                    for (int i = 0; i < giftData.getNumber(); i++) {
                        resourceKeyHandlerService.sendResourceData(uid, "HI2025GuardGiftReward", "FerrisWheel-guard gift", "FerrisWheel-guard gift");
                    }
                }
            }
        }
    }

    private List<RewardNotifyVO> getRewardNotifyList(String activityId) {
        List<RewardNotifyVO> list = new ArrayList<>();
        List<String> strValueList = guardianRedis.getRewardNotifyList(activityId);
        if (!CollectionUtils.isEmpty(strValueList)) {
            for (String strValue : strValueList) {
                RewardNotifyVO rewardNotify = JSONObject.parseObject(strValue, RewardNotifyVO.class);
                list.add(rewardNotify);
            }
        }
        return list;
    }

    private void asyncSendRewardAndSaveRecord(String activityId, String uid, int num, String head, List<ResourceKeyConfigData.ResourceMeta> resourceMetaList) {
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetaList) {
            resourceKeyHandlerService.sendOneResourceData(uid, resourceMeta, 905, "FerrisWheel-draw", "FerrisWheel-draw", "FerrisWheel-draw", "", "", 1);
            ResTypeEnum typeEnum = ResTypeEnum.getByType(resourceMeta.getResourceType());
            RewardNotifyVO rewardNotify;
            if (typeEnum != null) {
                rewardNotify = new RewardNotifyVO(head, resourceMeta.getResourceIcon(), typeEnum.getNameEn(), typeEnum.getNameAr(), resourceMeta.getResourceNumber());
            } else {
                rewardNotify = new RewardNotifyVO(head, resourceMeta.getResourceIcon(), ResTypeEnum.DIAMONDS.getNameEn(), ResTypeEnum.DIAMONDS.getNameAr(), resourceMeta.getResourceTime());
            }
            guardianRedis.addRewardNotify(activityId, JSON.toJSONString(rewardNotify));
        }
        doDrawPrizeRecordEvent(uid, num, resourceMetaList);
    }

    private List<GuardianRecordVO> getShowPrizeList(List<GuardianRecordVO> prizeConfigList) {
        Map<String, GuardianRecordVO> map = new HashMap<>(prizeConfigList.size());
        for (GuardianRecordVO prizeConfig : prizeConfigList) {
            if (map.containsKey(prizeConfig.getKey())) {
                map.get(prizeConfig.getKey()).setRewardNum(map.get(prizeConfig.getKey()).getRewardNum() + 1);
            } else {
                GuardianRecordVO newPrizeConfig = new GuardianRecordVO();
                BeanUtils.copyProperties(prizeConfig, newPrizeConfig);
                newPrizeConfig.setRewardNum(1);
                map.put(newPrizeConfig.getKey(), newPrizeConfig);
            }
        }
        return new ArrayList<>(map.values());
    }

    private List<ResourceKeyConfigData.ResourceMeta> drawPrize(String activityId, String uid, int num) {
        List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = new ArrayList<>();
        int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
        String drawPoolKey = rechargeMoney >= 5 ? DRAW_POOL_PAY_KEY : DRAW_POOL_KEY;

        ResourceKeyConfigData resourceKeyConfigData = resourceKeyHandlerService.getConfigData(drawPoolKey);
        if (resourceKeyConfigData != null) {
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
            initDrawPrizePool(activityId, resourceMetaMap, drawPoolKey);
            List<String> metaIdList = guardianRedis.popRewardFromPool(activityId, drawPoolKey, num);
            if (!CollectionUtils.isEmpty(metaIdList)) {
                metaIdList.forEach(k -> {
                    ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(k);
                    if (resourceMeta != null) {
                        resourceMetaList.add(resourceMeta);
                    }
                });
            }
        }
        return resourceMetaList;
    }

    private void initDrawPrizePool(String activityId, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String drawPoolKey) {
        int poolSize = guardianRedis.getPoolSize(activityId, drawPoolKey);
        if (poolSize <= 0) {
            expandPrizePool(activityId, resourceMetaMap.values(), drawPoolKey);
        } else if (poolSize <= LIMIT_INIT_POOL) {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    expandPrizePool(activityId, resourceMetaMap.values(), drawPoolKey);
                }
            });
        }
    }

    private void expandPrizePool(String activityId, Collection<ResourceKeyConfigData.ResourceMeta> resourceMetas, String drawPoolKey) {
        List<String> prizePoolList = new ArrayList<>(INIT_POOL_SIZE);
        for (ResourceKeyConfigData.ResourceMeta resourceMeta : resourceMetas) {
            // 添加元素到奖池
            int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
            prizePoolList.addAll(Collections.nCopies(rateNumber, resourceMeta.getMetaId()));
        }
        // 打乱奖池顺序
        Collections.shuffle(prizePoolList);
        guardianRedis.pushRewardInPool(activityId, drawPoolKey, prizePoolList);
    }

    /**
     * 榜单奖励
     */
    public void sendRankingReward(String activityId) {
        sendDrawRankingReward(activityId);
        sendReceiveRankingReward(activityId);
    }

    private void sendDrawRankingReward(String activityId) {
        try {
            Map<String, Long> rankingMap = guardianRedis.getRankingMap(activityId, DRAW_RANKING, 10);
            int rank = 1;
            for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
                String rewardKey = rank <= 5 ? String.format("HI2025DrawTop%s", rank) : "HI2025DrawTop6-10";
                String aid = entry.getKey();
                logger.info("sendRankingReward. aid={} rankType={} score={} rank={} rewardKey={}", aid, DRAW_RANKING, entry.getValue(), rank, rewardKey);
                resourceKeyHandlerService.sendResourceData(aid, rewardKey, "FerrisWheel-draw ranking", "FerrisWheel-draw ranking");
                rank++;
            }
        } catch (Exception e) {
            logger.error("sendDrawRankingReward error. activityId={} rankType={} {}", activityId, DRAW_RANKING, e.getMessage(), e);
        }
    }

    private void sendReceiveRankingReward(String activityId) {
        try {
            String title = "FerrisWheel-guard ranking";
            Map<String, Long> rankingMap = guardianRedis.getRankingMap(activityId, ANCHOR_RANKING, 10);
            int rank = 1;
            for (Map.Entry<String, Long> entry : rankingMap.entrySet()) {
                String rewardKey = rank <= 5 ? String.format("HI2025GuardTop%s", rank) : "HI2025GuardTop6-10";
                String aid = entry.getKey();
                logger.info("sendRankingReward. aid={} rankType={} score={} rank={} rewardKey={}", aid, DRAW_RANKING, entry.getValue(), rank, rewardKey);
                String guardianUid = (String) guardianRedis.getRankingMap(activityId, String.format(GUARD_RANKING, aid), 1).keySet().toArray()[0];
                resourceKeyHandlerService.sendResourceData(aid, rewardKey, 905, title, title, title, "", "", 1);
                if (!aid.equals(guardianUid)) {
                    resourceKeyHandlerService.sendResourceData(guardianUid, rewardKey, 905, title, title, title, "", "", 1);
                }
                rank++;
            }
        } catch (Exception e) {
            logger.error("sendRankingReward error. activityId={} rankType={} {}", activityId, ANCHOR_RANKING, e.getMessage(), e);
        }
    }

    private void doReportItemsChangeEvent(String activityId, String uid, int action, String itemId, int num, int source, String receiverUid) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setActivity_name(ACTIVITY_NAME);
        event.setActive_id(activityId);
        event.setChange_action(action);
        event.setActivity_special_items_id(itemId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc(receiverUid);
        event.setChange_nums(num);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void doDrawPrizeRecordEvent(String uid, int costNum, List<ResourceKeyConfigData.ResourceMeta> resourceMetaList) {
        DrawPrizeRecordEvent event = new DrawPrizeRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_NAME);
        event.setCost_ticket(costNum);
        event.setDraw_nums(costNum);
        event.setDraw_success_nums(resourceMetaList.size());
        event.setTicket_type(1);
        event.setSence_detail(0);
        event.setDraw_detail("");
        event.setDraw_result(JSONObject.toJSONString(resourceMetaList));
        eventReport.track(new EventDTO(event));
    }
}
