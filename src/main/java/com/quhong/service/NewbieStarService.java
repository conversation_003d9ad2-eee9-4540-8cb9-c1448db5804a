package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityApplicationEvent;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.NewbieStarVO;
import com.quhong.data.vo.ResKeyConfigVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.FollowDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.redis.DauUserRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class NewbieStarService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(NewbieStarService.class);
    public static final String ACTIVITY_ID = "672c7f7df3d1fef5319d2893";
    private static final String ACTIVITY_URL = ServerConfig.isProduct() ? String.format("https://static.youstar.live/welcome_star/?activityId=%s", ACTIVITY_ID): String.format("https://test2.qmovies.tv/welcome_star/?activityId=%s", ACTIVITY_ID);
    private static final String NEW_BIE_TITLE_EN = "Welcome New House";
    private static final String NEW_BIE_TASK_FINISH = "taskFinishStatus";   // 任务完成状态
    private static final String NEW_BIE_FORMAL_TASK_START = "formalTaskStart";   // 正式任务开始日期

    private static final Integer NEW_BIE_MAX_NUM_PRE = 3;
    private static final Integer NEW_BIE_MAX_NUM = 7;
    private static final String NEW_BIE_CONTINUE_NUM_PRE = "continueNumPre";   // 预播连续完成天数
    private static final String NEW_BIE_CONTINUE_NUM = "continueNum";   // 正播连续完成天数
    private static final String NEW_BIE_CONTINUE_DAY_PRE = "continueDayPre";   // 设置完成天
    private static final String NEW_BIE_CONTINUE_DAY = "continueDay";   // 设置完成天
    private static final String LIVE_START = "start";
    private static final String LIVE_END = "end";
    private static final String LIVE_ALARM = "alarm";
    private static final String CUSTOMER_ID = ServerConfig.isProduct() ? "5ac220d61bad4898c89dad7e" : "655c4c24b661b86b85455f3b";
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final List<TaskConfigVO> SIGN_TASK_LIST = new ArrayList<>();
    private static final List<TaskConfigVO> TRY_LIVE_TASK_LIST = new ArrayList<>();
    private static final List<String> TRY_LIVE_RES_KEY = Arrays.asList("welcomeStarTryLiveDay1", "welcomeStarTryLiveDay2", "welcomeStarTryLiveDay3");

    private static final List<TaskConfigVO> BASE_LIVE_TASK_LIST = new ArrayList<>();
    private static final List<String> BASE_LIVE_RES_KEY = Arrays.asList("welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay3", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay1", "welcomeStarFormalLiveDay7");
    private static final List<TaskConfigVO> HIGH_LIVE_TASK_LIST = new ArrayList<>();
    private static final List<String> TASK_ITEM_LIST = Arrays.asList(CommonMqTaskConstant.ON_MIC_TIME, CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL, CommonMqTaskConstant.INVITE_USER_ACTION,
            CommonMqTaskConstant.FOLLOW_USER, CommonMqTaskConstant.ADD_FRIEND);

    static {
        SIGN_TASK_LIST.add(new TaskConfigVO(1, 0, "sign_sex", "", "فقط للمستخدمين الإناث", "فقط للمستخدمين الإناث", null, null, null, null, null));
        SIGN_TASK_LIST.add(new TaskConfigVO(30, 0, "register_day", "", "التسجيل أكثر من 30 يوما", "التسجيل أكثر من 30 يوما", null, null, null, null, null));
        SIGN_TASK_LIST.add(new TaskConfigVO(50, 0, "follow_num", "", "عدد المتابعين أكثر من 50", "عدد المتابعين أكثر من 50", null, null, null, null, null));
        SIGN_TASK_LIST.add(new TaskConfigVO(3, 0, "live_daily", "", "أيام الاتصال بالاونلاين ≥ 3 أيام في آخر 7 أيام", "أيام الاتصال بالاونلاين ≥ 3 أيام في آخر 7 أيام", null, null, null, null, null));

        // 试播任务
        TRY_LIVE_TASK_LIST.add(new TaskConfigVO(60, 0, "on_mic_time", "", "", "افتح غرفتك واجلس على الميكروفون لمدة 60 دقيقة في غرفة الصوت الخاصة بك", null, null, null, null, null));
        TRY_LIVE_TASK_LIST.add(new TaskConfigVO(5, 0, "invite_user_on_mic_all", "", "", "إرسال طلبات دعوة الميكروفون بشكل نشط إلى 5 مستخدمًا جديدًا", null, null, null, null, null));
        TRY_LIVE_TASK_LIST.add(new TaskConfigVO(3, 0, "on_mic_time_other", "", "", "قم بدعوة 3 مستخدمين جدد لإكمال وقت الميكروفون الفعال في غرفتك  \n (وقت الميكروفون ≥ 3 دقائق)", null, null, null, null, null));

        // 正式任务
        BASE_LIVE_TASK_LIST.add(new TaskConfigVO(90, 0, "on_mic_time", "", "", "افتح غرفتك واجلس على الميكروفون لمدة 90 دقيقة في غرفة الصوت الخاصة بك", null, null, null, null, null));
        BASE_LIVE_TASK_LIST.add(new TaskConfigVO(10, 0, "invite_user_on_mic_all", "", "", "إرسال طلبات دعوة الميكروفون بشكل نشط إلى 10 مستخدمًا جديدًا", null, null, null, null, null));
        BASE_LIVE_TASK_LIST.add(new TaskConfigVO(5, 0, "on_mic_time_other", "", "", "قم بدعوة 5 مستخدمين جدد لإكمال وقت الميكروفون الفعال في غرفتك \n (وقت الميكروفون ≥ 5 دقائق)", null, null, null, null, null));

        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(2, 0, "high_follow_user", "", "", "احصل على مستخدم جديد يتابعك كل يوم؛", null, null, null, null, "welcomeStarAdvanceTask1"));
        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(2, 0, "high_add_friend", "", "", "كن صديقًا لمستخدم واحد جديد كل يوم؛", null, null, null, null, "welcomeStarAdvanceTask2"));
        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(10, 0, "high_receive_rose_gift", "", "", "احصل على 10 ورود من المستخدمين الجدد؛", null, null, null, null, "welcomeStarAdvanceTask3"));
        HIGH_LIVE_TASK_LIST.add(new TaskConfigVO(1, 0, "high_on_mic_time_other", "", "", "قم بدعوة مستخدمة واحدة إلى الميكروفون وتتجاوز مدة الميكروفون 15 دقيقة؛(يجب ان تكون صديقتك )", null, null, null, null, "welcomeStarAdvanceTask4"));

    }


    @Resource
    private FollowDao followDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private EventReport eventReport;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private DauUserRedis dauUserRedis;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private MonitorSender monitorSender;

    // 抽奖相关的每日key
    private String getDailyDate(String activityId) {
        return String.format("dailyDate:%s", activityId);
    }

    private String getHashActivityId(String activityId, String uid) {
        return String.format("newbie:%s:%s", activityId, uid);
    }

    private String getHashStrActivityId(String activityId, String uid) {
        return String.format("newbieStr:%s:%s", activityId, uid);
    }

    private String getDailyHashActivityId(String activityId, String uid, int liveStatus, String dateStr) {
        return String.format("dailyNewbie:%s:%s:%s:%s", activityId, uid, liveStatus, dateStr);
    }

    private String getDailyHighTaskStatus(String taskKey) {
        return String.format("status:%s", taskKey);
    }

    private String getUserSignSetKey(String activityId) {
        return String.format("userSign:%s", activityId);
    }

    private String getDeviceSignSetKey(String activityId) {
        return String.format("deviceSign:%s", activityId);
    }

    // 新用户给房主完成用户Key
    private String getNewUserOnMicZSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("newUserOnMic:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    // 用户帮助完成用户数
    private String getUserHelpSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("userHelpSet:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    private String getTaskUserSetKey(String activityId, String uid, String taskKey, String dateStr) {
        return String.format("taskUserSet:%s:%s:%s:%s", activityId, uid, taskKey, dateStr);
    }

    // 直播定时推送
    private String getLiveUserSetByHourKey(String activityId, int hour) {
        return String.format("liveUser:%s:%s", activityId, hour);
    }


    public NewbieStarVO newbieConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        NewbieStarVO vo = new NewbieStarVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String userSignSetKey = getUserSignSetKey(activityId);
        vo.setSignNumber(activityCommonRedis.getCommonSetNum(userSignSetKey));

        ActorData customerActor = actorDao.getActorDataFromCache(CUSTOMER_ID);
        vo.setUserId(customerActor.getStrRid());
        vo.setUserUId(CUSTOMER_ID);
        vo.setUserName(customerActor.getName());

        List<TaskConfigVO> signTaskList = new ArrayList<>();
        int finishNum = 0;
        int regDay = ActorUtils.getRegDays(uid);
        int userSex = actorData.getFb_gender();

        for (TaskConfigVO taskConfig : new ArrayList<>(SIGN_TASK_LIST)) {
            String taskKey = taskConfig.getTaskKey();
            int totalProcess = taskConfig.getTotalProcess();
            if (taskKey.equals(SIGN_TASK_LIST.get(0).getTaskKey())) {
                taskConfig.setCurrentProcess(userSex == 2 ? 1 : 0);
                taskConfig.setStatus(userSex == 2 ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(1).getTaskKey())) {
                taskConfig.setCurrentProcess(Math.min(regDay, totalProcess));
                taskConfig.setStatus(regDay >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(2).getTaskKey())) {
                int fanNumber = followDao.getFollowsCountByMonGo(uid);
                taskConfig.setCurrentProcess(Math.min(fanNumber, totalProcess));
                taskConfig.setStatus(fanNumber >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(3).getTaskKey())) {
                int liveNum = this.getLastDailyLiveDay(uid);
                taskConfig.setCurrentProcess(Math.min(liveNum, totalProcess));
                taskConfig.setStatus(liveNum >= totalProcess ? 1 : 0);
            }
            if (taskConfig.getStatus() > 0) {
                finishNum += 1;
            }
            signTaskList.add(taskConfig);
        }
        vo.setSignTaskList(signTaskList);


        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        int finishStatus = finishNum >= SIGN_TASK_LIST.size() ? 1 : 0;
        int signStatus = finishStatus > 0 ? 1 : 0;
        signStatus = currentSignStatus > 0 ? 2 : signStatus;
        vo.setSignStatus(signStatus);
        return vo;
    }

    // 最近7天日活天数
    private int getLastDailyLiveDay(String uid) {
        int liveNum = 0;
        List<DayTimeData> dateList = DateHelper.ARABIAN.getContinuesDays(-6, 0);
        for (DayTimeData item : dateList) {
            if (dauUserRedis.hasLogByDate(uid, item.getDate())) {
                liveNum += 1;
            }
        }
        return liveNum;
    }

    /**
     * 获取前n天日期
     */
    private List<String> getContinueDayListByDateStr(String dateStr, int reqNum) {
        List<String> continueDayList = new ArrayList<>();
        int timeStamp = DateHelper.ARABIAN.stringDateToStampSecond(dateStr);
        for (int i = reqNum; i >= 0; i--) {
            int reqTimeStamp = timeStamp - i * 86400;
            String reqDateStr = DateHelper.ARABIAN.formatDateInDay(new Date(reqTimeStamp * 1000L));
            continueDayList.add(reqDateStr);
        }
        return continueDayList;
    }

    /**
     * 报名
     */
    public void newbieSign(String activityId, String uid) {
        checkActivityTime(activityId);
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        int userSex = actorData.getFb_gender();
        if (userSex == 1) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        String tnId = actorData.getTn_id();
        if (StringUtils.isEmpty(tnId)) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus > 0) {
            throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY.getCode(), "مسجلة بالفعل");
        }

        String deviceSignSetKey = getDeviceSignSetKey(activityId);
        int deviceStatus = activityCommonRedis.isCommonSetData(deviceSignSetKey, tnId);
        if (deviceStatus > 0) {
            logger.info("newbieSign newbieSign:{}", deviceStatus);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        int finishNum = 0;
        int regDay = ActorUtils.getRegDays(uid);
        for (TaskConfigVO taskConfig : new ArrayList<>(SIGN_TASK_LIST)) {
            String taskKey = taskConfig.getTaskKey();
            int totalProcess = taskConfig.getTotalProcess();
            if (taskKey.equals(SIGN_TASK_LIST.get(0).getTaskKey())) {
                taskConfig.setCurrentProcess(userSex == 2 ? 1 : 0);
                taskConfig.setStatus(userSex == 2 ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(1).getTaskKey())) {
                taskConfig.setCurrentProcess(Math.min(regDay, totalProcess));
                taskConfig.setStatus(regDay >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(2).getTaskKey())) {
                int fanNumber = followDao.getFollowsCountByMonGo(uid);
                taskConfig.setCurrentProcess(Math.min(fanNumber, totalProcess));
                taskConfig.setStatus(fanNumber >= totalProcess ? 1 : 0);
            } else if (taskKey.equals(SIGN_TASK_LIST.get(3).getTaskKey())) {
                int liveNum = this.getLastDailyLiveDay(uid);
                taskConfig.setCurrentProcess(Math.min(liveNum, totalProcess));
                taskConfig.setStatus(liveNum >= totalProcess ? 1 : 0);
            }
            if (taskConfig.getStatus() > 0) {
                finishNum += 1;
            }
        }

        int finishStatus = finishNum >= SIGN_TASK_LIST.size() ? 1 : 0;
        if (finishStatus == 0) {
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "نأسف لأنك غير مؤهل للتجربة");
        }

        synchronized (stringPool.intern(uid)) {
            activityCommonRedis.addCommonSetData(userSignSetKey, uid);
            activityCommonRedis.addCommonSetData(deviceSignSetKey, tnId);
            doJoinReportEvent(uid);
        }
    }

    private void doJoinReportEvent(String uid) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(NEW_BIE_TITLE_EN);
        eventReport.track(new EventDTO(event));
    }


    private String getCurrentDay(String activityId){
        return DateHelper.ARABIAN.formatDateInDay();
        // return activityCommonRedis.getCommonStrValue(getDailyDate(activityId));
    }


    public NewbieStarVO newbieLiveConfig(String activityId, String uid) {
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }

        NewbieStarVO vo = new NewbieStarVO();
        String currentDay = this.getCurrentDay(activityId);
        vo.setCurrentDay(currentDay);
        String hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        String hashStrActivityId = getHashStrActivityId(activityId, uid);
        Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

        vo.setStart(userDataMap.getOrDefault(LIVE_START, 0));
        vo.setEnd(userDataMap.getOrDefault(LIVE_END, 0));
        vo.setAlarm(userDataMap.getOrDefault(LIVE_ALARM, 0));

        // 正式任务开播时间戳, 为0试播
        int liveStatus = this.getUserLiveStatus(userDataStrMap, currentDay);

        // 调整当前连续天数
        String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
        String continueSetDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;
        formatTaskContinueNum(userDataMap, hashActivityId, userDataStrMap, hashStrActivityId, currentDay, liveStatus, continueNumKey, continueSetDayKey);

        int continueNum = userDataMap.getOrDefault(continueNumKey, 0);
        String continueSetDay = userDataStrMap.getOrDefault(continueSetDayKey, "");
        vo.setLiveStatus(liveStatus);
        vo.setFinishContinueDay(continueNum);

        // 设置最近n天数完成状态
        int reqDayNum = liveStatus == 0 ? 2 : 6;
        List<String> dateList = this.getContinueDayListByDateStr(currentDay, reqDayNum);
        List<TaskConfigVO> continueList = new ArrayList<>();
        int lastDayTaskStatus = 0;
        for (String dateStr : dateList) {
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, dateStr);
            TaskConfigVO statusVO = new TaskConfigVO();
            statusVO.setTaskKey(dateStr);
            statusVO.setStatus(activityCommonRedis.getCommonHashValue(dailyHashActivityId, NEW_BIE_TASK_FINISH));
            if (dateStr.equals(dateList.get(dateList.size() - 1))) {
                lastDayTaskStatus = statusVO.getStatus();
            }
            continueList.add(statusVO);
        }
        vo.setContinueList(continueList);


        // 设置奖励
        int resKeyIndex = 0;
        if (!currentDay.equals(continueSetDay)){
            resKeyIndex = continueNum;
        }else {
            int afterIndex = continueNum - 1;
            resKeyIndex = Math.max(afterIndex, 0);
        }
        List<String> rewardList = vo.getLiveStatus() == 0 ? TRY_LIVE_RES_KEY : BASE_LIVE_RES_KEY;
        String resKey = rewardList.get(resKeyIndex);
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resKey);
        ResKeyConfigVO resKeyConfigVO = new ResKeyConfigVO();
        BeanUtils.copyProperties(resourceKeyConfigData, resKeyConfigVO);
        resKeyConfigVO.setStatus(lastDayTaskStatus);
        vo.setResKeyConfigData(resKeyConfigVO);


        // 设置任务列表
        fillTaskList(vo, activityId, uid, liveStatus, currentDay);
        return vo;
    }

    private void formatTaskContinueNum(Map<String, Integer> userDataMap, String hashActivityId, Map<String, String> userDataStrMap,
                                       String hashStrActivityId, String currentDay, int liveStatus, String continueNumKey,
                                       String continueSetDayKey){
        // 查看昨天状态, 设置完成次数、今日奖励资源
        String yesterdayStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
        int continueMaxNum = liveStatus == 0 ? NEW_BIE_MAX_NUM_PRE : NEW_BIE_MAX_NUM;

        // 获取当前连续完成天数
        int continueNum = userDataMap.getOrDefault(continueNumKey, 0);

        // 初始化设置当前连续完成天数
        String continueSetDay = userDataStrMap.getOrDefault(continueSetDayKey, "");

        if(StringUtils.isEmpty(continueSetDay) || (!currentDay.equals(continueSetDay) && !yesterdayStr.equals(continueSetDay))){
            activityCommonRedis.setCommonHashData(hashStrActivityId, continueSetDayKey, currentDay);
            activityCommonRedis.setCommonHashNum(hashActivityId, continueNumKey, 0);
            userDataStrMap.put(continueSetDayKey, currentDay);
            userDataMap.put(continueNumKey, 0);
        }

        // 达到最大天数，第二天要设置为0;
        if (!StringUtils.isEmpty(continueSetDay) && continueNum >= continueMaxNum && !currentDay.equals(continueSetDay)){
            activityCommonRedis.setCommonHashData(hashStrActivityId, continueSetDayKey, currentDay);
            activityCommonRedis.setCommonHashNum(hashActivityId, continueNumKey, 0);
            userDataStrMap.put(continueSetDayKey, currentDay);
            userDataMap.put(continueNumKey, 0);
        }
    }

    private int getUserLiveStatus(Map<String, String> userDataStrMap, String currentDay){
        String formalLiveDate = userDataStrMap.getOrDefault(NEW_BIE_FORMAL_TASK_START, "");
        if (StringUtils.isEmpty(formalLiveDate)){
            return 0;
        }

        int currentTimestamp = DateHelper.ARABIAN.stringDateToStampSecond(currentDay);
        int startTimestamp = DateHelper.ARABIAN.stringDateToStampSecond(formalLiveDate);

        if (currentTimestamp >= startTimestamp){
            return 1;
        }
        return 0;
    }

    private void fillTaskList(NewbieStarVO vo, String activityId, String uid, int liveStatus, String dateStr) {
        List<TaskConfigVO> taskConfigList = liveStatus == 0 ? TRY_LIVE_TASK_LIST : BASE_LIVE_TASK_LIST;
        String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, dateStr);
        Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
        List<TaskConfigVO> taskConfigVOList = new ArrayList<>();
        for (TaskConfigVO taskConfig : taskConfigList) {
            int currentProcess = userDailyMap.getOrDefault(taskConfig.getTaskKey(), 0);
            int totalProcess = taskConfig.getTotalProcess();
            taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
            taskConfig.setStatus(currentProcess >= totalProcess ? 1 : 0);
            taskConfigVOList.add(taskConfig);
        }
        vo.setBaseTaskList(taskConfigVOList);

        // 进阶任务
        if (vo.getLiveStatus() > 0) {
            List<TaskConfigVO> highVOList = new ArrayList<>();
            for (TaskConfigVO taskConfig : HIGH_LIVE_TASK_LIST) {
                int currentProcess = userDailyMap.getOrDefault(taskConfig.getTaskKey(), 0);
                int totalProcess = taskConfig.getTotalProcess();
                taskConfig.setCurrentProcess(Math.min(currentProcess, totalProcess));
                int taskStatus = userDailyMap.getOrDefault(this.getDailyHighTaskStatus(taskConfig.getTaskKey()), 0);
                taskConfig.setStatus(taskStatus > 0 ? taskStatus : (currentProcess >= totalProcess ? 1 : 0));
                highVOList.add(taskConfig);
            }
            vo.setHighTaskList(highVOList);
        }
    }

    /**
     * 领取基础任务奖励
     */
    public void newbieLiveReward(String activityId, String uid, int liveStatus, String resKey) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);
            int formalLiveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            if (liveStatus != formalLiveStatus) {
                logger.error("newbieLiveReward1 uid:{} liveStatus:{}, formalLiveStatus:{}", uid, liveStatus, formalLiveStatus);
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "المهمة لم تكتمل");
            }

            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
            int rewardStatus = activityCommonRedis.getCommonHashValue(dailyHashActivityId, NEW_BIE_TASK_FINISH);
            if (rewardStatus > 1) {
                logger.error("newbieLiveReward2 uid:{} liveStatus:{}, rewardStatus:{}", uid, liveStatus, rewardStatus);
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY.getCode(), "تم جمع المكافأة");
            }

            String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
            int resKeyIndex = userDataMap.getOrDefault(continueNumKey, 0);

            // 设置奖励
            String formalResKey = "";
            if (resKeyIndex > 0) {
                List<String> rewardList = liveStatus == 0 ? TRY_LIVE_RES_KEY : BASE_LIVE_RES_KEY;
                formalResKey = rewardList.get(resKeyIndex - 1);
            }

            if (!resKey.equals(formalResKey)) {
                String warnDetail = String.format("uid:%s, resKey: %s, resKeyIndex:%s, formalResKey: %s", uid, resKey, resKeyIndex, formalResKey);
                monitorSender.info("hwm_warn", "【明星房主任务】奖励领取告警", warnDetail);
                logger.error("newbieLiveReward3 liveStatus:{}, resKey:{}, formalResKey:{}", liveStatus, resKey, formalResKey);
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN.getCode(), "المهمة لم تكتمل");
            }
            resourceKeyHandlerService.sendResourceData(uid, resKey, NEW_BIE_TITLE_EN, NEW_BIE_TITLE_EN, NEW_BIE_TITLE_EN, "", "");
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, NEW_BIE_TASK_FINISH, 2);
        }
    }

    /**
     * 领取进阶任务奖励
     */
    public void newbieLiveHighReward(String activityId, String uid, String taskKey) {
        checkActivityTime(activityId);
        String userSignSetKey = getUserSignSetKey(activityId);
        int currentSignStatus = activityCommonRedis.isCommonSetData(userSignSetKey, uid);
        if (currentSignStatus <= 0) {
            throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
        }
        synchronized (stringPool.intern(uid)) {
            String currentDay = this.getCurrentDay(activityId);
            String hashStrActivityId = getHashStrActivityId(activityId, uid);
            Map<String, String> userStrDataMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

            int liveStatus = getUserLiveStatus(userStrDataMap, currentDay);
            String dailyHashActivityId = getDailyHashActivityId(activityId, uid, liveStatus, currentDay);
            Map<String, Integer> userDailyMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);

            Map<String, TaskConfigVO> rewardConfigMap =  HIGH_LIVE_TASK_LIST.stream().collect(Collectors.toMap(TaskConfigVO::getTaskKey, Function.identity()));
            TaskConfigVO taskConfig = rewardConfigMap.get(taskKey);
            if(taskConfig == null){
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }

            String statusKey = getDailyHighTaskStatus(taskKey);
            int statusNum = userDailyMap.getOrDefault(statusKey, 0);
            if(statusNum > 0){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            int currentProcess = userDailyMap.getOrDefault(taskKey, 0);
            int totalProcess = taskConfig.getTotalProcess();
            if(currentProcess < totalProcess){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }
            resourceKeyHandlerService.sendResourceData(uid, taskConfig.getResourceKey(), NEW_BIE_TITLE_EN, NEW_BIE_TITLE_EN, NEW_BIE_TITLE_EN, "", "");
            activityCommonRedis.setCommonHashNum(dailyHashActivityId, statusKey, 2);
        }
    }


    /**
     * 设置开播时间
     */
    public void newbieSetLiveTime(String activityId, String uid, int start, int end, int alarm) {
        String liveUserSetKey = getLiveUserSetByHourKey(activityId, start);
        activityCommonRedis.addCommonSetData(liveUserSetKey, uid);

        String hashActivityId = getHashActivityId(activityId, uid);
        activityCommonRedis.setCommonHashNum(hashActivityId, LIVE_START, start);
        activityCommonRedis.setCommonHashNum(hashActivityId, LIVE_END, end);
        activityCommonRedis.setCommonHashNum(hashActivityId, LIVE_ALARM, alarm);
    }


    // 统一加积分方法
    private void incActionUserPoint(TaskConfigVO taskConfig, int taskInnerNum, String uid, String aid, int incPoint, int liveStatus, String dateStr, boolean setFlag, int limitNum, boolean newUserFlag) {
        String taskKey = taskConfig.getTaskKey();
        int maxNum = taskConfig.getTotalProcess();
        logger.info("incActionUserPoint uid:{}, aid:{}, taskKey:{}", uid, aid, taskKey);
        synchronized (stringPool.intern("incPoint" + uid)) {

            String dailyHashActivityId = this.getDailyHashActivityId(ACTIVITY_ID, uid, liveStatus, dateStr);
            Map<String, Integer> dailyUserDataMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int dailyGetPoint = dailyUserDataMap.getOrDefault(taskKey, 0);
            if (dailyGetPoint >= maxNum) {
                return;
            }

            // 新用户限制
            if (newUserFlag) {
                boolean newRegisterFlag = ActorUtils.isNewRegisterActor(aid, 7);
                if (!newRegisterFlag) {
                    return;
                }

                ActorData actorData = actorDao.getActorDataFromCache(aid);
                if (StringUtils.isEmpty(actorData.getFirstTnId())) {
                    return;
                }
            }

            // 任务要达到某一额度才算完成taskInnerNum
            if (taskInnerNum > 0) {
                String newUserOnMicKey = getNewUserOnMicZSetKey(ACTIVITY_ID, uid, taskKey, dateStr);
                int beforeScore = activityCommonRedis.getCommonZSetRankingScore(newUserOnMicKey, aid);
                if (beforeScore >= taskInnerNum) {
                    return;
                }

                int afterMicTime = activityCommonRedis.incrCommonZSetRankingScoreSimple(newUserOnMicKey, aid, incPoint);
                if (afterMicTime < taskInnerNum) {
                    return;
                }
            }

            // 帮助完成限制
            if (limitNum > 0) {
                String userHelpSetKey = getUserHelpSetKey(ACTIVITY_ID, aid, taskKey, dateStr);
                int finishNum = activityCommonRedis.getCommonSetNum(userHelpSetKey);
                if (finishNum >= limitNum) {
                    return;
                }
                activityCommonRedis.addCommonSetData(userHelpSetKey, uid);
            }

            // 同一个用户操作限制
            if (setFlag) {
                String taskUserSetKey = getTaskUserSetKey(ACTIVITY_ID, uid, taskKey, dateStr);
                int isSet = activityCommonRedis.isCommonSetData(taskUserSetKey, aid);
                if (isSet > 0) {
                    return;
                }
                activityCommonRedis.addCommonSetData(taskUserSetKey, aid);
            }


            int leftIncPoint = maxNum - dailyGetPoint;
            incPoint = Math.min(incPoint, leftIncPoint);
            activityCommonRedis.incCommonHashNum(dailyHashActivityId, taskKey, incPoint);
        }
    }

    // 更新任务状态
    private void updateTaskUserStatus(String uid, int liveStatus, List<TaskConfigVO> taskConfigList, String currentDay) {

        synchronized (stringPool.intern("updateTaskUser" + uid)) {
            String dailyHashActivityId = getDailyHashActivityId(ACTIVITY_ID, uid, liveStatus, currentDay);
            Map<String, Integer> dailyUserHashMap = activityCommonRedis.getCommonHashAll(dailyHashActivityId);
            int rewardStatus = dailyUserHashMap.getOrDefault(NEW_BIE_TASK_FINISH, 0);
            if (rewardStatus >= 1) {
                return;
            }

            int finishNum = 0;
            for (TaskConfigVO taskConfig : taskConfigList) {
                String taskKey = taskConfig.getTaskKey();
                Integer totalProcess = taskConfig.getTotalProcess();
                int currentProcess = dailyUserHashMap.getOrDefault(taskKey, 0);
                if (currentProcess >= totalProcess) {
                    finishNum += 1;
                }
            }
            if (finishNum >= taskConfigList.size()) {
                activityCommonRedis.setCommonHashNum(dailyHashActivityId, NEW_BIE_TASK_FINISH, 1);
                String hashActivityId = getHashActivityId(ACTIVITY_ID, uid);
                String continueKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
                Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
                int finishContinueDay = userDataMap.getOrDefault(continueKey, 0);
                finishContinueDay += 1;
                if(liveStatus == 0){
                    finishContinueDay = Math.min(3, finishContinueDay);
                    if (finishContinueDay == 3) {
                        String hashStrActivityId = getHashStrActivityId(ACTIVITY_ID, uid);
                        Date currentDate = DateHelper.ARABIAN.parseDate(currentDay);
                        String nextDay = DateHelper.ARABIAN.formatDateInDay(DateHelper.ARABIAN.getNextDay(currentDate.getTime()));
                        activityCommonRedis.setCommonHashData(hashStrActivityId, NEW_BIE_FORMAL_TASK_START, nextDay);
                    }
                }else {
                    finishContinueDay = Math.min(7, finishContinueDay);
                }
                activityCommonRedis.setCommonHashNum(hashActivityId, continueKey, finishContinueDay);
                String continueDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;
                String hashStrActivityId = getHashStrActivityId(ACTIVITY_ID, uid);
                activityCommonRedis.setCommonHashData(hashStrActivityId, continueDayKey, currentDay);
                doReportEvent(uid, liveStatus, finishContinueDay);
            }
        }
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        Set<String> aidList = giftData.getAid_list();
        int incNumber = giftData.getNumber();

        TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(2);
        String currentDay = this.getCurrentDay(activityId);

        for (String aid : aidList) {
            if (activityCommonRedis.isCommonSetData(getUserSignSetKey(ACTIVITY_ID), aid) <= 0) {
                continue;
            }
            String hashActivityId = getHashActivityId(activityId, aid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

            String hashStrActivityId = getHashStrActivityId(activityId, aid);
            Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

            int liveStatus = getUserLiveStatus(userDataStrMap, currentDay);
            String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
            String continueSetDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;
            formatTaskContinueNum(userDataMap, hashActivityId, userDataStrMap, hashStrActivityId, currentDay, liveStatus, continueNumKey, continueSetDayKey);
            incActionUserPoint(taskConfig, 0, aid, fromUid, incNumber, liveStatus, currentDay, false, 0, true);
        }
    }

    public void officialMsgPush() {
        String currentHour = DateHelper.ARABIAN.formatDateInHour();
        String liveUserSetKey = getLiveUserSetByHourKey(ACTIVITY_ID, Integer.parseInt(currentHour.substring(11, 13)));
        Set<String>  allPushMember = activityCommonRedis.getCommonSetMember(liveUserSetKey);
        String currentDay = this.getCurrentDay(ACTIVITY_ID);

        for (String pushUid : allPushMember){
            String hashActivityId = getHashActivityId(ACTIVITY_ID, pushUid);
            Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);
            int alarm = userDataMap.getOrDefault(LIVE_ALARM, 0);

            if(alarm > 0){
                ActorData actorData = actorDao.getActorDataFromCache(pushUid);
                String hashStrActivityId = getHashStrActivityId(ACTIVITY_ID, pushUid);
                Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);
                int timeStamp = DateHelper.ARABIAN.stringDateToStampSecond(currentDay);
                String yesterdayStr = DateHelper.ARABIAN.getYesterdayStr(new Date(timeStamp * 1000L));
                int liveStatus = getUserLiveStatus(userDataStrMap, yesterdayStr);
                List<TaskConfigVO> taskConfigList = liveStatus == 0 ? TRY_LIVE_TASK_LIST : BASE_LIVE_TASK_LIST;

                TaskConfigVO taskConfig = taskConfigList.get(1);
                String taskUserSetKey = getTaskUserSetKey(ACTIVITY_ID, pushUid, taskConfig.getTaskKey(), yesterdayStr);
                int setNum = activityCommonRedis.getCommonSetNum(taskUserSetKey);
                String actText = "شاهد";
                String title = "تحية النجم الجديد";
                String body = String.format("عزيزي %s، شكرًا لك على جلب الدفء والسعادة إلى %s مستخدم جديد أمس. يمكنك الاستمرار في تلقي باقات هدايا غنية من خلال إكمال المهام اليوم. لنبدأ رحلتنا الاجتماعية الرائعة>>", actorData.getName(), setNum);
                String picture = "";
                commonOfficialMsg(pushUid, picture, 0, 0, actText, title, body, ACTIVITY_URL);
            }
        }
    }

    private void doReportEvent(String uid, int liveStatus, int finishDay) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setActivity_name(NEW_BIE_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(liveStatus);
        event.setActivity_stage_desc(String.valueOf(finishDay));
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }

        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }


        String hostUid = "";
        if (CommonMqTaskConstant.FOLLOW_USER.equals(item)) {
            hostUid = aid;
        } else if (CommonMqTaskConstant.INVITE_USER_ACTION.equals(item)) {
            hostUid = uid;
        } else {
            hostUid = StringUtils.isEmpty(roomId) ? uid : RoomUtils.getRoomHostId(roomId);
        }

        if (activityCommonRedis.isCommonSetData(getUserSignSetKey(ACTIVITY_ID), hostUid) <= 0) {
            return;
        }

        String currentDay = this.getCurrentDay(ACTIVITY_ID);
        String hashActivityId = getHashActivityId(ACTIVITY_ID, hostUid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        String hashStrActivityId = getHashStrActivityId(ACTIVITY_ID, hostUid);
        Map<String, String> userDataStrMap = activityCommonRedis.getCommonHashAllMapStr(hashStrActivityId);

        int liveStatus = getUserLiveStatus(userDataStrMap, currentDay);
        String continueNumKey = liveStatus == 0 ? NEW_BIE_CONTINUE_NUM_PRE : NEW_BIE_CONTINUE_NUM;
        String continueSetDayKey = liveStatus == 0 ? NEW_BIE_CONTINUE_DAY_PRE : NEW_BIE_CONTINUE_DAY;
        formatTaskContinueNum(userDataMap, hashActivityId, userDataStrMap, hashStrActivityId, currentDay, liveStatus, continueNumKey, continueSetDayKey);

        List<TaskConfigVO> taskConfigList = liveStatus == 0 ? TRY_LIVE_TASK_LIST : BASE_LIVE_TASK_LIST;
        if (CommonMqTaskConstant.ON_MIC_TIME.equals(data.getItem())) {
            if (hostUid.equals(uid)) {
                TaskConfigVO taskConfig = taskConfigList.get(0);
                incActionUserPoint(taskConfig, 0, hostUid, uid, 1, liveStatus, currentDay, false, 0, false);
                this.updateTaskUserStatus(hostUid, liveStatus, taskConfigList, currentDay);
            }
            int maxOnMicTime = liveStatus == 0 ? 3 : 5;
            incActionUserPoint(taskConfigList.get(2), maxOnMicTime, hostUid, uid, 1, liveStatus, currentDay, true, 5, true);
            this.updateTaskUserStatus(hostUid, liveStatus, taskConfigList, currentDay);

        } else if (CommonMqTaskConstant.INVITE_USER_ACTION.equals(data.getItem())) {
            incActionUserPoint(taskConfigList.get(1), 0, hostUid, aid, 1, liveStatus, currentDay, true, 5, true);
            this.updateTaskUserStatus(hostUid, liveStatus, taskConfigList, currentDay);
        }

        // 进阶任务
        if (liveStatus == 1) {
            if (CommonMqTaskConstant.FOLLOW_USER.equals(data.getItem())) {
                TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(0);
                incActionUserPoint(taskConfig, 0, hostUid, uid, 1, liveStatus, currentDay, true, 5, true);
            } else if (CommonMqTaskConstant.ADD_FRIEND.equals(data.getItem())) {
                TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(1);
                incActionUserPoint(taskConfig, 0, hostUid, aid, 1, liveStatus, currentDay, true, 5, true);

            } else if (CommonMqTaskConstant.ON_MIC_TIME.equals(data.getItem())) {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (actorData.getFb_gender() == 2 && friendsDao.isFriend(hostUid, uid)) {
                    TaskConfigVO taskConfig = HIGH_LIVE_TASK_LIST.get(3);
                    incActionUserPoint(taskConfig, 15, hostUid, uid, 1, liveStatus, currentDay, true, 5, false);
                }
            }
        }
    }
}
