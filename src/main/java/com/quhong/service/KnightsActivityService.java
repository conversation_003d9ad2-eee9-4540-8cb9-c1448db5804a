package com.quhong.service;


import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.FightConfigVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.data.BadgeData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.FightRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class KnightsActivityService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(KnightsActivityService.class);
    private static Integer ATTACK_GIFT = null;
    private static Integer HELP_GIFT = null;
    private static final Integer PROTECT_TIME = 600;  // 10分钟
    private static final Integer DEFENSE_NUM = 693;
    private static final List<Integer> ATTACK_AWARD_LIST = ServerConfig.isProduct() ? Arrays.asList(2209, 2210, 2211) : Arrays.asList(1702, 1703, 1704);
    private static final List<Integer> ATTACK_AWARD_DAY = Arrays.asList(15, 7, 3);
    private static final Integer ATTACK_AWARD_MIC = 497;
    private static final Integer ATTACK_AWARD_BUBBLE = 154;
    private static final Integer ATTACK_AWARD_FLOAT_SCREEN = 49;

    private static final List<Integer> HELP_AWARD_LIST = ServerConfig.isProduct() ? Arrays.asList(2212, 2213, 2214) : Arrays.asList(1702, 1703, 1704);
    private static final Integer HELP_AWARD_MIC = 498;
    private static final Integer HELP_AWARD_BUBBLE = 155;
    private static final Integer HELP_AWARD_FLOAT_SCREEN = 50;


    private static final String RANK_TYPE_ATTACK = "attack";
    private static final String RANK_TYPE_HELP = "help";
    private static final List<Integer> ATTACK_BADGE_LEVEL_LIST = Arrays.asList(100000, 300000);
    private static final List<Integer> HELP_BADGE_LEVEL_LIST = Arrays.asList(100000, 300000);
    private static final List<Integer> ATTACK_BADGE_ID_LIST = ServerConfig.isProduct() ? Arrays.asList(2215, 2216) : Arrays.asList(1709, 1710);
    private static final List<Integer> HELP_BADGE_ID_LIST = ServerConfig.isProduct() ? Arrays.asList(2217, 2218) : Arrays.asList(1711, 1712);

    private static final String ACTIVITY_NAME = "Knights Activity";
    static {
        if(ServerConfig.isProduct()){
            ATTACK_GIFT = 641;
            HELP_GIFT = 642;
        }else {
            ATTACK_GIFT = 791;
            HELP_GIFT = 792;
        }
    }

    @Resource
    private FightRedis fightRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private BadgeDao badgeDao;

    public FightConfigVO fightConfig(String uid, String activityId) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);

        FightConfigVO vo = new FightConfigVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        ActorData actorData =actorDao.getActorDataFromCache(uid);
        vo.setUserRid(actorData.getRid());

        // 设置攻击排行榜
        FightConfigVO.AttackVO myAttackVO = new FightConfigVO.AttackVO();

        List<FightConfigVO.AttackVO> attackVOList = new ArrayList<>();
        Map<String, Integer> rankingMap = fightRedis.getTotalAttackRankingMap(10);
        ActorData rankActor = null;
        int rank = 1;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            FightConfigVO.AttackVO attackVO = new FightConfigVO.AttackVO();
            String aid = entry.getKey();
            rankActor = actorDao.getActorDataFromCache(aid);

            attackVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            attackVO.setName(rankActor.getName());
            attackVO.setUid(aid);
            attackVO.setAttack(entry.getValue());
            attackVO.setReceiveAttack(fightRedis.getAttackReceiveScore(aid));
            attackVO.setSendAttack(fightRedis.getAttackSendScore(aid));
            attackVO.setReceiveProtect(fightRedis.getAttackReceiveProtectScore(aid));
            attackVO.setProtect(fightRedis.getHelpDefenseScore(aid));
            attackVO.setProtectTime(fightRedis.getProtectTimeScore(aid));


            attackVO.setRank(rank);
            if(uid.equals(entry.getKey())){
                BeanUtils.copyProperties(attackVO, myAttackVO);
            }
            attackVOList.add(attackVO);
            rank += 1;
        }

        vo.setAttackRankList(attackVOList);

        if(myAttackVO.getRank() == 0){
            myAttackVO.setName(actorData.getName());
            myAttackVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myAttackVO.setUid(uid);
            myAttackVO.setAttack(fightRedis.getTotalAttackRankingScore(uid));
            myAttackVO.setReceiveAttack(fightRedis.getAttackReceiveScore(uid));
            myAttackVO.setSendAttack(fightRedis.getAttackSendScore(uid));
            myAttackVO.setReceiveProtect(fightRedis.getAttackReceiveProtectScore(uid));
            myAttackVO.setProtect(fightRedis.getHelpDefenseScore(uid));
            myAttackVO.setProtectTime(fightRedis.getProtectTimeScore(uid));
            myAttackVO.setRank(-1);
        }

        vo.setMyAttackRank(myAttackVO);


        // 设置help排行榜
        FightConfigVO.HelpRankVO myHelpVO = new FightConfigVO.HelpRankVO();

        List<FightConfigVO.HelpRankVO> helpVOList = new ArrayList<>();
        Map<String, Integer> helpRankingMap = fightRedis.getTotalHelpRankingMap(10);
        int helpRank = 1;
        for (Map.Entry<String, Integer> entry : helpRankingMap.entrySet()) {
            FightConfigVO.HelpRankVO helpRankVO = new FightConfigVO.HelpRankVO();
            String aid = entry.getKey();
            rankActor = actorDao.getActorDataFromCache(aid);
            helpRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            helpRankVO.setName(rankActor.getName());
            helpRankVO.setUid(aid);
            helpRankVO.setScore(entry.getValue());
            helpRankVO.setSend(fightRedis.getHelpGiftSendScore(aid));
            helpRankVO.setReceive(fightRedis.getHelpReceiveScore(aid));
            helpRankVO.setRank(helpRank);
            if(uid.equals(entry.getKey())){
                BeanUtils.copyProperties(helpRankVO, myHelpVO);
            }
            helpVOList.add(helpRankVO);
            helpRank += 1;
        }

        vo.setHelpRankList(helpVOList);

        if(myHelpVO.getRank() == 0){
            myHelpVO.setName(actorData.getName());
            myHelpVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myHelpVO.setUid(uid);
            myHelpVO.setScore(fightRedis.getHelpGiftTotalScore(uid));
            myHelpVO.setSend(fightRedis.getHelpGiftSendScore(uid));
            myHelpVO.setReceive(fightRedis.getHelpReceiveScore(uid));
            myHelpVO.setRank(-1);
        }

        vo.setMyHelpRank(myHelpVO);

        return vo;
    }

    public void knightsProtectTimeHandle(){
        int curTime = DateHelper.getNowSeconds();
        Map<String, Integer> rankingMap = fightRedis.getProtectTimeRankingMapByScore(0, curTime);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String rankUid = entry.getKey();
            fightRedis.removeProtectTime(rankUid);
            fightRedis.removeHelpDefense(rankUid);
        }

    }

    public void distributionLionRankingAward(){
        try{
            // 攻击榜奖励
            Map<String, Integer> rankingMap = fightRedis.getTotalAttackRankingMap(3);
            int rank = 0;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String rankUid = entry.getKey();

                Integer badgeId = ATTACK_AWARD_LIST.get(rank);
                Integer day = ATTACK_AWARD_DAY.get(rank);

                distributionService.sendRewardResource(rankUid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                if(rank == 0 || rank == 1){
                    distributionService.sendRewardResource(rankUid, ATTACK_AWARD_MIC, ActivityRewardTypeEnum.getEnumByName("mic"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, ATTACK_AWARD_BUBBLE, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, ATTACK_AWARD_FLOAT_SCREEN, ActivityRewardTypeEnum.getEnumByName("float_screen"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }else {
                    distributionService.sendRewardResource(rankUid, ATTACK_AWARD_MIC, ActivityRewardTypeEnum.getEnumByName("mic"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, ATTACK_AWARD_BUBBLE, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }

                rank += 1;
            }


            // 助力榜
            Map<String, Integer> rankingHelpMap = fightRedis.getTotalHelpRankingMap(3);
            int helpRank = 0;
            for (Map.Entry<String, Integer> entry : rankingHelpMap.entrySet()) {
                String rankUid = entry.getKey();
                Integer badgeId = HELP_AWARD_LIST.get(helpRank);
                Integer day = ATTACK_AWARD_DAY.get(helpRank);

                distributionService.sendRewardResource(rankUid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                if(helpRank == 0 || helpRank == 1){
                    distributionService.sendRewardResource(rankUid, HELP_AWARD_MIC, ActivityRewardTypeEnum.getEnumByName("mic"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, HELP_AWARD_BUBBLE, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, HELP_AWARD_FLOAT_SCREEN, ActivityRewardTypeEnum.getEnumByName("float_screen"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }else {
                    distributionService.sendRewardResource(rankUid, HELP_AWARD_MIC, ActivityRewardTypeEnum.getEnumByName("mic"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(rankUid, HELP_AWARD_BUBBLE, ActivityRewardTypeEnum.getEnumByName("buddle"), day, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                }

                helpRank += 1;
            }
        }catch (Exception e){
            logger.error("distributionLionRankingAward error: {}", e.getMessage(), e);
        }
    }


    public void distributionLevelBadge(String uid, List<Integer> levelNumList, List<Integer> badgeList, String rankType){
        try {

            int currentNum;
            if(RANK_TYPE_ATTACK.equals(rankType)){
                currentNum = fightRedis.getTotalAttackRankingScore(uid);
            }else {
                currentNum = fightRedis.getHelpGiftTotalScore(uid);
            }

            List<Integer> tempLevelNumList = new ArrayList<>(levelNumList);
            int currentLevelIndex = 0;

            if(tempLevelNumList.contains(currentNum)){
                currentLevelIndex = tempLevelNumList.indexOf(currentNum);
            }else {
                tempLevelNumList.add(currentNum);
                tempLevelNumList.sort(Integer::compare);
                currentLevelIndex = tempLevelNumList.indexOf(currentNum) - 1;
            }

            if(currentLevelIndex < 0){
                return;
            }

            Integer badgeId = badgeList.get(currentLevelIndex);
            if (badgeId == null){
                return;
            }

            BadgeData badgeData = badgeDao.getBadgeData(uid, badgeId);
            if(badgeData == null){
                distributionService.sendRewardResource(uid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
            }

            // 移除低等级勋章
            List<Integer> removeBadgeList = new ArrayList<>();
            for (int i = 0; i < currentLevelIndex; i++) {
                removeBadgeList.add(badgeList.get(i));
            }

            if(!removeBadgeList.isEmpty()){
                badgeDao.removeBadges(uid, removeBadgeList);
            }
        }catch (Exception e){
            logger.error("distributionLevelBadge error:{}", e.getMessage(), e);
        }
    }





    public void knightsGiftHandle(SendGiftData giftData){
        int giftId = giftData.getGid();
        String fromUid = giftData.getFrom_uid();
        int totalPrice = giftData.getPrice() * giftData.getNumber();
        int allTotalPrice =  totalPrice * giftData.getAid_list().size();
        if(giftId == ATTACK_GIFT){

            // 1、发送攻击礼物, 增加攻击值
            // 2、接收者接收攻击礼物
            //    处于非防御状态，会降低攻击值
            //    处于防御状态，会增加攻击值
            fightRedis.incrAttackSendScore(fromUid, allTotalPrice);
            distributionLevelBadge(fromUid, ATTACK_BADGE_LEVEL_LIST, ATTACK_BADGE_ID_LIST, RANK_TYPE_ATTACK);

            for (String aid: giftData.getAid_list()) {
                int protectTime = fightRedis.getProtectTimeScore(aid);
                if(protectTime <= 0){
                    int currentAttack = fightRedis.getTotalAttackRankingScore(aid);
                    if(currentAttack > 0){
                        int reduceNum = currentAttack - totalPrice > 0 ? totalPrice : currentAttack;
                        fightRedis.incrAttackReceiveScore(aid, reduceNum);
                    }
                }else {
                    fightRedis.incrAttackReceiveProtectScore(aid, totalPrice);
                    distributionLevelBadge(aid, ATTACK_BADGE_LEVEL_LIST, ATTACK_BADGE_ID_LIST, RANK_TYPE_ATTACK);
                }
            }




        } else if (giftId == HELP_GIFT) {

            fightRedis.incrHelpGiftSendScore(fromUid, allTotalPrice);
            distributionLevelBadge(fromUid, HELP_BADGE_LEVEL_LIST, HELP_BADGE_ID_LIST, RANK_TYPE_HELP);

            // 发送或者接收【防御礼物】均会增加防御值, 防御值高于699时, 防御网自动形成, 持续10分钟
            int senderProtectTime = fightRedis.getProtectTimeScore(fromUid);
            if(senderProtectTime <= 0){
                int senderCurrentDefense =  fightRedis.getHelpDefenseScore(fromUid);
                if(senderCurrentDefense < DEFENSE_NUM){
                    int incSenderNum = senderCurrentDefense + allTotalPrice > DEFENSE_NUM ? DEFENSE_NUM - senderCurrentDefense : allTotalPrice;
                    int newSenderCurrentDefense = fightRedis.incrHelpDefenseScore(fromUid, incSenderNum);
                    if(newSenderCurrentDefense >= DEFENSE_NUM){
                        int endTime = DateHelper.getNowSeconds() + PROTECT_TIME;
                        fightRedis.setProtectTimeScore(fromUid, endTime);
                    }
                }
            }


            for (String aid: giftData.getAid_list()) {
                fightRedis.incrHelpReceiveScore(aid, totalPrice);
                distributionLevelBadge(aid, HELP_BADGE_LEVEL_LIST, HELP_BADGE_ID_LIST, RANK_TYPE_HELP);

                int protectTime = fightRedis.getProtectTimeScore(aid);
                if(protectTime <= 0){
                    int currentDefense =  fightRedis.getHelpDefenseScore(aid);
                    if(currentDefense < DEFENSE_NUM){
                        int incNum = currentDefense + totalPrice > DEFENSE_NUM ? DEFENSE_NUM - currentDefense : totalPrice;
                        int newCurrentDefense = fightRedis.incrHelpDefenseScore(aid, incNum);

                        if(newCurrentDefense >= DEFENSE_NUM){
                            int endTime = DateHelper.getNowSeconds() + PROTECT_TIME;
                            fightRedis.setProtectTimeScore(aid, endTime);
                        }

                    }
                }
            }
        }
    }


    public void fightCheck(String searchId, String activityId){
        checkActivityTime(activityId);
        int searchRid;
        try {
            searchId = searchId.trim();
            if(searchId.length() > 10){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }
            searchRid = Integer.parseInt(searchId);
        }catch (Exception e){
            logger.error("fightCheck searchId={} error: {}", searchId, e.getMessage(), e);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if(searchRid <= 0){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        ActorData actorData = actorDao.getActorByRid(searchRid);
        if(actorData == null){
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_FIND_EXIST);
        }

        int protectTime = fightRedis.getProtectTimeScore(actorData.getUid());
        if(protectTime > 0){
            throw new CommonH5Exception(ActivityHttpCode.USER_IN_DEFENSE_STATE);
        }else {
            throw new CommonH5Exception(ActivityHttpCode.USER_NOT_DEFENSE_STATE);
        }



    }






}
