package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivityParticipationEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.vo.TimeCapsuleV2VO;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.UserCapsuleDao;
import com.quhong.mongo.dao.UserLevelDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.UserCapsuleData;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.utils.ActorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class TimeCapsuleServiceV2 extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(TimeCapsuleServiceV2.class);
    private static final String ACTIVITY_TITLE = "TimeCapsule-2026";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private UserCapsuleDao userCapsuleDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private UserLevelDao userLevelDao;

    public String getShareWishV1ZSetKey(String activityId){
        return String.format("shareWishV1:%s:%s", activityId, DateHelper.ARABIAN.formatDateInDay());
    }

    public String getShareWishV2ZSetKey(String activityId){
        return String.format("shareWishV2:%s", activityId);
    }


    public TimeCapsuleV2VO timeCapsuleV2Config(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        TimeCapsuleV2VO vo = new TimeCapsuleV2VO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        vo.setUserName(actorData.getName());

        UserCapsuleData userCapsuleData = userCapsuleDao.findData(uid, 1);
        if (userCapsuleData != null){
            vo.setWishInfo(userCapsuleData.getWishInfo());
            vo.setWishStatus(1);
            vo.setWishDownDate(DateHelper.ARABIAN.formatDateInDay(new Date(userCapsuleData.getCtime() * 1000L)));
            vo.setWishDownDay(ActorUtils.getRegDays(userCapsuleData.get_id().toString()));
        }
        vo.setShareWish(activityCommonRedis.getCommonZSetRankingScore(getShareWishV1ZSetKey(activityId), uid));

        UserCapsuleData userCapsuleV2Data = userCapsuleDao.findData(uid, 2);
        if(userCapsuleV2Data != null){
            vo.setWishInfoV2(userCapsuleV2Data.getWishInfo());
            vo.setWishStatusV2(1);
        }
        vo.setShareWishV2(activityCommonRedis.getCommonZSetRankingScore(getShareWishV2ZSetKey(activityId), uid));
        vo.setUserLevel(userLevelDao.getUserLevel(uid));
        return vo;
    }

    public void timeCapsuleShare(String activityId, String uid, int shareType) {
        checkActivityTime(activityId);
        String shareWishKey = shareType == 0 ? getShareWishV1ZSetKey(activityId) : getShareWishV2ZSetKey(activityId);
        int userLevel = userLevelDao.getUserLevel(uid);
        if (userLevel < 2){
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE.getCode(), "مستوى المستخدم الخاص بك ليس كافيا");
        }

        int shareNum = activityCommonRedis.getCommonZSetRankingScore(shareWishKey, uid);
        if (shareNum > 0){
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE.getCode(), "أوقات المشاركة محدودة");
        }
        activityCommonRedis.incrCommonZSetRankingScore(shareWishKey, uid, 1);
    }

    public void timeCapsuleWriteWish(TimeCapsuleV2VO dto) {
        String activityId = dto.getActivityId();
        if(StringUtils.isEmpty(activityId)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        checkActivityTime(activityId);
        String uid = dto.getUid();
        if(StringUtils.isEmpty(uid)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        String wishInfo = dto.getWishInfo();
        if(StringUtils.isEmpty(wishInfo) || wishInfo.length() == 0){
            logger.error("not find wishInfo {}", uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        if(wishInfo.length() > 1000){
            logger.error("too long wishInfo {}", uid);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if(actorData == null || StringUtils.isEmpty(actorData.getTn_id())){
            logger.error("user tnId is error {}", uid);
            throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
        }

        synchronized (stringPool.intern(ACTIVITY_TITLE + uid)) {
            UserCapsuleData userCapsuleData = userCapsuleDao.findData(uid, 2);
            if(userCapsuleData != null){
                throw new CommonH5Exception(ActivityHttpCode.SIGN_ALREADY);
            }

            userCapsuleData = new UserCapsuleData();
            userCapsuleData.setUid(uid);
            userCapsuleData.setWishInfo(wishInfo);
            userCapsuleData.setRoundNum(2);
            userCapsuleDao.save(userCapsuleData);
            resourceKeyHandlerService.sendResourceData(uid, "TimeCapsuleReward", ACTIVITY_TITLE, ACTIVITY_TITLE, ACTIVITY_TITLE, "", "");
            doWriteWishEvent(activityId, uid);
        }
    }

    /**
     * 参与记录
     */
    private void doWriteWishEvent(String activityId, String uid) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE);
        event.setActive_id(activityId);
        eventReport.track(new EventDTO(event));
    }

}
