package com.quhong.service;

import com.quhong.config.AsyncConfig;
import com.quhong.msg.MarsServerMsg;
import com.quhong.room.RoomWebSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class MarsMsgActivityService {
    @Resource
    protected RoomWebSender roomWebSender;

    @Async(AsyncConfig.ASYNC_TASK)
    public void asyncSendMsg(String roomId, String fromUid, MarsServerMsg msg, boolean responseAck) {
        roomWebSender.sendRoomWebMsg(roomId, fromUid, msg, responseAck);
    }

    @Async(AsyncConfig.ASYNC_TASK)
    public void asyncSendPlayerMsg(String roomId, String uid, String aid, MarsServerMsg msg, boolean responseAck) {
        roomWebSender.sendPlayerWebMsg(roomId, uid, aid, msg, responseAck);
    }
}
