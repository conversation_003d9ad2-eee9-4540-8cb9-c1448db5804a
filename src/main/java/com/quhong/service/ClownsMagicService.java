package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.ClownsMagicVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 小丑与魔术师活动
 */
@Service
public class ClownsMagicService extends OtherActivityService {


    private static final Logger logger = LoggerFactory.getLogger(ClownsMagicService.class);
    private static final String ACTIVITY_TITLE = "Clowns And Magician Activity";
    private static final String ACTIVITY_DESC = "reaching reward of clowns and magicians";
    public static final List<Integer> GIFT_ID_LIST = ServerConfig.isProduct() ?  Arrays.asList(710, 712) : Arrays.asList(860, 862);
    public static final List<String> TASK_KEY_LIST = Arrays.asList("clowns", "magic");
    public static final Map<Integer, String> GIFT_TASK_KEY_MAP = new HashMap<>();
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        GIFT_TASK_KEY_MAP.put(GIFT_ID_LIST.get(0), TASK_KEY_LIST.get(0));
        GIFT_TASK_KEY_MAP.put(GIFT_ID_LIST.get(1), TASK_KEY_LIST.get(1));
    }

    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private ActivityCommonConfig activityCommonConfig;

    private String getStatusKey(String drawKey){
        return String.format("status:%s", drawKey);
    }

    private String getHashActivityId(String activityId, String uid, String taskKey){
        return String.format("clownsMagic:%s:%s:%s", activityId, uid, taskKey);
    }

    public ClownsMagicVO clownsMagicConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        ClownsMagicVO vo = new ClownsMagicVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        // 设置进阶等级奖励信息
        String clownsHashKey = getHashActivityId(activityId, uid, TASK_KEY_LIST.get(0));
        Map<String, Integer> clownsUserData = activityCommonRedis.getCommonHashAll(clownsHashKey);
        List<PrizeConfigVO> clownsConfigVOList = new ArrayList<>();
        List<ActivityCommonConfig.CommonAwardConfig> clownsConfigList = activityCommonConfig.getClownsConfigList();
        for (ActivityCommonConfig.CommonAwardConfig config: clownsConfigList) {
            PrizeConfigVO prizeConfig = new PrizeConfigVO();
            BeanUtils.copyProperties(config, prizeConfig);
            prizeConfig.setStatus(clownsUserData.getOrDefault(getStatusKey(config.getDrawType()), 0));
            clownsConfigVOList.add(prizeConfig);
        }
        vo.setClownsPrizeList(clownsConfigVOList);
        vo.setClownsNum(Math.min(clownsUserData.getOrDefault(TASK_KEY_LIST.get(0), 0), 30));

        // 设置整蛊等级奖励
        String magicHashKey = getHashActivityId(activityId, uid, TASK_KEY_LIST.get(1));
        Map<String, Integer> magicUserData = activityCommonRedis.getCommonHashAll(magicHashKey);

        List<PrizeConfigVO> magicConfigVOList = new ArrayList<>();
        List<ActivityCommonConfig.CommonAwardConfig> magicConfigList = activityCommonConfig.getMagicConfigList();
        for (ActivityCommonConfig.CommonAwardConfig config: magicConfigList) {
            PrizeConfigVO prizeConfig = new PrizeConfigVO();
            BeanUtils.copyProperties(config, prizeConfig);
            prizeConfig.setStatus(magicUserData.getOrDefault(getStatusKey(config.getDrawType()), 0));
            magicConfigVOList.add(prizeConfig);
        }
        vo.setMagicPrizeList(magicConfigVOList);
        vo.setMagicNum(Math.min(magicUserData.getOrDefault(TASK_KEY_LIST.get(1), 0), 50));
        return vo;
    }

    public void clownsMagicClaim(String activityId, String uid, String taskKey, String drawType) {

        checkActivityTime(activityId);
        if(!TASK_KEY_LIST.contains(taskKey)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        synchronized (stringPool.intern(ACTIVITY_TITLE)) {
            String userHashKey = getHashActivityId(activityId, uid, taskKey);
            Map<String, Integer> userData = activityCommonRedis.getCommonHashAll(userHashKey);
            int currentNum = userData.getOrDefault(taskKey, 0);
            List<ActivityCommonConfig.CommonAwardConfig> taskConfigList = null;
            if(TASK_KEY_LIST.get(0).equals(taskKey)){
                taskConfigList = activityCommonConfig.getClownsConfigList();
            }else {
                taskConfigList = activityCommonConfig.getMagicConfigList();
            }

            Map<String, ActivityCommonConfig.CommonAwardConfig> rewardConfigMap = taskConfigList.stream().collect(Collectors.toMap(ActivityCommonConfig.CommonAwardConfig::getDrawType, Function.identity()));
            ActivityCommonConfig.CommonAwardConfig config = rewardConfigMap.get(drawType);
            if(config == null){
                throw new CommonH5Exception(HttpCode.PARAM_ERROR);
            }

            int rateNum = config.getRateNum();
            if(currentNum < rateNum){
                logger.info("can not sign uid:{}, currentNum:{}, rateNum:{}", uid, currentNum, rateNum);
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }

            String statusKey = getStatusKey(config.getDrawType());
            int status = userData.getOrDefault(statusKey, 0);
            if(status != 1){
                throw new CommonH5Exception(ActivityHttpCode.CANNOT_SIGN);
            }
            activityCommonRedis.setCommonHashNum(userHashKey, statusKey, 2);
            distributionService.sendRewardResource(uid, config.getSourceId(), ActivityRewardTypeEnum.getEnumByName(config.getRewardType()), config.getRewardTime(), config.getRewardNum(), ACTIVITY_TITLE, ACTIVITY_DESC, 0);
        }
    }

    private void handleTaskStatus(String activityId, String uid, int giftId, int incNum){
        String taskKey = GIFT_TASK_KEY_MAP.get(giftId);
        String userHashKey = getHashActivityId(activityId, uid, taskKey);
        Map<String, Integer> userData = activityCommonRedis.getCommonHashAll(userHashKey);
        int currentNum = userData.getOrDefault(taskKey, 0);
        int afterNum = currentNum + incNum;
        activityCommonRedis.setCommonHashNum(userHashKey, taskKey, afterNum);

        List<ActivityCommonConfig.CommonAwardConfig> taskConfigList = null;
        if(TASK_KEY_LIST.get(0).equals(taskKey)){
            taskConfigList = activityCommonConfig.getClownsConfigList();
        }else {
            taskConfigList = activityCommonConfig.getMagicConfigList();
        }

        for (ActivityCommonConfig.CommonAwardConfig config: taskConfigList) {
            int status = userData.getOrDefault(getStatusKey(config.getDrawType()), 0);
            if(status != 0){
                continue;
            }

            if (afterNum >= config.getRateNum()){
                activityCommonRedis.setCommonHashNum(userHashKey, getStatusKey(config.getDrawType()), 1);
            }
        }
    }

    public void handleGiftMqMsg(SendGiftData giftData, String activityId) {
        String fromUid = giftData.getFrom_uid();
        int giftId = giftData.getGid();
        int sendNumber = giftData.getNumber();
        if(sendNumber < 17){
            return;
        }

        synchronized (stringPool.intern(ACTIVITY_TITLE)) {
            int totalNum = giftData.getAid_list().size();
            handleTaskStatus(activityId, fromUid, giftId, totalNum);
        }
    }


}
