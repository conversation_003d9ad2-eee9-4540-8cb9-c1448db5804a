package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.config.ServerConfig;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.ChristmasVO;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.redis.ActivityCommonRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ChristmasService extends OtherActivityService {

    private static final Logger logger = LoggerFactory.getLogger(ChristmasService.class);
    public static final String CHRISTMAS_ACTIVITY_ID = "6584f903aac7f113a154bddb";
    public static final String ACTIVITY_NAME = "christmas activity";
    private static final String GIFT1_KEY = "gift1";
    private static final String GIFT2_KEY = "gift2";
    private static final String GIFT3_KEY = "gift3";
    private static final String GIFT4_KEY = "gift4";
    private static final List<String> GIFT_STATUS_KEY_LIST = Arrays.asList("gift1Status", "gift2Status", "gift3Status", "gift4Status");
    private static final Map<Integer, String> GIFT_KEY_MAP = new HashMap<>();
    private static final Map<Integer, Integer> GIFT_NUM_MAP = new HashMap<>();
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    static {
        if(ServerConfig.isProduct()){
            GIFT_KEY_MAP.put(675, GIFT1_KEY);
            GIFT_KEY_MAP.put(553, GIFT2_KEY);
            GIFT_KEY_MAP.put(555, GIFT3_KEY);
            GIFT_KEY_MAP.put(676, GIFT4_KEY);

            GIFT_NUM_MAP.put(675, 5000);
            GIFT_NUM_MAP.put(553, 10000);
            GIFT_NUM_MAP.put(555, 50000);
            GIFT_NUM_MAP.put(676, 100000);
        }else {
            GIFT_KEY_MAP.put(806, GIFT1_KEY);
            GIFT_KEY_MAP.put(807, GIFT2_KEY);
            GIFT_KEY_MAP.put(808, GIFT3_KEY);
            GIFT_KEY_MAP.put(809, GIFT4_KEY);

            GIFT_NUM_MAP.put(806, 5000);
            GIFT_NUM_MAP.put(807, 10000);
            GIFT_NUM_MAP.put(808, 50000);
            GIFT_NUM_MAP.put(809, 100000);
        }
    }


    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private ChristmasService christmasService;
    @Resource
    private ResourceDistributionService distributionService;


    private String getHashActivityId(String activityId, String uid){
        return String.format("%s:%s", activityId, uid);
    }

    private String getGiftStatusKey(String giftKey){
        return giftKey + "Status";
    }


    public ChristmasVO christmasGiftConfig(String activityId, String uid){
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        Map<String, Integer> taskNumMap =  activityCommonRedis.getCommonHashAll(getHashActivityId(activityId, uid));
        ChristmasVO vo = JSON.parseObject(JSON.toJSONString(taskNumMap), ChristmasVO.class);

        vo.setGift1(Math.min(vo.getGift1(), 5000));
        vo.setGift2(Math.min(vo.getGift2(), 10000));
        vo.setGift3(Math.min(vo.getGift3(), 50000));
        vo.setGift4(Math.min(vo.getGift4(), 100000));

        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        return vo;
    }

    public void christmasGiftAward(String activityId, String uid, String giftKey) {

        synchronized (stringPool.intern(CHRISTMAS_ACTIVITY_ID + uid)) {
            Map<String, Integer> taskNumMap = activityCommonRedis.getCommonHashAll(getHashActivityId(activityId, uid));
            String giftStatusKey = christmasService.getGiftStatusKey(giftKey);
            int giftStatus = taskNumMap.getOrDefault(giftStatusKey, 0);
            if(giftStatus >= 1){
                return;
            }

            switch (giftKey){
                case GIFT1_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 500, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 313, ActivityRewardTypeEnum.getEnumByName("background"), 1, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
                case GIFT2_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 1000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 313, ActivityRewardTypeEnum.getEnumByName("background"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 186, ActivityRewardTypeEnum.getEnumByName("buddle"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
                case GIFT3_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 5000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 313, ActivityRewardTypeEnum.getEnumByName("background"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 186, ActivityRewardTypeEnum.getEnumByName("buddle"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 529, ActivityRewardTypeEnum.getEnumByName("mic"), 3, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
                case GIFT4_KEY:
                    distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 10000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 313, ActivityRewardTypeEnum.getEnumByName("background"), 5, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 186, ActivityRewardTypeEnum.getEnumByName("buddle"), 5, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 529, ActivityRewardTypeEnum.getEnumByName("mic"), 5, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    distributionService.sendRewardResource(uid, 67, ActivityRewardTypeEnum.getEnumByName("float_screen"), 5, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                    break;
            }

            taskNumMap.put(giftStatusKey, 1);
            activityCommonRedis.setCommonHashNum(getHashActivityId(activityId, uid), giftStatusKey, 1);

            boolean bigStatus = true;
            for (String status : GIFT_STATUS_KEY_LIST) {
                int tempGiftStatus = taskNumMap.getOrDefault(status, 0);
                if(tempGiftStatus <= 0){
                    bigStatus = false;
                    break;
                }
            }

            if(bigStatus){
                distributionService.sendRewardResource(uid, 0, ActivityRewardTypeEnum.getEnumByName("diamond"), 0, 10000, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                distributionService.sendRewardResource(uid, 313, ActivityRewardTypeEnum.getEnumByName("background"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                distributionService.sendRewardResource(uid, 186, ActivityRewardTypeEnum.getEnumByName("buddle"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                distributionService.sendRewardResource(uid, 529, ActivityRewardTypeEnum.getEnumByName("mic"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                distributionService.sendRewardResource(uid, 67, ActivityRewardTypeEnum.getEnumByName("float_screen"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
                distributionService.sendRewardResource(uid, 175, ActivityRewardTypeEnum.getEnumByName("ride"), 7, 0, ACTIVITY_NAME, ACTIVITY_NAME, 0);
            }
        }
    }




    public void handleChristmasGiftData(SendGiftData sendGiftData, String activityId) {
        try {

            int giftId = sendGiftData.getGid();
            String giftKey = GIFT_KEY_MAP.get(giftId);
            if(StringUtils.isEmpty(giftKey)){
                return;
            }

            String giftStatusKey = christmasService.getGiftStatusKey(giftKey);

            int maxNum = GIFT_NUM_MAP.get(giftId);
            int sendBeans = sendGiftData.getPrice() * sendGiftData.getNumber();
            Set<String> aidSet = sendGiftData.getAid_list();
            for (String aid : aidSet) {

                String hashActivityId = getHashActivityId(activityId, aid);
                Map<String, Integer> taskNumMap = activityCommonRedis.getCommonHashAll(hashActivityId);
                int giftStatus = taskNumMap.getOrDefault(giftStatusKey, 0);
                if(giftStatus >= 1){
                    continue;
                }

                int taskNum = taskNumMap.getOrDefault(giftKey, 0);
                int afterNum = taskNum + sendBeans;
                int actualNum = Math.min(afterNum, maxNum);
                activityCommonRedis.setCommonHashNum(hashActivityId, giftKey, actualNum);
                if(actualNum >= maxNum){
                    christmasService.christmasGiftAward(activityId, aid, giftKey);
                }
            }
        }catch (Exception e){
            logger.error("handleChristmasGiftData error={}", e.getMessage(), e);
        }
    }

}
