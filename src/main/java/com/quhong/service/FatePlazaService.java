package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventReport;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.FataPlazaDTO;
import com.quhong.data.vo.FatePlazaVO;
import com.quhong.data.vo.OtherMyRankVO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IFriendService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendApplyDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.*;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.FriendsListRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.redis.UserOnlineRedis;
import com.quhong.room.redis.RoomKickRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.MatchUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 交友广场活动
 */
@Service
public class FatePlazaService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(FatePlazaService.class);
    private static final String ACTIVITY_TITLE_EN = "Fate Plaza";
    private static final String ACTIVITY_TITLE_AR = "Fate Plaza";
    public static String ACTIVITY_ID = "688b1ee3c38edf1f8d2cd4b8";

    private static String ACTIVITY_URL = String.format("https://static.youstar.live/fate_plaza/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";


    private static final List<String> AREA_LIST = Arrays.asList("sa", "kw", "ae", "qa", "om", "bh", "us");
    private static final int AREA_1 = 1;
    private static final int AREA_2 = 2;

    // 常量定义

    private static final int MAX_FRIEND_REQUESTS_PER_DAY = 5;
    private static final int FRIEND_REQUEST_COUNT = 10;
    private static final int REGISTER_DAYS_LIMIT = 30;
    private static final int HOURS_24 = 24;

    private static final int ALL_GENDER = 3;

    private static final String FILED_RECEIVE_NOTE_COUNT = "filed_receive_note_count";
    private static final String FILED_SEND_NOTE_COUNT = "filed_send_note_count";
    private static final String FILED_SEND_GREET_COUNT = "filed_send_greet_count";
    private static final String FILED_DEVIVE_SEND_COUNT = "filed_devive_send_count";


    // 每日任务相关常量
    private static final int DAILY_TASK_SEND_NOTE_TARGET = 1;  // 发送1次小纸条到广场
    private static final int DAILY_TASK_GREET_TARGET = 5;      // 广场打招呼5个用户
    private static final int DAILY_TASK_ADD_FRIEND_TARGET = 5; // 广场成功添加5名好友

    // 每日任务奖励资源key-男性
    private static final String REWARD_SEND_NOTE_KEY = "FatePlazaSendNote";
    private static final String REWARD_GREET_KEY = "FatePlazaGreet";
    private static final String REWARD_ADD_FRIEND_KEY = "FatePlazaAddFriend";

    // 每日任务奖励资源key-女性
    private static final String REWARD_FEMALE_SEND_NOTE_KEY = "FatePlazaFemaleSendNote";
    private static final String REWARD_FEMALE_GREET_KEY = "FatePlazaFemaleGreet";
    private static final String REWARD_FEMALE_ADD_FRIEND_KEY = "FatePlazaFemaleAddFriend";

    private static final String REWARD_FATE_PLAZA_2DAYS_KEY = "FatePlaza2days";
    private static final String REWARD_FATE_PLAZA_3DAYS_KEY = "FatePlaza3days";
    private static final String REWARD_FATE_PLAZA_5DAYS_KEY = "FatePlaza5days";
    private static final String REWARD_FATE_PLAZA_7DAYS_KEY = "FatePlaza7days";
    /**
     * 事件埋点title
     */
    public static final Map<String, String> EVENT_TITLE_MAP = new HashMap<String, String>() {
        {
            put(REWARD_SEND_NOTE_KEY, "Fate Plaza-send note reward"); //
            put(REWARD_GREET_KEY, "Fate Plaza-say hello reward"); //
            put(REWARD_ADD_FRIEND_KEY, "Fate Plaza-make friend reward"); //

            put(REWARD_FEMALE_SEND_NOTE_KEY, "Fate Plaza-send note reward"); //
            put(REWARD_FEMALE_GREET_KEY, "Fate Plaza-say hello reward"); //
            put(REWARD_FEMALE_ADD_FRIEND_KEY, "Fate Plaza-make friend reward"); //

            put(REWARD_FATE_PLAZA_2DAYS_KEY, "Fate Plaza-chat with friend day2 reward");
            put(REWARD_FATE_PLAZA_3DAYS_KEY, "Fate Plaza-chat with friend day3 reward");
            put(REWARD_FATE_PLAZA_5DAYS_KEY, "Fate Plaza-chat with friend day5 reward");
            put(REWARD_FATE_PLAZA_7DAYS_KEY, "Fate Plaza-chat with friend day7 reward");
        }
    };


    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.ADD_FRIEND, CommonMqTaskConstant.SEND_PRIVATE_MSG);

    private static final List<Integer> TOTAL_REWARD_LIST = Arrays.asList(
            2, 3, 5, 7);

    public static final Map<Integer, String> DAY_MSG_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(2, REWARD_FATE_PLAZA_2DAYS_KEY); // 2天
            put(3, REWARD_FATE_PLAZA_3DAYS_KEY); // 3天
            put(5, REWARD_FATE_PLAZA_5DAYS_KEY); // 5天
            put(7, REWARD_FATE_PLAZA_7DAYS_KEY); // 7天
        }
    };


    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private EventReport eventReport;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private IFriendService ifriendService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private DAUDao dauDao;
    @Resource
    private FriendApplyDao friendApplyDao;
    @Resource
    private IDetectService idetectService;
    //    @Resource(name = AsyncConfig.ASYNC_TASK)
//    private Executor executor;
    @Resource
    private FatePlazaService fatePlazaService;
    @Resource
    private BasePlayerRedis basePlayerRedis ;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "6888935f36710475b95fb63b";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/fate_plaza/?activityId=%s", ACTIVITY_ID);
        }
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:fate_plaza:user:" + uid;
    }

    private String getLocalSendNoticeKey(String uid) {
        return "lock:fate_plaza:user:send:notice:" + uid;
    }

    private String getLocalGreetKey(String uid) {
        return "lock:fate_plaza:user:greet:" + uid;
    }

    /**
     * 获取纸条列表ActivityId (用于Hash存储)
     */
    private String getNoteListActivityId(String activityId) {
        return String.format("fate_plaza_notes_%s", activityId);
    }

    /**
     * 获取广场纸条ActivityId (用于ZSet存储)
     */
    private String getSquareNoteActivityId(String activityId) {
        return String.format("fate_plaza_square_%s", activityId);
    }

    /**
     * 获取广场纸条-地区-性别ActivityId (用于ZSet存储)
     */
    private String getSquareNoteAreaGender(String activityId, int area, int gender) {
        return String.format("fate_plaza_square_%s_%s_%s", activityId, area, gender);
    }

    /**
     * 获取用户每日任务配置限制ActivityId (用于Hash存储)
     */
    private String getDailyConfigLimitActivityId(String activityId, String uid, String dateStr) {
        return String.format("fate_plaza_daily_limit_%s_%s_%s", activityId, dateStr, uid);
    }

    /**
     * 获取每日任务数据ActivityId (用于Hash存储)
     */
    private String getDailyHashActivityId(String activityId, String dateStr) {
        return String.format("fate_plaza_daily_task_%s_%s", activityId, dateStr);
    }


    /**
     * 获取ActivityId (用于set存储)
     */
    private String getAllJoinActivityId(String activityId) {
        return String.format("fate_plaza_all_join_%s", activityId);
    }

    /**
     * 获取友谊任务ActivityId (用于Hash存储)
     */
    private String getFriendshipTaskActivityId(String activityId) {
        return String.format("fate_plaza_friendship_task_%s", activityId);
    }

    /**
     * 交友纸条列表
     *
     * @param activityId
     * @param uid
     * @return
     */
    public FatePlazaVO noteList(String activityId, String uid, int page) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        FatePlazaVO vo = new FatePlazaVO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        page = page <= 0 ? 1 : page;
        List<FatePlazaVO.NoteInfoVO> noteInfoList = getNoteInfoList(activityId, uid, page);
//        PageUtils.PageData<FatePlazaVO.NoteInfoVO> pageData = PageUtils.getPageData(noteInfoList, page, 20);
//        // 获取在线用户
//        Set<String> onlineUsers = userOnlineRedis.getAllUserOnline(); // 最近1小时在线
//        for (FatePlazaVO.NoteInfoVO noteInfoVO : noteInfoList) {
//            int greetStatus = checkGreetStatus(uid, noteInfoVO.getUid());
//            noteInfoVO.setGreetStatus(greetStatus);
//            noteInfoVO.setOnlineState(onlineUsers.contains(noteInfoVO.getUid()) ? 1 : 0);
//        }
        FatePlazaVO.FatePlazaListVO listVO = new FatePlazaVO.FatePlazaListVO(noteInfoList, noteInfoList.size() == 0 ? -1 : page + 1);
        vo.setNoteInfoList(listVO);
        return vo;
    }

    //    @Cacheable(value = "cache", key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE)
    public List<FatePlazaVO.NoteInfoVO> getNoteInfoList(String activityId, String uid, int page) {
        Set<String> myFriendSet = friendsListRedis.getFriendList(uid);
        // 获取在线用户
        Set<String> onlineUsers = userOnlineRedis.getAllUserOnline(); // 最近1小时在线
        List<FatePlazaVO.NoteInfoVO> noteInfoList = new ArrayList<>();
        try {
            // 获取当前用户信息
            ActorData currentUser = actorDao.getActorDataFromCache(uid);
            if (currentUser == null) {
                logger.error("获取用户信息失败, uid={}", uid);
                return noteInfoList;
            }

            // 获取最近24小时的广场纸条
            long currentTime = DateHelper.getNowSeconds();
            long startTime = currentTime - HOURS_24 * 3600;
            int pageSize = 20;

            int start = (page - 1) * pageSize;
            int end = start + pageSize;

//            String squareActivityId = getSquareNoteActivityId(activityId);
//            Map<String, Integer> noteIdsWithScore = activityCommonRedis.getCommonRankingMapByPage(
//                    squareActivityId, start, end);
//            Map<String, Integer> noteIdsWithScore = activityCommonRedis.getOtherRankingMapByScoreAPage(squareActivityId,
//                    (int) startTime, (int) currentTime, start, pageSize);

            int filterArea = AREA_LIST.contains(ActorUtils.getCountryCode(currentUser.getCountry())) ? AREA_1 : AREA_2;
            // int filterGender = currentUser.getFb_gender() == 1 ? 2 : 1;
            String squareAreaGenderActivityId = getSquareNoteAreaGender(activityId, filterArea, ALL_GENDER);

            Map<String, Integer> noteIdsWithScore = activityCommonRedis.getOtherRankingMapByScoreAPage(squareAreaGenderActivityId,
                    (int) startTime, (int) currentTime, start, pageSize);


            if (CollectionUtils.isEmpty(noteIdsWithScore)) {
                return noteInfoList;
            }

            String noteListActivityId = getNoteListActivityId(activityId);
//            int count = 0;
            for (String noteId : noteIdsWithScore.keySet()) {
                try {
                    // 从Redis Hash获取纸条信息
//                    String noteInfoJson = activityCommonRedis.getCommonHashStrValue(noteListActivityId, noteId);
                    String noteInfoJson = cacheDataService.getCommonHashStrValue(noteListActivityId, noteId);
                    if (StringUtils.isEmpty(noteInfoJson)) {
                        continue;
                    }

                    FatePlazaVO.NoteInfo noteInfo = JSONObject.parseObject(noteInfoJson, FatePlazaVO.NoteInfo.class);
                    if (noteInfo == null) {
                        continue;
                    }

                    // 获取发送者信息
                    ActorData senderData = actorDao.getActorDataFromCache(noteInfo.getUid());
                    if (senderData == null) {
                        continue;
                    }

                    // 过滤条件：非好友
                    if (!shouldShowNote(currentUser, senderData, myFriendSet)) {
                        continue;
                    }

                    // 构建NoteInfoVO
                    FatePlazaVO.NoteInfoVO noteInfoVO = buildNoteInfoVO(noteInfo, senderData, uid, onlineUsers);
                    if (noteInfoVO != null) {
                        noteInfoList.add(noteInfoVO);
                    }
                } catch (Exception e) {
                    logger.error("处理纸条信息失败, noteId={}, error={}", noteId, e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            logger.error("获取纸条列表失败, activityId={}, uid={}, error={}", activityId, uid, e.getMessage(), e);
        }

        return noteInfoList;
    }

    /**
     * 发送小纸条
     */
    public void sendNote(FataPlazaDTO reqDTO) {
        String activityId = reqDTO.getActivityId();
        String uid = reqDTO.getUid();
        String content = reqDTO.getContent();
        int isSendSquare = reqDTO.getIsSendSquare();
        String dateStr = getDayByBase(activityId, uid);

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);

        if (StringUtils.isEmpty(content) || content.length() > 100) {
            logger.error("内容为空或过长, content={}", content);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        ActorData aidInfo = basePlayerRedis.getActorFromRedis(uid);
        if (aidInfo != null && aidInfo.getGeneralConfActorData() != null && aidInfo.getGeneralConfActorData().getBuddy() != 0) {
            logger.info("you have set refuse to add friends. uid={}", uid);
            throw new CommonException(ActivityHttpCode.SET_REFUSE_TO_ADD_FRIENDS);
        }
        String tnId = aidInfo.getTn_id();
        if(StringUtils.isEmpty(tnId)){
            logger.info("tnId is empty. uid={}", uid);
            throw new CommonException(ActivityHttpCode.PARAM_ERROR);
        }

        // 脏词检测
        if (idetectService.detectText(new TextDTO(content, "activity")).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }

        synchronized (stringPool.intern(getLocalSendNoticeKey(uid))) {
            // 检查是否达到发送限制
            if (getDailyCount(activityId, uid, dateStr, FILED_SEND_NOTE_COUNT) >= 1) {
                logger.info("发送纸条失败, 已达到发送限制, activityId={}, uid={}", activityId, uid);
                throw new CommonH5Exception(ActivityHttpCode.SEND_NOTE_COUNT_MAX);
            }

            if (getDailyCount(activityId, tnId, dateStr, FILED_DEVIVE_SEND_COUNT) >= 2) {
                logger.info("发送纸条失败, 已达到设备发送限制, activityId={}, uid={} tnId={}", activityId, uid,tnId);
                throw new CommonH5Exception(ActivityHttpCode.SEND_NOTE_DEVICE_COUNT_MAX);
            }


            String noteId = new ObjectId().toString();
            int publishTime = DateHelper.getNowSeconds();

            // 创建纸条信息
            FatePlazaVO.NoteInfo noteInfo = new FatePlazaVO.NoteInfo();
            noteInfo.setNoteId(noteId);
            noteInfo.setUid(uid);
            noteInfo.setContent(content);
            noteInfo.setIsSendSquare(isSendSquare);
            noteInfo.setPublishTime(publishTime);

            // 存储到Redis Hash
            String noteListActivityId = getNoteListActivityId(activityId);
            String noteInfoJson = JSONObject.toJSONString(noteInfo);
            activityCommonRedis.setCommonHashData(noteListActivityId, noteId, noteInfoJson);

            // 如果发送到广场，存储到Redis ZSet
            if (isSendSquare == 1) {
                // String squareActivityId = getSquareNoteActivityId(activityId);
                // activityCommonRedis.addCommonZSetRankingScore(squareActivityId, noteId, publishTime);

                ActorData actorData = actorDao.getActorDataFromCache(uid);
                int area = AREA_LIST.contains(ActorUtils.getCountryCode(actorData.getCountry())) ? AREA_1 : AREA_2;
                // int gender = actorData.getFb_gender() == 2 ? 2 : 1;
                String squareNoteAreaGenderActivityId = getSquareNoteAreaGender(activityId, area, ALL_GENDER);
                activityCommonRedis.addCommonZSetRankingScore(squareNoteAreaGenderActivityId, noteId, publishTime);

                // 更新每日任务数据
                updateDailyTaskSendNote(activityId, uid, dateStr);
            }
            incrementDailyCount(activityId, uid, dateStr, FILED_SEND_NOTE_COUNT);
            incrementDailyCount(activityId, tnId, dateStr, FILED_DEVIVE_SEND_COUNT);
            logger.info("发送纸条成功, activityId={}, uid={}, noteId={}, isSendSquare={}",
                    activityId, uid, noteId, isSendSquare);
        }
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {

        } else {
            // 非灰度测试发送异步发送好友申请
            sendFriendRequestsAsync(activityId, uid, content);
        }

    }

    /**
     * 打招呼
     */
    public void greet(FataPlazaDTO reqDTO) {
        String activityId = reqDTO.getActivityId();
        String uid = reqDTO.getUid();
        String aid = reqDTO.getAid();
        String msg = reqDTO.getContent();
        String dateStr = getDayByBase(activityId, uid);
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        if (StringUtils.isEmpty(msg) || msg.length() > 100) {
            logger.error("内容为空或过长, content={}", msg);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (idetectService.detectText(new TextDTO(msg, DetectOriginConstant.ACTIVITY_RELATED)).getData().getIsSafe() == 0) {
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }
        if (StringUtils.isEmpty(aid)) {
            logger.error("打招呼失败, aid为空, activityId={}, uid={}", activityId, uid);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }


        int maxLimit = actorData.getFb_gender() == 2 ? 100 : 5;
        synchronized (stringPool.intern(getLocalGreetKey(uid))) {
            // 检查是否达到发送限制
            if (getDailyCount(activityId, uid, dateStr, FILED_SEND_GREET_COUNT) >= maxLimit) {
                throw new CommonH5Exception(ActivityHttpCode.SEND_GREET_COUNT_MAX);
            }
            FriendApplyData friendApplyData = friendApplyDao.findData(uid, aid);
            if (friendApplyData != null && friendApplyData.getOptType() == 0) {
                logger.info("打招呼失败, 已经发送过打招呼, activityId={}, uid={}, aid={}", activityId, uid, aid);
                throw new CommonH5Exception(ActivityHttpCode.ALREADY_APPLIED);
            }
            // 发送好友申请
            addFriendApply(activityId, uid, aid, msg, false);
//        cacheDataService.delSetCache(getAllJoinActivityId(activityId), 2);
            // 更新每日任务数据
            updateDailyTaskGreet(activityId, uid, aid, dateStr);
            // 增加发送计数
            incrementDailyCount(activityId, uid, dateStr, FILED_SEND_GREET_COUNT);

        }


        logger.info("打招呼成功, activityId={}, uid={}, aid={}", activityId, uid, aid);
    }

    /**
     * 我的每日任务
     *
     * @param activityId
     * @param uid
     * @return
     */
    public FatePlazaVO.DailyTaskVO myDailyTask(String activityId, String uid) {
        String dateStr = getDayByBase(activityId, uid);
        try {
            FatePlazaVO.DailyTaskInfo taskInfo = getDailyTaskInfo(activityId, uid, dateStr);

            FatePlazaVO.DailyTaskVO vo = new FatePlazaVO.DailyTaskVO();
            vo.setDayStr(dateStr);
            vo.setSendNoteCount(taskInfo.getSendNoteCount());
            vo.setGreetCount(taskInfo.getGreetAids() != null ? taskInfo.getGreetAids().size() : 0);
            vo.setAddFriendCount(taskInfo.getSuccessAddFriendAids() != null ? taskInfo.getSuccessAddFriendAids().size() : 0);
            vo.setDailyTaskList(Arrays.asList(vo.getSendNoteCount(), vo.getGreetCount(), vo.getAddFriendCount()));

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            OtherMyRankVO myRankVO = new OtherMyRankVO();
            myRankVO.setUid(uid);
            myRankVO.setName(actorData.getName());
            myRankVO.setGender(actorData.getFb_gender());
            myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

            vo.setMyInfo(myRankVO);
            return vo;

        } catch (Exception e) {
            logger.error("获取每日任务失败, activityId={}, uid={}, error={}", activityId, uid, e.getMessage(), e);
            // 返回默认数据
            FatePlazaVO.DailyTaskVO vo = new FatePlazaVO.DailyTaskVO();
            vo.setDayStr(dateStr);
            vo.setSendNoteCount(0);
            vo.setGreetCount(0);
            vo.setAddFriendCount(0);
            return vo;
        }
    }

    /**
     * 我的友谊任务
     *
     * @param activityId
     * @param uid
     * @param friendshipType 1 我发送的好友请求，2 我收到的好友请求
     * @param page
     * @return
     */
    public FatePlazaVO.FatePlazaListVO myFriendshipTask(String activityId, String uid, int friendshipType, int page) {
        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivity(activityId);
        if (activityData == null) {
            logger.error("活动不存在, activityId={}", activityId);
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
//        Set<String> allJoinIndexes = cacheDataService.getAllSetCache(getAllJoinActivityId(activityId), 2);
//        Set<String> allJoinIndexes = activityCommonRedis.getCommonSetMember(getAllJoinActivityId(activityId));
//        Set<String> allJoinUsers = new HashSet<>();
//        for (String index : allJoinIndexes) {
//            String[] uids = index.split("_");
//            allJoinUsers.add(uids[0]);
//            allJoinUsers.add(uids[1]);
//        }
//        if (!allJoinUsers.contains(uid)) {
//            logger.info("用户未参与活动, uid={}", uid);
//            return new FatePlazaVO.FatePlazaListVO(new ArrayList<>(), -1);
//        }

        int pageSize = 50;
        int offset = (page - 1) * pageSize;

        // 查询好友申请数据
        List<FriendApplyData> friendApplyList = getFriendApplyList(activityData, uid, friendshipType, offset, pageSize);

        List<FatePlazaVO.FriendshipTaskVO> taskList = new ArrayList<>();
        Set<String> myFriendSet = friendsListRedis.getFriendList(uid);
        for (FriendApplyData applyData : friendApplyList) {
            try {
                String friendUid = friendshipType == 1 ? applyData.getAid() : applyData.getUid();
                String friendIndex = MatchUtils.generateFriendIndex(uid, friendUid);
                boolean isJoin = activityCommonRedis.isCommonSetData(getAllJoinActivityId(activityId), friendIndex) == 1;
                // 检查是否是通过交友广场互动的用户或者好友
                if (!isJoin || !myFriendSet.contains(friendUid)) {
                    continue;
                }
//                logger.info("friendshipType={},uid={} friendUid={}",friendshipType, uid, friendUid);

                // 获取友谊任务信息
                FatePlazaVO.FriendshipTaskInfo taskInfo = getFriendshipTaskInfo(activityId, friendIndex);

                // 构建FriendshipTaskVO
                FatePlazaVO.FriendshipTaskVO taskVO = buildFriendshipTaskVO(uid, friendUid, applyData, taskInfo);
                if (taskVO != null) {
                    taskList.add(taskVO);
                }

            } catch (Exception e) {
                logger.error("处理友谊任务数据失败, applyData={}, error={}", applyData, e.getMessage(), e);
            }
        }
        int nextPage = friendApplyList.size() == pageSize ? page + 1 : -1;
        return new FatePlazaVO.FatePlazaListVO(taskList, nextPage);


    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        syncAddHandle(uid, data);
    }


    private boolean checkAc(String uid, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            boolean isWhiteTestAid = whiteTestDao.isMemberByType(mqData.getAid(), WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest && !isWhiteTestAid) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return false;
        }
        return true;
    }

    private void syncAddHandle(String uid, CommonMqTopicData mqData) {
        if (!checkAc(uid, mqData)) {
            return;
        }
        String dateStr = getDayByBase(ACTIVITY_ID, uid);
        String aid = mqData.getAid();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(aid)) {
            return;
        }
        if (CommonMqTaskConstant.ADD_FRIEND.equals(mqData.getItem())) {
            // 处理添加好友事件,mq发送端会发送两次，所以这里只处理一次
            updateDailyTaskAddFriend(ACTIVITY_ID, uid, aid, dateStr);
        } else if (CommonMqTaskConstant.SEND_PRIVATE_MSG.equals(mqData.getItem())) {
            synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                // 处理发送私信事件
                updateFriendshipTask(ACTIVITY_ID, uid, aid, dateStr);
            }
        }
    }


    // 发送好友请求
    private void addFriendApply(String activityId, String uid, String aid, String msg, boolean isBack) {
        ApiResult<List<String>> msgFriendList = ifriendService.addFriendApply(uid, aid, msg);
        if (msgFriendList.isOk()) {
            List<String> msgListData = msgFriendList.getData();
            if (!CollectionUtils.isEmpty(msgListData)) {
                if (isBack) {
                    logger.info("添加好友申请失败, uid={}, aid={}, error={}", uid, aid, msgListData.get(0));
                    return;
                }
                logger.info("添加好友申请失败, uid={}, aid={}, error={}", uid, aid, msgListData.get(0));
                throw new CommonH5Exception(new HttpCode(72, msgListData.get(0),
                        StringUtils.isEmpty(msgListData.get(1)) ? msgListData.get(0) : msgListData.get(1)));
            } else {
                String allJoinKey = getAllJoinActivityId(activityId);
                activityCommonRedis.addCommonSetData(allJoinKey, MatchUtils.generateFriendIndex(uid, aid));
                logger.info("添加好友申请成功, uid={}, aid={}", uid, aid);
            }
        } else {
            logger.error("添加好友申请失败, uid={}, aid={}, error={}", uid, aid, msgFriendList.getCode().getMsg());
            throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
        }

    }

    private void sendOfficialMsg(List<String> titleList, List<String> bodyList, String item) {
        ActorData actorData = actorDao.getActorDataFromCache(item);
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleList.get(1) : titleList.get(0);
        String body = slang == SLangType.ARABIC ? bodyList.get(1) : bodyList.get(0);
        commonOfficialMsg(item, "", 0, 0, "",
                title, body, ACTIVITY_URL);
    }

    private void handleRes(String aid, String resKey) {
        String eventTitle = EVENT_TITLE_MAP.getOrDefault(resKey, ACTIVITY_TITLE_EN);
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
    }

    /**
     * 判断是否应该显示纸条
     */
    public boolean shouldShowNote(ActorData currentUser, ActorData senderData, Set<String> myFriendSet) {
        if (currentUser.getUid().equals(senderData.getUid())) {
            return false;
        }
        // 检查地区是否相同
//        int currentArea = AREA_LIST.contains(ActorUtils.getCountryCode(currentUser.getCountry())) ? AREA_1 : AREA_2;
//        int senderArea = AREA_LIST.contains(ActorUtils.getCountryCode(senderData.getCountry())) ? AREA_1 : AREA_2;
//        if (currentArea != senderArea) {
//            return false;
//        }

        // 检查是否异性
//        int currentGender = currentUser.getFb_gender() == 1 ? 1 : 2;
//        int senderGender = senderData.getFb_gender() == 1 ? 1 : 2;
//        if (currentGender == senderGender) {
//            return false;
//        }

        // 检查是否已是好友
        if (myFriendSet.contains(senderData.getUid())) {
            return false;
        }

//        if (friendsListRedis.isFriend(currentUser.getUid(), senderData.getUid())) {
//            return false;
//        }

        // 检查是否打招呼
//        if (checkGreetStatus(senderData.getUid(), currentUser.getUid()) == 1) {
//            return false;
//        }

        return true;
    }

    /**
     * 构建NoteInfoVO对象
     */
    private FatePlazaVO.NoteInfoVO buildNoteInfoVO(FatePlazaVO.NoteInfo noteInfo, ActorData senderData, String currentUid, Set<String> onlineUsers) {
        try {
            FatePlazaVO.NoteInfoVO noteInfoVO = new FatePlazaVO.NoteInfoVO();
            Map<String, String> commonFlagConfigMap = cacheDataService.getFlagConfigMap();
            String countryCode = ActorUtils.getCountryCode(senderData.getCountry());
            noteInfoVO.setNoteId(noteInfo.getNoteId());
            noteInfoVO.setUid(senderData.getUid());
            noteInfoVO.setName(senderData.getName());
            noteInfoVO.setHead(ImageUrlGenerator.generateRoomUserUrl(senderData.getHead()));
            noteInfoVO.setGender(senderData.getFb_gender() == 1 ? 1 : 2);
            noteInfoVO.setCountryFlag(commonFlagConfigMap.getOrDefault(countryCode, ""));
//            noteInfoVO.setStarSign(getConstellation(senderData.getBirthday()));
            noteInfoVO.setStarSign(0);
            noteInfoVO.setContent(noteInfo.getContent());
            noteInfoVO.setPublishTime(noteInfo.getPublishTime());

            // 获取用户标签（随机3个）
//            List<UserLabelData> labelList = getUserLabels(senderData);
//            noteInfoVO.setLabelList(labelList);

            noteInfoVO.setLabelList(Collections.emptyList());

            // 检查打招呼状态（是否有好友申请）
            int greetStatus = checkGreetStatus(currentUid, senderData.getUid());
            noteInfoVO.setGreetStatus(greetStatus);

            noteInfoVO.setOnlineState(onlineUsers.contains(noteInfoVO.getUid()) ? 1 : 0);

            return noteInfoVO;

        } catch (Exception e) {
            logger.error("构建NoteInfoVO失败, noteId={}, error={}", noteInfo.getNoteId(), e.getMessage(), e);
            return null;
        }
    }


    /**
     * 获取用户标签（随机3个）
     */
//    private List<UserLabelData> getUserLabels(ActorData actorData) {
//        try {
//            List<Integer> labelIdList = actorData.getLabelList();
//            if (CollectionUtils.isEmpty(labelIdList)) {
//                return new ArrayList<>();
//            }
//
//            List<UserLabelData> allLabels = userLabelDao.getUserLabelFromRedis(actorData.getUid(), labelIdList);
//            if (allLabels.size() <= 3) {
//                return allLabels;
//            }
//
//            // 随机选择3个标签
//            Collections.shuffle(allLabels);
//            return allLabels.subList(0, 3);
//
//        } catch (Exception e) {
//            logger.error("获取用户标签失败, uid={}, error={}", actorData.getUid(), e.getMessage(), e);
//            return new ArrayList<>();
//        }
//    }

    /**
     * 检查打招呼状态
     */
    private int checkGreetStatus(String uid, String aid) {
        try {
            FriendApplyData data = friendApplyDao.findData(uid, aid);
            if (data != null && data.getOptType() == 0) {
                return 1;
            }
            return 0;
        } catch (Exception e) {
            logger.error("检查打招呼状态失败, uid={}, aid={}, error={}", uid, aid, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 异步发送好友申请
     */
    public void sendFriendRequestsAsync(String activityId, String uid, String content) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    logger.info("开始异步发送好友申请, activityId={}, uid={}", activityId, uid);


                    // 获取符合条件的用户列表
                    List<String> targetUsers = getTargetUsersForFriendRequest(uid);
                    if (CollectionUtils.isEmpty(targetUsers)) {
                        logger.info("没有找到符合条件的用户, uid={}", uid);
                        return;
                    }

                    String dateStr = getDayByBase(activityId, uid);
                    int successCount = 0;
                    Set<String> myFriendSet = friendsListRedis.getFriendList(uid);
                    for (String targetUid : targetUsers) {
                        try {
                            // 检查目标用户今日接收申请数量
                            if (getDailyCount(activityId, targetUid, dateStr, FILED_RECEIVE_NOTE_COUNT) >= MAX_FRIEND_REQUESTS_PER_DAY) {
                                continue;
                            }
                            // 检查是否已是好友
//                        if (friendsListRedis.isFriend(uid, targetUid)) {
//                            continue;
//                        }
                            if (myFriendSet.contains(targetUid)) {
                                continue;
                            }

                            // 检查是否已经发送过好友申请
                            FriendApplyData data = friendApplyDao.findData(uid, targetUid);
                            if (data != null && data.getOptType() == 0) {
                                continue;
                            }
                            // 发送好友申请
                            addFriendApply(activityId, uid, targetUid, content, true);
                            // 增加目标用户接收计数
                            incrementDailyCount(activityId, targetUid, dateStr, FILED_RECEIVE_NOTE_COUNT);

                            successCount++;

                            // 限制发送数量为10个
                            if (successCount >= FRIEND_REQUEST_COUNT) {
                                break;
                            }

                        } catch (Exception e) {
                            logger.error("发送好友申请失败, uid={}, targetUid={}, error={}", uid, targetUid, e.getMessage(), e);
                        }
                    }
//                cacheDataService.delSetCache(getAllJoinActivityId(activityId), 2);
                    logger.info("异步发送好友申请完成, uid={}, 成功发送数量={}", uid, successCount);

                } catch (Exception e) {
                    logger.error("异步发送好友申请异常, activityId={}, uid={}, error={}", activityId, uid, e.getMessage(), e);
                }

            }
        });
//        executor.execute(() -> {
//            });
    }


    /**
     * @deprecated 此方法存在内存问题，请使用 getNewUserActorListByPage 替代
     */
    @Deprecated
    @Cacheable(value = "cache", key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<MongoActorData> getNewUserActorList(int startDay, int endDay, int gender) {
        logger.info("使用已废弃的getNewUserActorList方法，建议使用分页版本");
        return actorDao.getNewUserActorListByPage(startDay, endDay, gender, 0, 10000); // 限制500条并缓存时间缩短
    }

    /**
     * 优化版本：分页获取新用户列表，避免内存问题
     */
    @Cacheable(value = "cache", key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE)
    public List<LightweightActorData> getNewUserActorListByPage(int startDay, int endDay, int gender, int page, int pageSize) {
        return actorDao.getLightweightNewUserActorListByPage(startDay, endDay, gender, page, pageSize);
    }

    /**
     * 获取符合条件的目标用户
     */

    public List<String> getTargetUsersForFriendRequest(String uid) {
        try {
            // 获取发送者信息
            ActorData senderData = actorDao.getActorDataFromCache(uid);
            if (senderData == null) {
                logger.error("获取发送者信息失败, uid={}", uid);
                return Collections.emptyList();
            }
            //  获取在线用户
            Set<String> onlineUsers = userOnlineRedis.getAllUserOnline(); // 最近3分钟在线
            // 获取最近3天活跃用户
//            Set<String> last3DayUsers = dauDao.getActiveUserSet(3);

            // 获取最近3天新增异性，使用分页版本避免内存问题
            int days = ServerConfig.isNotProduct() ? 30 : 3;
            List<LightweightActorData> last3DayUsers = fatePlazaService.getNewUserActorListByPage(days, 0, -1, 0, 10000);

//            List<String> candidateUsers = new ArrayList<>(last3DayUsers);

            // 过滤条件
            List<String> filteredUsers = last3DayUsers.stream()
                    .filter(item -> isValidTargetUser(item, senderData))
                    .map(item -> item.get_id().toString())
                    .collect(Collectors.toList());

            // Collections.shuffle(filteredUsers);
            // return filteredUsers;

            // 在线用户优先
            List<String> result = new ArrayList<>();
            List<String> onlineTargets = filteredUsers.stream()
                    .filter(onlineUsers::contains)
                    .collect(Collectors.toList());
//
            Collections.shuffle(onlineTargets);
            result.addAll(onlineTargets);

//            // 如果在线用户不够，添加离线用户
            List<String> offlineTargets = filteredUsers.stream()
                    .filter(itemUid -> !onlineUsers.contains(itemUid))
                    .collect(Collectors.toList());
            Collections.shuffle(offlineTargets);
            result.addAll(offlineTargets);
            return result;

        } catch (Exception e) {
            logger.error("获取目标用户失败, senderUid={}, error={}", uid, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查是否是有效的目标用户
     */
    private boolean isValidTargetUser(LightweightActorData targetData, ActorData senderData) {
        String targetUid = targetData.get_id().toString();
        try {
//            ActorData targetData = actorDao.getActorDataFromCache(targetUid);
            if (targetData == null) {
                return false;
            }
            if (targetUid.equals(senderData.getUid())) {
                return false;
            }
            // 检查注册时间（0-30天）
//            int regDays = ActorUtils.getRegDays(targetUid);
//            if (regDays > REGISTER_DAYS_LIMIT) {
//                return false;
//            }

            // 检查地区是否相同
            int targetArea = AREA_LIST.contains(ActorUtils.getCountryCode(targetData.getCountry())) ? AREA_1 : AREA_2;
            int senderArea = AREA_LIST.contains(ActorUtils.getCountryCode(senderData.getCountry())) ? AREA_1 : AREA_2;
            if (targetArea != senderArea) {
                return false;
            }

            // 检查是否异性
//            int senderGender = senderData.getFb_gender() == 1 ? 1 : 2;
//            int targetGender = targetData.getFb_gender() == 1 ? 1 : 2;
//            if (senderGender == targetGender) {
//                return false;
//            }

            return true;

        } catch (Exception e) {
            logger.error("检查目标用户有效性失败, targetUid={}, error={}", targetUid, e.getMessage(), e);
            return false;
        }
    }


    private int getDailyCount(String activityId, String uid, String dateStr, String hashFiled) {
        try {
            String dailyActivityId = getDailyConfigLimitActivityId(activityId, uid, dateStr);
            int count = activityCommonRedis.getCommonHashValue(dailyActivityId, hashFiled);
            return count;
        } catch (Exception e) {
            logger.error("检查每日限制失败, uid={}, error={}", uid, e.getMessage(), e);
            return 0;
        }
    }


    private void incrementDailyCount(String activityId, String uid, String dateStr, String hashFiled) {
        try {
            String dailyActivityId = getDailyConfigLimitActivityId(activityId, uid, dateStr);
            activityCommonRedis.incCommonHashNum(dailyActivityId, hashFiled, 1);
        } catch (Exception e) {
            logger.error("增加每日接收计数失败, uid={}, error={}", uid, e.getMessage(), e);
        }
    }


    /**
     * 更新每日任务中的添加好友数据
     */
    private void updateDailyTaskAddFriend(String activityId, String uid, String friendUid, String dateStr) {
        try {
            String dailyActivityId = getDailyHashActivityId(activityId, dateStr);

            // 获取当前的每日任务数据
            FatePlazaVO.DailyTaskInfo dailyTaskInfo = getDailyTaskInfo(activityId, uid, dateStr);

            // 检查是否是通过交友广场互动的用户
            if (isInteractedUser(activityId, uid, friendUid)) {
                // 添加到成功添加好友的集合中
                if (dailyTaskInfo.getSuccessAddFriendAids() == null) {
                    dailyTaskInfo.setSuccessAddFriendAids(new HashSet<>());
                }
                dailyTaskInfo.getSuccessAddFriendAids().add(friendUid);

                // 保存更新后的数据
                saveDailyTaskInfo(dailyActivityId, uid, dailyTaskInfo);

                // 检查任务完成情况并发放奖励
                checkAndRewardDailyTask(activityId, uid, dailyTaskInfo, dateStr);

                logger.info("更新每日任务添加好友成功, uid={}, friendUid={}, count={}",
                        uid, friendUid, dailyTaskInfo.getSuccessAddFriendAids().size());
            }

        } catch (Exception e) {
            logger.error("更新每日任务添加好友失败, uid={}, friendUid={}, error={}", uid, friendUid, e.getMessage(), e);
        }
    }

    /**
     * 获取每日任务信息
     */
    private FatePlazaVO.DailyTaskInfo getDailyTaskInfo(String activityId, String uid, String dateStr) {
        try {
            String dailyActivityId = getDailyHashActivityId(activityId, dateStr);
            String taskInfoJson = activityCommonRedis.getCommonHashStrValue(dailyActivityId, uid);

            if (StringUtils.isEmpty(taskInfoJson)) {
                // 创建新的每日任务信息
                FatePlazaVO.DailyTaskInfo taskInfo = new FatePlazaVO.DailyTaskInfo();
                taskInfo.setDayStr(dateStr);
                taskInfo.setSendNoteCount(0);
                taskInfo.setGreetAids(new HashSet<>());
                taskInfo.setSuccessAddFriendAids(new HashSet<>());
                taskInfo.setIsGetSendNoteReward(0);
                taskInfo.setIsGetGreetAidsReward(0);
                taskInfo.setIsGetSuccessAddFriendAidsReward(0);
                return taskInfo;
            }

            return JSONObject.parseObject(taskInfoJson, FatePlazaVO.DailyTaskInfo.class);

        } catch (Exception e) {
            logger.error("获取每日任务信息失败, uid={}, dateStr={}, error={}", uid, dateStr, e.getMessage(), e);
            // 返回默认的任务信息
            FatePlazaVO.DailyTaskInfo taskInfo = new FatePlazaVO.DailyTaskInfo();
            taskInfo.setDayStr(dateStr);
            taskInfo.setSendNoteCount(0);
            taskInfo.setGreetAids(new HashSet<>());
            taskInfo.setSuccessAddFriendAids(new HashSet<>());
            taskInfo.setIsGetSendNoteReward(0);
            taskInfo.setIsGetGreetAidsReward(0);
            taskInfo.setIsGetSuccessAddFriendAidsReward(0);
            return taskInfo;
        }
    }

    /**
     * 保存每日任务信息
     */
    private void saveDailyTaskInfo(String dailyActivityId, String uid, FatePlazaVO.DailyTaskInfo taskInfo) {
        try {
            String taskInfoJson = JSONObject.toJSONString(taskInfo);
            activityCommonRedis.setCommonHashData(dailyActivityId, uid, taskInfoJson);
        } catch (Exception e) {
            logger.error("保存每日任务信息失败, uid={}, taskInfo={}, error={}", uid, taskInfo, e.getMessage(), e);
        }
    }

    /**
     * 检查是否是通过交友广场互动的用户
     */
    private boolean isInteractedUser(String activityId, String uid, String friendUid) {
        try {
//            Set<String> allJoinIndexes = cacheDataService.getAllSetCache(getAllJoinActivityId(activityId), 2);
            String friendIndex = MatchUtils.generateFriendIndex(uid, friendUid);
//            if (allJoinIndexes.contains(friendIndex)) {
//                return true;
//            }
//            return false;
            return activityCommonRedis.isCommonSetData(getAllJoinActivityId(activityId), friendIndex) == 1;

        } catch (Exception e) {
            logger.error("检查互动用户失败, uid={}, friendUid={}, error={}", uid, friendUid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查任务完成情况并发放奖励
     */
    private void checkAndRewardDailyTask(String activityId, String uid, FatePlazaVO.DailyTaskInfo taskInfo, String dateStr) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            int gender = actorData.getFb_gender() == 2 ? 2 : 1;

            boolean taskInfoUpdated = false;

            // 检查发送纸条任务
            if (taskInfo.getSendNoteCount() == DAILY_TASK_SEND_NOTE_TARGET && taskInfo.getIsGetSendNoteReward() == 0) {
                handleRes(uid, gender == 1 ? REWARD_SEND_NOTE_KEY : REWARD_FEMALE_SEND_NOTE_KEY);
                taskInfo.setIsGetSendNoteReward(1);
                taskInfoUpdated = true;
                logger.info("发送纸条任务奖励发放成功, uid={}, gender={}", uid, gender);
            }

            // 检查打招呼任务
            if (taskInfo.getGreetAids() != null && taskInfo.getGreetAids().size() == DAILY_TASK_GREET_TARGET && taskInfo.getIsGetGreetAidsReward() == 0) {
                handleRes(uid, gender == 1 ? REWARD_GREET_KEY : REWARD_FEMALE_GREET_KEY);
                taskInfo.setIsGetGreetAidsReward(1);
                taskInfoUpdated = true;
                logger.info("打招呼任务奖励发放成功, uid={}, gender={}", uid, gender);
            }

            // 检查添加好友任务
            if (taskInfo.getSuccessAddFriendAids() != null && taskInfo.getSuccessAddFriendAids().size() == DAILY_TASK_ADD_FRIEND_TARGET && taskInfo.getIsGetSuccessAddFriendAidsReward() == 0) {
                handleRes(uid, gender == 1 ? REWARD_ADD_FRIEND_KEY : REWARD_FEMALE_ADD_FRIEND_KEY);
                taskInfo.setIsGetSuccessAddFriendAidsReward(1);
                taskInfoUpdated = true;
                logger.info("添加好友任务奖励发放成功, uid={}, gender={}", uid, gender);
            }

            // 如果任务信息有更新，需要重新保存
            if (taskInfoUpdated) {
                String dailyActivityId = getDailyHashActivityId(activityId, dateStr);
                saveDailyTaskInfo(dailyActivityId, uid, taskInfo);
            }

        } catch (Exception e) {
            logger.error("检查任务完成情况失败, uid={}, error={}", uid, e.getMessage(), e);
        }
    }


    /**
     * 更新每日任务发送纸条数据
     */
    private void updateDailyTaskSendNote(String activityId, String uid, String dateStr) {
        try {
            String dailyActivityId = getDailyHashActivityId(activityId, dateStr);
            FatePlazaVO.DailyTaskInfo taskInfo = getDailyTaskInfo(activityId, uid, dateStr);

            // 增加发送纸条计数
            taskInfo.setSendNoteCount(taskInfo.getSendNoteCount() + 1);

            // 保存更新后的数据
            saveDailyTaskInfo(dailyActivityId, uid, taskInfo);

            // 检查任务完成情况并发放奖励
            checkAndRewardDailyTask(activityId, uid, taskInfo, dateStr);

            logger.info("更新每日任务发送纸条成功, uid={}, count={}", uid, taskInfo.getSendNoteCount());

        } catch (Exception e) {
            logger.error("更新每日任务发送纸条失败, uid={}, error={}", uid, e.getMessage(), e);
        }
    }

    /**
     * 更新每日任务打招呼数据
     */
    private void updateDailyTaskGreet(String activityId, String uid, String aid, String dateStr) {
        try {
            String dailyActivityId = getDailyHashActivityId(activityId, dateStr);
            FatePlazaVO.DailyTaskInfo taskInfo = getDailyTaskInfo(activityId, uid, dateStr);

            // 添加到打招呼用户集合
            if (taskInfo.getGreetAids() == null) {
                taskInfo.setGreetAids(new HashSet<>());
            }
            taskInfo.getGreetAids().add(aid);

            // 保存更新后的数据
            saveDailyTaskInfo(dailyActivityId, uid, taskInfo);

            // 检查任务完成情况并发放奖励
            checkAndRewardDailyTask(activityId, uid, taskInfo, dateStr);

            logger.info("更新每日任务打招呼成功, uid={}, aid={}, count={}", uid, aid, taskInfo.getGreetAids().size());

        } catch (Exception e) {
            logger.error("更新每日任务打招呼失败, uid={}, aid={}, error={}", uid, aid, e.getMessage(), e);
        }
    }


    /**
     * 更新友谊任务数据
     */
    private void updateFriendshipTask(String activityId, String uid, String aid, String dateStr) {
        try {
            // 检查是否是通过交友广场互动的用户
            if (!isInteractedUser(activityId, uid, aid) || !friendsListRedis.isFriend(uid, aid)) {
                return;
            }
            String friendshipActivityId = getFriendshipTaskActivityId(activityId);
            String friendIndex = MatchUtils.generateFriendIndex(uid, aid);
            FatePlazaVO.FriendshipTaskInfo taskInfo = getFriendshipTaskInfo(activityId, friendIndex);

            if (taskInfo.getDaySet() != null && taskInfo.getDaySet().contains(dateStr)) {
                return;
            }

            // 确定用户在friendIndex中的位置
            List<String> friendPair = MatchUtils.getABFriend(uid, aid);
            boolean isFirstUser = uid.equals(friendPair.get(0));

            // 增加发送消息计数
            if (isFirstUser) {
                taskInfo.setFirstSendMsgCount(taskInfo.getFirstSendMsgCount() + 1);
            } else {
                taskInfo.setSecondSendMsgCount(taskInfo.getSecondSendMsgCount() + 1);
            }

            // 检查是否达到当天打卡条件（双方各发送5条消息）
            if (taskInfo.getFirstSendMsgCount() >= 5 && taskInfo.getSecondSendMsgCount() >= 5) {
                if (taskInfo.getDaySet() == null) {
                    taskInfo.setDaySet(new HashSet<>());
                }

                // 如果今天还没有打卡，则添加打卡记录
                if (!taskInfo.getDaySet().contains(dateStr)) {
                    taskInfo.getDaySet().add(dateStr);

                    // 重置当日消息计数
                    taskInfo.setFirstSendMsgCount(0);
                    taskInfo.setSecondSendMsgCount(0);

                    // 检查打卡天数奖励
                    checkAndRewardFriendshipTask(activityId, uid, aid, taskInfo.getDaySet().size());
                }

            }

            // 保存更新后的数据
            saveFriendshipTaskInfo(friendshipActivityId, friendIndex, taskInfo);

            logger.info("更新友谊任务成功, uid={}, aid={}, friendIndex={}, dayCount={} firstSendMsgCount={} secondSendMsgCount={}",
                    uid, aid, friendIndex, taskInfo.getDaySet() != null ? taskInfo.getDaySet().size() : 0,
                    taskInfo.getFirstSendMsgCount(), taskInfo.getSecondSendMsgCount());

        } catch (Exception e) {
            logger.error("更新友谊任务失败, uid={}, aid={}, error={}", uid, aid, e.getMessage(), e);
        }
    }

    /**
     * 获取友谊任务信息
     */
    private FatePlazaVO.FriendshipTaskInfo getFriendshipTaskInfo(String activityId, String friendIndex) {
        try {
            String friendshipActivityId = getFriendshipTaskActivityId(activityId);
            String taskInfoJson = activityCommonRedis.getCommonHashStrValue(friendshipActivityId, friendIndex);

            if (StringUtils.isEmpty(taskInfoJson)) {
                // 创建新的友谊任务信息
                FatePlazaVO.FriendshipTaskInfo taskInfo = new FatePlazaVO.FriendshipTaskInfo();
                taskInfo.setFriendIndex(friendIndex);
                taskInfo.setDaySet(new HashSet<>());
                taskInfo.setFirstSendMsgCount(0);
                taskInfo.setSecondSendMsgCount(0);
                return taskInfo;
            }

            return JSONObject.parseObject(taskInfoJson, FatePlazaVO.FriendshipTaskInfo.class);

        } catch (Exception e) {
            logger.error("获取友谊任务信息失败, friendIndex={}, error={}", friendIndex, e.getMessage(), e);
            // 返回默认的任务信息
            FatePlazaVO.FriendshipTaskInfo taskInfo = new FatePlazaVO.FriendshipTaskInfo();
            taskInfo.setFriendIndex(friendIndex);
            taskInfo.setDaySet(new HashSet<>());
            taskInfo.setFirstSendMsgCount(0);
            taskInfo.setSecondSendMsgCount(0);
            return taskInfo;
        }
    }

    /**
     * 保存友谊任务信息
     */
    private void saveFriendshipTaskInfo(String friendshipActivityId, String friendIndex, FatePlazaVO.FriendshipTaskInfo taskInfo) {
        try {
            String taskInfoJson = JSONObject.toJSONString(taskInfo);
            activityCommonRedis.setCommonHashData(friendshipActivityId, friendIndex, taskInfoJson);
        } catch (Exception e) {
            logger.error("保存友谊任务信息失败, friendIndex={}, taskInfo={}, error={}", friendIndex, taskInfo, e.getMessage(), e);
        }
    }

    /**
     * 检查友谊任务完成情况并发放奖励
     */
    private void checkAndRewardFriendshipTask(String activityId, String uid, String aid, int dayCount) {
        try {
            if (TOTAL_REWARD_LIST.contains(dayCount)) {
                // 发送奖励
                String sendKey = DAY_MSG_KEY_MAP.get(dayCount);
                if (!StringUtils.isEmpty(sendKey)) {
                    handleRes(uid, sendKey);
                    handleRes(aid, sendKey);
                }
                logger.info("友谊任务奖励发放成功, dayCount={}, sender={}, receiver={}", dayCount, uid, aid);
            }

        } catch (Exception e) {
            logger.error("检查友谊任务完成情况失败, uid={}, aid={}, dayCount={}, error={}", uid, aid, dayCount, e.getMessage(), e);
        }
    }

    /**
     * 获取好友申请列表
     */
    private List<FriendApplyData> getFriendApplyList(OtherRankingActivityData activityData, String uid, int friendshipType, int offset, int pageSize) {
        try {
            // 根据friendshipType确定查询条件
            String queryUid = friendshipType == 1 ? uid : null;  // 我发送的
            String queryAid = friendshipType == 2 ? uid : null;  // 我收到的

            return friendApplyDao.findFriendshipTaskList(queryUid, queryAid, activityData.getStartTime(), offset, pageSize);

        } catch (Exception e) {
            logger.error("获取好友申请列表失败, uid={}, friendshipType={}, error={}", uid, friendshipType, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建FriendshipTaskVO
     */
    private FatePlazaVO.FriendshipTaskVO buildFriendshipTaskVO(String uid, String friendUid, FriendApplyData applyData, FatePlazaVO.FriendshipTaskInfo taskInfo) {
        try {
            ActorData myData = actorDao.getActorDataFromCache(uid);
            ActorData friendData = actorDao.getActorDataFromCache(friendUid);

            if (myData == null || friendData == null) {
                return null;
            }

            FatePlazaVO.FriendshipTaskVO taskVO = new FatePlazaVO.FriendshipTaskVO();
            taskVO.setMyHead(ImageUrlGenerator.generateRoomUserUrl(myData.getHead()));
            taskVO.setAidHead(ImageUrlGenerator.generateRoomUserUrl(friendData.getHead()));
            taskVO.setMsg(applyData.getMsg());
            taskVO.setSignInDays(taskInfo.getDaySet() != null ? taskInfo.getDaySet().size() : 0);
            taskVO.setAid(friendUid);
            taskVO.setAidName(friendData.getName());

            return taskVO;

        } catch (Exception e) {
            logger.error("构建FriendshipTaskVO失败, uid={}, friendUid={}, error={}", uid, friendUid, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理资源发放（带埋点标题）
     */
    private void handleRes(String uid, String resKey, String eventTitle) {
        try {
            resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
            logger.info("友谊任务资源发放成功, uid={}, resKey={}, eventTitle={}", uid, resKey, eventTitle);
        } catch (Exception e) {
            logger.error("友谊任务资源发放失败, uid={}, resKey={}, error={}", uid, resKey, e.getMessage(), e);
        }
    }

}
