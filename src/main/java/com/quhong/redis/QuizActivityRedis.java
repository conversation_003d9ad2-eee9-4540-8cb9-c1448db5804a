package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/9/5
 */
@Lazy
@Component
public class QuizActivityRedis {

    private static final Logger logger = LoggerFactory.getLogger(QuizActivityRedis.class);

    // 最大分数
    private static final Integer MAX_SCORE = 100;
    // 随机答题
    private static final Integer RANDOM_ANSWER_TYPE = 0;
    // 固定答题
    private static final Integer FIXED_ANSWER_TYPE = 1;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 获取题库题目数量
     */
    public int getQuestionNumInRedis(String uid, Integer activityId, Integer answerType, Integer slang) {
        if (RANDOM_ANSWER_TYPE.equals(answerType)) {
            return getQuestionNumFromSet(uid, activityId, slang);
        } else {
            return getQuestionNumFromList(uid, activityId, slang);
        }
    }

    public int getQuestionNumFromSet(String uid, Integer activityId, Integer slang) {
        try {
            return Objects.requireNonNull(clusterTemplate.opsForSet().size(getQuestionSetKey(uid, activityId, slang))).intValue();
        } catch (Exception e) {
            logger.error("get question num in redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return 0;
        }
    }

    public int getQuestionNumFromList(String uid, Integer activityId, Integer slang) {
        try {
            return Objects.requireNonNull(clusterTemplate.opsForList().size(getQuestionListKey(uid, activityId, slang))).intValue();
        } catch (Exception e) {
            logger.error("get question num in redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 将题库题目保存的redis中
     */
    public void saveQuestionInRedis(String uid, Integer activityId, Integer tid, Integer answerType, Integer slang) {
        if (RANDOM_ANSWER_TYPE.equals(answerType)) {
            saveQuestionInSet(uid, activityId, tid, slang);
        } else {
            saveQuestionInList(uid, activityId, tid, slang);
        }
    }

    public void saveQuestionInSet(String uid, Integer activityId, Integer tid, Integer slang) {
        String questionGroupKey = getQuestionSetKey(uid, activityId, slang);
        try {
            clusterTemplate.opsForSet().add(questionGroupKey, String.valueOf(tid));
            clusterTemplate.expire(questionGroupKey, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save question in redis error. uid={} activityId={} tid={} {}", uid, activityId, tid, e.getMessage(), e);
        }
    }

    public void saveQuestionInList(String uid, Integer activityId, Integer tid, Integer slang) {
        String questionGroupKey = getQuestionListKey(uid, activityId, slang);
        try {
            clusterTemplate.opsForList().leftPush(questionGroupKey, String.valueOf(tid));
            clusterTemplate.expire(questionGroupKey, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save question in redis error. uid={} activityId={} tid={} {}", uid, activityId, tid, e.getMessage(), e);
        }
    }

    /**
     * 从redis中随机获取题目
     */
    public List<Integer> getQuestionFromRedis(String uid, Integer activityId, Integer questionNum, Integer answerType, Integer slang) {
        if (RANDOM_ANSWER_TYPE.equals(answerType)) {
            return getQuestionFromSet(uid, activityId, questionNum, slang);
        } else {
            return getQuestionFromList(uid, activityId, questionNum, slang);
        }
    }

    public List<Integer> getQuestionFromSet(String uid, Integer activityId, Integer questionNum, Integer slang) {
        List<Integer> tidList = new ArrayList<>();
        try {
            List<String> strTidList = clusterTemplate.opsForSet().pop(getQuestionSetKey(uid, activityId, slang), questionNum);
            if (!CollectionUtils.isEmpty(strTidList)) {
                for (String strTid: strTidList) {
                    if (!StringUtils.isEmpty(strTid)) {
                        int tid = Integer.parseInt(strTid);
                        tidList.add(tid);
                    }
                }
            }
            return tidList;
        } catch (Exception e) {
            logger.error("get question from redis error. uid={} activityId={} questionNum={} {}", uid, activityId, questionNum, e.getMessage(), e);
            return tidList;
        }
    }

    public List<Integer> getQuestionFromList(String uid, Integer activityId, Integer questionNum, Integer slang) {
        List<Integer> tidList = new ArrayList<>();
        try {
            for (int i = 0; i < questionNum; i++) {
                String strTid = clusterTemplate.opsForList().rightPop(getQuestionListKey(uid, activityId, slang));
                if (!StringUtils.isEmpty(strTid)) {
                    int tid = Integer.parseInt(strTid);
                    tidList.add(tid);
                }
            }
            return tidList;
        } catch (Exception e) {
            logger.error("get question from redis error. uid={} activityId={} questionNum={} {}", uid, activityId, questionNum, e.getMessage(), e);
            return tidList;
        }
    }

    /**
     * 保存用户排名
     */
    public void saveQuizRanking(String uid, Integer activityId, Integer score, Integer time, Integer ctime, Integer userLevel) {
        try {
            String rankingKey = getRankingKey(activityId);
            double rankingScore = getRankingScore(score, time, ctime, userLevel);
            clusterTemplate.opsForZSet().add(rankingKey, uid, rankingScore);
            clusterTemplate.expire(rankingKey, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save quiz ranking in redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
        }
    }

    public void saveQuizRanking(String uid, Integer activityId, double rankingScore) {
        try {
            String rankingKey = getRankingKey(activityId);
            clusterTemplate.opsForZSet().add(rankingKey, uid, rankingScore);
            clusterTemplate.expire(rankingKey, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save quiz ranking in redis error. uid={} activityId={} rankingScore={} {}", uid, activityId, rankingScore, e.getMessage(), e);
        }
    }

    public double getQuizRankingScore(String uid, Integer activityId) {
        try {
            String rankingKey = getRankingKey(activityId);
            Double score = clusterTemplate.opsForZSet().score(rankingKey, uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("get quiz ranking score error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取用户排名 (0为找不到)
     */
    public Integer getQuizRanking(String uid, Integer activityId) {
        try {
            String rankingKey = getRankingKey(activityId);
            Long rank = clusterTemplate.opsForZSet().rank(rankingKey, uid);
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("get quiz ranking from redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return 0;
        }
    }

    public int getQuizJoinNum(Integer activityId) {
        try {
            String rankingKey = getRankingKey(activityId);
            Long joinNum = clusterTemplate.opsForZSet().size(rankingKey);
            return joinNum != null ? joinNum.intValue() : 0;
        } catch (Exception e) {
            logger.error("get quiz join num error. activityId={} {}", activityId, e.getMessage(), e);
            return 0;
        }
    }

    public void saveGainReward(String uid, Integer activityId, String jsonValue) {
        try {
            String key = getGainRewardKey(activityId, uid);
            clusterTemplate.opsForZSet().add(key, jsonValue, DateHelper.getNowSeconds());
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save quiz gain reward in redis error. uid={} activityId={} reward={} {}", uid, activityId, jsonValue, e.getMessage(), e);
        }
    }

    public Set<String> getGainReward(String uid, Integer activityId) {
        try {
            String key = getGainRewardKey(activityId, uid);
            return clusterTemplate.opsForZSet().range(key, 0, 3);
        } catch (Exception e) {
            logger.error("get quiz gain reward from redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return null;
        }
    }

    public void addSubmitQuizCount(String uid, Integer activityId) {
        try {
            String key = getSubmitQuizCountKey(uid, activityId);
            clusterTemplate.opsForValue().increment(key);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_HOURS_ONE, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("increment submit quiz count in redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
        }
    }

    public int getSubmitQuizCount(String uid, Integer activityId) {
        int count = 0;
        try {
            String key = getSubmitQuizCountKey(uid, activityId);
            String strValue = clusterTemplate.opsForValue().get(key);
            if (!StringUtils.isEmpty(strValue)) {
                count = Integer.parseInt(strValue);
            }
            return count;
        } catch (Exception e) {
            logger.error("get submit quiz count from redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return 0;
        }
    }

    public void reduceSubmitQuizCount(String uid, Integer activityId) {
        try {
            String key = getSubmitQuizCountKey(uid, activityId);
            clusterTemplate.opsForValue().decrement(key);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_HOURS_ONE, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error("decrement submit quiz count in redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
        }
    }

    public void saveDeviceQuizCount(Integer activityId, String tn_id) {
        try {
            String key = getDeviceQuizCountKey(tn_id, activityId);
            clusterTemplate.opsForValue().increment(key);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("increment device quiz count in redis error. tn_id={} activityId={} {}", tn_id, activityId, e.getMessage(), e);
        }
    }

    public int getDeviceQuizCount(Integer activityId, String tn_id) {
        int count = 0;
        try {
            String key = getDeviceQuizCountKey(tn_id, activityId);
            String strValue = clusterTemplate.opsForValue().get(key);
            if (!StringUtils.isEmpty(strValue)) {
                count = Integer.parseInt(strValue);
            }
            return count;
        } catch (Exception e) {
            logger.error("get device quiz count from redis error. tn_id={} activityId={} {}", tn_id, activityId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 计算排行榜分值 (分越大排行名次越低)
     */
    private Double getRankingScore(Integer score, Integer time, Integer ctime, Integer userLevel) {
        return new BigDecimal(((MAX_SCORE - score) * 100000000L + time) + "." + (ctime * 10000L + (10000 - userLevel))).doubleValue();
    }

    private String getQuestionSetKey(String uid, Integer activityId, Integer slang) {
        return "set:questionGroup:" + DateHelper.ARABIAN.formatDateInDay() + "_" + activityId + "_" + uid + "_" + slang;
    }

    private String getQuestionListKey(String uid, Integer activityId, Integer slang) {
        return "list:questionGroup:" + DateHelper.ARABIAN.formatDateInDay() + "_" + activityId + "_" + uid+ "_" + slang;
    }

    private String getDeviceQuizCountKey(String tn_id, Integer activityId) {
        return "str:deviceQuizCount:" + DateHelper.ARABIAN.formatDateInDay()  + "_" + activityId + "_" + tn_id;
    }

    private String getSubmitQuizCountKey(String uid, Integer activityId) {
        return "str:submitQuizCount" + "_" + activityId + "_" + uid;
    }

    private String getRankingKey(Integer activityId) {
        return "zset:ranking:" + activityId;
    }

    private String getGainRewardKey(Integer activityId, String uid) { return "zset:gainReward:" + activityId + "_" + uid; }
}
