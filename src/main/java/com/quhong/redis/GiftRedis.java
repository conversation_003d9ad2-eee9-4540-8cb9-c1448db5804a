package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
@Lazy
@Component
public class GiftRedis {
    private static final Logger logger = LoggerFactory.getLogger(GiftRedis.class);

//    @Resource(name = DataRedisBean.GIFT)
//    private StringRedisTemplate giftRedis;
//
//    public RedisGiftData getGiftFromRedis(int giftId) {
//        try {
//            Map<Object, Object> entries = giftRedis.opsForHash().entries(String.valueOf(giftId));
//            return JSON.parseObject(JSON.toJSONString(entries), RedisGiftData.class);
//        } catch (Exception e) {
//            logger.error("get gift from redis error {}", e.getMessage());
//            return null;
//        }
//    }

    public static class RedisGiftData {
        @JSONField(name = "rid")
        private int giftId;
        @JSONField(name = "gname")
        private String giftName;
        @JSONField(name = "gicon")
        private String giftIcon;
        @JSONField(name = "zipInfo")
        private String zipInfo;
        @JSONField(name = "gtype")
        private int giftType; // // 1 钻石礼物，2 金币礼物，3 vip礼物
        @JSONField(name = "price")
        private int giftPrice;
        @JSONField(name = "gtime")
        private int giftTime;
        @JSONField(name = "gatype")
        private int gatype;
        @JSONField(name = "gplatform")
        private int gPlatform;

        public int getGiftId() {
            return giftId;
        }

        public void setGiftId(int giftId) {
            this.giftId = giftId;
        }

        public String getGiftName() {
            return giftName;
        }

        public void setGiftName(String giftName) {
            this.giftName = giftName;
        }

        public String getGiftIcon() {
            return giftIcon;
        }

        public void setGiftIcon(String giftIcon) {
            this.giftIcon = giftIcon;
        }

        public String getZipInfo() {
            return zipInfo;
        }

        public void setZipInfo(String zipInfo) {
            this.zipInfo = zipInfo;
        }

        public int getGiftType() {
            return giftType;
        }

        public void setGiftType(int giftType) {
            this.giftType = giftType;
        }

        public int getGiftPrice() {
            return giftPrice;
        }

        public void setGiftPrice(int giftPrice) {
            this.giftPrice = giftPrice;
        }

        public int getGiftTime() {
            return giftTime;
        }

        public void setGiftTime(int giftTime) {
            this.giftTime = giftTime;
        }

        public int getGatype() {
            return gatype;
        }

        public void setGatype(int gatype) {
            this.gatype = gatype;
        }

        public int getgPlatform() {
            return gPlatform;
        }

        public void setgPlatform(int gPlatform) {
            this.gPlatform = gPlatform;
        }
    }
}
