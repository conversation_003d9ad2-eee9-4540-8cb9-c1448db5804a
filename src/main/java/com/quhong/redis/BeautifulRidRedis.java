package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Lazy
@Component
public class BeautifulRidRedis {
    private static final Logger logger = LoggerFactory.getLogger(BeautifulRidRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public boolean isHideBeautifulRid(String rid) {
        try {
            return Boolean.TRUE.equals(clusterRedis.opsForSet().isMember(getHideBtfRidKey(), rid));
        } catch (Exception e) {
            logger.error("determine whether is hide beautiful rid error. rid={}", rid);
            return false;
        }
    }

    private String getHideBtfRidKey() {
        return "btf:rid:store:hide";
    }

}
