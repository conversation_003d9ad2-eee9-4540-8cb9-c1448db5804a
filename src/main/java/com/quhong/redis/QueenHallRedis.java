package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class QueenHallRedis {
    private static final Logger logger = LoggerFactory.getLogger(QueenHallRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 点赞女王key
     */
    public String getLikeQueenKey(String aid) {
        return "set:like_queen_" + aid;
    }

    /**
     * 获取喜欢女王数量
     */
    public int getLikeQueenSize(String aid) {
        try {
            Long likeSize = clusterTemplate.opsForSet().size(getLikeQueenKey(aid));
            return likeSize != null? likeSize.intValue():0;
        } catch (Exception e) {
            logger.info("getLikeQueenSize error uid={} e={}", aid, e);
            return 0;
        }
    }

    /**
     * uid 点赞 aid
     */
    public int addLikeQueen(String uid, String aid) {
        try {
            String likeQueenKey = getLikeQueenKey(aid);
            Long likeSize = clusterTemplate.opsForSet().add(likeQueenKey, uid);
            clusterTemplate.expire(likeQueenKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return likeSize != null? likeSize.intValue():0;
        } catch (Exception e) {
            logger.info("addLikeQueen error uid={} e={}", uid, e);
            return 0;
        }
    }

    /**
     * uid 是否已点赞 aid
     */
    public int isLikeQueen(String uid, String aid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getLikeQueenKey(aid), uid))?1:0;
        } catch (Exception e) {
            logger.info("isLikeQueen error uid={} e={}", uid, e);
            return 1;
        }
    }


    /**
     * 点赞评论
     */
    public String getLikeExpressKey(Integer expressId) {
        return "set:like_express_" + expressId;
    }


    public int getLikeExpressSize(Integer expressId) {
        try {
            Long likeExpressSize = clusterTemplate.opsForSet().size(getLikeExpressKey(expressId));
            return likeExpressSize != null? likeExpressSize.intValue():0;
        } catch (Exception e) {
            logger.info("getLikeExpressSize error expressId={} e={}", expressId, e);
            return 0;
        }
    }

    public int addLikeExpress(String uid, Integer expressId) {
        try {
            String likeExpressKey = getLikeExpressKey(expressId);
            Long likeExpressSize = clusterTemplate.opsForSet().add(likeExpressKey, uid);
            clusterTemplate.expire(likeExpressKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return likeExpressSize != null? likeExpressSize.intValue():0;
        } catch (Exception e) {
            logger.info("addLikeExpress error uid={} e={}  expressId={}", uid, e, expressId);
            return 0;
        }
    }


    public int isLikeExpress(String uid, Integer expressId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getLikeExpressKey(expressId), uid))?1:0;
        } catch (Exception e) {
            logger.info("isLikeExpress error uid={} e={}", uid, e);
            return 1;
        }
    }



}
