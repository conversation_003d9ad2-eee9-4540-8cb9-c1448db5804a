package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class TreasureRedis {
    private static final Logger logger = LoggerFactory.getLogger(TreasureRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String TREASURE_KEY = "treasure_game";
    private String rankingExpireDate = "";
    private String weeklyExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String poolSizeKey() {
        return "list:pool_size:"+ TREASURE_KEY;
    }

    public int getPoolSize() {
        try {
            Long poolSize = clusterTemplate.opsForList().size(poolSizeKey());
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getPoolSize error score={}", e.getMessage());
            return 0;
        }
    }


    public void initPoolSize(List<String> rewardConfigList) {
        try {
            clusterTemplate.opsForList().rightPushAll(poolSizeKey(), rewardConfigList);
            clusterTemplate.expire(poolSizeKey(), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("initPoolSize error clusterTemplate={}  score={}", clusterTemplate, e);
        }
    }


    // 抽奖
    public String drawTreasureKey() {
        try {
            String cardKey = clusterTemplate.opsForList().leftPop(poolSizeKey());
            return cardKey != null ? cardKey : "";
        } catch (Exception e) {
            logger.info("drawTreasureKey error e={}", e.getMessage());
            return "";
        }
    }



    /**
     *
     * @return key
     */
    private String getTreasureRankingKey(String dailyNum) {
        return "zset:TreasureRanking:" + dailyNum;
    }

    /**
     * 获取分数
     */
    public int getTreasureRankingScore(String aid, String dailyNum) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getTreasureRankingKey(dailyNum), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getTreasureRankingScore error aid={} dailyNum={}", aid, dailyNum, e);
            return 0;
        }
    }

    public void incrTreasureRankingScore(String aid, int score, String dailyNum) {
        try {
            String key = getTreasureRankingKey(dailyNum);
            int curScore = getTreasureRankingScore(aid, dailyNum);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrTreasureRankingScore aid={}  score={} total={} dailyNum={}", aid, score, rankScore, dailyNum);
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(rankingExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                rankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrTreasureRankingScore error aid={} giftId={} score={}", aid, score, e);
        }
    }



    /**
     * 获取排行榜
     *
     * @param dailyNum     排行榜日期
     * @param length     排行榜长度
     */
    public List<String> getTreasureRankingList(String dailyNum, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getTreasureRankingKey(dailyNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            logger.info("getTreasureRankingList aid={} score={} dailyNum={}", rangeWithScore.getValue(), rangeWithScore.getScore().longValue(), dailyNum);
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param dailyNum     排行榜日期
     * @param length     排行榜长度
     */
    public Map<String, Integer> getTreasureRankingMap(String dailyNum, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getTreasureRankingKey(dailyNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getTreasureRank(String aid, String dailyNum) {
        try {
            String key = getTreasureRankingKey(dailyNum);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getTreasureRank error aid={}", aid, e);
            return 0;
        }
    }


    /**
     * 周抽奖次数
     * @return key
     */
    private String getTreasureSaturdayKey(String saturday) {
        return "zset:TreasureSaturday:" + saturday;
    }

    /**
     * 获取分数
     */
    public int getTreasureSaturdayScore(String aid, String saturday) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getTreasureSaturdayKey(saturday), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getTreasureSaturdayScore error aid={} dailyNum={}", aid, saturday, e);
            return 0;
        }
    }

    public void incrTreasureSaturdayScore(String aid, int score, String saturday) {
        try {
            String key = getTreasureSaturdayKey(saturday);
            clusterTemplate.opsForZSet().incrementScore(key, aid, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(weeklyExpireDate)) {
                clusterTemplate.expire(key, 180, TimeUnit.DAYS);
                weeklyExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrTreasureSaturdayScore error aid={} giftId={} score={}", aid, score, e);
        }
    }

    /**
     * 获取带分数周排行榜
     *
     * @param saturday     排行榜日期
     */
    public Map<String, Integer> getTreasureWeeklyMap(String saturday) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getTreasureSaturdayKey(saturday);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, 800, 9999999);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }



}
