package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class NewYearRedis {
    private static final Logger logger = LoggerFactory.getLogger(NewYearRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String ACTIVITY_NAME = "newYear";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String messageKey(String momentId) {
        return "str:" + ACTIVITY_NAME + ":message:"+ momentId;
    }


    public String getMomentMessage(String momentId) {
        try {
            String message = clusterTemplate.opsForValue().get(messageKey(momentId));
            return message != null ? message: "";
        } catch (Exception e) {
            logger.info("getMomentMessage error recordId={}, e={}", momentId, e);
            return "";
        }
    }

    public void setMomentMessage(String momentId, String message) {
        try {
            clusterTemplate.opsForValue().set(messageKey(momentId), message);
            clusterTemplate.expire(messageKey(momentId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setMomentMessage error  momentId={}, e={}", momentId, e);
        }
    }

}
