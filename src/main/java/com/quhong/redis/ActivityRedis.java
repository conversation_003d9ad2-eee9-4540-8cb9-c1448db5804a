package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/9
 */
@Component
public class ActivityRedis {

    private static final Logger logger = LoggerFactory.getLogger(ActivityRedis.class);

    private static final int DEFAULT_EXPIRE_DAYS = 30;
    private static final Set<String> HAS_EXPIRE_TIME_KEY_SET = new HashSet<>();

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    protected StringRedisTemplate clusterTemplate;

    protected long getRankingScore(String key, String aid) {
        try {
            Double score = clusterTemplate.opsForZSet().score(key, aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRankingScore error. key={} aid={} {}", key, aid, e.getMessage(), e);
            return 0;
        }
    }

    protected int getRankingRank(String key, String aid) {
        try {
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, aid);
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("getRankingRank error. key={} aid={} {}", key, aid, e.getMessage(), e);
            return 0;
        }
    }

    protected int getRankingRank(String key, String aid, boolean reverse) {
        try {
            Long rank;
            if (reverse) {
                rank = clusterTemplate.opsForZSet().reverseRank(key, aid);
            } else {
                rank = clusterTemplate.opsForZSet().rank(key, aid);
            }
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("getRankingRank error. key={} aid={} {}", key, aid, e.getMessage(), e);
            return 0;
        }
    }

    protected long incrRankingScore(String key, String aid, int score) {
        try {
            long curScore = getRankingScore(key, aid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            setExpireTime(key);
            return (long) rankScore;
        } catch (Exception e) {
            logger.error("incrRankingScore error. key={} aid={} score={} {}", key, aid, score, e.getMessage(), e);
            return 0;
        }
    }

    protected int getRankingSizeByScoreRange(String key, double minScore, double maxScore) {
        try {
            Long count = clusterTemplate.opsForZSet().count(key, minScore, maxScore);
            return count == null ? 0 : count.intValue();
        } catch (Exception e) {
            logger.error("getRankingSizeByScoreRange error. key={} minScore={} maxScore={} {}", key, maxScore, maxScore, e.getMessage(), e);
            return 0;
        }
    }
    protected void setRankingScore(String key, String aid, double score) {
        try {
            clusterTemplate.opsForZSet().add(key, aid, score);
            setExpireTime(key);
        } catch (Exception e) {
            logger.error("setRankingScore error. key={} aid={} score={} {}", key, aid, score, e.getMessage(), e);
        }
    }

    protected Map<String, Long> getRankingMap(String key, int length) {
        return getRankingMap(key, length, true);
    }

    protected Map<String, Long> getRankingMap(String key, int length, boolean reverse) {
        try {
            Map<String, Long> linkedRankMap = new LinkedHashMap<>();
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores;
            if (reverse) {
                rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
            } else {
                rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, 0, length - 1);
            }
            if (null == rangeWithScores) {
                return linkedRankMap;
            }
            for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                    continue;
                }
                linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
            }
            return linkedRankMap;
        } catch (Exception e) {
            logger.error("getRankingMap error. key={} length={} {}", key, length, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    protected Map<String, Long> getRankingMap(String key, int start, int end, boolean reverse) {
        try {
            Map<String, Long> linkedRankMap = new LinkedHashMap<>();
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores;
            if (reverse) {
                rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, start, end);
            } else {
                rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, start, end);
            }
            if (null == rangeWithScores) {
                return linkedRankMap;
            }
            for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                    continue;
                }
                linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
            }
            return linkedRankMap;
        } catch (Exception e) {
            logger.error("getRankingMap error. key={} start={} end={} {}", key, start, end, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    protected int getRankingSize(String key) {
        try {
            Long size = clusterTemplate.opsForZSet().size(key);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("getRankingSize error. key={}", key, e);
            return 0;
        }
    }

    protected long incHashValue(String key, String hashKey, long hashValue) {
        try {
            Long increment = clusterTemplate.opsForHash().increment(key, hashKey, hashValue);
            setExpireTime(key);
            return increment;
        } catch (Exception e) {
            logger.error("incHashValue error. key={} hashKey={} hashValue={} {}", key, hashKey, hashValue, e.getMessage(), e);
            return 0;
        }
    }

    protected void setHashValue(String key, String hashKey, String hashValue) {
        try {
            clusterTemplate.opsForHash().put(key, hashKey, hashValue);
            setExpireTime(key);
        } catch (Exception e) {
            logger.error("setHashValue error. key={} hashKey={} hashValue={} {}", key, hashKey, hashValue, e.getMessage(), e);
        }
    }

    protected String getHashValue(String key, String hashKey) {
        try {
            return (String) clusterTemplate.opsForHash().get(key, hashKey);
        } catch (Exception e) {
            logger.error("getHashValue error. key={} hashKey={} {}", key, hashKey, e.getMessage(), e);
            return "";
        }
    }

    protected Set<String> getHashKeys(String key) {
        try {
            Set<Object> keys = clusterTemplate.opsForHash().keys(key);
            if (CollectionUtils.isEmpty(keys)) {
                return null;
            }
            return keys.stream().map(Object::toString).collect(Collectors.toSet());
        } catch (Exception e) {
            logger.error("getHashKeys error. key={} {}", key, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    protected long getHashLongValue(String key, String hashKey) {
        try {
            String strValue = getHashValue(key, hashKey);
            return StringUtils.hasLength(strValue) ? Long.parseLong(strValue) : 0;
        } catch (Exception e) {
            logger.error("getHashLongValue error. key={} hashKey={} {}", key, hashKey, e.getMessage(), e);
            return 0;
        }
    }

    protected int getHashIntValue(String key, String hashKey) {
        try {
            String strValue = getHashValue(key, hashKey);
            return StringUtils.hasLength(strValue) ? Integer.parseInt(strValue) : 0;
        } catch (Exception e) {
            logger.error("getHashIntValue error. key={} hashKey={} {}", key, hashKey, e.getMessage(), e);
            return 0;
        }
    }

    protected Map<String, String> getHashMap(String key) {
        try {
            Map<String, String> result = new HashMap<>();
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(key);
            entries.forEach((k, v) -> result.put(String.valueOf(k), String.valueOf(v)));
            return result;
        } catch (Exception e) {
            logger.error("getHashMap error. key={} {}", key, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    protected void addSetValue(String key, String value) {
        try {
            clusterTemplate.opsForSet().add(key, value);
            setExpireTime(key);
        } catch (Exception e) {
            logger.error("addSetValue error. key={} value={} {}", key, value, e.getMessage(), e);
        }
    }

    protected void addSetValues(String key, Set<String> valueSet) {
        try {
            clusterTemplate.opsForSet().add(key, valueSet.toArray(new String[0]));
            setExpireTime(key);
        } catch (Exception e) {
            logger.error("addSetValue error. key={} valueSet={} {}", key, Arrays.toString(valueSet.toArray()), e.getMessage(), e);
        }
    }

    protected void removeSetValue(String key, String value) {
        try {
            clusterTemplate.opsForSet().remove(key, value);
        } catch (Exception e) {
            logger.error("removeSetValue error. key={} value={} {}", key, value, e.getMessage(), e);
        }
    }

    protected int getSetSize(String key) {
        try {
            Long size = clusterTemplate.opsForSet().size(key);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("getSetSize error. key={} {}", key, e.getMessage(), e);
            return 0;
        }
    }

    protected Set<String> getSetValues(String key) {
        try {
            return clusterTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getSetValues error. key={} {}", key, e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    protected boolean isSetMember(String key, String value) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(key, value));
        } catch (Exception e) {
            logger.error("isSetMember error. key={} value={} {}", key, value, e.getMessage(), e);
            return false;
        }
    }

    protected int listLeftPush(String key, String value) {
        try {
            Long listSize = clusterTemplate.opsForList().leftPush(key, value);
            setExpireTime(key);
            return listSize != null ? listSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("listPush error. key={} value={} {}", key, value, e.getMessage(), e);
            return 0;
        }
    }

    protected int listRightPush(String key, String value) {
        try {
            Long listSize = clusterTemplate.opsForList().rightPush(key, value);
            setExpireTime(key);
            return listSize != null ? listSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("listPush error. key={} value={} {}", key, value, e.getMessage(), e);
            return 0;
        }
    }

    public List<String> listRange(String key, int start, int end) {
        try {
            return clusterTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            logger.error("listRange error. key={} start={} end={} {}", key, start, end, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    protected void listTrim(String key, int start, int end) {
        try {
            clusterTemplate.opsForList().trim(key, start, end);
        } catch (Exception e) {
            logger.error("listTrim error. key={} start={} end={} {}", key, start, end, e.getMessage(), e);
        }
    }

    protected void listRightPushAll(String key, Collection<String> collection) {
        try {
            clusterTemplate.opsForList().rightPushAll(key, collection);
            setExpireTime(key);
        } catch (Exception e) {
            logger.error("listRightPushAll error. key={} value={} {}", key, Arrays.toString(collection.toArray()), e.getMessage(), e);
        }
    }

    protected String listLeftPop(String key) {
        try {
            String value = clusterTemplate.opsForList().leftPop(key);
            return value != null ? value : "";
        } catch (Exception e) {
            logger.error("listLeftPop error. key={} {}", key, e.getMessage(), e);
            return "";
        }
    }

    protected void setValue(String key, String value) {
        try {
            clusterTemplate.opsForValue().set(key, value, DEFAULT_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setValue error. key={} value={} {}", key, value, e.getMessage(), e);
        }
    }

    protected String getValue(String key) {
        try {
            return clusterTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.error("getValue error. key={} {}", key, e.getMessage(), e);
            return "";
        }
    }

    protected int getListSize(String key) {
        try {
            Long size = clusterTemplate.opsForList().size(key);
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("getListSize error. key={} {}", key, e.getMessage(), e);
            return 0;
        }
    }

    protected boolean delKey(String key) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.delete(key));
        } catch (Exception e) {
            logger.error("delKey error. key={} {}", key, e.getMessage(), e);
            return false;
        }
    }

    private void setExpireTime(String key) {
        if (!HAS_EXPIRE_TIME_KEY_SET.contains(key)) {
            clusterTemplate.expire(key, DEFAULT_EXPIRE_DAYS, TimeUnit.DAYS);
            HAS_EXPIRE_TIME_KEY_SET.add(key);
        }
    }

    protected List<String> listLeftPop(String key, int num) {
        try {
            List<String> destList = new ArrayList<>(num);
            for (int i = 0; i < num; i++) {
                String value = clusterTemplate.opsForList().leftPop(key);
                destList.add(value);
            }
            return destList;
        } catch (Exception e) {
            logger.error("listLeftPop error. key={} num={} {}", key, num, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
