package com.quhong.redis;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/13
 */
@Component
public class LuxuryCarRedis extends ActivityRedis {

    // ================================ 用户数据 ================================

    public void setUserData(String activityId, String uid, String hashKey, String hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, hashValue);
    }

    public void setUserData(String activityId, String uid, String hashKey, int hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, String.valueOf(hashValue));
    }

    public long incUserData(String activityId, String uid, String hashKey, int incValue) {
        return incHashValue(getUserDataKey(activityId, uid), hashKey, incValue);
    }

    public Map<String, Long> getUserData(String activityId, String uid) {
        Map<String, String> hashMap = getHashMap(getUserDataKey(activityId, uid));
        return hashMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> Long.parseLong(entry.getValue())));
    }

    public long getUserData(String activityId, String uid, String hashKey) {
        return getHashIntValue(getUserDataKey(activityId, uid), hashKey);
    }

    private String getUserDataKey(String activityId, String uid) {
        return "hash:userData_" + activityId + "_" + uid;
    }
}
