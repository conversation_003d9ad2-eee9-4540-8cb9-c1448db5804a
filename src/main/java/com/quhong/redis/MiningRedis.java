package com.quhong.redis;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/14
 */
@Component
public class MiningRedis extends ActivityRedis {

    // ====================      用户数据      ===================

    public void setUserData(String activityId, String uid, String hashKey, int hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, String.valueOf(hashValue));
    }

    public void incUserData(String activityId, String uid, String hashKey, int incValue) {
        incHashValue(getUserDataKey(activityId, uid), hashKey, incValue);
    }

    public Map<String, Long> getUserData(String activityId, String uid) {
        Map<String, String> hashMap = getHashMap(getUserDataKey(activityId, uid));
        return hashMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> Long.parseLong(entry.getValue())));
    }

    public long getUserData(String activityId, String uid, String hashKey) {
        return getHashIntValue(getUserDataKey(activityId, uid), hashKey);
    }

    private String getUserDataKey(String activityId, String uid) {
        return "hash:userData_" + activityId + "_" + uid;
    }


    // ====================      设备签到记录      ===================

    public void addSignRecord(String activityId, String tnId,String dayStr) {
        addSetValue(getSignTnIdKey(activityId,dayStr), tnId);
    }

    public boolean hasSignRecord(String activityId, String tnId,String dayStr) {
        return isSetMember(getSignTnIdKey(activityId,dayStr), tnId);
    }

    private String getSignTnIdKey(String activityId,String dayStr) {
        return "set:signTnId_" + activityId + "_" + dayStr;
    }

    // ====================      已挖矿的格子记录      ===================

    public void addMinedPosition(String activityId, String uid, int position) {
        addSetValue(getMinedPositionKey(activityId, uid), position + "");
    }

    public void addMinedPosition(String activityId, String uid, Set<Integer> positionSet) {
        addSetValues(getMinedPositionKey(activityId, uid), positionSet.stream().map(String::valueOf).collect(Collectors.toSet()));
    }


    public Set<Integer> getAllMinedPosition(String activityId, String uid) {
        Set<String> strSet = getSetValues(getMinedPositionKey(activityId, uid));
        if (CollectionUtils.isEmpty(strSet)) {
            return Collections.emptySet();
        }
        return strSet.stream().map(Integer::parseInt).collect(Collectors.toSet());
    }

    public int getMinedPositionNum(String activityId, String uid) {
        return getSetSize(getMinedPositionKey(activityId, uid));
    }

    public void clearMinedPosition(String activityId, String uid) {
        delKey(getMinedPositionKey(activityId, uid));
    }

    private String getMinedPositionKey(String activityId, String uid) {
        return "set:minedPosition" + activityId + "_" + uid;
    }

    // ====================      抽奖奖池      ===================

    public int getPoolSize(String activityId, String poolName) {
        return getListSize(getDrawPoolKey(activityId, poolName));
    }

    public void pushRewardInPool(String activityId, String poolName, List<String> rewardList) {
        listRightPushAll(getDrawPoolKey(activityId, poolName), rewardList);
    }

    public String popRewardFromPool(String activityId, String poolName) {
        return listLeftPop(getDrawPoolKey(activityId, poolName));
    }

    private String getDrawPoolKey(String activityId, String poolName) {
        return "list:drawPool_" + activityId + "_" + poolName;
    }

    // ====================      抽奖通知      ===================

    public void addRewardNotify(String activityId, String jsonRecord) {
        String key = getRewardNotifyKey(activityId);
        int listSize = listLeftPush(key, jsonRecord);
        if (listSize > 100) {
            listTrim(key, 0, 10);
        }
    }

    public List<String> getRewardNotifyList(String activityId) {
        return listRange(getRewardNotifyKey(activityId), 0, 9);
    }

    private String getRewardNotifyKey(String activityId) {
        return "list:drawRewardNotify_" + activityId;
    }

    // ====================      设置了活动提醒的用户名单      ===================

    public void addReminderUser(String activityId, String uid) {
        String key = getReminderUserKey(activityId);
        addSetValue(key, uid);
        clusterTemplate.expire(key, 30, TimeUnit.DAYS);
    }

    public void removeReminderUser(String activityId, String uid) {
        removeSetValue(getReminderUserKey(activityId), uid);
    }

    public Set<String> getAllReminderUser(String activityId) {
        return getSetValues(getReminderUserKey(activityId));
    }

    public boolean isReminderUser(String activityId, String uid) {
        return isSetMember(getReminderUserKey(activityId), uid);
    }

    private String getReminderUserKey(String activityId) {
        return "set:activityReminderUser_" + activityId;
    }

    // ====================      挖矿记录      ===================

    public void saveMiningRecord(String activityId, String uid, String json) {
        listLeftPush(getMiningRecordKey(activityId, uid), json);
    }

    public List<String> getMiningRecordList(String activityId, String uid, int start, int end) {
        return listRange(getMiningRecordKey(activityId, uid), start, end);
    }

    private String getMiningRecordKey(String activityId, String uid) {
        return "list:miningRecord_" + activityId + "_" + uid;
    }
}
