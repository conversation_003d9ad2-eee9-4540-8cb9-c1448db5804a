package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class VoyagerRedis {
    private static final Logger logger = LoggerFactory.getLogger(VoyagerRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String GAME_NAME = "Voyager";
    private String totalMileageExpireDate = "";
    private String dailyRoomLevelDevoteExpireDate = "";
    private String dailyUserRoomDevoteLevelExpireDate = "";
    private String dailyRoomLevelExpireDate = "";
    private String roomLevelDevoteExpireDate = "";
    private String roomNameExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    /**
     * 总量程key
     */
    private String totalMileageKey(String activityId) {
        return String.format("zset:%s:totalMileage:%s", GAME_NAME, activityId);
    }

    // 获取分数
    public int getTotalMileageScore(String activityId, String roomId) {
        try{
            Double score = clusterTemplate.opsForZSet().score(totalMileageKey(activityId), roomId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getTotalMileageScore error activityId={} roomId={} e={}", activityId, roomId, e.getMessage(), e);
            return 0;
        }
    }

    public void incrTotalMileageScore(String activityId, String roomId, int score) {
        try {
            String key = totalMileageKey(activityId);
            int curScore = getTotalMileageScore(activityId, roomId);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, roomId, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(totalMileageExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                totalMileageExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrTotalMileageScore error activityId={} roomId={} score={} e={}", activityId, roomId, score, e.getMessage(), e);
        }
    }

    // 获取排行榜
    public List<String> getTotalMileageRankingList(String activityId, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = totalMileageKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    // 获取带分数排行榜
    public Map<String, Integer> getTotalMileageRankingMap(String activityId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = totalMileageKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    private String getDateNumStrKey(){
        return String.format("str:%s:DateNum", GAME_NAME);
    }

    public String getDateNumStr() {
        try{
            String score = clusterTemplate.opsForValue().get(getDateNumStrKey());
            return score != null ? score : "0";
        } catch (Exception e) {
            logger.info("getDateNumStr error  e={}", e.getMessage(), e);
        }
        return "0";
    }

    public void incDateNumStr() {
        try{
            clusterTemplate.opsForValue().increment(getDateNumStrKey());
        } catch (Exception e) {
            logger.info("incDateNumStr error  e={}", e.getMessage(), e);
        }
    }


    /**
     * 房间每个里程贡献
     */
    private String roomDevoteKey(String activityId) {
        return String.format("zset:%s:roomDevote:%s", GAME_NAME, activityId);
    }

    // 获取分数
    public int getRoomDevoteScore(String activityId, String roomId) {
        try{
            Double score = clusterTemplate.opsForZSet().score(roomDevoteKey(activityId), roomId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getRoomDevoteScore error activityId={} roomId={} e={}", activityId, roomId, e.getMessage(), e);
            return 0;
        }
    }

    public void setRoomDevoteScore(String activityId, String roomId, int score) {
        try {
            String key = roomDevoteKey(activityId);
            clusterTemplate.opsForZSet().add(key, roomId, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(roomLevelDevoteExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                roomLevelDevoteExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("setRoomDevoteScore error activityId={} roomId={} score={} e={}", activityId, roomId, score, e.getMessage(), e);
        }
    }



    /**
     * 每日房间每个等级贡献
     */
    private String dailyRoomLevelDevoteKey(String activityId, int level) {
        String dailyStr = getDateNumStr();
        return String.format("zset:%s:roomLevelDevote:%s:%s:%s", GAME_NAME, activityId, dailyStr, level);
    }

    // 获取分数
    public int getDailyRoomLevelDevoteScore(String activityId, int level, String roomId) {
        try{
            Double score = clusterTemplate.opsForZSet().score(dailyRoomLevelDevoteKey(activityId, level), roomId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getDailyRoomLevelDevoteScore error activityId={} level={} roomId={} e={}", activityId, level, roomId, e.getMessage(), e);
            return 0;
        }
    }

    public void setDailyRoomLevelDevoteScore(String activityId, int level, String roomId, int score) {
        try {
            String key = dailyRoomLevelDevoteKey(activityId, level);
            clusterTemplate.opsForZSet().add(key, roomId, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(dailyRoomLevelDevoteExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                dailyRoomLevelDevoteExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("setDailyRoomLevelDevoteScore error activityId={} level={} uid={} score={} e={}", activityId, level, roomId, score, e.getMessage(), e);
        }
    }


    /**
     * 每日房间钻石等级贡献统计
     * 某个用户在房间内不同等级贡献
     *
     */
    private String dailyUserRoomDevoteLevelKey(String activityId, String roomId, int level) {
        String dailyStr = getDateNumStr();
        return String.format("zset:%s:dailyMileage:%s:%s:%s:%s", GAME_NAME, activityId, roomId, dailyStr, level);
    }


    public int getDailyUserRoomDevoteLevelScore(String activityId, String roomId, int level, String uid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(dailyUserRoomDevoteLevelKey(activityId, roomId, level), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getDailyRoomDevoteLevelScore error activityId={} roomId={} level={}, uid={}, e={}", activityId, roomId, level, uid, e.getMessage(), e);
            return 0;
        }
    }

    public void incrDailyUserRoomDevoteLevelScore(String activityId, String roomId, int level, String uid, int score) {
        try {
            String key = dailyUserRoomDevoteLevelKey(activityId, roomId, level);
            int curScore = getDailyUserRoomDevoteLevelScore(activityId, roomId, level, uid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(dailyUserRoomDevoteLevelExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                dailyUserRoomDevoteLevelExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrTotalMileageScore error activityId={} uid={} score={} e={}", activityId, uid, score, e.getMessage(), e);
        }
    }

    // 获取带分数排行榜
    public Map<String, Integer> getDailyUserRoomDevoteLevelRankingMap(String activityId, String roomId, int level, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = dailyUserRoomDevoteLevelKey(activityId, roomId, level);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 当前每日房间钻石等级
     */
    private String dailyRoomLevelKey(String activityId) {
        String dailyStr = getDateNumStr();
        return String.format("hash:%s:dailyMileage:%s:%s", GAME_NAME, activityId, dailyStr);
    }


    public int getDailyRoomLevelValue(String activityId, String roomId) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(dailyRoomLevelKey(activityId), roomId);
            if (StringUtils.isEmpty(valueStr)) {
                return 1;
            }
            return Integer.parseInt(valueStr);
        } catch (Exception e) {
            logger.error("getDailyRoomLevelValue error activityId={} roomId={}  e={}", activityId, roomId, e.getMessage(), e);
        }
        return 1;
    }

    public void setDailyRoomLevelValue(String activityId, String roomId, int value) {
        try {
            String key = dailyRoomLevelKey(activityId);
            clusterTemplate.opsForHash().put(key, roomId, String.valueOf(value));
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(dailyRoomLevelExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                dailyRoomLevelExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("setRamadanEndKey error roomId={} e={}", roomId, e.getMessage(), e);
        }
    }


    /**
     * 房间名称修改
     */
    private String roomNameKey(String activityId) {
        return String.format("hash:%s:roomName:%s", GAME_NAME, activityId);
    }


    public String getRoomNameValue(String activityId, String roomId) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(roomNameKey(activityId), roomId);
            return valueStr == null ? "": valueStr;
        } catch (Exception e) {
            logger.error("getRoomNameValue error activityId={} roomId={}  e={}", activityId, roomId, e.getMessage(), e);
        }
        return "";
    }

    public void setRoomNameValue(String activityId, String roomId, String boatName) {
        try {
            String key = roomNameKey(activityId);
            clusterTemplate.opsForHash().put(key, roomId, boatName);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(roomNameExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                roomNameExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("setRoomNameValue error roomId={} boatName={} e={}", roomId, boatName, e.getMessage(), e);
        }
    }



}
