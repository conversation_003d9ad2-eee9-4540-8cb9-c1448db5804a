package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import com.quhong.service.FootballActivityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class FootballRedis {
    private static final Logger logger = LoggerFactory.getLogger(FootballRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String ACTIVITY_NAME = FootballActivityService.ACTIVITY_ID;
    private String totalRankingExpireDate = "";


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    /**
     * 足球排行榜key
     */
    private String getTotalFootballRankingKey(){
        return String.format("zset:total_football_ranking_key:%s", ACTIVITY_NAME);
    }

    /**
     * 获取分数
     */
    public int getTotalFootballRankingScore(String uid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getTotalFootballRankingKey(), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getTotalAttackScore  aid={}", uid, e);
            return 0;
        }
    }

    public void incTotalFootballRankingScore(String uid, int score) {
        try {
            String key = getTotalFootballRankingKey();
            int curScore = getTotalFootballRankingScore(uid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incTotalFootballRankingScore uid={}  score={} total={}", uid, score, rankScore);
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(totalRankingExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                totalRankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incTotalFootballRankingScore error uid={} giftId={} score={}", uid, score, e);
        }
    }


    /**
     * 每个足球排行榜key
     */
    private String getGiftFootballRankingKey(int giftId){
        return String.format("zset:gift_football_ranking_key:%s:%s", ACTIVITY_NAME, giftId);
    }

    /**
     * 获取分数
     */
    public int getGiftFootballRankingScore(String uid, int giftId) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getGiftFootballRankingKey(giftId), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getGiftFootballRankingScore  aid={}", uid, e);
            return 0;
        }
    }

    public void incGiftFootballRankingScore(String uid, int giftId, int score) {
        try {
            String key = getGiftFootballRankingKey(giftId);
            clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incGiftFootballRankingScore error uid={} giftId={} score={}", uid, giftId, score);
        }
    }


    // 足球竞赛key
    /**
     * 受邀key
     */
    public String getInvitedKey() {
        return String.format("set:invited:%s", ACTIVITY_NAME);
    }

    //  uid 是否在受邀集合
    public int isInvited(String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getInvitedKey(), uid)) ? 1 : 0;
        } catch (Exception e) {
            logger.error("isInvited error uid={} e={}", uid, e);
        }
        return 0;
    }


    /**
     * 队伍排行榜
     */
    private String getTotalTeamRankingKey(String activityId){
        return String.format("zset:total_team_ranking_key:%s", activityId);
    }

    /**
     * 获取分数
     */
    public int getTotalTeamRankingScore(String activityId, String giftId) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getTotalTeamRankingKey(activityId), giftId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getTotalTeamRankingScore  activityId={}", activityId, e);
            return 0;
        }
    }

    public void incTotalTeamRankingScore(String activityId, String giftId, int score) {
        try {
            String key = getTotalTeamRankingKey(activityId);
            int curScore = getTotalTeamRankingScore(activityId, giftId);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, giftId, rankScore);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incTotalTeamRankingScore error activityId={} giftId={} e={}", activityId, giftId, e.getMessage(), e);
        }
    }


    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动名称
     */
    public Map<String, Integer> getTotalTeamRankingMap(String activityId) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getTotalTeamRankingKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0,  -1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }



    /**
     * 每个队伍top贡献Key
     */
    private String getGiftTeamRankingKey(String activityId, int giftId){
        return String.format("zset:gift_team_ranking_key:%s:%s", activityId, giftId);
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动名称
     * @param giftId   礼物id
     * @param length   排行榜长度
     */
    public Map<String, Integer> getGiftTeamRankingMap(String activityId, int giftId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getGiftTeamRankingKey(activityId, giftId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public void incGiftTeamRankingScore(String activityId, int giftId, String uid, int score) {
        try {
            String key = getGiftTeamRankingKey(activityId, giftId);
            clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("incGiftTeamRankingScore error uid={} giftId={} score={}", uid, giftId, score);
        }
    }



}
