package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class FightRedis {
    private static final Logger logger = LoggerFactory.getLogger(FightRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String ACTIVITY_NAME = "fightV1";
    private String totalRankingExpireDate = "";
    private String attackExpireDate = "";
    private String helpTotalExpireDate = "";
    private String helpSendExpireDate = "";
    private String helpReceiveExpireDate = "";
    private String helpDefenseExpireDate = "";
    private String protectExpireDate = "";


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    /**
     * 攻击值排行榜key
     */
    private String getTotalAttackRankingKey(){
        return String.format("zset:total_attack_ranking_key:%s", ACTIVITY_NAME);
    }

    /**
     * 获取分数
     */
    public int getTotalAttackRankingScore(String uid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getTotalAttackRankingKey(), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getTotalAttackScore  aid={}", uid, e);
            return 0;
        }
    }

    public void incTotalAttackRankingScore(String uid, int score) {
        try {
            String key = getTotalAttackRankingKey();
            int curScore = getTotalAttackRankingScore(uid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incTotalAttackScore uid={}  score={} total={}", uid, score, rankScore);
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(totalRankingExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                totalRankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incTotalAttackScore error uid={} giftId={} score={}", uid, score, e);
        }
    }

    /**
     * 获取带分数排行榜
     */
    public Map<String, Integer> getTotalAttackRankingMap(int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getTotalAttackRankingKey();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 发送攻击礼物总数量
     */
    private String getAttackSendKey(){
        return String.format("zset:attack_send_key:%s", ACTIVITY_NAME);
    }

    public int getAttackSendScore(String uid) {
        try{
            String key = getAttackSendKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getAttackSendScore error uid={}", uid, e);
            return 0;
        }
    }

    public int incrAttackSendScore(String uid, int score) {
        try {

            incTotalAttackRankingScore(uid, score);

            String key = getAttackSendKey();
            Double value = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            logger.info("incrAttackSendScore aid={} score={} total={}", uid, score, value);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(attackExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                attackExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrAttackSendScore error uid={} score={}", uid, score, e);
            return 0;
        }
    }

    /**
     * 接收攻击礼物统计-非防御状态
     */

    private String getAttackReceiveKey(){
        return String.format("zset:attack_receive_key:%s", ACTIVITY_NAME);
    }

    public int getAttackReceiveScore(String uid) {
        try{
            String key = getAttackReceiveKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getAttackSendScore error uid={}", uid, e);
            return 0;
        }
    }

    public int incrAttackReceiveScore(String uid, int score) {
        try {

            incTotalAttackRankingScore(uid, -score);

            String key = getAttackReceiveKey();
            Double value = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            logger.info("incrAttackReceiveScore aid={} score={} total={}", uid, score, value);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrAttackSendScore error uid={} score={}", uid, score, e);
            return 0;
        }
    }


    /**
     * 接收攻击礼物统计-防御状态
     */

    private String getAttackReceiveProtectKey(){
        return String.format("zset:attack_receive_protect_key:%s", ACTIVITY_NAME);
    }

    public int getAttackReceiveProtectScore(String uid) {
        try{
            String key = getAttackReceiveProtectKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getAttackReceiveProtectScore error uid={}", uid, e);
            return 0;
        }
    }

    public int incrAttackReceiveProtectScore(String uid, int score) {
        try {

            incTotalAttackRankingScore(uid, score);

            String key = getAttackReceiveProtectKey();
            Double value = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            logger.info("incrAttackReceiveProtectScore aid={} score={} total={}", uid, score, value);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(attackExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                attackExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrAttackReceiveProtectScore error uid={} score={}", uid, score, e);
            return 0;
        }
    }



    /**
     * 发送保护礼物统计
     */
    private String getHelpGiftTotalKey(){
        return String.format("zset:help_total_key:%s", ACTIVITY_NAME);
    }

    public int getHelpGiftTotalScore(String uid) {
        try{
            String key = getHelpGiftTotalKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.error("getHelpGiftTotalScore error uid={}", uid, e);
            return 0;
        }
    }

    public int incrHelpGiftTotalScore(String uid, int score) {
        try {

            String key = getHelpGiftTotalKey();
            Double value = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(helpTotalExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                helpTotalExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrHelpGiftTotalScore error uid={} score={}", uid, score, e);
            return 0;
        }
    }

    /**
     * 获取带分数排行榜
     */
    public Map<String, Integer> getTotalHelpRankingMap(int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getHelpGiftTotalKey();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 发送保护礼物统计
     */
    private String getHelpGiftSendKey(){
        return String.format("zset:help_send_key:%s", ACTIVITY_NAME);
    }

    public int getHelpGiftSendScore(String uid) {
        try{
            String key = getHelpGiftSendKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.error("getHelpGiftSendScore error uid={}", uid, e);
            return 0;
        }
    }

    public int incrHelpGiftSendScore(String uid, int score) {
        try {

            incrHelpGiftTotalScore(uid, score);

            String key = getHelpGiftSendKey();
            Double value = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(helpSendExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                helpSendExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrHelpReceiveScore error uid={} score={}", uid, score, e);
            return 0;
        }
    }

    /**
     * 接收保护礼物统计
     */

    private String getHelpReceiveKey(){
        return String.format("zset:help_receive_key:%s", ACTIVITY_NAME);
    }

    public int getHelpReceiveScore(String uid) {
        try{
            String key = getHelpReceiveKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getHelpReceiveScore error uid={}", uid, e);
            return 0;
        }
    }

    public int incrHelpReceiveScore(String uid, int score) {
        try {
            incrHelpGiftTotalScore(uid, score);

            String key = getHelpReceiveKey();
            Double value = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(helpReceiveExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                helpReceiveExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrHelpReceiveScore error uid={} score={}", uid, score, e);
            return 0;
        }
    }



    private String getHelpDefenseKey(){
        return String.format("zset:help_defense_key:%s", ACTIVITY_NAME);
    }

    public int getHelpDefenseScore(String uid) {
        try{
            String key = getHelpDefenseKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getHelpDefenseScore error uid={}", uid, e);
            return 0;
        }
    }

    public int incrHelpDefenseScore(String uid, int score) {
        try {

            String key = getHelpDefenseKey();
            Double value = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            logger.info("incrHelpDefenseScore aid={} score={} total={}", uid, score, value);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(helpDefenseExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                helpDefenseExpireDate = dateStr;
            }
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrHelpDefenseScore error uid={} score={}", uid, score, e);
            return 0;
        }
    }

    public void removeHelpDefense(String uid) {
        try {

            String key = getHelpDefenseKey();
            clusterTemplate.opsForZSet().remove(key, uid);
            logger.info("removeHelpDefense aid={}", uid);
        } catch (Exception e) {
            logger.info("removeHelpDefense error uid={}", uid, e);
        }
    }


    private String getProtectTimeKey(){
        return String.format("zset:protect_time_key:%s", ACTIVITY_NAME);
    }

    public int getProtectTimeScore(String uid) {
        try{
            String key = getProtectTimeKey();
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getProtectTimeScore error uid={}", uid, e);
            return 0;
        }
    }

    public void setProtectTimeScore(String uid, int endTime) {
        try {

            String key = getProtectTimeKey();
            clusterTemplate.opsForZSet().add(key, uid, endTime);
            logger.info("setProtectTimeScore aid={} score={} ", uid, endTime);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(protectExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                protectExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("setProtectTimeScore error uid={} score={}", uid, endTime, e);
        }
    }

    public void removeProtectTime(String uid) {
        try {

            String key = getProtectTimeKey();
            clusterTemplate.opsForZSet().remove(key, uid);
            logger.info("removeProtectTime aid={}", uid);
        } catch (Exception e) {
            logger.info("removeProtectTime error uid={}", uid, e);
        }
    }

    public Map<String, Integer> getProtectTimeRankingMapByScore(int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getProtectTimeKey();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }



}
