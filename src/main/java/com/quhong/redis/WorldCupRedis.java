package com.quhong.redis;

import com.quhong.core.date.DateSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class WorldCupRedis {
    private static final Logger logger = LoggerFactory.getLogger(WorldCupRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String activityName = "WorldCup";
    private static final String CHAMPION_BET = "Champion";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String matchSizeKey(String matchType, int teamId) {
        return "str:match_size:"+ activityName + ":" + matchType + ":" + teamId;
    }


    public int getMatchSize(String matchType, int teamId) {
        try {
            String matchSize = clusterTemplate.opsForValue().get(matchSizeKey(matchType, teamId));
            return matchSize != null ? Integer.parseInt(matchSize): 0;
        } catch (Exception e) {
            logger.info("getMatchSize error matchType={}, e={}", matchType, e);
            return 0;
        }
    }

    public int incMatchSize(String matchType, int teamId, int score) {
        try {
            Long afterSize = clusterTemplate.opsForValue().increment(matchSizeKey(matchType, teamId), score);
            clusterTemplate.expire(matchSizeKey(matchType, teamId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return afterSize != null ? afterSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("incMatchSize error matchType={}, e={}", matchType, e);
            return 0;
        }
    }


    private String champSizeKey(int recordId) {
        return "str:champ_size:"+ activityName + ":" + recordId;
    }


    public int getChampSize(int recordId) {
        try {
            String champSize = clusterTemplate.opsForValue().get(matchSizeKey(CHAMPION_BET, recordId));
            return champSize != null ? Integer.parseInt(champSize): 0;
        } catch (Exception e) {
            logger.info("getChampSize error recordId={}, e={}", recordId, e);
            return 0;
        }
    }

    public int incChampSize(int recordId, int score) {
        try {
            Long afterSize = clusterTemplate.opsForValue().increment(champSizeKey(recordId), score);
            clusterTemplate.expire(champSizeKey(recordId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return afterSize != null ? afterSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("incMatchSize error  recordId={}, e={}", recordId, e);
            return 0;
        }
    }



    // 世界杯房间反钻活动
    private String worldCupRoomKey() {
        return "set:world_cup_room:" + activityName;
    }


    public boolean isWorldCupRoom(String roomId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(worldCupRoomKey(), roomId));
        } catch (Exception e) {
            logger.info("isWorldCupRoom error e={}", e.getMessage());
            return false;
        }
    }

    public void addWorldCupRoom(String roomId) {
        try {
            clusterTemplate.opsForSet().add(worldCupRoomKey(), roomId);
            clusterTemplate.expire(worldCupRoomKey(), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addWorldCupRoom error e={}", e.getMessage());
        }
    }




}
