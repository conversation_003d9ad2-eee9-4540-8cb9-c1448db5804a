package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 任务榜单模板redis处理方式
 */


@Lazy
@Component
public class TaskRankRedis {
    private static final Logger logger = LoggerFactory.getLogger(TaskRankRedis.class);
    private static final Integer MAX_SIZE = 30;


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 日任务hash key相关
     */
    private String getDailyHashKey(String activityId, String uid, String dateStr) {
        return String.format("hash:dailyTaskRank:%s:%s:%s", activityId, uid, dateStr);
    }

    public Map<String, Integer> getDailyHashAll(String activityId, String uid, String dateStr) {
        Map<String, Integer> hashMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getDailyHashKey(activityId, uid, dateStr));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getDailyHashAll error activityId={} uid={}, dateStr:{} e={}", activityId, uid, dateStr, e.getMessage(), e);
        }
        return hashMap;
    }

    public int incDailyHashNum(String activityId, String uid, String dateStr, String hKey, int num) {
        try {
            Long afterNum = clusterTemplate.opsForHash().increment(getDailyHashKey(activityId, uid, dateStr), hKey, num);
            clusterTemplate.expire(getDailyHashKey(activityId, uid, dateStr), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return afterNum.intValue();
        } catch (Exception e) {
            logger.error("incDailyHashNum error activityId={} e={}", activityId, e);
            return num;
        }
    }

    public void setDailyHashNum(String activityId, String uid, String dateStr, String hKey, int num) {
        try {
            clusterTemplate.opsForHash().put(getDailyHashKey(activityId, uid, dateStr), hKey, String.valueOf(num));
            clusterTemplate.expire(getDailyHashKey(activityId, uid, dateStr), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setDailyHashNum error activityId={} e={}", activityId, e);
        }
    }

    public void delDailyHashData(String activityId, String uid, String dateStr, String hKey) {
        try {
            clusterTemplate.opsForHash().delete(getDailyHashKey(activityId, uid, dateStr), hKey);
        } catch (Exception e) {
            logger.error("delDailyHashData error activityId={} e={}", activityId, e);
        }
    }

    public int getDailyHashValue(String activityId, String uid, String dateStr, String hKey) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(getDailyHashKey(activityId, uid, dateStr), hKey);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getDailyHashValue error activityId={} hKey={}  e={}", activityId, hKey, e);
            return 0;
        }
    }

    /**
     * 里程碑任务hash key相关
     */
    private String getPeriodHashKey(String activityId, String uid) {
        return String.format("hash:periodTaskRank:%s:%s", activityId, uid);
    }

    public Map<String, Integer> getPeriodHashAll(String activityId, String uid) {
        Map<String, Integer> hashMap = new HashMap<>();
        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getPeriodHashKey(activityId, uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getPeriodHashAll error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return hashMap;
    }

    public int getPeriodHashKey(String activityId, String uid, String hKey, int num) {
        try {
            Long afterNum = clusterTemplate.opsForHash().increment(getPeriodHashKey(activityId, uid), hKey, num);
            clusterTemplate.expire(getPeriodHashKey(activityId, uid), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return afterNum.intValue();
        } catch (Exception e) {
            logger.error("getPeriodHashKey error activityId={} e={}", activityId, e);
            return num;
        }
    }

    public int incPeriodHashNum(String activityId, String uid, String hKey, int num) {
        try {
            Long afterNum = clusterTemplate.opsForHash().increment(getPeriodHashKey(activityId, uid), hKey, num);
            clusterTemplate.expire(getPeriodHashKey(activityId, uid), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return afterNum.intValue();
        } catch (Exception e) {
            logger.error("incPeriodHashNum error activityId={} e={}", activityId, e);
            return num;
        }
    }

    public void setPeriodHashNum(String activityId, String uid, String hKey, int num) {
        try {
            clusterTemplate.opsForHash().put(getPeriodHashKey(activityId, uid), hKey, String.valueOf(num));
            clusterTemplate.expire(getPeriodHashKey(activityId, uid), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setPeriodHashNum error activityId={} e={}", activityId, e);
        }
    }

    public void delPeriodHashData(String activityId, String uid, String hKey) {
        try {
            clusterTemplate.opsForHash().delete(getPeriodHashKey(activityId, uid), hKey);
        } catch (Exception e) {
            logger.error("delPeriodHashData error activityId={} e={}", activityId, e);
        }
    }

    public int getPeriodHashValue(String activityId, String uid, String hKey) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(getPeriodHashKey(activityId, uid), hKey);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getDailyHashValue error activityId={} hKey={}  e={}", activityId, hKey, e);
            return 0;
        }
    }



    /**
     * str类型相关
     */
    private String getActivityStrKey(String activityId) {
        return String.format("str:activity:%s", activityId);
    }

    public int getActivityStrScore(String activityId) {
        try {
            String score = clusterTemplate.opsForValue().get(getActivityStrKey(activityId));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getActivityStrScore error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    public String getActivityStrValue(String activityId) {
        try {
            String value = clusterTemplate.opsForValue().get(getActivityStrKey(activityId));
            return value != null ? value : "";
        } catch (Exception e) {
            logger.info("getActivityStrValue error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return "";
    }

    public void setActivityStrScore(String activityId, int score) {
        try {
            String key = getActivityStrKey(activityId);
            clusterTemplate.opsForValue().set(key, String.valueOf(score), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setActivityStrScore error activityId={} score={} e={}", activityId, score, e.getMessage(), e);
        }
    }

    public long incActivityStrScore(String activityId, int score) {
        try {
            String key = getActivityStrKey(activityId);
            Long ret = clusterTemplate.opsForValue().increment(key, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return ret == null ? 0 : ret;
        } catch (Exception e) {
            logger.info("incActivityStrScore error activityId={} score={} e={}", activityId, score, e.getMessage(), e);
        }
        return 0;
    }

    public void setActivityStrData(String activityId, String data) {
        try {
            String key = getActivityStrKey(activityId);
            clusterTemplate.opsForValue().set(key, data, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setActivityStrData error activityId={} data={} e={}", activityId, data, e.getMessage(), e);
        }
    }

    /**
     * zset相关
     */
    private String getActivityZSetKey(String activityId, String rankType) {
        return String.format("zset:activity:%s:%s", activityId, rankType);
    }

    /**
     * 获取分数
     */
    public int getActivityZSetRankScore(String activityId, String rankType, String rankId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getActivityZSetKey(activityId, rankType), rankId);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getActivityZSetRankScore error activityId={} rankId={}, e={}", activityId, rankId, e.getMessage(), e);
            return 0;
        }
    }

    public double getActivityZSetRankDoubleScore(String activityId, String rankType, String rankId) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getActivityZSetKey(activityId, rankType), rankId);
            return score != null ? score : 0;
        } catch (Exception e) {
            logger.info("getActivityZSetRankDoubleScore error activityId={} rankId={}, e={}", activityId, rankId, e.getMessage(), e);
            return 0;
        }
    }

    public int incrActivityZSetRankScoreWithTime(String activityId, String rankType, String rankId, int score) {
        try {
            String key = getActivityZSetKey(activityId, rankType);
            int curScore = getActivityZSetRankScore(activityId, rankType, rankId);
            int nowScore = curScore + score;
            double rankScore = new BigDecimal(nowScore + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, rankId, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return nowScore;
        } catch (Exception e) {
            logger.info("incrActivityZSetRankScoreWithTime error activityId={} rankId={} giftId={} score={}", activityId, rankId, score, e.getMessage(), e);
            return 0;
        }
    }

    public void addActivityZSetRankScoreWithTime(String activityId, String rankType, String rankId, int score) {
        try {
            String key = getActivityZSetKey(activityId, rankType);
            double rankScore = new BigDecimal(score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, rankId, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addActivityZSetRankScoreWithTime error activityId={} rankId={} giftId={} score={}", activityId, rankId, score, e.getMessage(), e);
        }
    }

    public int incrActivityZSetRankScore(String activityId, String rankType, String rankId, int score) {
        Double ret = null;
        try {
            String key = getActivityZSetKey(activityId, rankType);
            ret = clusterTemplate.opsForZSet().incrementScore(key, rankId, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScoreSimple error activityId={} rankId={} giftId={} score={}", activityId, rankId, score, e.getMessage(), e);
        }
        return ret == null ? 0 : ret.intValue();
    }

    public void addActivityZSetRankScore(String activityId, String rankType, String rankId, int score) {
        try {
            String key = getActivityZSetKey(activityId, rankType);
            clusterTemplate.opsForZSet().add(key, rankId, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addActivityZSetRankScore error activityId={} rankId={} giftId={} score={}", activityId, rankId, score, e.getMessage(), e);
        }
    }


    public double incrActivityZSetRankingScoreDouble(String activityId, String rankType, String rankId, double score) {
        Double ret = null;
        try {
            String key = getActivityZSetKey(activityId, rankType);
            ret = clusterTemplate.opsForZSet().incrementScore(key, rankId, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScoreDouble error activityId={} rankId={} giftId={} score={}", activityId, rankId, score, e.getMessage(), e);
        }
        return ret == null ? 0 : ret;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getActivityZSetRank(String activityId, String rankType, String rankId) {
        try {
            String key = getActivityZSetKey(activityId, rankType);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, rankId);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getActivityZSetRank error activityId={} rankId={}", activityId, rankId, e);
            return 0;
        }
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     * 正序
     */
    public int getActivityZSetPositiveRank(String activityId, String rankType, String rankId) {
        try {
            String key = getActivityZSetKey(activityId, rankType);
            Long rank = clusterTemplate.opsForZSet().rank(key, rankId);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getActivityZSetPositiveRank error activityId={} rankId={}", activityId, rankId, e);
            return 0;
        }
    }

    /**
     * 获取排行榜
     * -获取top-n
     * -倒序不带分数
     */
    public List<String> getActivityRankList(String activityId, String rankType, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     * -获取start-end 排名
     * -倒序不带分数
     */
    public List<String> getActivityRankList(String activityId, String rankType, int start, int end) {
        List<String> rankingList = new ArrayList<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, start, end - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     * -获取top-n
     * -倒序带分数
     */
    public Map<String, Integer> getActivityRankMap(String activityId, String rankType, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 获取排行榜
     * -获取top-n
     * -正序不带分数
     */
    public List<String> getActivityRangeRankList(String activityId, String rankType, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     * -获取top-n
     * -正序带分数
     */
    public Map<String, Integer> getActivityRangeRankMap(String activityId, String rankType, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 分页获取带分数排行榜
     * -获取start-end排名
     * -正序不带分数
     */
    public Map<String, Integer> getActivityRangeRankMapByPage(String activityId, String rankType, int start, int end) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, start, end - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 通过分数获取成员
     * 正序
     */
    public Map<String, Integer> getActivityRangeRankMapByScore(String activityId, String rankType, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 通过分数获取成员
     * 倒序
     */
    public Map<String, Integer> getActivityRankMapByScore(String activityId, String rankType, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 分页获取指定分数段带分数排行榜
     * 倒序
     * offset: 偏移多少
     * count: 取多少
     */
    public Map<String, Integer> getActivityRankMapByScoreAPage(String activityId, String rankType, int min, int max, int offset, int count) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getActivityZSetKey(activityId, rankType);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max, offset, count);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 移除排名
     */
    public void removeActivityZSet(String activityId, String rankType, String rankId) {
        try {
            String key = getActivityZSetKey(activityId, rankType);
            clusterTemplate.opsForZSet().remove(key, rankId);
        } catch (Exception e) {
            logger.info("removeActivityZSet error activityId={} rankId={}", activityId, rankId, e);
        }
    }

    /**
     * 获取成员数量
     */
    public int getActivityZSetMemberNum(String activityId, String rankType) {
        try {
            String key = getActivityZSetKey(activityId, rankType);
            Long memberNum = clusterTemplate.opsForZSet().zCard(key);
            return memberNum == null ? 0 : memberNum.intValue();
        } catch (Exception e) {
            logger.info("getActivityZSetMemberNum error activityId={} error={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 集合set相关
     */

    private String getActivitySetKey(String setKey) {
        return String.format("set:activity:%s", setKey);
    }

    public void addActivitySetData(String setKey, String data) {
        try {
            String key = getActivitySetKey(setKey);
            clusterTemplate.opsForSet().add(key, data);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addActivitySetData error setKey={} data={}", setKey, data, e);
        }
    }

    public void removeActivitySetData(String setKey, String data) {
        try {
            String key = getActivitySetKey(setKey);
            clusterTemplate.opsForSet().remove(key, data);
        } catch (Exception e) {
            logger.error("removeActivitySetData error setKey={} e={}", setKey, data, e);
        }
    }

    public boolean isActivitySetData(String setKey, String data) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getActivitySetKey(setKey), data));
        } catch (Exception e) {
            logger.error("isDeviceData error setKey={} data={}, e={}", setKey, data, e);
            return false;
        }
    }

    public int getActivitySetNum(String setKey) {
        try {

            String key = getActivitySetKey(setKey);
            Long number = clusterTemplate.opsForSet().size(key);
            return ObjectUtils.isEmpty(number) ? 0 : number.intValue();
        } catch (Exception e) {
            logger.info("getActivitySetNum error setKey={}  score={}", setKey, e);
            return 0;
        }
    }

    public Set<String> getActivitySetMember(String setKey) {
        try {

            String key = getActivitySetKey(setKey);
            return clusterTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.info("getActivitySetMember error activityId={}  score={}", setKey, e);
            return Collections.emptySet();
        }
    }

    public String getActivitySetOneMember(String setKey) {
        try {
            String key = getActivitySetKey(setKey);
            return clusterTemplate.opsForSet().randomMember(key);
        } catch (Exception e) {
            logger.info("getActivitySetOneMember error setKey={}  score={}", setKey, e);
            return "";
        }
    }


}
