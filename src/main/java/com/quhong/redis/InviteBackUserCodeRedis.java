package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Component
@Lazy
public class InviteBackUserCodeRedis {

    private static final Logger logger = LoggerFactory.getLogger(InviteBackUserCodeRedis.class);

    private static final Pattern PATTERN = Pattern.compile("^[a-z0-9]*$");

    private static final String CHARACTERS = "abcdefghijklmnopqrstuvwxyz";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    private String expireDate;

    public void addHostCode(String uid, String hostCode) {
        String hostCodeUidKey = getHostCodeUidKey();
        String uidHostCodeKey = getUidHostCodeKey();
        try {
            // key=hostCode value=uid
            clusterRedis.opsForHash().put(hostCodeUidKey, hostCode, uid);
            // key=uid value=hostCode
            clusterRedis.opsForHash().put(uidHostCodeKey, uid, hostCode);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(expireDate)) {
                clusterRedis.expire(hostCodeUidKey, 180, TimeUnit.DAYS);
                clusterRedis.expire(uidHostCodeKey, 180, TimeUnit.DAYS);
                expireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("addHostCode error. uid={} hostCode={}", uid, hostCode);
        }
    }

    public boolean hasExisted(String hostCode) {
        return StringUtils.hasLength(getUidByHostCode(hostCode));
    }

    public String getHostCodeByUid(String uid) {
        String key = getUidHostCodeKey();
        try {
            Object value = clusterRedis.opsForHash().get(key, uid);
            if (value != null) {
                return String.valueOf(value);
            }
            // 无主播码即生成一个
            return initHostCode(uid);
        } catch (Exception e) {
            logger.error("getHostCodeByUid error. uid={}", uid);
            return "";
        }
    }

    public String getUidByHostCode(String hostCode) {
        String key = getHostCodeUidKey();
        try {
            Object value = clusterRedis.opsForHash().get(key, hostCode);
            return value != null ? String.valueOf(value) : "";
        } catch (Exception e) {
            logger.error("getUidByHostCode error. hostCode={}", hostCode);
            return "";
        }
    }

    public void removeHostCode(String uid, String hostCode) {
        String key = getHostCodeUidKey();
        try {
            clusterRedis.opsForHash().delete(getHostCodeUidKey(), hostCode, uid);
            clusterRedis.opsForHash().delete(getUidHostCodeKey(), uid, hostCode);
        } catch (Exception e) {
            logger.error("removeHostCode error. uid={} hostCode={}", uid, hostCode);
        }
    }

    /**
     * 初始化主播码
     */
    private String initHostCode(String uid) {
        String hostCode;
        while (true) {
            List<String> charList = new ArrayList<>();
            // 从1~9数字中获取3个
            for (int i = 0; i < 3; i++) {
                charList.add(ThreadLocalRandom.current().nextInt(10) + "");
            }
            // 从26个小写字母中获取3个
            for (int i = 0; i < 3; i++) {
                charList.add(String.valueOf(CHARACTERS.charAt(ThreadLocalRandom.current().nextInt(CHARACTERS.length()))));
            }
            Collections.shuffle(charList);
            hostCode = charList.stream().map(String::valueOf).collect(Collectors.joining(""));
            if (!hasExisted(hostCode)) {
                addHostCode(uid, hostCode);
                break;
            }
        }
        return hostCode;
    }

    /**
     * 校验主播码格式
     */
    public boolean checkHostCode(String hostCode) {
        if (!StringUtils.hasLength(hostCode) || hostCode.length() != 6) {
            return false;
        }
        return PATTERN.matcher(hostCode).matches();
    }

    /**
     * key=uid value=hostCode
     */
    public String getUidHostCodeKey() {
        return "hash:uid_hostCode:back";
    }

    /**
     * key=hostCode value=uid
     */
    public String getHostCodeUidKey() {
        return "hash:hostCode_uid:back";
    }
}
