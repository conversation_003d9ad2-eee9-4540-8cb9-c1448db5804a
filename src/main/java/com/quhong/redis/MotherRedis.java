package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.PopularListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class MotherRedis {
    private static final Logger logger = LoggerFactory.getLogger(MotherRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String ACTIVITY_NAME = "motherDay";
    private String motherExpireDate = "";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String motherKey(String activityId) {
        return "str:" + ACTIVITY_NAME + ":" + activityId;
    }


    public Integer getMotherTotal(String activityId) {
        try {
            String motherTotal = clusterTemplate.opsForValue().get(motherKey(activityId));
            return motherTotal != null ? Integer.parseInt(motherTotal) : 0;
        } catch (Exception e) {
            logger.info("getMotherTotal error e={}", e.getMessage());
            return 0;
        }
    }

    public void incrMotherTotal(String activityId, int score) {
        try {
            String key = motherKey(activityId);
            Long value = clusterTemplate.opsForValue().increment(key, score);
            logger.info("incrMotherTotal activityId={} score={} total={}", activityId, score, value);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(motherExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                motherExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrMotherTotal error activityId={} score={} e={}", activityId, score, e);
        }
    }

    private String getPopularListKey(int area, int page) {
        return String.format("str:popularList:area%d:page%d", area, page);
    }

    public List<PopularListVO> getPopularList(int area, int page) {
        List<PopularListVO> result = new ArrayList<>();
        try {
            String json = clusterTemplate.opsForValue().get(getPopularListKey(area, page));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, PopularListVO.class);
        } catch (Exception e) {
            logger.error("get popularList error area={} page={}", area, page, e);
            return result;
        }
    }

}
