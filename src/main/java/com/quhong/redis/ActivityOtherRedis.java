package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.PopularListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class ActivityOtherRedis {
    private static final Logger logger = LoggerFactory.getLogger(ActivityOtherRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String rankingExpireDate = "";
    private String reachingExpireDate = "";

    /**
     *
     * @param activityId  活动名称
     * @param rankType  排行榜类型 1: 发送 2: 接收 3: 房间
     * @return key
     */
    private String getOtherActivityKey(String activityId, int rankType, int roundNum) {
        if(roundNum > 0){
            return String.format("zset:otherActivity:%s:%s:%s", activityId, rankType, roundNum);
        }else {
            return String.format("zset:otherActivity:%s:%s", activityId, rankType);
        }
    }

    /**
     * 获取分数
     */
    public int getOtherRankingScore(String activityId, String aid, int rankType, int roundNum) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getOtherActivityKey(activityId, rankType, roundNum), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }

    public void incrOtherRankingScore(String activityId, String aid, int score, int rankType, int roundNum) {
        try {
            String key = getOtherActivityKey(activityId, rankType, roundNum);
            int curScore = getOtherRankingScore(activityId, aid, rankType, roundNum);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrRankingScore activityId={} aid={}  score={} total={} rankType={}",
                    activityId, aid, score, rankScore, rankType);
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrRankingScore error activityId={} aid={} giftId={} score={}", activityId, aid, score, e);
        }
    }



    /**
     * 获取排行榜
     *
     * @param activityId 活动
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public List<String> getOtherRankingList(String activityId, int rankType, int length, int roundNum) {
        List<String> rankingList = new ArrayList<>();
        String key = getOtherActivityKey(activityId, rankType, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            // logger.info("getOtherRankingList aid={} score={} rankType={}", rangeWithScore.getValue(), rangeWithScore.getScore().longValue(), rankType);
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getOtherRankingMap(String activityId, int rankType, int length, int roundNum) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityKey(activityId, rankType, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取指定分数指定长度段带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getOtherRankingMapByScore(String activityId, int rankType, int length, int min, int max, int roundNum) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityKey(activityId, rankType, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max, 0, length);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取指定分数带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     */
    public Map<String, Integer> getOtherRankingMapByScore(String activityId, int rankType, int min, int max, int roundNum) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityKey(activityId, rankType, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getOtherRank(String activityId, String aid, int rankType, int roundNum) {
        try {
            String key = getOtherActivityKey(activityId, rankType, roundNum);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getOtherRank error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }



    /**
     * 记录每日排行榜
     * @param activityId  活动名称
     * @param rankType  排行榜类型 1: 发送 2: 接收 3: 房间
     * @return key
     */
    private String getOtherActivityDailyKey(String activityId, int rankType, String dailyNum, int roundNum) {
        if(roundNum > 0){
            return String.format("zset:otherActivity:%s:%s:%s:%s", activityId, rankType, dailyNum, roundNum);
        }else {
            return String.format("zset:otherActivity:%s:%s:%s", activityId, rankType, dailyNum);
        }
    }

    /**
     * 获取每日分数
     */
    public int getOtherRankingDailyScore(String activityId, String aid, int rankType, String dailyNum, int roundNum) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getOtherActivityDailyKey(activityId, rankType, dailyNum, roundNum), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getScore error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }

    public void incrOtherRankingDailyScore(String activityId, String aid, int score, int rankType, String dailyNum, int roundNum) {
        try {
            String key = getOtherActivityDailyKey(activityId, rankType, dailyNum, roundNum);
            int curScore = getOtherRankingDailyScore(activityId, aid, rankType, dailyNum, roundNum);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrOtherRankingDailyScore activityId={} aid={}  score={} total={} rankType={}, key={}",
                    activityId, aid, score, rankScore, rankType, key);
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrOtherRankingDailyScore error activityId={} aid={} giftId={} score={}", activityId, aid, score, e);
        }
    }

    /**
     * 获取每日带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     * @param length     排行榜长度
     */
    public Map<String, Integer> getOtherRankingDailyMap(String activityId, int rankType, int length, String dailyNum, int roundNum) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherActivityDailyKey(activityId, rankType, dailyNum, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getOtherDailyRank(String activityId, String aid, int rankType, String dailyNum, int roundNum) {
        try {
            String key = getOtherActivityDailyKey(activityId, rankType, dailyNum, roundNum);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, aid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getOtherDailyRank error activityId={} aid={} rankType={}", activityId, aid, rankType, e);
            return 0;
        }
    }



    /**
     * 等级勋章另外记录一个key
     * @param activityId 活动名称
     * @param rankType 排行榜类型
     * @return key
     */
    private String getOtherReachingKey(String activityId, int rankType, int roundNum) {
        if(roundNum > 0){
            return String.format("zset:otherReaching:%s:%s:%s", activityId, rankType, roundNum);
        }else {
            return String.format("zset:otherReaching:%s:%s", activityId, rankType);
        }
    }

    /**
     * 获取等级勋章指定分数带分数排行榜
     *
     * @param activityId 活动名称
     * @param rankType   排行榜类型
     */
    public Map<String, Integer> getOtherReachingMapByScore(String activityId, int rankType, int min, int max, int roundNum) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getOtherReachingKey(activityId, rankType, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    public int getOtherReachingScore(String activityId, String uid, int rankType, int roundNum) {
        try{
            String key = getOtherReachingKey(activityId, rankType, roundNum);
            Double score = clusterTemplate.opsForZSet().score(key, uid);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.info("getReachingScore error activityName={} uid={} rankType={}", activityId, uid, rankType, e);
            return 0;
        }
    }

    public int incrOtherReachingScore(String activityId, String aid, int score, int rankType, int roundNum) {
        try {
            String key = getOtherReachingKey(activityId, rankType, roundNum);
            Double value = clusterTemplate.opsForZSet().incrementScore(key, aid, score);
            logger.info("incrOtherReachingScore activityId={} aid={} score={} total={} rankType={}",
                    activityId, aid, score, value, rankType);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.info("incrOtherReachingScore error activityId={} aid={} score={}", activityId, aid, score, e);
            return 0;
        }
    }

    /**
     * popular 房间列表
     */

    private String getPopularListKey() {
        return "str:popularList:area1:page1";
    }


    public List<PopularListVO> getPopularList() {
        List<PopularListVO> result = new ArrayList<>();
        try {
            String json = clusterTemplate.opsForValue().get(getPopularListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, PopularListVO.class);
        } catch (Exception e) {
            logger.error("get popularList error e={}", e.getMessage());
            return result;
        }
    }


}
