package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/29
 */
@Lazy
@Component
public class CheckpointQuizRedis {

    private static final Logger logger = LoggerFactory.getLogger(CheckpointQuizRedis.class);


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 保存关卡答题次数
     */
    public void saveCheckpointQuizCount(Integer activityId, Integer checkpointNo, String tnId, Integer timesType) {
        tnId = tnId == null ? "" : tnId;
        try {
            String key = getCheckpointQuizCountKey(activityId, checkpointNo, timesType);
            clusterTemplate.opsForHash().increment(key, tnId, 1L);
            if (timesType == 0) {
                clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
            } else {
                clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.error("saveCheckpointQuizCount error. activityId={}, checkpointNo={}, tnId={} timesType={} {}", activityId, checkpointNo, tnId, timesType, e.getMessage(), e);
        }
    }

    /**
     * 获取关卡答题次数
     */
    public int getCheckpointQuizCount(Integer activityId, Integer checkpointNo, String tnId, Integer timesType) {
        tnId = tnId == null ? "" : tnId;
        try {
            String value = (String) clusterTemplate.opsForHash().get(getCheckpointQuizCountKey(activityId, checkpointNo, timesType), tnId);
            return !StringUtils.isEmpty(value) ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            logger.error("getCheckpointQuizCount error. activityId={}, checkpointNo={}, tnId={} timesType={} {}", activityId, checkpointNo, tnId, timesType, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存关卡解锁记录
     */
    public void saveCheckpointUnlockRecord(Integer activityId, Integer checkpointNo, String tnId) {
        tnId = tnId == null ? "" : tnId;
        try {
            String key = getCheckpointUnlockRecordKey(activityId, checkpointNo);
            clusterTemplate.opsForSet().add(key, tnId);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveCheckpointUnlockRecord error. activityId={} checkpointNo={} tnId={} {}", activityId, checkpointNo, tnId, e.getMessage(), e);
        }
    }

    /**
     * 判断是否解锁该关卡
     */
    public boolean checkCheckpointUnlock(Integer activityId, Integer checkpointNo, String tnId) {
        tnId = tnId == null ? "" : tnId;
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getCheckpointUnlockRecordKey(activityId, checkpointNo), tnId));
        } catch (Exception e) {
            logger.error("checkCheckpointUnlock error. activityId={} checkpointNo={} tnId={} {}", activityId, checkpointNo, tnId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取关卡解锁人数
     */
    public int getCheckpointUnlockPeopleNum(Integer activityId, Integer checkpointNo) {
        try {
            Long size = clusterTemplate.opsForSet().size(getCheckpointUnlockRecordKey(activityId, checkpointNo));
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("getCheckpointUnlockPeopleNum error. activityId={} checkpointNo={} {}", activityId, checkpointNo, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存获取奖励记录
     */
    public void saveGainReward(String uid, Integer activityId, String jsonValue) {
        try {
            String key = getGainRewardKey(activityId, uid);
            clusterTemplate.opsForZSet().add(key, jsonValue, DateHelper.getNowSeconds());
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save quiz gain reward in redis error. uid={} activityId={} reward={} {}", uid, activityId, jsonValue, e.getMessage(), e);
        }
    }

    /**
     * 查询获取奖励记录
     */
    public Set<String> getGainReward(String uid, Integer activityId) {
        try {
            String key = getGainRewardKey(activityId, uid);
            return clusterTemplate.opsForZSet().range(key, 0, -1);
        } catch (Exception e) {
            logger.error("get quiz gain reward from redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return null;
        }
    }

    public void saveQuestionInRedis(String uid, Integer activityId, Integer checkpointNo, Integer slang, List<String> tidList) {
        try {
            String key = getQuestionListKey(uid, activityId, checkpointNo, slang);
            clusterTemplate.opsForList().rightPushAll(key, tidList);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveQuestionInRedis error. uid={} activityId={} checkpointNo={} slang={} {}", uid, activityId, checkpointNo, slang, e.getMessage(), e);
        }
    }

    public int getQuestionNum(String uid, Integer activityId, Integer checkpointNo, Integer slang) {
        try {
            Long size = clusterTemplate.opsForList().size(getQuestionListKey(uid, activityId, checkpointNo, slang));
            return size != null ? size.intValue() : 0;
        } catch (Exception e) {
            logger.error("get question num in redis error. uid={} activityId={} checkpointNo={} slang={} {}", uid, activityId, checkpointNo, slang, e.getMessage(), e);
            return 0;
        }
    }

    public List<Integer> getQuestionFromRedis(String uid, Integer activityId, Integer checkpointNo, Integer questionNum, Integer slang) {
        List<Integer> tidList = new ArrayList<>();
        String key = getQuestionListKey(uid, activityId, checkpointNo, slang);
        try {
            for (int i = 0; i < questionNum; i++) {
                String strTid = clusterTemplate.opsForList().rightPop(key);
                if (!StringUtils.isEmpty(strTid)) {
                    int tid = Integer.parseInt(strTid);
                    if (tidList.contains(tid)) {
                        i--;
                        continue;
                    }
                    tidList.add(tid);
                }
            }
            return tidList;
        } catch (Exception e) {
            logger.error("get question from redis error. uid={} activityId={} questionNum={} {}", uid, activityId, questionNum, e.getMessage(), e);
            return tidList;
        }
    }

    public int getCurrentCheckpointNo(Integer activityId, String uid) {
        try {
            String rankingKey = getRankingKey(activityId);
            Double score = clusterTemplate.opsForZSet().score(rankingKey, uid);
            return score != null ? score.intValue() + 1 : 1;
        } catch (Exception e) {
            logger.error("getCurrentCheckpointNo error. activityId={} uid={} {}", activityId, uid, e.getMessage(), e);
            return 1;
        }
    }

    public void saveQuizRanking(String uid, Integer activityId, double rankingScore) {
        try {
            String rankingKey = getRankingKey(activityId);
            clusterTemplate.opsForZSet().add(rankingKey, uid, rankingScore);
            clusterTemplate.expire(rankingKey, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save quiz ranking in redis error. uid={} activityId={} rankingScore={} {}", uid, activityId, rankingScore, e.getMessage(), e);
        }
    }

    public Map<String, Double> getRankingMap(Integer activityId) {
        Map<String, Double> linkedRankMap = new LinkedHashMap<>();
        String key = getRankingKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, 10);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore());
        }
        return linkedRankMap;
    }

    public double getQuizRankingScore(String uid, Integer activityId) {
        try {
            String rankingKey = getRankingKey(activityId);
            Double score = clusterTemplate.opsForZSet().score(rankingKey, uid);
            return score != null ? score : 0;
        } catch (Exception e) {
            logger.error("get quiz ranking score error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return 0;
        }
    }

    public int getQuizJoinNum(Integer activityId) {
        try {
            String rankingKey = getRankingKey(activityId);
            Long joinNum = clusterTemplate.opsForZSet().size(rankingKey);
            return joinNum != null ? joinNum.intValue() : 0;
        } catch (Exception e) {
            logger.error("get quiz join num error. activityId={} {}", activityId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取用户排名 (0为找不到)
     */
    public Integer getQuizRanking(String uid, Integer activityId) {
        try {
            String rankingKey = getRankingKey(activityId);
            Long rank = clusterTemplate.opsForZSet().reverseRank(rankingKey, uid);
            return rank == null ? 0 : rank.intValue() + 1;
        } catch (Exception e) {
            logger.error("get quiz ranking from redis error. uid={} activityId={} {}", uid, activityId, e.getMessage(), e);
            return 0;
        }
    }

    public void removeQuizActivityRedis(String uid, String tnId, Integer activityId, Integer checkpointNo, Integer timesType) {
        try {
            // 清答题次数
            clusterTemplate.opsForHash().delete(getCheckpointQuizCountKey(activityId, checkpointNo, timesType), tnId);
            // 清获奖记录
            clusterTemplate.delete(getGainRewardKey(activityId, uid));
            // 清用户排名
            clusterTemplate.opsForZSet().remove(getRankingKey(activityId), uid);
            // 清用户解锁的关卡
            clusterTemplate.opsForSet().remove(getCheckpointUnlockRecordKey(activityId, checkpointNo), tnId);
        } catch (Exception e) {
            logger.error("{}", e.getMessage(), e);
        }
    }

    private String getQuestionListKey(String uid, Integer activityId, Integer checkpointNo, Integer slang) {
        return "list:checkpointQuestionGroup:" + DateHelper.ARABIAN.formatDateInDay() + "_" + activityId + "_" + checkpointNo + "_" + uid + "_" + slang;
    }

    private String getCheckpointQuizCountKey(Integer activityId, Integer checkpointNo, Integer timesType) {
        return "hash:checkpointQuizCount:" + (timesType == 0 ? DateHelper.ARABIAN.formatDateInDay() : "") + "_" + activityId + "_" + checkpointNo;
    }

    private String getGainRewardKey(Integer activityId, String uid) {
        return "zset:quizGainRewardRecord:" + activityId + "_" + uid;
    }

    private String getRankingKey(Integer activityId) {
        return "zset:checkpointRanking:" + activityId;
    }

    private String getCheckpointUnlockRecordKey(Integer activityId, Integer checkpointNo) {
        return "set:checkpointUnlockRecord:" + "_" + activityId + "_" + checkpointNo;
    }
}
