package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class BlindBoxRedis {
    private static final Logger logger = LoggerFactory.getLogger(BlindBoxRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String GAME_NAME = "BlindBox";
    private String blindBoxDailyRankingExpireDate = "";


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    /**
     * 每日任务
     */
    private String getBlindBoxDailyTaskKey(String activityId, String uid, String taskId) {
        String dateStr = DateHelper.ARABIAN.formatDateInDay();
        return String.format("str:%s:BlindBoxDailyTask:%s:%s:%s:%s", GAME_NAME, activityId, dateStr, uid, taskId);
    }

    public int getBlindBoxDailyTaskScore(String activityId, String uid, String taskId) {
        try{
            String score = clusterTemplate.opsForValue().get(getBlindBoxDailyTaskKey(activityId, uid, taskId));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getBlindBoxDailyTaskScore error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    public void incrBlindBoxDailyTaskScore(String activityId, String uid, String taskId, int score) {
        try {
            String key = getBlindBoxDailyTaskKey(activityId, uid, taskId);
            clusterTemplate.opsForValue().increment(key, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrBlindBoxDailyTaskScore error activityId={} uid={} taskId={} score={} e={}", activityId, uid, taskId, score, e.getMessage(), e);
        }
    }

    /**
     * 进阶任务
     */
    private String getBlindBoxAdvanceTaskKey(String activityId, String uid, String taskId) {
        return String.format("str:%s:BlindBoxAdvanceTask:%s:%s:%s", GAME_NAME, activityId, uid, taskId);
    }

    public int getBlindBoxAdvanceTaskScore(String activityId, String uid, String taskId) {
        try{
            String score = clusterTemplate.opsForValue().get(getBlindBoxAdvanceTaskKey(activityId, uid, taskId));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getBlindBoxAdvanceTaskScore error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    public void incrBlindBoxAdvanceTaskScore(String activityId, String uid, String taskId, int score) {
        try {
            String key = getBlindBoxAdvanceTaskKey(activityId, uid, taskId);
            clusterTemplate.opsForValue().increment(key, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrBlindBoxAdvanceTaskScore error activityId={} uid={} taskId={} score={} e={}", activityId, uid, taskId, score, e.getMessage(), e);
        }
    }

    public void setBlindBoxAdvanceTaskScore(String activityId, String uid, String taskId, int score) {
        try {
            String key = getBlindBoxAdvanceTaskKey(activityId, uid, taskId);
            clusterTemplate.opsForValue().set(key, String.valueOf(score));
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setBlindBoxAdvanceTaskScore error activityId={} uid={} taskId={} score={} e={}", activityId, uid, taskId, score, e.getMessage(), e);
        }
    }

    /**
     * 任务完成次数
     */
    private String getBlindBoxFinishNumKey(String activityId, String uid, String taskId) {
        String currentMonth = DateHelper.ARABIAN.formatDateInMonth();
        return String.format("str:BlindBoxFinishNum:%s:%s:%s:%s", activityId, uid, currentMonth, taskId);
    }

    public int getBlindBoxFinishNumScore(String activityId, String uid, String taskId) {
        try{
            String score = clusterTemplate.opsForValue().get(getBlindBoxFinishNumKey(activityId, uid, taskId));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getBlindBoxFinishNumScore error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    public void incrBlindBoxFinishNumScore(String activityId, String uid, String taskId, int score) {
        try {
            String key = getBlindBoxFinishNumKey(activityId, uid, taskId);
            clusterTemplate.opsForValue().increment(key, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrBlindBoxFinishNumScore error activityId={} uid={} taskId={} score={} e={}", activityId, uid, taskId, score, e.getMessage(), e);
        }
    }



    /**
     * 每天top
     */
    private String getBlindBoxDailyKey(String activityId, int giftId, int roundNum) {
        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        return String.format("zset:BlindBoxDaily:%s:%s:%s:%s", activityId, giftId, currentDay, roundNum);
    }

    /**
     * 获取分数
     */
    public int getBlindBoxDailyRankingScore(String activityId, String uid, int giftId, int roundNum) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getBlindBoxDailyKey(activityId, giftId, roundNum), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getBlindBoxDailyRankingScore error activityId={} uid={} giftId={}", activityId, uid, giftId, e);
            return 0;
        }
    }

    public void incrBlindBoxDailyRankingScore(String activityId, String uid, int giftId, int score, int roundNum) {
        try {
            String key = getBlindBoxDailyKey(activityId, giftId, roundNum);
            int curScore = getBlindBoxDailyRankingScore(activityId, uid, giftId, roundNum);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrBlindBoxDailyRankingScore activityId={} uid={}  score={} rankScore={} giftId={}",
                    activityId, uid, score, rankScore, giftId);
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrBlindBoxDailyRankingScore error activityId={} uid={} giftId={} score={}", activityId, uid, score, e);
        }
    }


    /**
     * 获取排行榜
     *
     * @param activityId 活动
     * @param giftId   礼物id
     * @param length     排行榜长度
     */
    public List<String> getBlindBoxDailyRankingList(String activityId, int giftId, int length, int roundNum) {
        List<String> rankingList = new ArrayList<>();
        String key = getBlindBoxDailyKey(activityId, giftId, roundNum);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            logger.info("getBlindBoxDailyRankingList aid={} score={} rankType={}", rangeWithScore.getValue(), rangeWithScore.getScore().longValue(), giftId);
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }


}
