package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.data.MomentActivityData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class PainterRedis {
    private static final Logger logger = LoggerFactory.getLogger(PainterRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String ACTIVITY_NAME = "painter";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String messageKey(String momentId) {
        return "str:" + ACTIVITY_NAME + ":message:"+ momentId;
    }


    public String getMomentMessage(String momentId) {
        try {
            String message = clusterTemplate.opsForValue().get(messageKey(momentId));
            return message != null ? message: "";
        } catch (Exception e) {
            logger.info("getMomentMessage error recordId={}, e={}", momentId, e);
            return "";
        }
    }

    public void setMomentMessage(String momentId, String message) {
        try {
            clusterTemplate.opsForValue().set(messageKey(momentId), message);
            clusterTemplate.expire(messageKey(momentId), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setMomentMessage error  momentId={}, e={}", momentId, e);
        }
    }


    private String momentRankingKey(String dateParser) {
        return "zset:" + ACTIVITY_NAME + ":ranking:" + dateParser;
    }


    public void incrMomentRankingScore(String aid, int score, String dateParser) {
        try {
            String key = momentRankingKey(dateParser);
            clusterTemplate.opsForZSet().incrementScore(key, aid, score);
            clusterTemplate.expire(key, 180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrMomentRankingScore error aid={} giftId={} score={}", aid, score, e);
        }
    }

    public List<String> getMomentRankingList(String dateParser, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = momentRankingKey(dateParser);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            logger.info("getMomentRankingList aid={} score={} dailyNum={}", rangeWithScore.getValue(), rangeWithScore.getScore().longValue(), dateParser);
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }


    private String momentWrittenKey(String dateParser) {
        return "str:" + ACTIVITY_NAME + ":Written:" + dateParser;
    }


    public String getMomentWritten(String dateParser) {
        try {
            String momentId = clusterTemplate.opsForValue().get(momentWrittenKey(dateParser));
            return momentId != null ? momentId: "";
        } catch (Exception e) {
            logger.info("getMomentWritten error recordId={}, e={}", dateParser, e);
            return "";
        }
    }

    public void setMomentWritten(String dateParser, String momentId) {
        try {
            clusterTemplate.opsForValue().set(momentWrittenKey(dateParser), momentId);
            clusterTemplate.expire(momentWrittenKey(dateParser), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setMomentWritten error  momentId={}, dateParser={}, e={}", momentId, dateParser, e);
        }
    }



}
