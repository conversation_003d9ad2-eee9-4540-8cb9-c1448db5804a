package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class WorldTravelRedis {
    private static final Logger logger = LoggerFactory.getLogger(WorldTravelRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String GAME_KEY = "WorldTravelActivity";
    private String rankingExpireDate = "";


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 页面任务hash_key
     */
    private String hashActivityKey(String uid) {
        return String.format("hash:%s:%s", GAME_KEY, uid);
    }

    /**
     * 获取hash_key所有值
     */
    public Map<String, Integer> getAllHashValue(String uid) {
        Map<String, Integer> hashMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(hashActivityKey(uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.info("getAllHashValue error uid={} e={}", uid, e);
            return hashMap;
        }
    }


    public void setHashNum(String uid, String key, int num) {
        try {
            clusterTemplate.opsForHash().put(hashActivityKey(uid), key, String.valueOf(num));
            clusterTemplate.expire(hashActivityKey(uid), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incHashNum error uid={} e={}", uid, e);
        }
    }

    public int getHashValue(String uid, String key) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(hashActivityKey(uid), key);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getHashValue error uid={} key={}  e={}", uid, key, e);
            return 0;
        }
    }


    /**
     * 点赞key
     */
    public String getLikeKey(String cardKey) {
        return String.format("set:like:%s:%s", GAME_KEY, cardKey);
    }

    // 获取点赞数量
    public int getLikeSize(String cardKey) {
        try {
            Long likeSize = clusterTemplate.opsForSet().size(getLikeKey(cardKey));
            return likeSize != null ? likeSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getLikeSize error uid={} e={}", cardKey, e);
            return 0;
        }
    }

    // 点赞
    public int addLike(String uid, String cardKey) {
        try {
            String likeKey = getLikeKey(cardKey);
            Long likeSize = clusterTemplate.opsForSet().add(likeKey, uid);
            clusterTemplate.expire(likeKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return likeSize != null ? likeSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("addLike error uid={} e={}", uid, e);
            return 0;
        }
    }

    //  uid 是否已点赞
    public int isLike(String uid, String cardKey) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getLikeKey(cardKey), uid)) ? 1 : 0;
        } catch (Exception e) {
            logger.info("isLikeQueen error uid={} e={}", uid, e);
            return 1;
        }
    }



    /**
     *  排行榜key
     * @return key
     */
    private String getRankingKey() {
        return "zset:" + GAME_KEY;
    }

    /**
     * 获取分数
     */
    public int getRankingScore(String uid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getRankingKey(), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getRankingScore error uid={}", uid, e);
            return 0;
        }
    }

    public void incrRankingScore(String uid, int score) {
        try {
            String key = getRankingKey();
            int curScore = getRankingScore(uid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrRankingScore aid={}  score={} total={}", uid, score, rankScore);
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(rankingExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                rankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrRankingScore error aid={} giftId={} score={}", uid, score, e);
        }
    }


    /**
     * 获取排行榜
     *
     * @param length     排行榜长度
     */
    public List<String> getRankingList(int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getRankingKey();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     *
     * @param length     排行榜长度
     */
    public Map<String, Integer> getRankingMap(int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getRankingKey();
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getRank(String uid) {
        try {
            String key = getRankingKey();
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, uid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getRank error aid={}", uid, e);
            return 0;
        }
    }


    /**
     * 奖池key
     * @return key
     */
    private String poolSizeKey(String rateKey) {
        return String.format("list:pool_size:%s:%s", GAME_KEY, rateKey);
    }


    // 获取奖池大小
    public int getPoolSize(String rateKey) {
        try {
            Long poolSize = clusterTemplate.opsForList().size(poolSizeKey(rateKey));
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getPoolSize error score={}", e.getMessage());
            return 0;
        }
    }

    // 初始化奖池
    public void initPoolSize(List<String> rewardConfigList, String rateKey) {
        try {
            clusterTemplate.opsForList().rightPushAll(poolSizeKey(rateKey), rewardConfigList);
            clusterTemplate.expire(poolSizeKey(rateKey), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("initPoolSize error clusterTemplate={}  score={}", clusterTemplate, e);
        }
    }


    // 抽奖
    public String drawPoolSizeKey(String rateKey) {
        try {
            String cardKey = clusterTemplate.opsForList().leftPop(poolSizeKey(rateKey));
            return cardKey != null ? cardKey : "";
        } catch (Exception e) {
            logger.info("drawPoolSizeKey error e={}", e.getMessage());
            return "";
        }
    }



}
