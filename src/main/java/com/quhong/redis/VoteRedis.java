package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
@Lazy
@Component
public class VoteRedis {

    private static final Logger logger = LoggerFactory.getLogger(VoteRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    public void saveRoomVote(String roomId, int voteId) {
        String key = getRoomVoteKey();
        try {
            clusterTemplate.opsForHash().put(key, roomId, String.valueOf(voteId));
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save room vote error. roomId={} voteId={} {}", roomId, voteId, e.getMessage(), e);
        }
    }

    public int getRoomVote(String roomId) {
        try {
            String strValue = (String) clusterTemplate.opsForHash().get(getRoomVoteKey(), roomId);
            return !StringUtils.isEmpty(strValue) ? Integer.parseInt(strValue) : 0;
        } catch (Exception e) {
            logger.error("get room vote error. roomId={} {}", roomId, e.getMessage(), e);
            return 0;
        }
    }

    public void removeRoomVote(String roomId) {
        try {
            clusterTemplate.opsForHash().delete(getRoomVoteKey(), roomId);
        } catch (Exception e) {
            logger.error("remove room vote error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public void saveVoteGiftId(int voteId, int giftId) {
        String key = getGiftVoteGidKey();
        try {
            clusterTemplate.opsForHash().put(key, String.valueOf(voteId), String.valueOf(giftId));
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save vote gift id error. voteId={} giftId={} {}", voteId, giftId, e.getMessage(), e);
        }
    }

    public int getVoteGiftId(int voteId) {
        try {
            String strValue = (String) clusterTemplate.opsForHash().get(getGiftVoteGidKey(), String.valueOf(voteId));
            return !StringUtils.isEmpty(strValue) ? Integer.parseInt(strValue) : 0;
        } catch (Exception e) {
            logger.error("get vote gift id error. voteId={} {}", voteId, e.getMessage(), e);
            return 0;
        }
    }

    public void saveVoteUserId(int voteId, String uid) {
        String key = getVoteUserKey(voteId);
        try {
            clusterTemplate.opsForSet().add(key, uid);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THREE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save room vote user id error. voteId={} uid={} {}", voteId, uid, e.getMessage(), e);
        }
    }

    public Set<String> getVoteUserId(int voteId) {
        String key = getVoteUserKey(voteId);
        try {
            return clusterTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.error("getVoteUserId error. voteId={} {}", voteId, e.getMessage(), e);
        }
        return Collections.emptySet();
    }

    public int getVotingUserNum(Integer voteId) {
        try {
            Long size = clusterTemplate.opsForSet().size(getVoteUserKey(voteId));
            return null == size ? 0 : size.intValue();
        } catch (Exception e) {
            logger.error("get vote user num error. voteId={} {}", voteId, e.getMessage(), e);
            return 0;
        }
    }

    public boolean hasAlreadyVoted(int voteId, String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getVoteUserKey(voteId), uid));
        } catch (Exception e) {
            logger.error("get room vote user id error. voteId={} uid={} {}", voteId, uid, e.getMessage(), e);
        }
        return false;
    }

    public void saveUserVotingOption(int voteId, String uid, List<Integer> optionList) {
        String key = getUserVotingOptionKey(uid, voteId);
        try {
            String[] toArray = optionList.stream().map(String::valueOf).toArray(String[]::new);
            clusterTemplate.opsForSet().add(key, toArray);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save user voting option error. voteId={} uid={} {}", voteId, uid, e.getMessage(), e);
        }
    }

    public boolean hasAlreadyVotedOption(int voteId, String uid, int optionId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getUserVotingOptionKey(uid, voteId), String.valueOf(optionId)));
        } catch (Exception e) {
            logger.error("check user has already voted option error. voteId={} uid={} optionId={} {}", voteId, uid, optionId, e.getMessage(), e);
        }
        return false;
    }

    public List<Integer> getUserVotingOption(int voteId, String uid) {
        try {
            Set<String> strOptionIds = clusterTemplate.opsForSet().members(getUserVotingOptionKey(uid, voteId));
            if (!CollectionUtils.isEmpty(strOptionIds)) {
                return strOptionIds.stream().map(Integer::parseInt).collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("get user voting option error. voteId={} uid={}  {}", voteId, uid, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public void setVotingCloseTime(int voteId, int endTime) {
        try {
            clusterTemplate.opsForZSet().add(getVotingCloseTimeKey(), String.valueOf(voteId), endTime);
        } catch (Exception e) {
            logger.error("set voting close time error. voteId={} endTime={} {}", voteId, endTime, e.getMessage(), e);
        }
    }

    public Set<Integer> getNeedCloseVotes(int timestamp) {
        try {
            Set<String> strVoteIds = clusterTemplate.opsForZSet().rangeByScore(getVotingCloseTimeKey(), 0, timestamp);
            if (!CollectionUtils.isEmpty(strVoteIds)) {
                return strVoteIds.stream().map(Integer::parseInt).collect(Collectors.toSet());
            }
        } catch (Exception e) {
            logger.error("get voting close time error. timestamp={} {}", timestamp, e.getMessage(), e);
        }
        return null;
    }

    public void removeVotingCloseTime(int voteId) {
        try {
            clusterTemplate.opsForZSet().remove(getVotingCloseTimeKey(), String.valueOf(voteId));
        } catch (Exception e) {
            logger.error("remove voting close time error. voteId={} {}", voteId, e.getMessage(), e);
        }
    }

    private String getVotingCloseTimeKey() {
        return "zset:votingCloseTime";
    }

    private String getRoomVoteKey() {
        return "hash:roomVote";
    }

    private String getGiftVoteGidKey() {
        return "hash:giftVoteGid";
    }

    private String getVoteUserKey(int voteId) {
        return "set:voteUser_" + voteId;
    }

    private String getUserVotingOptionKey(String uid, int voteId) {
        return "set:userVotingOption_" + uid + "_" + voteId;
    }
}
