package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.PopularListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Lazy
@Component
public class PlantTreeRedis {

    private static final Logger logger = LoggerFactory.getLogger(PlantTreeRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private String waterRankingExpireDate = "";


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    private String shareMomentKey(String uid,  String currentDate) {
        return "str:plant_tree_share_moment:" + uid + ":" + currentDate;
    }

    public int getShareMoment(String uid, String currentDate) {
        try {
            String valueStr = clusterTemplate.opsForValue().get(shareMomentKey(uid, currentDate));
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getShareMoment error uid={} currentDate={}  e={}", uid, currentDate, e);
            return 0;
        }
    }

    public void setShareMoment(String uid, String currentDate, int value) {
        try {
            clusterTemplate.opsForValue().set(shareMomentKey(uid, currentDate), String.valueOf(value));
            clusterTemplate.expire(shareMomentKey(uid, currentDate), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setShareMoment error uid={} e={}", uid, e);
        }
    }

    /**
     * popular 房间列表
     */

    private String getPopularListKey() {
        return "str:popularList:area1:page1";
    }


    public List<PopularListVO> getPopularList() {
        List<PopularListVO> result = new ArrayList<>();
        try {
            String json = clusterTemplate.opsForValue().get(getPopularListKey());
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, PopularListVO.class);
        } catch (Exception e) {
            logger.error("get popularList error e={}", e.getMessage());
            return result;
        }
    }

    /**
     * 浇水勤劳榜key
     * @return key
     */
    private String getWaterActivityKey(String activityId) {
        return "zset:plant_tree_water:" + activityId;
    }

    /**
     * 获取分数
     */
    public int getWaterRankingScore(String activityId, String aid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getWaterActivityKey(activityId), aid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getWaterRankingScore activityId={} aid={}", activityId, aid, e);
            return 0;
        }
    }

    public void incrWaterRankingScore(String activityId, String aid, int score) {
        try {
            String key = getWaterActivityKey(activityId);
            int curScore = getWaterRankingScore(activityId, aid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            logger.info("incrWaterRankingScore activityId={} aid={}  score={} total={}", activityId, aid, score, rankScore);
            clusterTemplate.opsForZSet().add(key, aid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(waterRankingExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                waterRankingExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrWaterRankingScore error activityId={} aid={} giftId={} score={}", activityId, aid, score, e);
        }
    }

    /**
     * 获取带分数排行榜
     */
    public Map<String, Integer> getWaterRankingMap(String activityId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getWaterActivityKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    private String getDecorationKey(String uid) {
        return "str:user_take_decoration:" + uid;
    }

    public void setUserDecoration(String uid, String pictureUrl) {
        try {
            clusterTemplate.opsForValue().set(getDecorationKey(uid), pictureUrl);
            clusterTemplate.expire(getDecorationKey(uid), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setUserDecoration error uid={} e={}", uid, e);
        }
    }

}
