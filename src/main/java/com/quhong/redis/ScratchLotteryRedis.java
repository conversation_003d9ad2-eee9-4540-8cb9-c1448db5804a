package com.quhong.redis;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/11
 */
@Component
public class ScratchLotteryRedis extends ActivityRedis {

    public long getWinnerRankingScore(String activityId, String aid) {
        return getRankingScore(getRankingKey(activityId), aid);
    }

    public int getWinnerRankingRank(String activityId, String aid) {
        return getRankingRank(getRankingKey(activityId), aid);
    }

    public void incrWinnerRankingScore(String activityId, String aid, int score) {
        incrRankingScore(getRankingKey(activityId), aid, score);
    }

    public Map<String, Long> getWinnerRankingMap(String activityId, int length) {
        return getRankingMap(getRankingKey(activityId), length);
    }

    private String getRankingKey(String activityId) {
        return "zset:ranking_" + activityId;
    }


    public void setUserData(String activityId, String uid, String hashKey, String hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, hashValue);
    }

    public void setUserData(String activityId, String uid, String hashKey, int hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, String.valueOf(hashValue));
    }

    public long incUserData(String activityId, String uid, String hashKey, int incValue) {
       return  incHashValue(getUserDataKey(activityId, uid), hashKey, incValue);
    }



    public Map<String, Long> getUserData(String activityId, String uid) {
        Map<String, String> hashMap = getHashMap(getUserDataKey(activityId, uid));
        return hashMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> Long.parseLong(entry.getValue())));
    }

    public long getUserData(String activityId, String uid, String hashKey) {
        return getHashIntValue(getUserDataKey(activityId, uid), hashKey);
    }

    private String getUserDataKey(String activityId, String uid) {
        return "hash:userData_" + activityId + "_" + uid;
    }

    public int getPoolSize(String activityId, String poolName) {
        return getListSize(getDrawPoolKey(activityId, poolName));
    }

    public void pushRewardInPool(String activityId, String poolName, List<String> rewardList) {
        listRightPushAll(getDrawPoolKey(activityId, poolName), rewardList);
    }

    public List<String> popRewardFromPool(String activityId, String poolName, int num) {
        return listLeftPop(getDrawPoolKey(activityId, poolName), num);
    }

    private String getDrawPoolKey(String activityId, String poolName) {
        return "list:drawPool_" + activityId + "_" + poolName;
    }

    public void addRewardNotify(String activityId, String jsonRecord) {
        String key = getRewardNotifyKey(activityId);
        int listSize = listLeftPush(key, jsonRecord);
        if (listSize > 100) {
            listTrim(key, 0, 10);
        }
    }

    public List<String> getRewardNotifyList(String activityId) {
        return listRange(getRewardNotifyKey(activityId), 0, 9);
    }

    private String getRewardNotifyKey(String activityId) {
        return "list:drawRewardNotify_" + activityId;
    }

    public void saveRecord(String activityId, String recordType, String uid, String json) {
        listLeftPush(getRecordKey(activityId, recordType, uid), json);
    }

    public List<String> getRecordList(String activityId, String recordType, String uid, int start, int end) {
        return listRange(getRecordKey(activityId, recordType, uid), start, end);
    }

    private String getRecordKey(String activityId, String recordType, String uid) {
        return "list:record_" + activityId + "_" + recordType + "_" + uid;
    }


    // ============================   用户记录   =================================

    public void saveUserRecord(String activityId, String recordType, String uid, String json) {
        listLeftPush(getUserRecordKey(activityId, recordType, uid), json);
    }

    public List<String> getUserRecordList(String activityId, String recordType, String uid, int start, int end) {
        return listRange(getUserRecordKey(activityId, recordType, uid), start, end);
    }

    private String getUserRecordKey(String activityId, String recordType, String uid) {
        return "list:userRecord_" + activityId  + "_" + recordType + "_" + uid;
    }

    // ============================   排行榜   =================================

    public long getRankingScore(String activityId, String rankingName, String aid) {
        return getRankingScore(getRankingKey(activityId, rankingName), aid);
    }

    public int getRankingRank(String activityId, String rankingName, String aid) {
        return getRankingRank(getRankingKey(activityId, rankingName), aid);
    }

    public long incrRankingScore(String activityId, String rankingName, String aid, int score) {
        return incrRankingScore(getRankingKey(activityId, rankingName), aid, score);
    }

    public int getRankingSize(String activityId, String rankingName) {
        return getRankingSize(getRankingKey(activityId, rankingName));
    }

    public int getRankingSize(String activityId, String rankType, int min) {
        return getRankingSizeByScoreRange(getRankingKey(activityId, rankType), min, Double.MAX_VALUE);
    }

    public Map<String, Long> getRankingMap(String activityId, String rankingName, int length) {
        return getRankingMap(getRankingKey(activityId, rankingName), length);
    }

    public Map<String, Long> getRankingMap(String activityId, String rankingName, int start, int end) {
        return getRankingMap(getRankingKey(activityId, rankingName), start, end, true);
    }

    public Map<String, Long> getRankingMap(String activityId, String rankingName, int start, int end, boolean reverse) {
        return getRankingMap(getRankingKey(activityId, rankingName), start, end, reverse);
    }

    private String getRankingKey(String activityId, String rankingName) {
        return "zset:ranking_" + activityId + "_" + rankingName;
    }

    // ====================      通知      ===================

    public void addNotify(String activityId, String notifyType, String jsonRecord) {
        String key = getNotifyKey(activityId, notifyType);
        int listSize = listLeftPush(key, jsonRecord);
        if (listSize > 100) {
            listTrim(key, 0, 20);
        }
    }

    public List<String> getNotifyList(String activityId, String notifyType, int length) {
        return listRange(getNotifyKey(activityId, notifyType), 0, length - 1);
    }

    private String getNotifyKey(String activityId, String notifyType) {
        return "list:notify_" + activityId + "_" + notifyType;
    }
}
