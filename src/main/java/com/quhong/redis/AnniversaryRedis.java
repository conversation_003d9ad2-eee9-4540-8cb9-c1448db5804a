package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class AnniversaryRedis {
    private static final Logger logger = LoggerFactory.getLogger(AnniversaryRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * 六周年荣誉称号下发key
     */
    private String getAnniversarySixHonorTitleKey(String activityId, String uid) {
        return String.format("hash:AnniversarySixHonorTitle:%s:%s", activityId, uid);
    }

    public Map<String, Integer> getAnniversarySixAll(String activityId, String uid) {
        Map<String, Integer> anniversary = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getAnniversarySixHonorTitleKey(activityId, uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                anniversary.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return anniversary;
        } catch (Exception e) {
            logger.info("getAnniversarySixAll error uid={} e={}", uid, e);
            return anniversary;
        }
    }


    public void updateAnniversarySixHonorTitle(String activityId, String uid, String key, int status) {
        try {
            String rdsKey = getAnniversarySixHonorTitleKey(activityId, uid);
            clusterTemplate.opsForHash().put(rdsKey, key, String.valueOf(status));
            clusterTemplate.expire(rdsKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("updateAnniversarySixHonorTitle error uid={} e={}", uid, e);
        }
    }

    /**
     * 六周年任务完成度key
     */
    private String getAnniversarySixTaskKey(String activityId, String uid) {
        return String.format("hash:AnniversarySixTask:%s:%s", activityId, uid);
    }

    public Map<String, Integer> getAnniversaryTaskAll(String activityId, String uid) {
        Map<String, Integer> anniversary = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getAnniversarySixTaskKey(activityId, uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                anniversary.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return anniversary;
        } catch (Exception e) {
            logger.info("getAnniversaryTaskAll error uid={} e={}", uid, e);
            return anniversary;
        }
    }

    public void updateAnniversarySixTask(String activityId, String uid, String key, int num) {
        try {
            String taskKey = getAnniversarySixTaskKey(activityId, uid);
            clusterTemplate.opsForHash().put(taskKey, key, String.valueOf(num));
            clusterTemplate.expire(taskKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("updateAnniversarySixTask error uid={} e={}", uid, e);
        }
    }

    // 进入房间
    private String getUserRoomTaskKey(String activityId, String uid) {
        return String.format("set:AnniversaryTaskUserRoom:%s:%s", activityId, uid);
    }

    public int addUserRoomTask(String activityId, String uid, String roomId) {
        try {
            String key = getUserRoomTaskKey(activityId, uid);
            Long size = clusterTemplate.opsForSet().add(key, roomId);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return size != null ? size.intValue():0;
        } catch (Exception e) {
            logger.info("addUserRoomTask error uid={} e={}", uid, e);
            return 0;
        }
    }

    // 玩游戏
    private String getUserPlayGameTaskKey(String activityId, String uid) {
        return String.format("set:AnniversaryTaskPlayGame:%s:%s", activityId, uid);
    }

    public int addUserPlayGameTask(String activityId, String uid, String item) {
        try {
            String key = getUserPlayGameTaskKey(activityId, uid);
            Long size = clusterTemplate.opsForSet().add(key, item);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return size != null ? size.intValue():0;
        } catch (Exception e) {
            logger.info("addUserPlayGameTask error uid={} e={}", uid, e);
            return 0;
        }
    }


    private String getBagGiftKey(String activityId) {
        return String.format("set:AnniversaryBagGift:%s", activityId);
    }

    public void addUserBagGift(String activityId, String tnId) {
        try {
            String key = getBagGiftKey(activityId);
            clusterTemplate.opsForSet().add(key, tnId);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addUserBagGift error tnId={} e={}", tnId, e);
        }
    }

    public int isGetBagGift(String activityId, String tnId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getBagGiftKey(activityId), tnId)) ? 1 : 0;
        } catch (Exception e) {
            logger.error("isLikeExpress error tnId={} e={}", tnId, e);
            return 1;
        }
    }

}
