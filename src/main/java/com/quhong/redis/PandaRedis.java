package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class PandaRedis {
    private static final Logger logger = LoggerFactory.getLogger(PandaRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String GAME_NAME = "Panda";
    private String totalFeedExpireDate = "";
    private String userDevoteExpireDate = "";
    private String reFeedExpireDate = "";


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;


    /**
     * 总发送数量
     */
    private String totalFeedKey(String activityId) {
        return String.format("str:%s:totalFeed:%s", GAME_NAME, activityId);
    }

    public int getTotalFeedScore(String activityId) {
        try{
            String score = clusterTemplate.opsForValue().get(totalFeedKey(activityId));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getTotalFeedScore error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    public void incrTotalFeedScore(String activityId, int score) {
        try {
            String key = totalFeedKey(activityId);
            clusterTemplate.opsForValue().increment(key, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(totalFeedExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                totalFeedExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrTotalFeedScore error activityId={} score={} e={}", activityId, score, e.getMessage(), e);
        }
    }


    /**
     * 每个人在每个阶段贡献
     *
     */
    private String getUserDevoteKey(String activityId, int stage) {
        return String.format("zset:%s:userDevote:%s:%s", GAME_NAME, activityId, stage);
    }


    public int getUserDevoteScore(String activityId, int stage, String uid) {
        try{
            Double score = clusterTemplate.opsForZSet().score(getUserDevoteKey(activityId, stage), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getUserDevoteLevelScore error activityId={} stage={}, uid={}, e={}", activityId, stage, uid, e.getMessage(), e);
            return 0;
        }
    }

    public void incrUserDevoteScore(String activityId, int stage, String uid, int score) {
        try {
            String key = getUserDevoteKey(activityId, stage);
            int curScore = getUserDevoteScore(activityId, stage, uid);
            double rankScore = new BigDecimal(curScore + score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(userDevoteExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                userDevoteExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incrTotalMileageScore error activityId={} uid={} score={} e={}", activityId, uid, score, e.getMessage(), e);
        }
    }

    public List<String> getUserDevoteRankingList(String activityId, int stage, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getUserDevoteKey(activityId, stage);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    // 获取带分数排行榜
    public Map<String, Integer> getUserDevoteRankingMap(String activityId, int stage, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getUserDevoteKey(activityId, stage);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 签到
     */
    private String getSignDayKey(String activityId, String uid, String signDay) {
        return String.format("str:%s:SignDay:%s:%s:%s", GAME_NAME, activityId, uid, signDay);
    }

    public int getSignDayValue(String activityId, String uid, String signDay) {
        try{
            String sign = clusterTemplate.opsForValue().get(getSignDayKey(activityId, uid, signDay));
            return sign == null ? 0 : 1;
        } catch (Exception e) {
            logger.info("getSignDayValue error activityId={} uid={}, signDay={}, e={}", activityId, uid, signDay, e.getMessage(), e);
        }
        return 0;
    }

    public void setSignDayValue(String activityId, String uid, String signDay) {
        try{
            clusterTemplate.opsForValue().set(getSignDayKey(activityId, uid, signDay), "1", COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setSignDayValue error activityId={} e={}", activityId, e.getMessage(), e);
        }
    }

    /**
     * 补签次数
     */
    private String getReFeedKey(String activityId, String uid) {
        return String.format("str:%s:ReFeed:%s:%s", GAME_NAME, activityId, uid);
    }

    public int getReFeedValue(String activityId, String uid) {
        try{
            String reFeed = clusterTemplate.opsForValue().get(getReFeedKey(activityId, uid));
            return reFeed == null ? 0 : Integer.parseInt(reFeed);
        } catch (Exception e) {
            logger.info("getReFeedValue error activityId={} uid={}, e={}", activityId, uid, e.getMessage(), e);
        }
        return 0;
    }

    public void incReFeedValue(String activityId, String uid, int score) {
        try{
            String key = getReFeedKey(activityId, uid);
            clusterTemplate.opsForValue().increment(key, score);
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            if (!dateStr.equals(reFeedExpireDate)) {
                clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
                reFeedExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.info("incReFeedValue error activityId={} e={}", activityId, e.getMessage(), e);
        }
    }

    /**
     * 今天是否已分享
     */
    private String getSharePandaKey(String uid, String shareDay) {
        return String.format("str:%s:ReFeed:%s:%s", GAME_NAME, uid, shareDay);
    }

    public int getSharePandaValue(String uid, String shareDay) {
        try{
            String share = clusterTemplate.opsForValue().get(getSharePandaKey(uid, shareDay));
            return share == null ? 0 : 1;
        } catch (Exception e) {
            logger.info("getReFeedValue error uid={}, shareDay={}, e={}",  uid, shareDay, e.getMessage(), e);
        }
        return 0;
    }

    public void setSharePandaValue(String uid, String shareDay) {
        try{
            String key =getSharePandaKey(uid, shareDay);
            clusterTemplate.opsForValue().set(key, "1");
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setSharePandaValue error uid={}, shareDay={}, e={}",  uid, shareDay, e.getMessage(), e);
        }
    }

    /**
     * 抽奖次数
     */
    private String getDrawAwardKey(String activityId, String uid) {
        return String.format("str:%s:DrawAward:%s:%s", GAME_NAME, activityId, uid);
    }

    public int getDrawAwardValue(String activityId, String uid) {
        try{
            String value = clusterTemplate.opsForValue().get(getDrawAwardKey(activityId, uid));
            return value == null ? 0 : Integer.parseInt(value);
        } catch (Exception e) {
            logger.info("getDrawAwardValue error activityId={} uid={}, e={}", activityId, uid, e.getMessage(), e);
        }
        return 0;
    }

    public void incDrawAwardValue(String activityId, String uid, int score) {
        try{
            String key = getDrawAwardKey(activityId, uid);
            clusterTemplate.opsForValue().increment(key, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incDrawAwardValue error activityId={} e={}", activityId, e.getMessage(), e);
        }
    }


    private String getPoolSizeKey() {
        return "list:pool_size:" + GAME_NAME;
    }

    public int getPoolSize() {
        try {
            Long poolSize = clusterTemplate.opsForList().size(getPoolSizeKey());
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getPoolSize error={}", e.getMessage(), e);
            return 0;
        }
    }


    public void initPoolSize(List<String> rewardConfigList) {
        try {
            clusterTemplate.opsForList().rightPushAll(getPoolSizeKey(), rewardConfigList);
            clusterTemplate.expire(getPoolSizeKey(), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            logger.info("initPoolSize  error={}", e.getMessage(), e);
        }
    }


    // 抽奖
    public String drawCardKey() {
        try {
            String cardKey = clusterTemplate.opsForList().leftPop(getPoolSizeKey());
            return cardKey != null ? cardKey : "";
        } catch (Exception e) {
            logger.info("drawCardKey error = {}", e.getMessage(), e);
            return "";
        }
    }

}
