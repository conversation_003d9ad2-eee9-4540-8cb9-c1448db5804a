package com.quhong.redis;

import com.quhong.core.date.DateSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class RamadanEndRedis {
    private static final Logger logger = LoggerFactory.getLogger(RamadanEndRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String ACTIVITY_NAME = "ramadan_food";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String poolSizeKey() {
        return "list:pool_size:"+ ACTIVITY_NAME;
    }

    public int getPoolSize() {
        try {
            Long poolSize = clusterTemplate.opsForList().size(poolSizeKey());
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.info("getPoolSize error={}", e.getMessage(), e);
            return 0;
        }
    }


    public void initPoolSize(List<String> rewardConfigList) {
        try {
            clusterTemplate.opsForList().rightPushAll(poolSizeKey(), rewardConfigList);
            clusterTemplate.expire(poolSizeKey(), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            logger.info("initPoolSize  error={}", e.getMessage(), e);
        }
    }


    // 抽奖
    public String drawCardKey() {
        try {
            String cardKey = clusterTemplate.opsForList().leftPop(poolSizeKey());
            return cardKey != null ? cardKey : "";
        } catch (Exception e) {
            logger.info("drawCardKey error = {}", e.getMessage(), e);
            return "";
        }
    }


    /**
     * 获取美食所有值
     */
    private String hashRamadanEndKey(String uid) {
        return "hash:ramadan_end:" + uid;
    }


    public Map<String, Integer> getRamadanEndAll(String uid) {
        Map<String, Integer> ramadanMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(hashRamadanEndKey(uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                ramadanMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return ramadanMap;
        } catch (Exception e) {
            logger.info("getRamadanEndAll error uid={} e={}", uid, e);
            return ramadanMap;
        }
    }

    public int getRamadanEndValue(String uid, String key) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(hashRamadanEndKey(uid), key);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getRamadanEndValue error uid={} key={}  e={}", uid, key, e);
            return 0;
        }
    }

    public void setRamadanEndKey(String uid, String key, int value) {
        try {
            clusterTemplate.opsForHash().put(hashRamadanEndKey(uid), key, String.valueOf(value));
            clusterTemplate.expire(hashRamadanEndKey(uid), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setRamadanEndKey error uid={} e={}", uid, e);
        }
    }

    /**
     * 点赞美食
     */
    private String ramadanFoodSetKey(String foodKey) {
        return "set:ramadan_end:" + foodKey;
    }

    public int getLikeFoodNums(String foodKey) {
        try {
            Long likeUserNums = clusterTemplate.opsForSet().size(ramadanFoodSetKey(foodKey));
            return ObjectUtils.isEmpty(likeUserNums) ? 0 : likeUserNums.intValue();
        } catch (Exception e) {
            logger.info("getLikeUserNums error foodKey={}  score={}", foodKey, e);
            return 0;
        }
    }

    public void setLikeFoodNums(String foodKey, String uid) {
        try {

            boolean likeUserFlag = Boolean.TRUE.equals(clusterTemplate.hasKey(ramadanFoodSetKey(foodKey)));
            clusterTemplate.opsForSet().add(ramadanFoodSetKey(foodKey), uid);
            if (!likeUserFlag) {
                clusterTemplate.expire(ramadanFoodSetKey(foodKey), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            logger.info("setLikeUserNums error foodKey={}  score={}", foodKey, e);
        }
    }

    public boolean isLikeFood(String foodKey, String uid) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(ramadanFoodSetKey(foodKey), uid));
        } catch (Exception e) {
            logger.info("isLikeUser error foodKey={}  score={}", foodKey, e);
        }
        return false;
    }


    /**
     * 获取宝箱所有值
     */
    private String hashRamadanBoxKey(String uid) {
        return "hash:ramadan_box:" + uid;
    }


    public Map<String, Integer> getRamadanBoxAll(String uid) {
        Map<String, Integer> ramadanBoxMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(hashRamadanBoxKey(uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                ramadanBoxMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return ramadanBoxMap;
        } catch (Exception e) {
            logger.info("getRamadanBoxAll error uid={} e={}", uid, e);
            return ramadanBoxMap;
        }
    }

    public void setRamadanBoxKey(String uid, String key, int value) {
        try {
            clusterTemplate.opsForHash().put(hashRamadanBoxKey(uid), key, String.valueOf(value));
            clusterTemplate.expire(hashRamadanBoxKey(uid), COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setRamadanBoxKey error uid={} e={}", uid, e);
        }
    }




}
