package com.quhong.redis;

import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class MiniTaskRedis {
    private static final Logger logger = LoggerFactory.getLogger(MiniTaskRedis.class);
    private static final int COMMON_EXPIRE_DAYS = 30;
    private static final String ACTIVITY_NAME = "miniTaskV2";

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String getMiniTaskKey(String taskKey, String uid) {
        return String.format("hash:%s:%s:%s", ACTIVITY_NAME, taskKey, uid);
    }


    public Map<String, Integer> getMiniTaskAll(String taskKey, String uid) {
        Map<String, Integer> miniTask = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getMiniTaskKey(taskKey, uid));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                miniTask.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return miniTask;
        } catch (Exception e) {
            logger.error("getMiniTaskAll error uid={} e={}", uid, e.getMessage(), e);
        }
        return miniTask;
    }

    public void updateMiniTask(String taskPhase, String uid, String key, int num) {
        try {
            String taskKey = getMiniTaskKey(taskPhase, uid);
            clusterTemplate.opsForHash().put(taskKey, key, String.valueOf(num));
            clusterTemplate.expire(taskKey, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("updateMiniTask error uid={} e={}", uid, e.getMessage(), e);
        }
    }



    /**
     * 通用set
     */
    public String getMiniCommonSetKey(String taskKey, String uid) {
        return String.format("set:MiniCommon:%s:%s:%s", ACTIVITY_NAME, taskKey, uid);
    }

    public int addMiniCommonSet(String taskKey, String uid, String targetId) {
        try {
            String key = getMiniCommonSetKey(taskKey, uid);
            Long setSize = clusterTemplate.opsForSet().add(key, targetId);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return setSize != null ? setSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("addMiniCommonSet error uid={} e={}", uid, e.getMessage(), e);
        }
        return 0;
    }

    public int isMiniCommonSet(String taskKey, String uid, String targetId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getMiniCommonSetKey(taskKey, uid), targetId)) ? 1 : 0;
        } catch (Exception e) {
            logger.error("isMiniCommonSet error uid={} e={}", uid, e);
        }
        return 1;
    }

    private String getSpendGiftKey(String taskKey, String uid) {
        return String.format("zset:SpendGift:%s:%s:%s", ACTIVITY_NAME, taskKey, uid);
    }


    public int getSpendGiftScore(String taskKey, String uid, String hostId) {
        try{
            String key = getSpendGiftKey(taskKey, uid);
            Double score = clusterTemplate.opsForZSet().score(key, hostId);
            if (score != null) {
                return score.intValue();
            }
            return 0;
        } catch (Exception e) {
            logger.error("getSpendGiftScore error uid={} hostId={}, e={}", uid, hostId, e.getMessage(), e);
        }
        return 0;
    }

    public int incrSpendGiftScore(String taskKey, String uid, String hostId, int score) {
        try {
            String key = getSpendGiftKey(taskKey, uid);
            Double value = clusterTemplate.opsForZSet().incrementScore(key, hostId, score);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            logger.error("incrSpendGiftScore error uid={} hostId={} score={}", uid, hostId, score, e);
        }
        return 0;
    }


    private String getDeviceAwardKey(String taskPhase) {
        return String.format("set:%s:%s", ACTIVITY_NAME, taskPhase);
    }

    public void addDeviceAward(String taskPhase, String tnId) {
        try {
            String key = getDeviceAwardKey(taskPhase);
            clusterTemplate.opsForSet().add(key, tnId);
            clusterTemplate.expire(key, COMMON_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addDeviceAward error tnId={} e={}", tnId, e);
        }
    }

    public int isGetDeviceAward(String taskPhase, String tnId) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getDeviceAwardKey(taskPhase), tnId)) ? 1 : 0;
        } catch (Exception e) {
            logger.error("isGetDeviceAward error tnId={} e={}", tnId, e);
            return 1;
        }
    }

}
