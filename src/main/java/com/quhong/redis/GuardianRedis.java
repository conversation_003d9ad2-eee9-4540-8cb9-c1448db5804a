package com.quhong.redis;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/23
 */
@Component
public class GuardianRedis extends ActivityRedis {

    // ====================      榜单数据      ===================

    public long getRankingScore(String activityId, String rankType, String aid) {
        return getRankingScore(getRankingKey(activityId, rankType), aid);
    }

    public int getRankingRank(String activityId, String rankType, String aid) {
        return getRankingRank(getRankingKey(activityId, rankType), aid);
    }

    public void incrRankingScore(String activityId, String rankType, String aid, int score) {
        incrRankingScore(getRankingKey(activityId, rankType), aid, score);
    }

    public Map<String, Long> getRankingMap(String activityId, String rankType, int length) {
        return getRankingMap(getRankingKey(activityId, rankType), length);
    }

    private String getRankingKey(String activityId, String rankType) {
        return "zset:ranking_" + activityId + "_" + rankType;
    }

    // ====================      用户个人数据      ===================

    public void setUserData(String activityId, String uid, String hashKey, String hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, hashValue);
    }

    public void setUserData(String activityId, String uid, String hashKey, int hashValue) {
        setHashValue(getUserDataKey(activityId, uid), hashKey, String.valueOf(hashValue));
    }

    public void incUserData(String activityId, String uid, String hashKey, int incValue) {
        incHashValue(getUserDataKey(activityId, uid), hashKey, incValue);
    }

    public Map<String, Long> getUserData(String activityId, String uid) {
        Map<String, String> hashMap = getHashMap(getUserDataKey(activityId, uid));
        return hashMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> Long.parseLong(entry.getValue())));
    }

    public long getUserData(String activityId, String uid, String hashKey) {
        return getHashIntValue(getUserDataKey(activityId, uid), hashKey);
    }

    private String getUserDataKey(String activityId, String uid) {
        return "hash:userData_" + activityId + "_" + uid;
    }

    // ====================      抽奖奖池      ===================

    public int getPoolSize(String activityId, String poolName) {
        return getListSize(getDrawPoolKey(activityId, poolName));
    }

    public void pushRewardInPool(String activityId, String poolName, List<String> rewardList) {
        listRightPushAll(getDrawPoolKey(activityId, poolName), rewardList);
    }

    public List<String> popRewardFromPool(String activityId, String poolName, int num) {
        List<String> rewardDrawList = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            rewardDrawList.add(listLeftPop(getDrawPoolKey(activityId, poolName)));
        }
        return rewardDrawList;
    }

    private String getDrawPoolKey(String activityId, String poolName) {
        return "list:drawPool_" + activityId + "_" + poolName;
    }

    // ====================      抽奖通知      ===================

    public void addRewardNotify(String activityId, String jsonRecord) {
        String key = getRewardNotifyKey(activityId);
        int listSize = listLeftPush(key, jsonRecord);
        if (listSize > 100) {
            listTrim(key, 0, 10);
        }
    }

    public List<String> getRewardNotifyList(String activityId) {
        return listRange(getRewardNotifyKey(activityId), 0, 9);
    }

    private String getRewardNotifyKey(String activityId) {
        return "list:drawRewardNotify_" + activityId;
    }

    // ====================      抽奖通知      ===================

    public void saveRecord(String activityId, int type, String uid, String json) {
        listLeftPush(getRecordKey(activityId, type, uid), json);
    }

    public List<String> getRecordList(String activityId, int type, String uid, int start, int end) {
        return listRange(getRecordKey(activityId, type, uid), start, end);
    }

    private String getRecordKey(String activityId, int type, String uid) {
        return "list:record_" + activityId + "_" + type + "_" + uid;
    }
}
