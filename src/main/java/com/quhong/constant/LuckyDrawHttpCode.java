package com.quhong.constant;

import com.quhong.enums.HttpCode;

public class LuckyDrawHttpCode extends HttpCode {
    public static final HttpCode LUCKY_DRAW_BEANS_OUT = new HttpCode(1, "lucky_draw_beans_out");
    public static final HttpCode LUCKY_DRAW_TIME_OUT = new HttpCode(2, "lucky_draw_time_out");
    public static final HttpCode LUCKY_DRAW_TOTAL_NUM_OUT = new HttpCode(3, "lucky_draw_total_num_out");
    public static final HttpCode LUCKY_DRAW_DAILY_NUM_OUT = new HttpCode(4, "lucky_draw_daily_num_out");
    // public static final HttpCode THREE_TIMES_LIMIT = new HttpCode(9002, "three_times_limit");
    // public static final HttpCode NOT_START = new HttpCode(9003, "The activity is not start.");
    // public static final HttpCode SIGN_ALREADY = new HttpCode(9004, "You already signed.");
    // public static final HttpCode CANNOT_SIGN = new HttpCode(9005, "You cannot sign.");
}
