package com.quhong.enums;

/**
 * <AUTHOR>
 * @date 2022/9/19
 */
public enum ActivityRewardTypeEnum {

    // 金币
    COIN(-1, "coin"),
    // 钻石
    DIAMOND(0, "diamond"),
    // 勋章
    BADGE(1, "badge"),
    // 麦位框
    MIC(2, "mic"),
    // 坐骑
    RIDE(3, "ride"),
    // 背包礼物
    BAG_GIFT(4, "gift"),
    // 房间锁
    ROOM_LOCK(5, "lock"),
    // 气泡框
    BUDDLE(6, "buddle"),
    // 声波
    RIPPLE(7, "ripple"),
    // 浮屏
    FLOAT_SCREEN(8, "float_screen"),
    // 房间背景
    BACKGROUND(9, "background"),
    // 进场通知
    ENTRY_EFFECT(11, "entry_effect"),
    // 荣誉称号
    HONOR_TITLE(12, "honor_title"),
    // 充值优惠劵
    RECHARGE_COUPON(13, "recharge_coupon"),
    // 抽奖券
    TICKET(14, "ticket");

    private int code;
    private String name;

    ActivityRewardTypeEnum(int code, String name){
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ActivityRewardTypeEnum getEnumByName(String name) {
        for (ActivityRewardTypeEnum rewardTypeEnum : ActivityRewardTypeEnum.values()) {
            if (rewardTypeEnum.getName().equals(name)) {
                return rewardTypeEnum;
            }
        }
        return null;
    }
}
