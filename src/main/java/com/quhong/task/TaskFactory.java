package com.quhong.task;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.pools.TaskPool;
import org.springframework.stereotype.Component;

@Component
public class TaskFactory extends BaseTaskFactory {
    public static TaskFactory getFactory() {
        return (TaskFactory) factory;
    }

    private TaskPool msgPool;

    private TaskPool dbPool;

    public TaskFactory() {
        super(4, 8);
    }

    @Override
    public void init() {
        super.init();
    }


}
