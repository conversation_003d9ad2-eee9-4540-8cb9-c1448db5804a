package com.quhong.deliver;

import com.mongodb.client.result.UpdateResult;
import com.quhong.analysis.ActivityRankingEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.ScoreRecordEvent;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.constant.TaskRankConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.LogType;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RankingActivityDao;
import com.quhong.mongo.dao.TaskRankTemplateDao;
import com.quhong.mongo.data.ConquerActivity;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.mongo.data.TaskRankTemplateData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.mq.ResourceDeliveryService;
import com.quhong.msg.obj.RankInfoListObject;
import com.quhong.msg.obj.RankInfoObject;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.msg.room.RankNotificationPushMsg;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.redis.RankingActivityRedis;
import com.quhong.redis.TaskRankRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.TestRoomService;
import com.quhong.service.ActivityUtilService;
import com.quhong.service.RankActivityService;
import com.quhong.service.ResourceKeyHandlerService;
import com.quhong.service.TaskRankTemplateService;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class TaskRankTemplateDeliver {

    private final static Logger logger = LoggerFactory.getLogger(TaskRankTemplateDeliver.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    @Resource
    private TestRoomService testRoomService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private TaskRankRedis taskRankRedis;
    @Resource
    private TaskRankTemplateDao taskRankTemplateDao;
    @Resource
    private EventReport eventReport;

    /**
     * 任务榜单模板活动结算
     */
    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<TaskRankTemplateData> initActivities = taskRankTemplateDao.getTaskRankingActivity();
        msgLogger.info("TaskRank activity deliver check. initActivities={}", initActivities.size());
        for (TaskRankTemplateData activity : initActivities) {
            try {
                // 活动已到期结束
                if (activity.getEndTime() < nowSeconds) {
                    msgLogger.info("TaskRank activity deliver activityId={}", activity.getActivityId());
                    UpdateResult updateResult = taskRankTemplateDao.updateData(activity.getActivityId(), new Update().set("status", TaskRankConstant.STATUS_OFFLINE));
                    if (null == updateResult || updateResult.getModifiedCount() == 0) {
                        if (ServerConfig.isProduct()) {
                            monitorSender.info("activity", "任务榜单活动结束异常", "任务榜单活动更新失败");
                        }
                        continue;
                    }
                    doSendReward(activity);
                    if (ServerConfig.isProduct()) {
                        monitorSender.info("activity", "任务榜单活动成功结束", "活动名：" + activity.getNameEn());
                    }
                }
            } catch (Exception e) {
                logger.error("TaskRank ActivityDeliver error.", e);
                if (ServerConfig.isProduct()) {
                    monitorSender.info("activity", "任务榜单活动结束异常", e.getMessage());
                }
            }
        }
    }

    private void doSendReward(TaskRankTemplateData activity) {
        if (!activity.isRankConfigEnable()) {
            msgLogger.info("doSendReward TaskRank activityId={} no rankReward", activity.getActivityId());
            return;
        }
        // 这里只能配置一种榜单奖励类型
        TaskRankTemplateData.TaskRankConfig taskRankConfig = activity.getRankConfig();
        List<TaskRankTemplateData.RankRewardConfig> rankingConfigList = taskRankConfig.getRankRewardList();
        String acNameEn = activity.getNameEn();
        String itemTitle = String.format(TaskRankTemplateService.TITLE_RANK_TASK, acNameEn);
        String taskRankKey = taskRankConfig.getTaskRankKey();
        List<String> rankingList = taskRankRedis.getActivityRankList(activity.getActivityId(), taskRankKey, TaskRankConstant.RANK_TOP_N);
        boolean isRoomIdType = TaskRankConstant.RANK_ROOM_DEVOTE.equals(taskRankKey) || TaskRankConstant.RANK_ROOM_EVENT_DEVOTE.equals(taskRankKey);
        for (TaskRankTemplateData.RankRewardConfig rankingRewardConfig : rankingConfigList) {
            // 奖励对象
            int rank = rankingRewardConfig.getRankNum();
            if (rankingList.size() < rank) {
                logger.error("cannot find rank activityId={} rank={}", activity.getActivityId(), rank);
                continue;
            }
            String aid = rankingList.get(rank - 1);
            if (isRoomIdType) {
                aid = RoomUtils.getRoomHostId(aid);
            }

            // 资源key下发资源
            String rewardResourceKey = rankingRewardConfig.getResourceKey();
            if (!StringUtils.isEmpty(rewardResourceKey)) {
                resourceKeyHandlerService.sendResourceData(aid, rewardResourceKey, itemTitle, itemTitle, itemTitle, "", "");
            }

            msgLogger.info("TaskRankTemplateDeliver doSendReward aid:{} rank{} rewardResourceKey:{} itemTitle:{} done",
                    aid, rank, rewardResourceKey, itemTitle);
        }

        int rank = 1;
        Map<String, Integer> rankingMap = taskRankRedis.getActivityRankMap(activity.getActivityId(), taskRankKey, TaskRankConstant.RANK_TOP_N);
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            String aid = entry.getKey();
            if (isRoomIdType) {
                aid = RoomUtils.getRoomHostId(aid);
            }
            doReportActivityRankingEvent(aid, activity.getActivityId(), activity.getNameEn(), rank, entry.getValue());
            rank += 1;
        }
    }


    private void doReportActivityRankingEvent(String uid, String activityId, String activityName, int rank, int rankValue) {
        ActivityRankingEvent event = new ActivityRankingEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActive_id(activityId);
        event.setActivity_name(activityName);
        event.setRank(rank);
        event.setRank_value(rankValue);
        eventReport.track(new EventDTO(event));
    }
}
