package com.quhong.deliver;

import com.mongodb.client.result.UpdateResult;
import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.mongo.dao.WeeklyGiftIdDevoteDao;
import com.quhong.mongo.data.BadgeData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.WeeklyStarRedis;
import com.quhong.service.ResourceKeyHandlerService;
import com.quhong.service.WeeklyStarService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Component
public class WeeklyStarDeliver {

    private final static Logger logger = LoggerFactory.getLogger(WeeklyStarDeliver.class);
    private static final List<Integer> WEEK_BADGE_LIST = Arrays.asList(2220, 2221, 2222, 2223);
    private static final List<Integer> SEND_BEANS_LIST = Arrays.asList(5999, 3999, 1999);
    private static final List<Integer> LOW_LEVEL_GIFT = Arrays.asList(110, 53, 253, 671, 672, 9, 670, 435, 7, 112, 49, 108, 401, 10, 111, 25, 109, 419, 13);
    private static final List<Integer> MIDDLE_LEVEL_GIFT = Arrays.asList(90, 601, 659, 44, 402, 318, 458, 468, 420, 469, 395, 19);
    private static final List<Integer> HIGH_LEVEL_GIFT = Arrays.asList(687, 142, 347, 59, 575, 677, 576, 364, 577, 643, 207, 71, 421, 644, 113, 358, 440, 467, 114, 57, 496, 64);

    /**
     * top rank 奖励
     */
    private static final List<String> TOP_RES_KEY_LIST = Arrays.asList("WeeklyStarTop1", "WeeklyStarTop2", "WeeklyStarTop3");

    /**
     * 获得top1次数奖励
     */
    private static final Map<Integer, String> TOP_1_COUNT_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(5, "WeeklyStarSent-5t");
            put(10, "WeeklyStarSent-10t");
            put(20, "WeeklyStarSent-20t");
            put(30, "WeeklyStarSent-30t");
            put(50, "WeeklyStarSent-50t");
        }
    };

    private static final String WEEKLY_STAR_RANKING_TITLE = "Weekly Star-ranking reward";

    private static final String WEEKLY_STAR_TASK_TITLE = "Weekly Star-task reward";

    @Resource
    private BadgeDao badgeDao;
    @Resource
    private SysConfigDao configDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    protected MqSenderService mqService;
    @Resource
    private WeeklyStarRedis weeklyStarRedis;
    @Resource
    private WeeklyGiftIdDevoteDao weeklyGiftIdDevoteDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private WeeklyStarService weeklyStarService;

    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        // 正式服北京时间2022-07-01 10:00:00开始生效，nowSeconds < 1656640800 &&
        // if (ServerConfig.isProduct()) {
        //     logger.info("prod skip deliver nowSeconds={} need=1656640800", nowSeconds);
        //     return;
        // }
        int lastWeekEndTime = configDao.getIntValue(SysConfigDao.WEEK_CONFIG, SysConfigDao.WEEK_CONFIG, false);
        logger.info("weekly star deliver now={} lastWeekEndTime={}", nowSeconds, lastWeekEndTime);
        if (nowSeconds >= lastWeekEndTime) {
            logger.info("start weekly star deliver.");
            // 新的一周开始，更新下周活动结束时间
            long thisWeekEndTime = lastWeekEndTime + TimeUnit.DAYS.toSeconds(7);
            UpdateResult updateResult = configDao.updateValue(SysConfigDao.WEEK_CONFIG, SysConfigDao.WEEK_CONFIG, thisWeekEndTime);
            if (!checkUpdateResult(updateResult)) {
                return;
            }
            // 先更新下周的活动数据
            int lastWeek = configDao.getIntValue(SysConfigDao.WEEK_COUNT, SysConfigDao.WEEK_COUNT);
            // 上周礼物id列表
            List<Integer> lastWeekGiftList = configDao.getList(SysConfigDao.NEW_GIFT_KEY, SysConfigDao.NEW_GIFT_LIST_KEY, false);
            boolean hasError = false;
            try {
                // 下发上周的奖励
                doSendReward(lastWeekGiftList, lastWeek);
            } catch (Exception e) {
                logger.error("sendRewardError! msg={}", e.getMessage(), e);
                monitorSender.info("activity", ServerConfig.isProduct() ? "正式服" : "测试服" + "周礼物奖励下发异常", e.getMessage());
                hasError = true;
            }
            // 累加新一周的周活动id
            checkUpdateResult(configDao.updateValue(SysConfigDao.WEEK_COUNT, SysConfigDao.WEEK_COUNT, lastWeek + 1));
            weeklyStarService.delWeekCount();
            // 更新本周礼物id列表
            List<Integer> thisWeekGiftList = configDao.getList(SysConfigDao.NEXT_WEEK_LIST, SysConfigDao.NEXT_WEEK_LIST, false);
            checkUpdateResult(configDao.updateValue(SysConfigDao.NEW_GIFT_KEY, SysConfigDao.NEW_GIFT_LIST_KEY, thisWeekGiftList));
            weeklyStarService.delWeeklyGiftList();
            if (!hasError && ServerConfig.isProduct()) {
                monitorSender.info("activity", "周礼物奖励下发完成", "周礼物奖励下发完毕, 下轮礼物：" + thisWeekGiftList.toString());
            }
            // 更新下周礼物id列表
            List<Integer> nextWeekGiftList = configDao.getList(SysConfigDao.NEXT_2_WEEK_LIST, SysConfigDao.NEXT_2_WEEK_LIST, false);
            checkUpdateResult(configDao.updateValue(SysConfigDao.NEXT_WEEK_LIST, SysConfigDao.NEXT_WEEK_LIST, nextWeekGiftList));
            // 更新上周礼物id列表
            checkUpdateResult(configDao.updateValue(SysConfigDao.LAST_WEEK_LIST, SysConfigDao.LAST_WEEK_LIST, lastWeekGiftList));

            // 将下下周的礼物id设置为空列表
            List<Integer> next2WeekList = new ArrayList<>();
            List<Integer> lowGiftList = new ArrayList<>(LOW_LEVEL_GIFT);
            lowGiftList.removeAll(lastWeekGiftList);
            lowGiftList.removeAll(thisWeekGiftList);
            lowGiftList.removeAll(nextWeekGiftList);
            next2WeekList.add(lowGiftList.stream().skip(new Random().nextInt(lowGiftList.size())).findFirst().orElse(null));

            List<Integer> middleGiftList = new ArrayList<>(MIDDLE_LEVEL_GIFT);
            middleGiftList.removeAll(lastWeekGiftList);
            middleGiftList.removeAll(thisWeekGiftList);
            middleGiftList.removeAll(nextWeekGiftList);
            next2WeekList.add(middleGiftList.stream().skip(new Random().nextInt(middleGiftList.size())).findFirst().orElse(null));

            List<Integer> highGiftList = new ArrayList<>(HIGH_LEVEL_GIFT);
            highGiftList.removeAll(lastWeekGiftList);
            highGiftList.removeAll(thisWeekGiftList);
            highGiftList.removeAll(nextWeekGiftList);
            next2WeekList.add(highGiftList.stream().skip(new Random().nextInt(highGiftList.size())).findFirst().orElse(null));

            configDao.updateValue(SysConfigDao.NEXT_2_WEEK_LIST, SysConfigDao.NEXT_2_WEEK_LIST, next2WeekList);
            monitorSender.info("activity", "周礼物自动配置完成", String.format("本周礼物:%s  下周礼物: %s 下下周礼物: %s", thisWeekGiftList, nextWeekGiftList, next2WeekList));

        }
    }

    /**
     * 更新失败时增加告警
     */
    private boolean checkUpdateResult(UpdateResult updateResult) {
        if (null == updateResult || updateResult.getModifiedCount() == 0) {
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "周礼物结束异常", "活动更新失败");
            }
            return false;
        }
        return true;
    }

    /**
     * 奖励下发
     *
     * @param lastWeekGiftList 上周礼物id列表
     * @param lastWeek         上周活动id
     */
    private void doSendReward(List<Integer> lastWeekGiftList, int lastWeek) {
//        List<Integer> weekBadgeList = configDao.getList(SysConfigDao.WEEK_BADGE_SEND, SysConfigDao.WEEK_BADGE_SEND, false);
        for (Integer giftId : lastWeekGiftList) {
            Map<String, Integer> rankingMap = weeklyStarRedis.getRankingMap(giftId, lastWeek, 3);
            int rank = 1;
            for (String uid : rankingMap.keySet()) {
                if (rank <= TOP_RES_KEY_LIST.size()) {
                    resourceKeyHandlerService.sendResourceData(uid, TOP_RES_KEY_LIST.get(rank - 1),
                            WEEKLY_STAR_RANKING_TITLE, WEEKLY_STAR_RANKING_TITLE, WEEKLY_STAR_RANKING_TITLE, "", "");

                }
                // 处理累计获得top1的数量勋章
                if (rank == 1) {
                    int top1Count = weeklyStarRedis.incrTop1Count(uid);
                    String countResKey = TOP_1_COUNT_KEY_MAP.getOrDefault(top1Count, null);
                    if (!StringUtils.isEmpty(countResKey)) {
                        weeklyStarRedis.setTop1ShowScore(uid,top1Count);
                        resourceKeyHandlerService.sendResourceData(uid, countResKey,
                                WEEKLY_STAR_TASK_TITLE, WEEKLY_STAR_TASK_TITLE, WEEKLY_STAR_TASK_TITLE, "", "");

                    }
                }
                logger.info("weekly star sendReward success giftId:{} rank:{} uid:{}", giftId, rank, uid);
                rank = rank + 1;
            }
        }
//        executor.execute(() -> {
//            // 同步redis数据至mongo
//            syncRedisToMongo(lastWeekGiftList, lastWeek);
//        });

    }

    public void syncRedisToMongo(List<Integer> lastWeekGiftList, int lastWeek) {
        for (Integer giftId : lastWeekGiftList) {
            Map<String, Integer> rankingMap = weeklyStarRedis.getRankingMap(giftId, lastWeek, 0);
            weeklyGiftIdDevoteDao.bulkInsert(rankingMap, giftId, lastWeek);
        }
    }

    /**
     * 下发钻石奖励
     */
    private void giveBeansToUser(String uid, int rank, int lastWeekEndTime) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(901);
        moneyDetailReq.setChanged(SEND_BEANS_LIST.get(rank - 1));
        moneyDetailReq.setTitle("Gift Competition Win");
        moneyDetailReq.setDesc(String.format("Top%d sending in the gift competition", rank));
        moneyDetailReq.setMtime(lastWeekEndTime);
        logger.info("giveBeansToUser uid={} rank={} beans={}", uid, rank, moneyDetailReq.getChanged());
        mqService.asyncChargeDiamonds(moneyDetailReq);
    }

    /**
     * 下发勋章奖励
     */
    private void giveBadgeToUser(String uid, int rank, List<Integer> weekBadgeList, int lastWeekEndTime) {
        if (weekBadgeList.size() >= rank + 1) {
            BadgeData badgeData = new BadgeData();
            badgeData.setUid(uid);
            badgeData.setBadge_id(weekBadgeList.get(rank - 1));
            badgeData.setStatus(0);
            badgeData.setGet_time(lastWeekEndTime);
            badgeData.setEnd_time(rank <= 2 ? lastWeekEndTime + 604800 : lastWeekEndTime + 259200);
            logger.info("giveBadgeToUser uid={} rank={} badgeId={} endTime={}", uid, rank, badgeData.getBadge_id(), badgeData.getEnd_time());
            badgeDao.upsert(badgeData);
        } else {
            logger.error("gaveBadgeToUser error uid={} rank={} weekBadgeList={}", uid, rank, weekBadgeList);
        }
    }

    private void giveWeekBadgeToUser(String uid, int badgeId, int lastWeekEndTime) {
        badgeDao.removeBadges(uid, WEEK_BADGE_LIST);
        BadgeData badgeData = new BadgeData();
        badgeData.setUid(uid);
        badgeData.setBadge_id(badgeId);
        badgeData.setStatus(0);
        badgeData.setGet_time(lastWeekEndTime);
        // 1767196800表示永久
        badgeData.setEnd_time(Integer.MAX_VALUE);
        logger.info("giveWeekBadgeToUser uid={} badgeId={} endTime={}", uid, badgeData.getBadge_id(), badgeData.getEnd_time());
        badgeDao.save(badgeData);
    }
}
