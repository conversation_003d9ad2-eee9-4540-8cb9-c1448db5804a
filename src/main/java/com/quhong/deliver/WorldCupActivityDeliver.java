package com.quhong.deliver;


import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.TemporaryMoneyEvent;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.WorldCupChampionVO;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.WorldCupBetDao;
import com.quhong.mysql.dao.WorldCupChipDao;
import com.quhong.mysql.dao.WorldCupMatchDao;
import com.quhong.mysql.dao.WorldCupTeamDao;
import com.quhong.mysql.data.WorldCupBetData;
import com.quhong.mysql.data.WorldCupChipData;
import com.quhong.mysql.data.WorldCupMatchData;
import com.quhong.mysql.data.WorldCupTeamData;
import com.quhong.redis.ActivityOtherRedis;
import com.quhong.redis.WorldCupRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Component
public class WorldCupActivityDeliver {

    private final static Logger logger = LoggerFactory.getLogger(WorldCupActivityDeliver.class);
    private static final double SERVICE_FREE = 0.9;
    private static final Integer EVENT_TYPE_AWARD = 4;  // 奖励
    private static final String EVENT_MONEY_NANE = "counter";
    private static final String EVENT_TITLE_WIN_A = "team a win";
    private static final String EVENT_TITLE_WIN_B = "team b win";
    private static final String EVENT_TITLE_WIN_DRAW = "dram";
    private static final String EVENT_TITLE_WIN_CHAMPION = "champion country";
    private static final String WORLD_CUP_ACTIVITY = ServerConfig.isProduct()?"63805b0bbb1b74e7f8d285df":"636dad3cfbf369043bb84c07";

    @Resource
    private WorldCupMatchDao worldCupMatchDao;
    @Resource
    private WorldCupChipDao worldCupChipDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private WorldCupBetDao worldCupBetDao;
    @Resource
    private WorldCupRedis worldCupRedis;
    @Resource
    private ActivityOtherRedis activityOtherRedis;
    @Resource
    private WorldCupTeamDao worldCupTeamDao;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Autowired(required = false)
    private EventReport eventReport;

    public void worldCupEventReport(String uid, String matchType, int ship, int afterChange, int aType, String title, String teamName){
        TemporaryMoneyEvent moneyEvent = new TemporaryMoneyEvent();
        moneyEvent.setUid(uid);
        moneyEvent.setTemporary_money_name(EVENT_MONEY_NANE);
        moneyEvent.setTemporary_money_changed(ship);
        moneyEvent.setTemporary_money_after_changed(afterChange);
        moneyEvent.setTemporary_money_atype(aType);
        moneyEvent.setTemporary_money_title(title);
        moneyEvent.setMatch_id(matchType);
        moneyEvent.setCountry_code(teamName);
        moneyEvent.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(moneyEvent));
    }


    /**
     * 世界杯赛事预测
     */
    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<WorldCupMatchData> endWorldCupList = worldCupMatchDao.selectEndList();
        logger.info("WorldCupActivity deliver check. endWorldCupList={}", endWorldCupList.size());
        for (WorldCupMatchData matchData : endWorldCupList) {
            try {
                // 活动已到期结束
                matchData.setStatus(1);
                int count = worldCupMatchDao.updateOne(matchData);
                if (count == 0){
                    if (ServerConfig.isProduct()) {
                        monitorSender.info("activity", "世界杯活动结束异常", "活动更新失败");
                    }
                    continue;
                }

                int firstChip = worldCupRedis.getMatchSize(matchData.getMatchType(), matchData.getFirstKey());
                int secondChip = worldCupRedis.getMatchSize(matchData.getMatchType(), matchData.getSecondKey());
                int drawChip = worldCupRedis.getMatchSize(matchData.getMatchType(), -1);
                int firstSize = (int) ((secondChip + drawChip) * SERVICE_FREE);
                int secondSize = (int) ((firstChip + drawChip) * SERVICE_FREE);
                int drawSize = (int) ((firstChip + secondChip) * SERVICE_FREE);

                List<WorldCupBetData> betDataList = worldCupBetDao.selectList(matchData.getMatchType());
                int winTeamKey = 0;
                int awardShip = 0;
                int awardSize = 0;
                switch (matchData.getWinType()){
                    case 1:
                        winTeamKey = matchData.getFirstKey();
                        awardShip = firstChip;
                        awardSize = firstSize;
                        break;
                    case 2:
                        winTeamKey = matchData.getSecondKey();
                        awardShip = secondChip;
                        awardSize = secondSize;
                        break;
                    case -1:
                        winTeamKey = -1;
                        awardShip = drawChip;
                        awardSize = drawSize;
                        break;
                }

                if(winTeamKey != 0 ){
                    for(WorldCupBetData betData: betDataList){
                        try{

                            if (betData.getTeamKey() == winTeamKey){
                                int myWardChip;
                                if(awardShip == 0){
                                    myWardChip = 0;
                                }else{
                                    BigDecimal myBetNum = new BigDecimal(betData.getBetNum());
                                    BigDecimal myWardChipDec = myBetNum.multiply(BigDecimal.valueOf(awardSize)).divide(BigDecimal.valueOf(awardShip), 2, BigDecimal.ROUND_HALF_DOWN);
                                    myWardChip = myWardChipDec.intValue();
                                }

                                logger.info("firstChip: {}, secondChip:{}, drawChip:{}, firstSize:{}, secondSize:{}, drawSize:{}, myWardChip: {}",
                                        firstChip, secondChip, drawChip, firstSize, secondSize, drawSize, myWardChip);

                                logger.info("winTeamKey: {}, awardShip:{}, awardSize:{}", winTeamKey, awardShip, awardSize);
                                WorldCupChipData myChipData = worldCupChipDao.selectOne(betData.getUid());
                                int afterChange = myChipData.getChipNum() + betData.getBetNum() + myWardChip;
                                myChipData.setChipNum(afterChange);
                                worldCupChipDao.updateOne(myChipData);

                                String recordTitle = winTeamKey == -1 ? EVENT_TITLE_WIN_DRAW : winTeamKey == matchData.getFirstKey() ? EVENT_TITLE_WIN_A : EVENT_TITLE_WIN_B;
                                worldCupEventReport(betData.getUid(), betData.getMatchType(), myWardChip, afterChange, EVENT_TYPE_AWARD, recordTitle, "");

                                betData.setWinType(1);
                                betData.setWinNum(myWardChip);

                                // 设置获得奖励排行榜
                                if(myWardChip > 0){
                                    activityOtherRedis.incrOtherRankingScore(WORLD_CUP_ACTIVITY, betData.getUid(), myWardChip, 1, 0);
                                }

                            }else {
                                betData.setWinType(0);
                                betData.setWinNum(-betData.getBetNum());
                            }

                            worldCupBetDao.updateOne(betData);

                        }catch (Exception e) {
                            logger.error("WorldCupActivityDeliver update betData error.", e);
                        }
                    }

                }

            } catch (Exception e) {
                logger.error("WorldCupActivityDeliver error.", e);
                if (ServerConfig.isProduct()) {
                    monitorSender.info("activity", "世界杯活动结束异常", e.getMessage());
                }
            }
        }
    }


    public void deliverChampion() {

        try {
            int curTime = DateHelper.getNowSeconds();
            if(curTime < activityCommonConfig.getChampionEndTime()){
                return;
            }

            WorldCupTeamData winnerData = worldCupTeamDao.winnerChampion();
            if(winnerData != null){
                // 活动已到期结束
                winnerData.setStatus(1);
                int count = worldCupTeamDao.updateOne(winnerData);
                if (count == 0){
                    if (ServerConfig.isProduct()) {
                        monitorSender.info("activity", "世界杯活动结束异常", "冠军活动更新失败");
                    }
                }

                int winTeamKey = winnerData.getId();
                int awardChip = worldCupRedis.getChampSize(winTeamKey);
                int totalChip = 0;

                List<WorldCupTeamData>  teamDataList = worldCupTeamDao.selectChampionList();

                for(WorldCupTeamData championData: teamDataList){

                    if(Objects.equals(winnerData.getId(), championData.getId())){
                        continue;
                    }
                    int teamShip = worldCupRedis.getChampSize(championData.getId());
                    totalChip += teamShip;
                }

                int awardSize = (int) (totalChip * SERVICE_FREE);
                List<WorldCupBetData> betDataList = worldCupBetDao.selectList("Champion");
                for(WorldCupBetData betData: betDataList){
                    try{
                        if (betData.getTeamKey() == winTeamKey){
                            int myWardChip;

                            if(awardChip == 0){
                                myWardChip = 0;

                            }else{
                                BigDecimal myBetNum = new BigDecimal(betData.getBetNum());
                                BigDecimal myWardChipDec = myBetNum.multiply(BigDecimal.valueOf(awardSize)).divide(BigDecimal.valueOf(awardChip), 2, BigDecimal.ROUND_HALF_DOWN);
                                myWardChip = myWardChipDec.intValue();
                            }

                            logger.info("uid: {}, winTeamKey: {}, awardChip:{}, totalChip:{}, myWardChip:{}", betData.getUid(), winTeamKey, awardChip, totalChip, myWardChip);

                            WorldCupChipData myChipData = worldCupChipDao.selectOne(betData.getUid());
                            if( myChipData == null) {
                                continue;
                            }

                            int afterChange = myChipData.getChipNum() + betData.getBetNum() + myWardChip;
                            myChipData.setChipNum(afterChange);
                            worldCupChipDao.updateOne(myChipData);

                            worldCupEventReport(betData.getUid(), betData.getMatchType(), myWardChip, afterChange, EVENT_TYPE_AWARD, EVENT_TITLE_WIN_CHAMPION, "");

                            betData.setWinType(1);
                            betData.setWinNum(myWardChip);

                            // 设置获得奖励排行榜
                            if(myWardChip > 0){
                                activityOtherRedis.incrOtherRankingScore(WORLD_CUP_ACTIVITY, betData.getUid(), myWardChip, 1, 0);
                            }

                        }else {
                            betData.setWinType(0);
                            betData.setWinNum(-betData.getBetNum());
                        }
                        worldCupBetDao.updateOne(betData);

                    }catch (Exception e) {
                        logger.error("deliverChampion update betData error.", e);
                    }


                }
            }

        } catch (Exception e) {
            logger.error("deliverChampion error.", e);
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "世界杯活动结束异常", e.getMessage());
            }
        }
    }

}
