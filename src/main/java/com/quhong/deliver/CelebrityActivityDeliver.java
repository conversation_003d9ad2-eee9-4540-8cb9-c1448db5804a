package com.quhong.deliver;

import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.CelebrityActivityDao;
import com.quhong.mongo.data.CelebrityActivity;
import com.quhong.monitor.MonitorSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class CelebrityActivityDeliver {

    private final static Logger logger = LoggerFactory.getLogger(CelebrityActivityDeliver.class);

    @Resource
    private MonitorSender monitorSender;
    @Resource
    private CelebrityActivityDao celebrityActivityDao;

    /**
     * 名人活动结算，奖励都由人工下发， 这里变更状态
     */
    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<CelebrityActivity> initActivities = celebrityActivityDao.getInitActivities();
        logger.info("celebrity activity deliver check. initActivities={}", initActivities.size());
        for (CelebrityActivity activity : initActivities) {
            try {
                // 活动已到期结束
                if (activity.getEndTime() < nowSeconds) {
                    logger.info("celebrity activity deliver activityId={}", activity.get_id().toString());
                    UpdateResult updateResult = celebrityActivityDao.updateData(activity, new Update().set("status", ActivityConstant.STATUS_DONE));
                    if (null == updateResult || updateResult.getModifiedCount() == 0) {
                        if (ServerConfig.isProduct()) {
                            monitorSender.info("activity", "名人活动结束异常", "活动更新失败");
                        }
                    } else {
                        if (ServerConfig.isProduct()) {
                            monitorSender.info("activity", "名人活动成功结束", "活动名：" + activity.getAcNameEn());
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("celebrityActivityDeliver error.", e);
                if (ServerConfig.isProduct()) {
                    monitorSender.info("activity", "名人活动结束异常", e.getMessage());
                }
            }
        }
    }
}
