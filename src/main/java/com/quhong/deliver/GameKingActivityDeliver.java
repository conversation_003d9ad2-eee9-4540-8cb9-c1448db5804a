package com.quhong.deliver;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.enums.ResTypeEnum;
import com.quhong.enums.SLangType;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.dao.PackConfigDao;
import com.quhong.mongo.data.NoticeNewData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.PackConfigData;
import com.quhong.mongo.data.PackData;
import com.quhong.monitor.MonitorSender;
import com.quhong.msg.chat.OfficialPushMsg;
import com.quhong.redis.GameKingRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.service.GameKingService;
import com.quhong.service.PackService;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/19
 */
@Component
public class GameKingActivityDeliver {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final int A_TYPE = 962;
    private static final String TITLE = "Game King rewards";
    private static final String DESC = "Game King rewards";

    private static final String MSG_TITLE_EN = "Congratulations on winning the rewards of Game Master";
    private static final String MSG_TITLE_AR = "تهانينا على الفوز بمكافآت سيد اللعبة";

    private static final String H5_URL_DEBUG = "https://test2.qmovies.tv/game_king/";
    private static final String H5_URL_PRO = "https://static.youstar.live/game_king/";

    public static final String ACTION_EN = "View";
    public static final String ACTION_AR = "شاهد";

    @Resource
    private ActorDao actorDao;
    @Resource
    private GameKingRedis gameKingRedis;
    @Resource
    private PackConfigDao packConfigDao;
    @Resource
    private PackService packService;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private RoomWebSender roomWebSender;

    public void deliver() {
        try {
            long timeMillis = System.currentTimeMillis();
            int lastWeekNum = gameKingRedis.getLastWeekNum();
            logger.info("GameKingActivityDeliver start. lastWeekNum={}", lastWeekNum);
            Map<String, Integer> rankingMap = gameKingRedis.getRangeRankingMap(lastWeekNum, GameKingService.MIN_DIAMOND_LIMIT, 0);
            if (CollectionUtils.isEmpty(rankingMap)) {
                logger.error("game king activity ranking list is empty.");
                return;
            }
            Map<Integer, String> rewardConfigMap = GameKingService.REWARD_CONFIG_MAP;
            List<PackConfigData> packConfigList = packConfigDao.findListByKeys(new HashSet<>(rewardConfigMap.values()));
            Map<String, PackConfigData> packConfigMap = CollectionUtil.listToKeyMap(packConfigList, PackConfigData::getKey);
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                int target = 0;
                String uid = entry.getKey();
                for (int limit : rewardConfigMap.keySet()) {
                    if (entry.getValue() >= limit) {
                        target = Math.max(target, limit);
                    }
                }
                if (!rewardConfigMap.containsKey(target)) {
                    logger.error("can not get game king reward. uid={} diamonds={} target={}", uid, entry.getValue(), target);
                    continue;
                }
                String packKey = rewardConfigMap.get(target);
                PackConfigData packConfigData = packConfigMap.get(packKey);
                if (packConfigData == null) {
                    logger.error("can not get pack config data. uid={} packKey={} ", uid, packKey);
                    if (ServerConfig.isProduct()) {
                        monitorSender.info("activity", "游戏王活动结算奖励异常", "奖励配置缺失, packKey=" + packKey + ", uid=" + uid);
                    }
                    continue;
                }
                packService.sendPackage(uid, A_TYPE, TITLE, DESC, 1, packConfigData);
                sendActivityMsg(uid, packConfigData);
            }
            logger.info("GameKingActivityDeliver end. lastWeekNum={} cost={}", lastWeekNum, System.currentTimeMillis() - timeMillis);
        } catch (Exception e) {
            logger.error("GameKingActivityDeliver error. {}", e.getMessage(), e);
            if (ServerConfig.isProduct()) {
                monitorSender.info("activity", "游戏王活动结算奖励异常", e.getMessage());
            }
        }
    }

    private void sendActivityMsg(String uid, PackConfigData packConfigData) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", uid);
            return;
        }
        int slang = actorData.getSlang();
        OfficialData officialData = new OfficialData();
        officialData.setTo_uid(uid);
        officialData.setTitle(slang == SLangType.ENGLISH ? MSG_TITLE_EN : MSG_TITLE_AR);
        officialData.setBody("");
        officialData.setValid(1);
        officialData.setAtype(0);
        officialData.setUrl(ServerConfig.isProduct() ? H5_URL_PRO : H5_URL_DEBUG);
        officialData.setAct(slang == SLangType.ENGLISH ? ACTION_EN : ACTION_AR);
        officialData.setNews_type(6);
        // 活动消息
        officialData.setNtype(1);
        List<OfficialData.AwardInfo> awardList = new ArrayList<>();
        for (PackData packData : packConfigData.getPackList()) {
            ResTypeEnum typeEnum = packData.getResTypeEnum();
            if (typeEnum == null) {
                continue;
            }
            awardList.add(new OfficialData.AwardInfo(
                    typeEnum.getNameBySlang(slang),
                    packData.getIcon(),
                    typeEnum.formatTag(slang, packData.getNum())));
        }
        officialData.setAward_list(awardList);
        // 和记录时间相同方便撤回时删除
        officialData.setCtime(DateHelper.getNowSeconds());
        officialMsgPush(officialData);
    }

    public void officialMsgPush(OfficialData officialData) {
        officialDao.save(officialData);
        if (officialData.get_id() != null) {
            NoticeNewData noticeNewData = new NoticeNewData(officialData.getTo_uid(), officialData.get_id().toString());
            noticeNewData.setNtype(1);
            noticeNewDao.save(noticeNewData);
            OfficialPushMsg msg = new OfficialPushMsg();
            msg.setTitle(officialData.getTitle());
            msg.setBody(officialData.getBody());
            msg.setMsg_type(1);
            roomWebSender.sendPlayerWebMsg(null, null, officialData.getTo_uid(), msg, true);
        }
    }
}
