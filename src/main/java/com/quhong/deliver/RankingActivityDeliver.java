package com.quhong.deliver;

import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RankingActivityDao;
import com.quhong.mongo.data.ConquerActivity;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.mq.ResourceDeliveryService;
import com.quhong.msg.obj.RankInfoListObject;
import com.quhong.msg.obj.RankInfoObject;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.msg.room.RankNotificationPushMsg;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.RankingActivityRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.TestRoomService;
import com.quhong.service.ActivityUtilService;
import com.quhong.service.RankActivityService;
import com.quhong.service.ResourceKeyHandlerService;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class RankingActivityDeliver {

    private final static Logger logger = LoggerFactory.getLogger(RankingActivityDeliver.class);

    @Resource
    private TestRoomService testRoomService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private RankingActivityDao rankingActivityDao;
    @Resource
    private RankingActivityRedis rankingActivityRedis;
    @Resource
    private ResourceDeliveryService resourceDeliveryService;
    @Resource
    private RankActivityService rankActivityService;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private ActivityUtilService activityUtilService;
    @Resource
    private GiftDao giftDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;

    /**
     * 冲榜活动结算
     */
    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<RankingActivity> initActivities = rankingActivityDao.getInitActivities();
        logger.info("ranking activity deliver check. initActivities={}", initActivities.size());
        for (RankingActivity activity : initActivities) {
            try {
                // 活动已到期结束
                if (activity.getEndTime() < nowSeconds) {
                    logger.info("ranking activity deliver activityId={}", activity.get_id().toString());
                    UpdateResult updateResult = rankingActivityDao.updateData(activity, new Update().set("status", ActivityConstant.STATUS_DONE));
                    if (null == updateResult || updateResult.getModifiedCount() == 0) {
                        if (ServerConfig.isProduct()) {
                            monitorSender.info("activity", "冲榜活动结束异常", "活动更新失败");
                        }
                        continue;
                    }
                    RankNotificationPushMsg msg = new RankNotificationPushMsg();
                    msg.setTitle_en(activity.getAcNameEn());
                    msg.setTitle_ar(activity.getAcNameAr());
                    msg.setRankList(new ArrayList<>());
                    doSendReward(activity, msg);
                    if (ServerConfig.isProduct()) {
                        monitorSender.info("activity", "冲榜活动成功结束", "活动名：" + activity.getAcNameEn());
                    }
                    // 活动结束消息，
                    boolean test = activity.getAcNameEn().startsWith("test");
                    roomWebSender.sendRoomWebMsg(test ? testRoomService.getTestRoom() : "all", null, msg, false);
                }
            } catch (Exception e) {
                logger.error("rankingActivityDeliver error.", e);
                if (ServerConfig.isProduct()) {
                    monitorSender.info("activity", "冲榜活动结束异常", e.getMessage());
                }
            }
        }
    }

    private void doSendReward(RankingActivity activity, RankNotificationPushMsg msg) {
        if (1 == activity.getConfig().getNoRankReward()) {
            logger.info("doSendReward activityId={} no rankReward", activity.get_id().toString());
            return;
        }
        List<RankingActivity.RankingConfig> rankingConfigList = activity.getRankingConfigList();
        String acNameEn = activity.getAcNameEn();
        // 遍历每种奖励类型
        for (RankingActivity.RankingConfig rankingConfig : rankingConfigList) {
            // 奖励配置
            List<RankingActivity.RankingRewardConfig> rewardConfigList = rankingConfig.getRankingRewardConfigList();
            // 指定礼物的排行榜
            int rankGiftId = 0;
            if (null != rankingConfig.getGiftId() && 0 != rankingConfig.getGiftId()) {
                rankGiftId = rankingConfig.getGiftId();
            }
            // 指定性别排行榜
            int rankGender = rankingConfig.getRankingGender();
            List<String> rankingList;
            if (rankingConfig.getRankingAttribute() == ActivityConstant.CONQUER_RANK) {
                ConquerActivity conquerActivity = rankActivityService.getConquerCache();
                if (conquerActivity == null) {
                    monitorSender.info("activity", "征服活动结束异常，无法找到征服活动活动id", "活动名：" + activity.getAcNameEn());
                    continue;
                }
                String conquerId = conquerActivity.get_id().toString();
                rankingList = rankActivityService.getConquerRankingList(activity.get_id().toString(), rankingConfig.getRankingAttribute(), rankGiftId, conquerId, rankGender);
            } else {
                rankingList = rankingActivityRedis
                        .getRankingList(activity.get_id().toString(), rankingConfig.getRankingAttribute(), rankGiftId, 10, null, rankGender);
            }
            // 排行榜消息
            fillRankMsg(rankingConfig, rankingList, msg);
            for (RankingActivity.RankingRewardConfig rankingRewardConfig : rewardConfigList) {
                // 奖励对象
                for (Integer rank : rankingRewardConfig.getRewardObject()) {
                    if (rankingList.size() < rank) {
                        logger.error("cannot find rank activityId={} rank={}", activity.get_id().toString(), rank);
                        continue;
                    }
                    String aid = rankingList.get(rank - 1);
                    if (ActivityConstant.ROOM_RANK == rankingConfig.getRankingAttribute()) {
                        aid = RoomUtils.getRoomHostId(aid);
                    }

                    // 资源key下发资源
                    String rewardResourceKey = rankingRewardConfig.getRewardResourceKey();
                    if(!StringUtils.isEmpty(rewardResourceKey)){
                        resourceKeyHandlerService.sendResourceData(aid, rewardResourceKey, acNameEn, acNameEn, acNameEn, "", "");
                    }

                    // 可能有多个奖励
                    List<RankingActivity.RewardConfigDetail> rewardConfigDetailList = rankingRewardConfig.getRewardConfigDetailList();
                    if (!CollectionUtils.isEmpty(rewardConfigDetailList)){
                        for (RankingActivity.RewardConfigDetail reward : rewardConfigDetailList) {
                            logger.info("ranking reward activityId={} uid={} rewardType={} sourceId={} rewardTime={} num={} rank={} rankType={}",
                                    activity.get_id().toString(), aid, reward.getRewardType(), reward.getSourceId(), reward.getRewardTime(),
                                    reward.getRewardNum(), rank, rankingConfig.getRankingAttribute());
                            if(StringUtils.isEmpty(reward.getRewardType())){
                                continue;
                            }

                            if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
                                doAsyncChargeDiamonds(activity, aid, reward.getRewardNum(), rank);

                            } else if (ResourceConstant.HEART.equals(reward.getRewardType())){
                                String recordTitle = activity.getAcNameEn() + " Competition Win";
                                String recordDesc = String.format("Top %d in the %s Competition Win", rank, activity.getAcNameEn());
                                heartRecordDao.changeHeart(aid, reward.getRewardNum(), recordTitle, recordDesc);
                            } else {
                                if (ResourceConstant.OTHER.equals(reward.getRewardType())) {
                                    continue;
                                }
                                // 使用py下发资源
                                activityUtilService.handleResources(new GiftsMqVo(aid,
                                        reward.getRewardType(),
                                        reward.getSourceId(),
                                        reward.getRewardTime(),
                                        reward.getRewardNum() <=0 ? 1 : reward.getRewardNum()));
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 排行列表消息
     */
    private void fillRankMsg(RankingActivity.RankingConfig rankingConfig, List<String> rankingList, RankNotificationPushMsg msg) {
        // 排行列表消息
        RankInfoListObject rankInfoListObject = new RankInfoListObject();
        rankInfoListObject.setTagType(rankingConfig.getRankingAttribute());
        rankInfoListObject.setTitle_en(rankingConfig.getRankingTitleEn());
        rankInfoListObject.setTitle_ar(rankingConfig.getRankingTitleAr());
        List<RankInfoObject> rankInfoObjects = new ArrayList<>();
        rankInfoListObject.setRankInfo(rankInfoObjects);
        for (int i = 0; i < rankingList.size(); i++) {
            String aid = rankingList.get(i);
            RankInfoObject rankInfoObject = new RankInfoObject();
            rankInfoObject.setRank(i + 1);
            rankInfoObject.setTagType(rankingConfig.getRankingAttribute());
            // 消息兼容，征服房间榜转换为发送榜
            if (ActivityConstant.CONQUER_RANK == rankingConfig.getRankingAttribute()) {
                rankInfoObject.setTagType(ActivityConstant.SEND_RANK);
            }
            if (ActivityConstant.ROOM_RANK == rankingConfig.getRankingAttribute()) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(aid);
                rankInfoObject.setRoom_id(aid);
                rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                rankInfoObject.setName(roomData.getName());
                ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(aid));
                rankInfoObject.setRid(actorData.getRid());
                rankInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
            } else {
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                rankInfoObject.setRoom_id(aid);
                rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                rankInfoObject.setName(actorData.getName());
                rankInfoObject.setRid(actorData.getRid());
                rankInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
            }
            rankInfoObjects.add(rankInfoObject);
        }
        msg.getRankList().add(rankInfoListObject);
    }

    /**
     * 异步打钻
     */
    private void doAsyncChargeDiamonds(RankingActivity activity, String uid, int changed, int rank) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(905);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(activity.getAcNameEn() + " Competition Win");
        moneyDetailReq.setDesc(String.format("Top %d in the %s Competition Win", rank, activity.getAcNameEn()));
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }
}
