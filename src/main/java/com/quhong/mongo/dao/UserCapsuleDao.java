package com.quhong.mongo.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.UserCapsuleData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

@Lazy
@Component
public class UserCapsuleDao {

    private static final Logger logger = LoggerFactory.getLogger(UserCapsuleDao.class);


    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;


    public UserCapsuleData findData(String uid, int roundNum) {
        try {

            Criteria criteria = Criteria.where("uid").is(uid).and("roundNum").is(roundNum);
            Query query = new Query(criteria);
            query.limit(1);
            return mongoTemplate.findOne(query, UserCapsuleData.class);
        } catch (Exception e) {
            logger.error("findData error uid={} error={}", uid, e.getMessage(), e);
            return null;
        }
    }

    public void save(UserCapsuleData data) {
        int nowSeconds = DateHelper.getNowSeconds();
        data.setCtime(nowSeconds);
        mongoTemplate.save(data);
    }

}
