package com.quhong.mongo.dao;

import com.quhong.config.CaffeineCacheConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.MomentCountData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Lazy
@Component
public class MomentActivityDao {
    private static final Logger logger = LoggerFactory.getLogger(MomentActivityDao.class);

    public static final String TABLE_NAME = "moment";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;


    /**
     * 获取最新发布的朋友圈
     */
    public MomentActivityData findLastData(String uid) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid)
                    .and("show").is(1);
            Query query = new Query(criteria).with(Sort.by(Sort.Direction.DESC, "_id"));
            return mongoTemplate.findOne(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findLastData error={}", e.getMessage(), e);
            return null;
        }
    }


    /**
     * 获取寄语的朋友圈
     */
    public List<MomentActivityData> findNewYearMsgList(String uid, String origin) {
        try {
            Criteria criteria = Criteria.where("location").is(origin).and("uid").ne(uid);
            Query query = new Query(criteria).with(Sort.by(Sort.Direction.DESC, "_id")).limit(5);
            return mongoTemplate.find(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findLastData error={}", e.getMessage(), e);
            return null;
        }
    }

    public MomentActivityData findNewYearMsgOne(String uid, String origin) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("location").is(origin);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findFutureOne error={}", e.getMessage(), e);
            return null;
        }
    }


    public MomentActivityData findMomentOne(String momentId, String origin) {
        try {
            Criteria criteria = Criteria.where("_id").is(momentId).and("location").is(origin);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findMomentOne error={}", e.getMessage(), e);
            return null;
        }
    }

    public MomentActivityData findMomentById(String momentId) {
        try {
            if (ObjectUtils.isEmpty(momentId)) {
                return null;
            }
            Criteria criteria = Criteria.where("_id").is(momentId);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findMomenfindMomentByIdtOne error={}", e.getMessage(), e);
            return null;
        }
    }


    public MomentActivityData findMomentOneByTime(String uid, String origin, int startTime, int endTime) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("location").is(origin);
            criteria.and("c_time").gte(startTime).lte(endTime);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findFutureOne error={}", e.getMessage(), e);
            return null;
        }
    }

    public MomentActivityData findMomentOneByTitleTime(String uid, String title, int startTime, int endTime) {
        try {
            Criteria criteria = Criteria.where("uid").is(uid).and("text").regex(title);
            criteria.and("c_time").gte(startTime).lte(endTime);
            Query query = new Query(criteria);
            return mongoTemplate.findOne(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findMomentOneByTitleTime error={}", e.getMessage(), e);
            return null;
        }
    }


    public List<MomentActivityData> findMomentList(String uid, String origin) {
        try {
            Criteria criteria = Criteria.where("location").is(origin).and("uid").is(uid);
            Query query = new Query(criteria).with(Sort.by(Sort.Direction.DESC, "_id"));
            return mongoTemplate.find(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findLastData error={}", e.getMessage(), e);
            return null;
        }
    }


    public List<MomentActivityData> findMsgPageList(String momentId, String origin, Integer start, Integer pageSize) {
        try {

            Criteria criteria = Criteria.where("location").is(origin);
            if (!StringUtils.isEmpty(momentId)) {
                criteria.and("_id").ne(new ObjectId(momentId));
            }

            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findLastData error={}", e.getMessage(), e);
            return null;
        }
    }

    public List<MomentActivityData> findUserMsgPageList(String uid, String origin, Integer start, Integer pageSize) {
        try {

            Criteria criteria = Criteria.where("location").is(origin);
            criteria.and("uid").is(uid);
            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findLastData error={}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public List<MomentActivityData> findMsgPageListByTime(String momentId, String origin, Integer start, Integer pageSize, int startTime, int endTime) {
        try {

            Criteria criteria = Criteria.where("location").is(origin);
            if (!StringUtils.isEmpty(momentId)) {
                criteria.and("_id").ne(new ObjectId(momentId));
            }

            if (startTime != 0 && endTime != 0) {
                criteria.and("c_time").gte(startTime).lte(endTime);
            }

            Query query = new Query(criteria);
            Sort sort = Sort.by(Sort.Direction.DESC, "_id");
            query.with(sort);
            query.skip(start).limit(pageSize);
            return mongoTemplate.find(query, MomentActivityData.class);
        } catch (Exception e) {
            logger.error("findLastData error={}", e.getMessage(), e);
            return null;
        }
    }


    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<MomentActivityData> momentRankingCache(String origin, int startTime, int endTime, int limit,int sortType) {
        if (sortType == 1) {
            return momentRankingByGiftTotalPrice(origin, startTime, endTime, limit);
        }
        return momentRanking(origin, startTime, endTime, limit);
    }

    public List<MomentActivityData> momentRankingByGiftTotalPrice(String origin, int startTime, int endTime, int limit) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (!StringUtils.isEmpty(origin)) {
                criteria = Criteria.where("location").is(origin);
            }

            if (startTime != 0 && endTime != 0) {
                criteria.and("c_time").gte(startTime).lte(endTime);
            }

            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project("_id", "uid", "text", "comments", "repost", "likes", "imgs", "giftTotalPrice"),
                    // 排序
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "giftTotalPrice").
                            and(Sort.by(Sort.Direction.DESC, "comments")).
                            and(Sort.by(Sort.Direction.DESC, "repost")).
                            and(Sort.by(Sort.Direction.ASC, "_id"))),
                    Aggregation.limit(limit)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, MomentActivityData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("momentRanking error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<MomentActivityData> momentRanking(String origin, int startTime, int endTime, int limit) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (!StringUtils.isEmpty(origin)) {
                criteria = Criteria.where("location").is(origin);
            }

            if (startTime != 0 && endTime != 0) {
                criteria.and("c_time").gte(startTime).lte(endTime);
            }

            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project("_id", "uid", "text", "comments", "repost", "likes", "imgs", "giftTotalPrice").
                            andExpression("{'$size': {'$cond': {{'$isArray': '$likes'}, '$likes', {}}}}").as("like_count"),
                    // 排序
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "like_count").
                            and(Sort.by(Sort.Direction.DESC, "comments")).
                            and(Sort.by(Sort.Direction.DESC, "repost")).
                            and(Sort.by(Sort.Direction.ASC, "_id"))),
                    Aggregation.limit(limit)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, MomentActivityData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("momentRanking error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public List<MomentActivityData> momentRanking(String origin, int startTime, int endTime, Integer start, Integer pageSize) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            if (!StringUtils.isEmpty(origin)) {
                criteria = Criteria.where("location").is(origin);
            }

            if (startTime != 0 && endTime != 0) {
                criteria.and("c_time").gte(startTime).lte(endTime);
            }

            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project("_id", "uid", "text", "comments", "repost", "likes").
                            andExpression("{'$size': {'$cond': {{'$isArray': '$likes'}, '$likes', {}}}}").as("like_count"),
                    // 排序
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "like_count").
                            and(Sort.by(Sort.Direction.DESC, "comments")).
                            and(Sort.by(Sort.Direction.DESC, "repost")).
                            and(Sort.by(Sort.Direction.ASC, "_id"))),
                    Aggregation.skip(start),
                    Aggregation.limit(pageSize)
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, MomentActivityData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("momentRanking error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    public List<MomentCountData> momentCount(String uid, int startTime, int endTime) {
        try {
            // 查询条件
            Criteria criteria = new Criteria();
            criteria.and("uid").is(uid);
            if (startTime != 0 && endTime != 0) {
                criteria.and("c_time").gte(startTime).lte(endTime);
            }

            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project("uid", "comments", "uidCount", "commentCount", "likeCount").
                            andExpression("{'$size': {'$cond': {{'$isArray': '$likes'}, '$likes', {}}}}").as("likeTotal"),
                    Aggregation.group("uid").count().as("uidCount").sum("comments").as("commentCount").sum("likeTotal").as("likeCount")
            );
            return mongoTemplate.aggregate(aggregation, TABLE_NAME, MomentCountData.class).getMappedResults();
        } catch (Exception e) {
            logger.error("momentCount error.{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

}
