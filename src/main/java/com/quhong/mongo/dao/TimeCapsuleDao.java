package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.TimeCapsuleData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Lazy
@Component
public class TimeCapsuleDao {

    private static final Logger logger = LoggerFactory.getLogger(TimeCapsuleDao.class);


    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public List<TimeCapsuleData> findAll() {
        try {
            return mongoTemplate.findAll(TimeCapsuleData.class);
        } catch (Exception e) {
            logger.error("findAll error={}", e.getMessage(), e);

        }
        return Collections.emptyList();
    }
}
