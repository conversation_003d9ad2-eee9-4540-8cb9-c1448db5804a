package com.quhong.mongo.data;


import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 *
 */
@Document(collection = "t_activity_time_capsule")
public class TimeCapsuleData {

    @Id
    private ObjectId _id;
    private String wishEn;
    private String wishAr;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getWishEn() {
        return wishEn;
    }

    public void setWishEn(String wishEn) {
        this.wishEn = wishEn;
    }

    public String getWishAr() {
        return wishAr;
    }

    public void setWishAr(String wishAr) {
        this.wishAr = wishAr;
    }
}
