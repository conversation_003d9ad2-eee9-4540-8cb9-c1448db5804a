package com.quhong.mongo.data;


import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 *
 */
@Document(collection = "t_activity_user_capsule")
public class UserCapsuleData {

    @Id
    private ObjectId _id;
    private String uid;
    private String wishInfo;
    private int roundNum;
    private int ctime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getWishInfo() {
        return wishInfo;
    }

    public void setWishInfo(String wishInfo) {
        this.wishInfo = wishInfo;
    }

    public int getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(int roundNum) {
        this.roundNum = roundNum;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
