package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ActivityLocalConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.RechargeInfo;
import com.quhong.service.InviteFissionCollectService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@Component
public class ActivityRefundConsumer {

    private static final Logger logger = LoggerFactory.getLogger(ActivityRefundConsumer.class);

    @Resource
    private InviteFissionCollectService inviteFissionCollectService;

    @RabbitListener(queues = ActivityLocalConstant.REFUND_QUEUE)
    public void handleRefundMessage(Message message) {
        try {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
                    logger.info("Consumer refund rabbit mq message, message body={}", json);
                    if (StringUtils.isEmpty(json)) {
                        logger.error("message invalid. message={}", json);
                        return;
                    }
                    RechargeInfo rechargeInfo = JSON.parseObject(json, RechargeInfo.class);
                    if (rechargeInfo == null || StringUtils.isEmpty(rechargeInfo.getUid())) {
                        logger.error("rechargeInfo is null or uid is empty or orderId is empty rechargeInfo={}", rechargeInfo);
                        return;
                    }
                    inviteFissionCollectService.handleUserScore(null,null,null,rechargeInfo);
                }
            });
        } catch (Exception e) {
            logger.error("handle refund message error. message body={}", new String(message.getBody()), e);
        }
    }
}
