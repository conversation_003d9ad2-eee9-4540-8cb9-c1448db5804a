package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ActivityLocalConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.RechargeInfo;
import com.quhong.handler.ActivityRechargeHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Component
public class ActivityRechargeConsumer {

    private static final Logger logger = LoggerFactory.getLogger(ActivityRechargeConsumer.class);

    private static final List<ActivityRechargeHandler> HANDLERS = new ArrayList<>();

    @PostConstruct
    public void postInit() {
        // 初始化监听器
        Map<String, ActivityRechargeHandler> beans = SpringUtils.getApplicationContext().getBeansOfType(ActivityRechargeHandler.class);
        for (String bean : beans.keySet()) {
            HANDLERS.add(beans.get(bean));
            logger.info("add ActivityRechargeHandler handler name={}", bean);
        }
    }

    @RabbitListener(queues = ActivityLocalConstant.ACTIVITY_RECHARGE_QUEUE)
    public void handleMessage(Message message) {
        try {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
                    logger.info("RechargeConsumer rabbit mq message, message body={}", json);
                    for (ActivityRechargeHandler handler : HANDLERS) {
                        try {
                            RechargeInfo rechargeInfo = JSON.parseObject(json, RechargeInfo.class);
                            handler.process(rechargeInfo);
                        } catch (Exception e) {
                            logger.error("process handler error handler={}", handler.getClass().getSimpleName(), e);
                        }
                    }
                }
            });
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        }
    }
}
