package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ActivityLocalConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.LogType;
import com.quhong.handler.ActivityHandler;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.service.*;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Component
public class ActivityTaskMsgConsumer {

    private static final Logger logger = LoggerFactory.getLogger(ActivityTaskMsgConsumer.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private static final List<TaskMsgHandler> TASK_HANDLERS = new ArrayList<>();
    @PostConstruct
    public void postInit() {
        // 初始化监听器
        Map<String, TaskMsgHandler> beans = SpringUtils.getApplicationContext().getBeansOfType(TaskMsgHandler.class);
        for (String bean : beans.keySet()) {
            TASK_HANDLERS.add(beans.get(bean));
            logger.info("add TaskMsgHandler name={}", bean);
        }
    }


    @Resource
    private LlluminateYouStarService llluminateYouStarService;
    @Resource
    private SuperPlayerService superPlayerService;
    @Resource
    private Eid2024Service eid2024Service;
    @Resource
    private MomentHotPostService momentHotPostService;
    @Resource
    private CarromGameService carromGameService;
    @Resource
    private GiftCollectService giftCollectService;
    @Resource
    private WomenSupportService womenSupportService;
    @Resource
    private BackUserCollectService backUserCollectService;
    @Resource
    private AnniversaryV7Service anniversaryV7Service;
    @Resource
    private RoomReturnBonusService roomReturnBonusService;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private AcRoomRocketV2Service acRoomRocketV2Service;

    @RabbitListener(queues = ActivityLocalConstant.ACTIVITY_TOPIC_QUEUE)
    public void handleMessage(Message message) {
        try {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
                    msgLogger.info("receive rabbit mq message, message body={}", json);
                    CommonMqTopicData mqData = JSON.parseObject(json, CommonMqTopicData.class);
                    for (TaskMsgHandler handler : TASK_HANDLERS) {
                        try {
                            handler.taskMsgProcess(JSON.parseObject(json, CommonMqTopicData.class));
                        } catch (Exception e) {
                            logger.error("process taskMsgHandler error handler={}", handler.getClass().getSimpleName(), e);
                        }
                    }

                    handleTaskMqData(mqData);
                }
            });
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        }
    }

    private void handleTaskMqData(CommonMqTopicData mqData) {
        if (mqData == null || StringUtils.isEmpty(mqData.getUid())) {
            return;
        }
        switch (mqData.getItem()) {
            case CommonMqTaskConstant.ON_MIC_TIME:
            case CommonMqTaskConstant.JOIN_ROOM_MEMBER:
                if (RoomUtils.isGameRoom(mqData.getRoomId())) {
                    return;
                }
                roomReturnBonusService.handleUserScore(null, mqData);
                acRoomRocketV2Service.handleUserScore(null, mqData);
                womenSupportService.handleMqMsg(mqData);
                anniversaryV7Service.handleMqMsg(mqData);
                break;
            case CommonMqTaskConstant.SUB_ROOM_EVENT:
                if (StringUtils.isEmpty(mqData.getRoomId())) {
                    return;
                }
                llluminateYouStarService.handleUserScore(null, mqData);
                anniversaryV7Service.handleMqMsg(mqData);
                break;
            case CommonMqTaskConstant.WIN_BAI_SHUN_GAME:
            case CommonMqTaskConstant.WIN_FRUIT_GAME:
            case CommonMqTaskConstant.WIN_GREEDY_GAME:
                superPlayerService.handleMqMsg(mqData);
                break;
            case CommonMqTaskConstant.PLAY_CARNIVAL_GAME:
                eid2024Service.handleMqMsg(mqData);
                giftCollectService.handleMqMsg(mqData);
                break;
            case CommonMqTaskConstant.PLAY_CARNIVAL_REWARD:
                eid2024Service.handleCommonMqGiftMsg(mqData);
                giftCollectService.handleCommonMqGiftMsg(mqData);
                break;
            case CommonMqTaskConstant.PLAY_SUD_GAME:
                carromGameService.handleUserScore(mqData);
                break;
            case CommonMqTaskConstant.LIKE_MOMENT:
            case CommonMqTaskConstant.CANCEL_LIKE_MOMENT:
            case CommonMqTaskConstant.SEND_MOMENT_GIFT:
            case CommonMqTaskConstant.POST_MOMENT:
                momentHotPostService.handleMqMsg(mqData);
                break;
            case CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL:
                womenSupportService.handleMqMsg(mqData);
                break;
            case CommonMqTaskConstant.ADD_FRIEND:
                womenSupportService.handleMqMsg(mqData);
//                backUserCollectService.handleUserScore(null, mqData, null);
                break;
            case CommonMqTaskConstant.DAILY_LOGIN:
//                backUserCollectService.handleUserScore(null, mqData, null);
                break;
            default:
                break;
        }
    }

}
