package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.constant.ActivityLocalConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.SpringUtils;
import com.quhong.data.SendGiftData;
import com.quhong.handler.ActivityHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.amqp.core.Message;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Component
public class ActivityConsumer {

    private static final Logger logger = LoggerFactory.getLogger(ActivityConsumer.class);
    private static final List<ActivityHandler> HANDLERS = new ArrayList<>();

    @PostConstruct
    public void postInit() {
        // 初始化监听器
        Map<String, ActivityHandler> beans = SpringUtils.getApplicationContext().getBeansOfType(ActivityHandler.class);
        for (String bean : beans.keySet()) {
            HANDLERS.add(beans.get(bean));
            logger.info("add send gift handler name={}", bean);
        }
    }

    @RabbitListener(queues = ActivityLocalConstant.ACTIVITY_COLLECT_QUEUE)
    public void handleMessage(Message message) {
        try {
            BaseTaskFactory.getFactory().addSlow(new Task() {
                @Override
                protected void execute() {
                    String json = new String(message.getBody());
                    logger.info("ActivityConsumer rabbit mq message, message body={}", json);
                    for (ActivityHandler handler : HANDLERS) {
                        try {
                            handler.process(JSON.parseObject(json, SendGiftData.class));
                        } catch (Exception e) {
                            logger.error("process handler error handler={}", handler.getClass().getSimpleName(), e);
                        }
                    }
                }
            });
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
        }
    }


    // @SuppressWarnings("SynchronizationOnLocalVariableOrMethodParameter")
    // private void parseActivityGiftSendData(SendGiftData data) {
    //
    //     if (StringUtils.isEmpty(data.getUid()) || StringUtils.isEmpty(data.getDate_str())) {
    //         logger.error("parse user exp data error uid or date str is empty item={}", data.getItem());
    //         return;
    //     }
    //     UserExpDetailData detail = userExpDetailDao.getUserExpDetail(data.getUid(), data.getDate_str());
    //     int nowSeconds = DateHelper.getNowSeconds();
    //     if (null == detail) {
    //         synchronized (LOCK) {
    //             detail = userExpDetailDao.getUserExpDetail(data.getUid(), data.getDate_str());
    //             if (null == detail) {
    //                 detail = new UserExpDetailData();
    //                 detail.setUid(data.getUid());
    //                 detail.setDate_str(data.getDate_str());
    //                 detail.setCtime(nowSeconds);
    //             }
    //             detail.setMtime(nowSeconds);
    //             handleUserExpData(data, detail);
    //         }
    //     } else {
    //         synchronized (detail) {
    //             detail.setMtime(nowSeconds);
    //             handleUserExpData(data, detail);
    //         }
    //     }
    // }
}
