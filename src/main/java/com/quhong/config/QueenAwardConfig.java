package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import com.quhong.mongo.data.RankingActivity;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@PropertySource(value = "classpath:activity_award_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "queen-hall")
public class QueenAwardConfig extends ActivityAwardConfig{

    // protected String activityName; // 活动名称
    // protected int startTime; // 活动开始时间
    // protected int endTime; // 活动结束时间
    // protected List<Integer> giftIdList; // 礼物id
    // protected int calculateMethod; // 等级礼物计算方式
    // protected int sendingRewardGender; // 等级奖励是否限制性别 0: 不限制  1: 男  2: 女
    // protected int receiveRewardGender; // 等级奖励是否限制性别 0: 不限制  1: 男  2: 女
    //
    // protected List<ReachingReward> sendingRewardList;
    // protected List<ReachingReward> receiveRewardList;

    private List<Integer> giftIdTestList; // 礼物id
    private List<Integer> giftIdProdList; // 礼物id

    private List<ReachingReward> sendingRewardListTest;
    private List<ReachingReward> sendingRewardListProd;
    private List<ReachingReward> receiveRewardListTest;
    private List<ReachingReward> receiveRewardListProd;


    public List<Integer> getGiftIdList() {
        if(ServerConfig.isProduct()) {
            return giftIdProdList;
        } else {
            return giftIdTestList;
        }
    }

    public List<Integer> getGiftIdTestList() {
        return giftIdTestList;
    }

    public void setGiftIdTestList(List<Integer> giftIdTestList) {
        this.giftIdTestList = giftIdTestList;
    }

    public List<Integer> getGiftIdProdList() {
        return giftIdProdList;
    }

    public void setGiftIdProdList(List<Integer> giftIdProdList) {
        this.giftIdProdList = giftIdProdList;
    }

    public List<ReachingReward> getSendingRewardList() {
        if(ServerConfig.isProduct()) {
            return sendingRewardListProd;
        } else {
            return sendingRewardListTest;
        }
    }

    public List<ReachingReward> getSendingRewardListTest() {
        return sendingRewardListTest;
    }

    public void setSendingRewardListTest(List<ReachingReward> sendingRewardListTest) {
        this.sendingRewardListTest = sendingRewardListTest;
    }

    public List<ReachingReward> getSendingRewardListProd() {
        return sendingRewardListProd;
    }

    public void setSendingRewardListProd(List<ReachingReward> sendingRewardListProd) {
        this.sendingRewardListProd = sendingRewardListProd;
    }

    public List<ReachingReward> getReceiveRewardList() {
        if(ServerConfig.isProduct()) {
            return receiveRewardListProd;
        } else {
            return receiveRewardListTest;
        }
    }

    public List<ReachingReward> getReceiveRewardListTest() {
        return receiveRewardListTest;
    }

    public void setReceiveRewardListTest(List<ReachingReward> receiveRewardListTest) {
        this.receiveRewardListTest = receiveRewardListTest;
    }

    public List<ReachingReward> getReceiveRewardListProd() {
        return receiveRewardListProd;
    }

    public void setReceiveRewardListProd(List<ReachingReward> receiveRewardListProd) {
        this.receiveRewardListProd = receiveRewardListProd;
    }
}
