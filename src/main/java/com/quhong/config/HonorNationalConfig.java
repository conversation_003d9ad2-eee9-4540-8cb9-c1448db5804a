package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@PropertySource(value = "classpath:activity_national_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "honor-national")
public class HonorNationalConfig extends ActivityAwardConfig{

    // protected String activityName; // 活动名称
    // protected int startTime; // 活动开始时间
    // protected int endTime; // 活动结束时间
    // protected List<Integer> giftIdList; // 礼物id
    // protected int calculateMethod; // 等级礼物计算方式  1; // 计算礼物数  2; // 2计算钻石数
    // protected int sendingRewardGender; // 等级奖励是否限制性别 0: 不限制  1: 男  2: 女
    // protected int receiveRewardGender; // 等级奖励是否限制性别 0: 不限制  1: 男  2: 女
    //
    // protected List<ReachingReward> sendingRewardList;
    // protected List<ReachingReward> receiveRewardList;

    private List<Integer> giftIdTestList; // 礼物id
    private List<Integer> giftIdProdList; // 礼物id

    private Map<String, List<ReachingReward>> sendingRewardMapTest;
    private Map<String, List<ReachingReward>> sendingRewardMapProd;

    private List<FlagConfig> giftTestList; // 礼物id
    private List<FlagConfig> giftProdList; // 礼物id

    public static class FlagConfig {
        private Integer giftId;  // 礼物id
        private String name;  // 礼物名称
        private String nameAr;  // 礼物名称
        private String icon;  // 礼物图标

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }
    }



    public List<Integer> getGiftIdList() {
        if(ServerConfig.isProduct()) {
            return giftIdProdList;
        } else {
            return giftIdTestList;
        }
    }

    public List<Integer> getGiftIdTestList() {
        return giftIdTestList;
    }

    public void setGiftIdTestList(List<Integer> giftIdTestList) {
        this.giftIdTestList = giftIdTestList;
    }

    public List<Integer> getGiftIdProdList() {
        return giftIdProdList;
    }

    public void setGiftIdProdList(List<Integer> giftIdProdList) {
        this.giftIdProdList = giftIdProdList;
    }

    public Map<String, List<ReachingReward>> getSendingRewardMap() {
        if(ServerConfig.isProduct()) {
            return sendingRewardMapProd;
        } else {
            return sendingRewardMapTest;
        }
    }

    public Map<String, List<ReachingReward>> getSendingRewardMapTest() {
        return sendingRewardMapTest;
    }

    public void setSendingRewardMapTest(Map<String, List<ReachingReward>> sendingRewardMapTest) {
        this.sendingRewardMapTest = sendingRewardMapTest;
    }

    public Map<String, List<ReachingReward>> getSendingRewardMapProd() {
        return sendingRewardMapProd;
    }

    public void setSendingRewardMapProd(Map<String, List<ReachingReward>> sendingRewardMapProd) {
        this.sendingRewardMapProd = sendingRewardMapProd;
    }


    public List<FlagConfig> getGiftList() {
        if(ServerConfig.isProduct()) {
            return giftProdList;
        } else {
            return giftTestList;
        }
    }

    public List<FlagConfig> getGiftTestList() {
        return giftTestList;
    }

    public void setGiftTestList(List<FlagConfig> giftTestList) {
        this.giftTestList = giftTestList;
    }

    public List<FlagConfig> getGiftProdList() {
        return giftProdList;
    }

    public void setGiftProdList(List<FlagConfig> giftProdList) {
        this.giftProdList = giftProdList;
    }
}
