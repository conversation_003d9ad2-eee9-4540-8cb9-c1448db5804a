package com.quhong.config;

import com.quhong.intercepters.H5InterceptorConfig;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拦截内容
 */
@Configuration
public class ActivityInterceptorConfig extends H5InterceptorConfig {
    @Override
    protected Map<String, Long> getRequestWarnDurationMap() {
        Map<String, Long> map = new HashMap<>();
        map.put("/activity/uploadBaseEncode", 16000L);
        map.put("/activity/uploadFileOSS", 16000L);
        map.put("/activity/painterPicture", 6000L);
        map.put("/activity/smash_egg/home_info", 5000L);
        map.put("/activity/resourceKeyList", 10000L);
        map.put("/activity/submitSharingOfficer", 16000L);
        map.put("/activity/uploadFileOSSLimit", 16000L);
        map.put("/activity/arabicPainterPicture", 5000L);
        map.put("/activity/arabicPainterDayCreateList", 5000L);
        map.put("/activity/backUser/inviterHomeInfo", 6000L);
        map.put("/activity/fatePlaza/noteList", 5000L);
        map.put("/activity/collectRoomReturnBonus", 5000L);
        map.put("/activity/summerPhotoPicture", 5000L);
        map.put("/activity/summerPhotoDayCreateList", 5000L);
        return map;
    }


    protected List<String> getExcludePaths() {
        return Arrays.asList(baseUrl + "clearNationalDayV2",
                baseUrl + "share_page/**", baseUrl + "share",
                baseUrl + "share_activity_page/**", baseUrl + "activity_download_url",
                baseUrl + "officialSite",
                baseUrl + "poster",
                baseUrl + "feedback/**",
                baseUrl + "roomEventConfig",
                baseUrl + "out/resourceKeyList"
        );
    }
}
