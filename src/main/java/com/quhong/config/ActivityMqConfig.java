package com.quhong.config;

import com.quhong.constant.ActivityLocalConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.mq.MqSenderService;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class ActivityMqConfig {




    @Bean
    public Queue giftCollectQueue() {
        return new Queue(ActivityLocalConstant.ACTIVITY_COLLECT_QUEUE, true);
    }




    @Bean
    public Queue activityRechargeQueue() {
        return new Queue(ActivityLocalConstant.ACTIVITY_RECHARGE_QUEUE, true);
    }


    @Bean
    public DirectExchange sendGiftExchange() {
        return new DirectExchange(ActivityLocalConstant.ACTIVITY_COLLECT_EXCHANGE, false, false);
    }



    @Bean
    public TopicExchange activityTopicCommonExchange() {
        return new TopicExchange(ActivityLocalConstant.ACTIVITY_TOPIC_EXCHANGE, false, false);
    }

    @Bean
    Binding bindingExchange(Queue giftCollectQueue, DirectExchange directCommonMsg) {
        return BindingBuilder.bind(giftCollectQueue).to(directCommonMsg).with(ActivityLocalConstant.ACTIVITY_COLLECT_ROUTING_KEY);
    }

    @Bean
    Binding bindingExchangeRechargeMsg(Queue activityRechargeQueue, TopicExchange activityTopicCommonExchange) {
        return BindingBuilder.bind(activityRechargeQueue).to(activityTopicCommonExchange).with(ActivityLocalConstant.ACTIVITY_RECHARGE_TOPIC_ROUTING_KEY);
    }

    // 定义队列
    @Bean
    public Queue activityTopicQueue() {
        return new Queue(ActivityLocalConstant.ACTIVITY_TOPIC_QUEUE, true);
    }

    // 定义交换机
    @Bean
    public TopicExchange topicTaskExchange() {
        return new TopicExchange(CommonMqTaskConstant.TASK_COMMON_EXCHANGE, false, false);
    }

    // 将队列以指定key绑定到交换机上
    @Bean
    Binding bindingExchangeTaskMsg(Queue activityTopicQueue, TopicExchange topicTaskExchange) {
        return BindingBuilder.bind(activityTopicQueue).to(topicTaskExchange).with(CommonMqTaskConstant.USER_TASK_ROUTING_KEY);
    }

    @Bean
    Binding bindingExchangeLevelTaskMsg(Queue activityTopicQueue, TopicExchange topicTaskExchange) {
        return BindingBuilder.bind(activityTopicQueue).to(topicTaskExchange).with(CommonMqTaskConstant.USER_FRIEND_LEVEL_KEY);
    }

    @Bean
    Binding bindingExchangeUserRoomMsg(Queue activityTopicQueue, TopicExchange activityTopicCommonExchange) {
        return BindingBuilder.bind(activityTopicQueue).to(activityTopicCommonExchange).with(MqSenderService.ROUTE_KEY_USER_ENTER_ROOM);
    }



    @Bean
    public Queue refundQueue() {
        return new Queue(ActivityLocalConstant.REFUND_QUEUE, true);
    }

    @Bean
    Binding bindingExchangeRefundMessages(Queue refundQueue, TopicExchange activityTopicCommonExchange) {
        return BindingBuilder.bind(refundQueue).to(activityTopicCommonExchange).with(ActivityLocalConstant.REFUND_TOPIC_ROUTING_KEY);
    }

}
