package com.quhong.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

public class ActivityAwardConfig {
    protected String activityName; // 活动名称
    protected int startTime; // 活动开始时间
    protected int endTime; // 活动结束时间
    protected List<Integer> giftIdList; // 礼物id
    protected int calculateMethod; // 等级礼物计算方式
    protected int sendingRewardGender; // 等级奖励是否限制性别 0: 不限制  1: 男  2: 女
    protected int receiveRewardGender; // 等级奖励是否限制性别 0: 不限制  1: 男  2: 女

    protected List<ReachingReward> sendingRewardList;
    protected List<ReachingReward> receiveRewardList;


    public static class ReachingReward {
        private Integer giftNum; // 礼物数量(礼物钻石总数)
        private List<RewardConfig> rewardConfigList; // 奖励配置

        public Integer getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(Integer giftNum) {
            this.giftNum = giftNum;
        }

        public List<RewardConfig> getRewardConfigList() {
            return rewardConfigList;
        }

        public void setRewardConfigList(List<RewardConfig> rewardConfigList) {
            this.rewardConfigList = rewardConfigList;
        }
    }

    public static class RewardConfig {
        // 资源类型 背包礼物:gift 麦位框:mic 气泡框:buddle 入场动画:ride 麦位声波:ripple 钻石:diamond 勋章:badge 浮屏:float_screen 钻石:diamond
        private String rewardType;
        private Integer sourceId; // 奖励资源id
        private Integer rewardTime; //资源时长（天） 0永久
        private Integer rewardNum; // 礼物数量 可能为空

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            return sourceId;
        }

        public void setSourceId(Integer sourceId) {
            this.sourceId = sourceId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getGiftIdList() {
        return giftIdList;
    }

    public void setGiftIdList(List<Integer> giftIdList) {
        this.giftIdList = giftIdList;
    }

    public int getCalculateMethod() {
        return calculateMethod;
    }

    public void setCalculateMethod(int calculateMethod) {
        this.calculateMethod = calculateMethod;
    }

    public int getSendingRewardGender() {
        return sendingRewardGender;
    }

    public void setSendingRewardGender(int sendingRewardGender) {
        this.sendingRewardGender = sendingRewardGender;
    }

    public int getReceiveRewardGender() {
        return receiveRewardGender;
    }

    public void setReceiveRewardGender(int receiveRewardGender) {
        this.receiveRewardGender = receiveRewardGender;
    }

    public List<ReachingReward> getSendingRewardList() {
        return sendingRewardList;
    }

    public void setSendingRewardList(List<ReachingReward> sendingRewardList) {
        this.sendingRewardList = sendingRewardList;
    }

    public List<ReachingReward> getReceiveRewardList() {
        return receiveRewardList;
    }

    public void setReceiveRewardList(List<ReachingReward> receiveRewardList) {
        this.receiveRewardList = receiveRewardList;
    }
}
