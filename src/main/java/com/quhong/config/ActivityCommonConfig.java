package com.quhong.config;


import com.quhong.core.config.ServerConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@PropertySource(value = "classpath:activity_common_config.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "common-config")
public class ActivityCommonConfig {

    // 运营房配置
    private List<String> activityRoomProdList;
    private List<String> activityRoomTestList;

    // 射门赢大奖配置
    private List<ShootCardConfig> shootCardList;
    private List<CommonAwardConfig> shootDrawList;

    // 世界杯竞猜配置
    private Integer championStartTime;
    private Integer championEndTime;
    private Integer betChangeStartTime;


    // 开宝箱配置
    private List<TreasureWeeklyConfig> treasureWeeklyList;
    private List<TreasureRewardConfig> treasureRankList;

    // 开斋
    private Map<String, List<RamadanAwardConfig>> ramadanConfig;

    // 植树奖励
    private List<PlantTreeConfig> plantTreeConfigList;
    private Map<String, List<PlantRankConfig>> plantTreeRankConfig;

    // 问题反馈
    private List<FeedbackConfig> feedbackConfigList;

    // 航海家升级配置
    private Map<String, List<CommonAwardConfig>> voyagerCaptainConfig;
    private Map<String, List<CommonAwardConfig>> voyagerCrewConfig;

    // 熊猫配置
    private List<CommonAwardConfig> pandaConfigList;

    // 盲盒配置
    private List<BlindBoxConfig> blindBoxConfigList;

    // 六周年配置
    private List<AnniversarySixConfig> anniversarySixConfigList;

    // mini任务
    private List<TaskConfig> miniTaskList;

    // 礼物心愿单配置
    private Map<Integer, List<GiftWishConfig>> giftWishConfig;
    private List<GiftWishWeeklyConfig> giftWishWeeklyConfigList;

    // 爱神丘比特配置
    private List<CommonAwardConfig> cupidConfigList;

    // 小丑与魔术师配置
    private List<CommonAwardConfig> clownsConfigList;
    private List<CommonAwardConfig> magicConfigList;

    // 保护地球
    private List<CommonAwardConfig> guardEarthConfigList;

    // 猎鹰配置
    private List<CommonAwardConfig> falconConfigList;

    private List<CommonFlagConfig> flagConfigList;

    private List<QueenConfig> queenCharmConfigList;

    private List<QueenConfig> queenGenerousConfigList;

    private List<QueenConfig> queenPassionConfigList;

    public static class GiftWishWeeklyConfig {
        private String nameEn;
        private String nameAr;
        private String dailyKey;
        private Integer giftId;

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getDailyKey() {
            return dailyKey;
        }

        public void setDailyKey(String dailyKey) {
            this.dailyKey = dailyKey;
        }

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }
    }

    public static class GiftWishConfig {
        private Integer giftId;
        private Integer collect;
        private String giftEffect;

        public Integer getGiftId() {
            return giftId;
        }

        public void setGiftId(Integer giftId) {
            this.giftId = giftId;
        }

        public Integer getCollect() {
            return collect;
        }

        public void setCollect(Integer collect) {
            this.collect = collect;
        }

        public String getGiftEffect() {
            return giftEffect;
        }

        public void setGiftEffect(String giftEffect) {
            this.giftEffect = giftEffect;
        }
    }


    public static class TaskConfig {
        private Integer totalProcess;    // 总任务进度
        private int currentProcess;      // 当前进度
        private int roundNum;            // 任务阶段
        private String taskKey;
        private String nameEn;
        private String nameAr;
        private String taskIcon;

        public Integer getTotalProcess() {
            return totalProcess;
        }

        public void setTotalProcess(Integer totalProcess) {
            this.totalProcess = totalProcess;
        }

        public int getCurrentProcess() {
            return currentProcess;
        }

        public void setCurrentProcess(int currentProcess) {
            this.currentProcess = currentProcess;
        }

        public int getRoundNum() {
            return roundNum;
        }

        public void setRoundNum(int roundNum) {
            this.roundNum = roundNum;
        }

        public String getTaskKey() {
            return taskKey;
        }

        public void setTaskKey(String taskKey) {
            this.taskKey = taskKey;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getTaskIcon() {
            return taskIcon;
        }

        public void setTaskIcon(String taskIcon) {
            this.taskIcon = taskIcon;
        }
    }

    public static class AnniversarySixConfig {
        private Integer totalProcess;    // 总任务进度
        private int currentProcess;      // 当前进度
        private String star;
        private String nameEn;
        private String nameAr;
        private String roomId;

        public Integer getTotalProcess() {
            return totalProcess;
        }

        public void setTotalProcess(Integer totalProcess) {
            this.totalProcess = totalProcess;
        }

        public int getCurrentProcess() {
            return currentProcess;
        }

        public void setCurrentProcess(int currentProcess) {
            this.currentProcess = currentProcess;
        }

        public String getStar() {
            return star;
        }

        public void setStar(String star) {
            this.star = star;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }
    }

    public static class BlindBoxConfig extends CommonAwardConfig {
        private Integer totalProcess;    // 总任务进度
        private int direct;              // 0: 判断发送礼物id  1: 判断接收礼物id
        private int directUser;          // 0: 发送者  1: 接收者
        private Integer taskTestGiftId;
        private Integer taskProdGiftId;
        private int jumpTestGiftId;
        private int jumpProdGiftId;
        private String rewardNameEn;
        private String rewardNameAr;

        public Integer getTotalProcess() {
            return totalProcess;
        }

        public void setTotalProcess(Integer totalProcess) {
            this.totalProcess = totalProcess;
        }

        public int getDirect() {
            return direct;
        }

        public void setDirect(int direct) {
            this.direct = direct;
        }

        public int getDirectUser() {
            return directUser;
        }

        public void setDirectUser(int directUser) {
            this.directUser = directUser;
        }

        public Integer getTaskTestGiftId() {
            return taskTestGiftId;
        }

        public void setTaskTestGiftId(Integer taskTestGiftId) {
            this.taskTestGiftId = taskTestGiftId;
        }

        public Integer getTaskProdGiftId() {
            return taskProdGiftId;
        }

        public void setTaskProdGiftId(Integer taskProdGiftId) {
            this.taskProdGiftId = taskProdGiftId;
        }

        public Integer getTaskGiftId() {
            if (ServerConfig.isProduct()) {
                return taskProdGiftId;
            } else {
                return taskTestGiftId;
            }
        }

        public int getJumpTestGiftId() {
            return jumpTestGiftId;
        }

        public void setJumpTestGiftId(int jumpTestGiftId) {
            this.jumpTestGiftId = jumpTestGiftId;
        }

        public int getJumpProdGiftId() {
            return jumpProdGiftId;
        }

        public void setJumpProdGiftId(int jumpProdGiftId) {
            this.jumpProdGiftId = jumpProdGiftId;
        }

        public Integer getJumpGiftId() {
            if (ServerConfig.isProduct()) {
                return jumpProdGiftId;
            } else {
                return jumpTestGiftId;
            }
        }

        public String getRewardNameEn() {
            return rewardNameEn;
        }

        public void setRewardNameEn(String rewardNameEn) {
            this.rewardNameEn = rewardNameEn;
        }

        public String getRewardNameAr() {
            return rewardNameAr;
        }

        public void setRewardNameAr(String rewardNameAr) {
            this.rewardNameAr = rewardNameAr;
        }
    }

    public static class CommonAwardConfig {
        private String drawType;
        private String iconEn;
        private String iconAr;
        private String nameEn;
        private String nameAr;
        private String nameShortEn;
        private String nameShortAr;
        private String rewardType;
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;
        private Integer rateNum;
        private Integer inRoomScreen;
        private Integer allRoomScreen;
        private Integer broadcast;


        public String getDrawType() {
            return drawType;
        }

        public void setDrawType(String drawType) {
            this.drawType = drawType;
        }

        public String getIconEn() {
            return iconEn;
        }

        public void setIconEn(String iconEn) {
            this.iconEn = iconEn;
        }

        public String getIconAr() {
            return iconAr;
        }

        public void setIconAr(String iconAr) {
            this.iconAr = iconAr;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getNameShortEn() {
            return nameShortEn;
        }

        public void setNameShortEn(String nameShortEn) {
            this.nameShortEn = nameShortEn;
        }

        public String getNameShortAr() {
            return nameShortAr;
        }

        public void setNameShortAr(String nameShortAr) {
            this.nameShortAr = nameShortAr;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            if (ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }

        public Integer getRateNum() {
            return rateNum;
        }

        public void setRateNum(Integer rateNum) {
            this.rateNum = rateNum;
        }

        public Integer getInRoomScreen() {
            return inRoomScreen;
        }

        public void setInRoomScreen(Integer inRoomScreen) {
            this.inRoomScreen = inRoomScreen;
        }

        public Integer getAllRoomScreen() {
            return allRoomScreen;
        }

        public void setAllRoomScreen(Integer allRoomScreen) {
            this.allRoomScreen = allRoomScreen;
        }

        public Integer getBroadcast() {
            return broadcast;
        }

        public void setBroadcast(Integer broadcast) {
            this.broadcast = broadcast;
        }
    }

    public static class FeedbackConfig {
        private String problemEn;
        private String problemAr;
        private List<String> showList;
        private List<SubTypeConfig> subTypeList;

        public String getProblemEn() {
            return problemEn;
        }

        public void setProblemEn(String problemEn) {
            this.problemEn = problemEn;
        }

        public String getProblemAr() {
            return problemAr;
        }

        public void setProblemAr(String problemAr) {
            this.problemAr = problemAr;
        }

        public List<String> getShowList() {
            return showList;
        }

        public void setShowList(List<String> showList) {
            this.showList = showList;
        }

        public List<SubTypeConfig> getSubTypeList() {
            return subTypeList;
        }

        public void setSubTypeList(List<SubTypeConfig> subTypeList) {
            this.subTypeList = subTypeList;
        }

        public static class SubTypeConfig {
            private String subTypeEn;
            private String subTypeAr;

            public String getSubTypeEn() {
                return subTypeEn;
            }

            public void setSubTypeEn(String subTypeEn) {
                this.subTypeEn = subTypeEn;
            }

            public String getSubTypeAr() {
                return subTypeAr;
            }

            public void setSubTypeAr(String subTypeAr) {
                this.subTypeAr = subTypeAr;
            }
        }

    }


    public static class PlantRankConfig {
        private String rewardType;
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            if (ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }

    public static class PlantTreeConfig {
        private Integer awardType;
        private String icon;
        private String nameEn;
        private String nameAr;
        private String rewardType;
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;

        public Integer getAwardType() {
            return awardType;
        }

        public void setAwardType(Integer awardType) {
            this.awardType = awardType;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceId() {
            if (ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }

    public static class RamadanAwardConfig {
        private String icon;
        private String nameEn;
        private String nameAr;
        private String rewardType;
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            if (ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }


    public static class SmashEggConfig {
        private String winType;
        private String icon;
        private String nameEn;
        private String nameAr;
        private String pageAwardEn;
        private String pageAwardAr;
        private String pageAwardUrl;
        private Integer pageOrder;
        private Integer screen;
        private Integer prize;
        private String rewardType;
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;

        public String getWinType() {
            return winType;
        }

        public void setWinType(String winType) {
            this.winType = winType;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getNameEn() {
            return nameEn;
        }

        public void setNameEn(String nameEn) {
            this.nameEn = nameEn;
        }

        public String getNameAr() {
            return nameAr;
        }

        public void setNameAr(String nameAr) {
            this.nameAr = nameAr;
        }

        public String getPageAwardEn() {
            return pageAwardEn;
        }

        public void setPageAwardEn(String pageAwardEn) {
            this.pageAwardEn = pageAwardEn;
        }

        public String getPageAwardAr() {
            return pageAwardAr;
        }

        public void setPageAwardAr(String pageAwardAr) {
            this.pageAwardAr = pageAwardAr;
        }

        public String getPageAwardUrl() {
            return pageAwardUrl;
        }

        public void setPageAwardUrl(String pageAwardUrl) {
            this.pageAwardUrl = pageAwardUrl;
        }

        public Integer getPageOrder() {
            return pageOrder;
        }

        public void setPageOrder(Integer pageOrder) {
            this.pageOrder = pageOrder;
        }

        public Integer getScreen() {
            return screen;
        }

        public void setScreen(Integer screen) {
            this.screen = screen;
        }

        public Integer getPrize() {
            return prize;
        }

        public void setPrize(Integer prize) {
            this.prize = prize;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            if (ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }


    public static class TreasureWeeklyConfig {
        private String icon; // 图标
        private String rewardType; // 图标
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            if (ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public void setSourceId(Integer sourceId) {
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }

    public static class TreasureRewardConfig {
        private String icon; // 图标
        private String titleEn;
        private String titleAr;
        private String rewardType; // 图标
        private Integer sourceId;
        private Integer sourceProdId;
        private Integer sourceTestId;
        private Integer rewardTime;
        private Integer rewardNum;

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getTitleEn() {
            return titleEn;
        }

        public void setTitleEn(String titleEn) {
            this.titleEn = titleEn;
        }

        public String getTitleAr() {
            return titleAr;
        }

        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }

        public String getRewardType() {
            return rewardType;
        }

        public void setRewardType(String rewardType) {
            this.rewardType = rewardType;
        }

        public Integer getSourceId() {
            if (ServerConfig.isProduct()) {
                return sourceProdId;
            } else {
                return sourceTestId;
            }
        }

        public void setSourceId(Integer sourceId) {
            this.sourceId = sourceId;
        }

        public Integer getSourceProdId() {
            return sourceProdId;
        }

        public void setSourceProdId(Integer sourceProdId) {
            this.sourceProdId = sourceProdId;
        }

        public Integer getSourceTestId() {
            return sourceTestId;
        }

        public void setSourceTestId(Integer sourceTestId) {
            this.sourceTestId = sourceTestId;
        }

        public Integer getRewardTime() {
            return rewardTime;
        }

        public void setRewardTime(Integer rewardTime) {
            this.rewardTime = rewardTime;
        }

        public Integer getRewardNum() {
            return rewardNum;
        }

        public void setRewardNum(Integer rewardNum) {
            this.rewardNum = rewardNum;
        }
    }

    public static class ShootCardConfig {
        private String title; // 标题
        private String titleAr; // 标题
        private String icon;
        private String iconAr;
        private String roomIcon;
        private String roomIconAr;
        private String cardKey; // key
        private List<Integer> rateList; // 概率

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTitleAr() {
            return titleAr;
        }

        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getIconAr() {
            return iconAr;
        }

        public void setIconAr(String iconAr) {
            this.iconAr = iconAr;
        }

        public String getRoomIcon() {
            return roomIcon;
        }

        public void setRoomIcon(String roomIcon) {
            this.roomIcon = roomIcon;
        }

        public String getRoomIconAr() {
            return roomIconAr;
        }

        public void setRoomIconAr(String roomIconAr) {
            this.roomIconAr = roomIconAr;
        }

        public String getCardKey() {
            return cardKey;
        }

        public void setCardKey(String cardKey) {
            this.cardKey = cardKey;
        }

        public List<Integer> getRateList() {
            return rateList;
        }

        public void setRateList(List<Integer> rateList) {
            this.rateList = rateList;
        }
    }

    public static class CommonFlagConfig {
        private String countryName;
        private String flagUrl;

        public String getCountryName() {
            return countryName;
        }

        public void setCountryName(String countryName) {
            this.countryName = countryName;
        }

        public String getFlagUrl() {
            return flagUrl;
        }

        public void setFlagUrl(String flagUrl) {
            this.flagUrl = flagUrl;
        }
    }

    public static class QueenConfig {
        private Integer order;
        private String titleEn;
        private String titleAr;
        private String conditionEn;
        private String conditionAr;
        private Integer maxPoint;
        private Integer maxNum;
        private Integer numPerPoint;
        private String eventDesc;
        private String icon ;

        public QueenConfig() {
        }

        public QueenConfig(Integer order, String titleEn, String titleAr, String conditionEn, String conditionAr, Integer maxPoint, Integer maxNum, Integer numPerPoint, String eventDesc, String icon) {
            this.order = order;
            this.titleEn = titleEn;
            this.titleAr = titleAr;
            this.conditionEn = conditionEn;
            this.conditionAr = conditionAr;
            this.maxPoint = maxPoint;
            this.maxNum = maxNum;
            this.numPerPoint = numPerPoint;
            this.eventDesc = eventDesc;
            this.icon = icon;
        }

        public Integer getOrder() {
            return order;
        }

        public void setOrder(Integer order) {
            this.order = order;
        }

        public Integer getMaxPoint() {
            return maxPoint;
        }

        public void setMaxPoint(Integer maxPoint) {
            this.maxPoint = maxPoint;
        }

        public String getTitleEn() {
            return titleEn;
        }

        public void setTitleEn(String titleEn) {
            this.titleEn = titleEn;
        }

        public String getTitleAr() {
            return titleAr;
        }

        public void setTitleAr(String titleAr) {
            this.titleAr = titleAr;
        }

        public String getConditionEn() {
            return conditionEn;
        }

        public void setConditionEn(String conditionEn) {
            this.conditionEn = conditionEn;
        }

        public String getConditionAr() {
            return conditionAr;
        }

        public void setConditionAr(String conditionAr) {
            this.conditionAr = conditionAr;
        }

        public Integer getMaxNum() {
            return maxNum;
        }

        public void setMaxNum(Integer maxNum) {
            this.maxNum = maxNum;
        }

        public Integer getNumPerPoint() {
            return numPerPoint;
        }

        public void setNumPerPoint(Integer numPerPoint) {
            this.numPerPoint = numPerPoint;
        }

        public String getEventDesc() {
            return eventDesc;
        }

        public void setEventDesc(String eventDesc) {
            this.eventDesc = eventDesc;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }
    }

    public List<String> getActivityRoomList() {
        return ServerConfig.isProduct() ? activityRoomProdList : activityRoomTestList;
    }

    public List<String> getActivityRoomProdList() {
        return activityRoomProdList;
    }

    public void setActivityRoomProdList(List<String> activityRoomProdList) {
        this.activityRoomProdList = activityRoomProdList;
    }

    public List<String> getActivityRoomTestList() {
        return activityRoomTestList;
    }

    public void setActivityRoomTestList(List<String> activityRoomTestList) {
        this.activityRoomTestList = activityRoomTestList;
    }

    public List<ShootCardConfig> getShootCardList() {
        return shootCardList;
    }

    public void setShootCardList(List<ShootCardConfig> shootCardList) {
        this.shootCardList = shootCardList;
    }

    public List<CommonAwardConfig> getShootDrawList() {
        return shootDrawList;
    }

    public void setShootDrawList(List<CommonAwardConfig> shootDrawList) {
        this.shootDrawList = shootDrawList;
    }

    public Integer getChampionStartTime() {
        return championStartTime;
    }

    public void setChampionStartTime(Integer championStartTime) {
        this.championStartTime = championStartTime;
    }

    public Integer getChampionEndTime() {
        return championEndTime;
    }

    public void setChampionEndTime(Integer championEndTime) {
        this.championEndTime = championEndTime;
    }

    public Integer getBetChangeStartTime() {
        return betChangeStartTime;
    }

    public void setBetChangeStartTime(Integer betChangeStartTime) {
        this.betChangeStartTime = betChangeStartTime;
    }


    public List<TreasureWeeklyConfig> getTreasureWeeklyList() {
        return treasureWeeklyList;
    }

    public void setTreasureWeeklyList(List<TreasureWeeklyConfig> treasureWeeklyList) {
        this.treasureWeeklyList = treasureWeeklyList;
    }

    public List<TreasureRewardConfig> getTreasureRankList() {
        return treasureRankList;
    }

    public void setTreasureRankList(List<TreasureRewardConfig> treasureRankList) {
        this.treasureRankList = treasureRankList;
    }

    public Map<String, List<RamadanAwardConfig>> getRamadanConfig() {
        return ramadanConfig;
    }

    public void setRamadanConfig(Map<String, List<RamadanAwardConfig>> ramadanConfig) {
        this.ramadanConfig = ramadanConfig;
    }

    public List<PlantTreeConfig> getPlantTreeConfigList() {
        return plantTreeConfigList;
    }

    public void setPlantTreeConfigList(List<PlantTreeConfig> plantTreeConfigList) {
        this.plantTreeConfigList = plantTreeConfigList;
    }

    public Map<String, List<PlantRankConfig>> getPlantTreeRankConfig() {
        return plantTreeRankConfig;
    }

    public void setPlantTreeRankConfig(Map<String, List<PlantRankConfig>> plantTreeRankConfig) {
        this.plantTreeRankConfig = plantTreeRankConfig;
    }

    public List<FeedbackConfig> getFeedbackConfigList() {
        return feedbackConfigList;
    }

    public void setFeedbackConfigList(List<FeedbackConfig> feedbackConfigList) {
        this.feedbackConfigList = feedbackConfigList;
    }

    public Map<String, List<CommonAwardConfig>> getVoyagerCaptainConfig() {
        return voyagerCaptainConfig;
    }

    public void setVoyagerCaptainConfig(Map<String, List<CommonAwardConfig>> voyagerCaptainConfig) {
        this.voyagerCaptainConfig = voyagerCaptainConfig;
    }

    public Map<String, List<CommonAwardConfig>> getVoyagerCrewConfig() {
        return voyagerCrewConfig;
    }

    public void setVoyagerCrewConfig(Map<String, List<CommonAwardConfig>> voyagerCrewConfig) {
        this.voyagerCrewConfig = voyagerCrewConfig;
    }

    public List<CommonAwardConfig> getPandaConfigList() {
        return pandaConfigList;
    }

    public void setPandaConfigList(List<CommonAwardConfig> pandaConfigList) {
        this.pandaConfigList = pandaConfigList;
    }

    public List<BlindBoxConfig> getBlindBoxConfigList() {
        return blindBoxConfigList;
    }

    public void setBlindBoxConfigList(List<BlindBoxConfig> blindBoxConfigList) {
        this.blindBoxConfigList = blindBoxConfigList;
    }

    public List<AnniversarySixConfig> getAnniversarySixConfigList() {
        return anniversarySixConfigList;
    }

    public void setAnniversarySixConfigList(List<AnniversarySixConfig> anniversarySixConfigList) {
        this.anniversarySixConfigList = anniversarySixConfigList;
    }

    public List<TaskConfig> getMiniTaskList() {
        return miniTaskList;
    }

    public void setMiniTaskList(List<TaskConfig> miniTaskList) {
        this.miniTaskList = miniTaskList;
    }

    public Map<Integer, List<GiftWishConfig>> getGiftWishConfig() {
        return giftWishConfig;
    }

    public void setGiftWishConfig(Map<Integer, List<GiftWishConfig>> giftWishConfig) {
        this.giftWishConfig = giftWishConfig;
    }

    public List<GiftWishWeeklyConfig> getGiftWishWeeklyConfigList() {
        return giftWishWeeklyConfigList;
    }

    public void setGiftWishWeeklyConfigList(List<GiftWishWeeklyConfig> giftWishWeeklyConfigList) {
        this.giftWishWeeklyConfigList = giftWishWeeklyConfigList;
    }

    public List<CommonAwardConfig> getCupidConfigList() {
        return cupidConfigList;
    }

    public void setCupidConfigList(List<CommonAwardConfig> cupidConfigList) {
        this.cupidConfigList = cupidConfigList;
    }

    public List<CommonAwardConfig> getClownsConfigList() {
        return clownsConfigList;
    }

    public void setClownsConfigList(List<CommonAwardConfig> clownsConfigList) {
        this.clownsConfigList = clownsConfigList;
    }

    public List<CommonAwardConfig> getMagicConfigList() {
        return magicConfigList;
    }

    public void setMagicConfigList(List<CommonAwardConfig> magicConfigList) {
        this.magicConfigList = magicConfigList;
    }

    public List<CommonAwardConfig> getGuardEarthConfigList() {
        return guardEarthConfigList;
    }

    public void setGuardEarthConfigList(List<CommonAwardConfig> guardEarthConfigList) {
        this.guardEarthConfigList = guardEarthConfigList;
    }

    public List<CommonAwardConfig> getFalconConfigList() {
        return falconConfigList;
    }

    public void setFalconConfigList(List<CommonAwardConfig> falconConfigList) {
        this.falconConfigList = falconConfigList;
    }

    public List<CommonFlagConfig> getFlagConfigList() {
        return flagConfigList;
    }

    public void setFlagConfigList(List<CommonFlagConfig> flagConfigList) {
        this.flagConfigList = flagConfigList;
    }

    public List<QueenConfig> getQueenCharmConfigList() {
        return queenCharmConfigList;
    }

    public void setQueenCharmConfigList(List<QueenConfig> queenCharmConfigList) {
        this.queenCharmConfigList = queenCharmConfigList;
    }

    public List<QueenConfig> getQueenGenerousConfigList() {
        return queenGenerousConfigList;
    }

    public void setQueenGenerousConfigList(List<QueenConfig> queenGenerousConfigList) {
        this.queenGenerousConfigList = queenGenerousConfigList;
    }

    public List<QueenConfig> getQueenPassionConfigList() {
        return queenPassionConfigList;
    }

    public void setQueenPassionConfigList(List<QueenConfig> queenPassionConfigList) {
        this.queenPassionConfigList = queenPassionConfigList;
    }
}
