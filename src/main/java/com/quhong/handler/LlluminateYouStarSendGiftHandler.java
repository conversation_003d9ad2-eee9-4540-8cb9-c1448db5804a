package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RoomRocketLogEvent;
import com.quhong.constant.AchieveBadgeConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RocketRewardConfigDao;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.RocketRewardConfigData;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.msg.room.RocketLaunchPlatformMsg;
import com.quhong.msg.room.RocketProgressChangeMsg;
import com.quhong.msg.room.RoomRocketLaunchMsg;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.RoomRocketRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.TestRoomService;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.service.*;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/17
 */
@Component
public class LlluminateYouStarSendGiftHandler implements ActivityHandler {

    private static final Logger logger = LoggerFactory.getLogger(LlluminateYouStarSendGiftHandler.class);

    @Resource
    private GiftDao giftDao;
    @Resource
    private LlluminateYouStarService llluminateYouStarService;
    @Resource
    private BackUserCollectService backUserCollectService;
    @Resource
    private SuperQueen2025Service superQueen2025Service;
    @Resource
    private MiniTaskService miniTaskService;
    @Resource
    private InviteFissionCollectService inviteFissionCollectService;
    @Override
    public void process(SendGiftData data) {
        if (data == null || StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }
        GiftData giftData = giftDao.getGiftFromCache(data.getGid());
        if (giftData != null && giftData.getGtype() == 2) {
            // 金币礼物不加
            return;
        }
        if (!RoomUtils.isGameRoom(data.getRoomId())) {
            // roomId 可能为空
            superQueen2025Service.handleUserScore(data, null);
            if (RoomUtils.isVoiceRoom(data.getRoomId())) {
//                llluminateYouStarService.handleUserScore(data, null);
                backUserCollectService.handleUserScore(data, null, null);
                miniTaskService.handleGiftMqMsg(data);
                inviteFissionCollectService.handleUserScore(data, null, null, null);
            }
        }

    }


}
