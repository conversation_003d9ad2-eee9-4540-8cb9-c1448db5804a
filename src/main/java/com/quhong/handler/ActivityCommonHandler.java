package com.quhong.handler;

import com.quhong.config.ActivityAwardConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BadgeDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mq.MqSenderService;
import com.quhong.mq.ResourceDeliveryService;
import com.quhong.msg.obj.*;
import com.quhong.msg.room.ActivityBroadcastPushMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.msg.room.RoomRankChangeMsg;
import com.quhong.redis.ActivityOtherRedis;
import com.quhong.redis.FootballRedis;
import com.quhong.room.TestRoomService;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.service.ActivityUtilService;
import com.quhong.service.BadgeService;
import com.quhong.service.MarsMsgActivityService;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class ActivityCommonHandler {
    private static final Logger logger = LoggerFactory.getLogger(ActivityCommonHandler.class);
    protected static final String CONTENT_EN = "#name# rising to #rank# in #xxx# activity";
    protected static final String CONTENT_AR = "ارتفع #name# إلى #rank# في #xxx#";
    protected static final String CONTENT_EN_V2 = "rising to #rank# in #xxx# activity";
    protected static final String CONTENT_AR_V2 = "ارتفع إلى #rank# في #xxx#";
    protected static final String BLIND_BOX_NAME = "Blind Box";

    @Resource
    private ActivityOtherRedis activityOtherRedis;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private ResourceDeliveryService resourceDeliveryService;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private BadgeService badgeService;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    protected MarsMsgActivityService marsMsgActivityService;
    @Resource
    private TestRoomService testRoomService;
    @Resource
    private ActivityUtilService activityUtilService;
    @Resource
    private FootballRedis footballRedis;

    /**
     * 排名变化弹窗
     */

    private void doSendPopupMsg(SendGiftData req, OtherRankingActivityData activity, int rankType, MongoRoomData roomData,
                                RoomActorDetailData actorDetailData, Map<String, Integer> rankingMap, int afterRank, int roundNum, int calculateMethod) {
        RoomRankChangeMsg msg = new RoomRankChangeMsg();
        msg.setRank(afterRank);
        msg.setType(calculateMethod == 1 ? 2 : 1);
        // 新的排行榜
        Map<String, Integer> newRankingMap = activityOtherRedis.getOtherRankingMap(activity.get_id().toString(), rankType, 3, roundNum);
        if (ActivityConstant.ROOM_RANK == rankType) {
            List<RankUserInfoObject> rankUserOld = getRoomRankList(rankingMap);
            List<RankUserInfoObject> rankUserNew = getRoomRankList(newRankingMap);
            if (rankUserOld.size() != rankUserNew.size()) {
                rankUserOld = rankUserNew;
            }
            msg.setRank_user_old(rankUserOld);
            msg.setRank_user_new(rankUserNew);
            msg.setName(roomData.getName());
        } else {
            List<RankUserInfoObject> rankUserOld = getUserRankList(rankingMap);
            List<RankUserInfoObject> rankUserNew = getUserRankList(newRankingMap);
            if (rankUserOld.size() != rankUserNew.size()) {
                rankUserOld = rankUserNew;
            }
            msg.setRank_user_old(rankUserOld);
            msg.setRank_user_new(rankUserNew);
            msg.setName(actorDetailData.getName());
        }
        marsMsgActivityService.asyncSendMsg(getAllRoomId(req.getRoomId(), activity), null, msg, false);
    }

    private List<RankUserInfoObject> getRoomRankList(Map<String, Integer> rankingMap) {
        List<RankUserInfoObject> rankUserInfoObjects = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            MongoRoomData roomData = mongoRoomDao.getDataFromCache(entry.getKey());
            if (null == roomData) {
                logger.error("cannot find room roomId={}", entry.getKey());
                return rankUserInfoObjects;
            }
            RankUserInfoObject rankUserInfoObject = new RankUserInfoObject();
            rankUserInfoObject.setName(roomData.getName());
            ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomData.getRid()));
            rankUserInfoObject.setRid("" + actorData.getRid());
            rankUserInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
            rankUserInfoObject.setUid(roomData.getRid());
            rankUserInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
            rankUserInfoObject.setDiamond(String.valueOf(entry.getValue()));
            rankUserInfoObjects.add(rankUserInfoObject);
        }
        return rankUserInfoObjects;
    }

    private List<RankUserInfoObject> getUserRankList(Map<String, Integer> rankingMap) {
        List<RankUserInfoObject> rankUserInfoObjects = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            ActorData actorData = actorDao.getActorDataFromCache(entry.getKey());
            if (null == actorData) {
                logger.error("cannot find actor uid={}", entry.getKey());
                return rankUserInfoObjects;
            }
            RankUserInfoObject rankUserInfoObject = new RankUserInfoObject();
            rankUserInfoObject.setName(actorData.getName());
            rankUserInfoObject.setRid("" + actorData.getRid());
            rankUserInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
            rankUserInfoObject.setUid(actorData.getUid());
            rankUserInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            rankUserInfoObject.setDiamond(String.valueOf(entry.getValue()));
            rankUserInfoObjects.add(rankUserInfoObject);
        }
        return rankUserInfoObjects;
    }


    /**
     * 排名变化横幅、公屏消息
     */
    private void doSendMsg(SendGiftData req, OtherRankingActivityData activity, String aid, int afterRank, int rankType,
                           Map<String, Integer> rankingMap, int calculateMethod) {
        String roomId = ActivityConstant.ROOM_RANK == rankType ? aid : req.getRoomId();
        String uid = ActivityConstant.ROOM_RANK == rankType ? RoomUtils.getRoomHostId(aid) : aid;
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        if (null == roomData) {
            logger.error("cannot find room roomId={}", roomId);
            return;
        }
        RoomActorDetailData actorDetailData = roomActorCache.getData("", uid, false);
        ActivityBroadcastPushMsg msg = new ActivityBroadcastPushMsg();
        msg.setContent_en_v2(CONTENT_EN_V2.replace("#rank#", "Top" + afterRank).replace("#xxx#", activity.getAcNameEn()));
        msg.setContent_ar_v2(CONTENT_AR_V2.replace("#rank#", "Top" + afterRank).replace("#xxx#", activity.getAcNameAr()));
        if (ActivityConstant.ROOM_RANK == rankType) {
            msg.setContent_en(CONTENT_EN.replace("#name#", roomData.getName())
                    .replace("#rank#", "Top" + afterRank)
                    .replace("#xxx#", activity.getAcNameEn()));
            msg.setContent_ar(CONTENT_AR.replace("#name#", roomData.getName())
                    .replace("#rank#", "Top" + afterRank)
                    .replace("#xxx#", activity.getAcNameAr()));
        } else {
            String contentEn = CONTENT_EN.replace("#name#", actorDetailData.getName())
                    .replace("#rank#", "Top" + afterRank)
                    .replace("#xxx#", activity.getAcNameEn());
            String contentAr = CONTENT_AR.replace("#name#", actorDetailData.getName())
                    .replace("#rank#", "Top" + afterRank)
                    .replace("#xxx#", activity.getAcNameAr());
            msg.setFrom_uid(uid);
            msg.setContent_en(contentEn);
            msg.setContent_ar(contentAr);
            msg.setUser(generateUNameObject(actorDetailData));

            // 非房间榜还需要发送公屏消息
            List<HighlightTextObject> list = new ArrayList<>();
            HighlightTextObject object = new HighlightTextObject();
            object.setText(actorDetailData.getName());
            object.setHighlightColor("#00ffcc");
            list.add(object);
            object = new HighlightTextObject();
            object.setText("Top" + afterRank);
            object.setHighlightColor("#FFE200");
            list.add(object);
            if (!StringUtils.isEmpty(req.getRoomId())) {
                doSendRoomNotificationMsg(req, activity, actorDetailData, msg.getContent_en(), msg.getContent_ar(), list);
            }
        }
        RankInfoObject rankInfoObject = new RankInfoObject();
        rankInfoObject.setRoom_id(req.getRoomId());
        rankInfoObject.setName(roomData.getName());
        rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
        // rankInfoObject.setRid(ActivityConstant.ROOM_RANK == rankType ? context.getOwnerData().getRid() : actorDetailData.getRid());
        rankInfoObject.setRid(actorDetailData.getRid());
        rankInfoObject.setRidInfo(new RidInfoObject(actorDetailData.getRidData()));
        msg.setTitle_en(activity.getAcNameEn());
        msg.setTitle_ar(activity.getAcNameAr());
        msg.setRank(afterRank);
        msg.setType(ActivityConstant.ROOM_RANK == rankType ? 2 : 1);
        msg.setRoomInfo(rankInfoObject);
        marsMsgActivityService.asyncSendMsg(getAllRoomId(req.getRoomId(), activity), null, msg, false);
        doSendPopupMsg(req, activity, rankType, roomData, actorDetailData, rankingMap, afterRank, activity.getRoundNum(), calculateMethod);
    }

    public UNameObject generateUNameObject(RoomActorDetailData detailData) {
        UNameObject object = new UNameObject();
        object.setRid(detailData.getRid());
        object.setRidInfo(new RidInfoObject(detailData.getRidData()));
        object.setName(detailData.getName());
        object.setRole(detailData.getNewRole());
        object.setHead(detailData.getHead());
        // 设置vip
        object.setVip(detailData.getVipLevel());
        // 设置徽章
        object.setBadgeList(detailData.getBadgeList());
        object.setLevel(detailData.getLevel());
        // 设置气泡
        object.setBid(detailData.getBubbleId());
        object.setIdentify(detailData.getIdentify());
        return object;
    }

    /**
     * 发送房间公屏消息
     */
    private void doSendRoomNotificationMsg(SendGiftData req, OtherRankingActivityData activity, RoomActorDetailData actorDetailData,
                                           String text, String textAr, List<HighlightTextObject> list) {
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(actorDetailData.getAid());
        msg.setUser_name(actorDetailData.getName());
        msg.setUser_head(actorDetailData.getHead());
        msg.setText(text);
        msg.setText_ar(textAr);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
        msg.setWeb_url(activity.getAcUrl());
        msg.setWeb_type(0);
        marsMsgActivityService.asyncSendMsg(getAllRoomId(req.getRoomId(), activity), null, msg, false);
    }

    private String getAllRoomId(String roomId, OtherRankingActivityData activity) {
        if (activity.getAcNameEn().startsWith("test")) {
            return testRoomService.getTestRoom();
        } else {
            return "all_" + roomId;
        }
    }


    /**
     * 记录分数及处理排名变化消息
     */
    public void doOtherIncrScore(SendGiftData req, OtherRankingActivityData activity, String aid, String activityId, int rankType, int score,
                                 int pushStatus, int dailyRank, int roundNum, int calculateMethod) {

        if (pushStatus == 1) {

            int beforeRank = activityOtherRedis.getOtherRank(activityId, aid, rankType, roundNum);
            // 获取前三名数据
            Map<String, Integer> rankingMap = activityOtherRedis.getOtherRankingMap(activityId, rankType, 3, roundNum);
            // 增加分数
            activityOtherRedis.incrOtherRankingScore(activityId, aid, score, rankType, roundNum);
            if (DateHelper.getNowSeconds() - activity.getStartTime() > 6 * 60 * 60) {
                int afterRank = activityOtherRedis.getOtherRank(activityId, aid, rankType, roundNum);
                if (afterRank < 4 && afterRank < beforeRank) {
                    logger.info("rank change aid={} rankType={} beforeRank={} afterRank={}", aid, rankType, beforeRank, afterRank);
                    doSendMsg(req, activity, aid, afterRank, rankType, rankingMap, calculateMethod);
                }
            }
        } else {
            activityOtherRedis.incrOtherRankingScore(activityId, aid, score, rankType, roundNum);
        }

        if (dailyRank == 1) {
            String dailyNum = DateSupport.ARABIAN.yyyyMMdd();
            activityOtherRedis.incrOtherRankingDailyScore(activityId, aid, score, rankType, dailyNum, roundNum);
        }


    }

    /**
     * @param sendGiftData 礼物数据
     * @param activity     活动对象
     */
    public void doOtherRankingDataCollect(SendGiftData sendGiftData, OtherRankingActivityData activity) {
        if (activity.getRankingRewardList() == null) {
            return;
        }
        String activityId = activity.get_id().toString();
        int roundNum = activity.getRoundNum();
        String acNameEn = activity.getAcNameEn();


        for (OtherRankingActivityData.RankingConfig rankingConfig : activity.getRankingRewardList()) {

            int rankType = rankingConfig.getRankType();
            int calculateMethod = rankingConfig.getCalculateMethod();
            int pushStatus = rankingConfig.getPushStatus();
            int dailyRank = rankingConfig.getDailyRank();
            int rankingGender = rankingConfig.getRankingGender();

            int score = getOtherReachingScore(calculateMethod, sendGiftData.getNumber(), sendGiftData);
            int totalSendScore = score * sendGiftData.getAid_list().size();

            if (ActivityConstant.SEND_RANK == rankType) {
                boolean incFlag = true;

                if (rankingGender != 0) {
                    ActorData actorData = actorDao.getActorData(sendGiftData.getFrom_uid());
                    incFlag = actorData.getFb_gender() == rankingGender;
                }

                if (incFlag) {
                    doOtherIncrScore(sendGiftData, activity, sendGiftData.getFrom_uid(), activityId, ActivityConstant.SEND_RANK, totalSendScore, pushStatus, dailyRank, roundNum, calculateMethod);
                }

            } else if (ActivityConstant.RECEIVE_RANK == rankType) {
                if (acNameEn.contains(BLIND_BOX_NAME)) {
                    score = getOtherBlindBoxReceiveScore(calculateMethod, sendGiftData.getNumber(), sendGiftData);
                }
                for (String aid : sendGiftData.getAid_list()) {

                    boolean incFlag = true;
                    if (rankingGender != 0) {
                        ActorData actorData = actorDao.getActorData(aid);
                        incFlag = actorData.getFb_gender() == rankingGender;
                    }

                    if (incFlag) {
                        doOtherIncrScore(sendGiftData, activity, aid, activityId, ActivityConstant.RECEIVE_RANK, score, pushStatus, dailyRank, roundNum, calculateMethod);
                    }
                }

            } else if (ActivityConstant.ROOM_RANK == rankType) {
                String roomId = sendGiftData.getRoomId();
                if (!StringUtils.isEmpty(roomId)) {
                    doOtherIncrScore(sendGiftData, activity, roomId, activityId, ActivityConstant.ROOM_RANK, totalSendScore, pushStatus, dailyRank, roundNum, calculateMethod);
                }
            } else if (ActivityConstant.SEND_RECEIVE_RANK == rankType) {
                boolean incSendFlag = true;
                if (rankingGender != 0) {
                    ActorData actorData = actorDao.getActorData(sendGiftData.getFrom_uid());
                    incSendFlag = actorData.getFb_gender() == rankingGender;
                }

                if (incSendFlag) {
                    doOtherIncrScore(sendGiftData, activity, sendGiftData.getFrom_uid(), activityId, ActivityConstant.SEND_RECEIVE_RANK, totalSendScore, pushStatus, dailyRank, roundNum, calculateMethod);
                    doOtherIncrScore(sendGiftData, activity, sendGiftData.getFrom_uid(), activityId, ActivityConstant.SEND_RANK, totalSendScore, 0, dailyRank, roundNum, calculateMethod);
                }

                for (String aid : sendGiftData.getAid_list()) {
                    boolean incReceiveFlag = true;
                    if (rankingGender != 0) {
                        ActorData actorData = actorDao.getActorData(aid);
                        incReceiveFlag = actorData.getFb_gender() == rankingGender;
                    }
                    if (incReceiveFlag) {
                        doOtherIncrScore(sendGiftData, activity, aid, activityId, ActivityConstant.SEND_RECEIVE_RANK, score, pushStatus, dailyRank, roundNum, calculateMethod);
                        doOtherIncrScore(sendGiftData, activity, aid, activityId, ActivityConstant.RECEIVE_RANK, score, 0, dailyRank, roundNum, calculateMethod);
                    }
                }
            }
        }

    }


    /**
     * 获取数据或钻石
     */
    public int getOtherReachingScore(Integer calculateMethod, int number, SendGiftData sendGiftData) {
        if (ActivityConstant.GIFT_NUM == calculateMethod) {
            return number;
        } else if (ActivityConstant.GIFT_DIAMONDS == calculateMethod) {
            return number * sendGiftData.getPrice();
        }
        logger.error("calculateMethod not support. calculateMethod={}", calculateMethod);
        return 0;
    }

    public int getOtherBlindBoxReceiveScore(Integer calculateMethod, int number, SendGiftData sendGiftData) {
        if (ActivityConstant.GIFT_NUM == calculateMethod) {
            return number;
        } else if (ActivityConstant.GIFT_DIAMONDS == calculateMethod) {
            return number * sendGiftData.getReceivePrice();
        }
        logger.error("getOtherBlindBoxScore not support. calculateMethod={}", calculateMethod);
        return 0;
    }

    /**
     * 等级奖励计数及下发
     *
     * @param sendGiftData 礼物数据
     * @param activity     活动
     */
    public void doOtherLevelDataCollect(SendGiftData sendGiftData, OtherRankingActivityData activity) {
        if (activity.getReachingConfigList() == null) {
            return;
        }
        String activityId = activity.get_id().toString();
        int roundNum = activity.getRoundNum();
        String activityName = activity.getAcNameEn();
        for (OtherRankingActivityData.ReachingConfig reachingConfig : activity.getReachingConfigList()) {
            int rankType = reachingConfig.getReachingRewardType();
            int calculateMethod = reachingConfig.getCalculateMethod();
            int reachingGender = reachingConfig.getReachingGender();
            int gotScore = getOtherReachingScore(calculateMethod, sendGiftData.getNumber(), sendGiftData);

            if (ActivityConstant.RECEIVE_RANK == rankType) {
                for (String aid : sendGiftData.getAid_list()) {
                    // int oldScore = activityOtherRedis.getOtherReachingScore(activityName, aid, rankType);
                    boolean incFlag = true;

                    if (reachingGender != 0) {
                        ActorData actorData = actorDao.getActorData(aid);
                        incFlag = actorData.getFb_gender() == reachingGender;
                    }

                    if (incFlag) {
                        int reachingScore = activityOtherRedis.incrOtherReachingScore(activityId, aid, gotScore, rankType, roundNum);
                        doOtherReachingAward(reachingConfig, aid, reachingScore, gotScore, activityName);
                    }
                }
            } else if (ActivityConstant.SEND_RANK == rankType) {
                boolean incFlag = true;
                String fromUid = sendGiftData.getFrom_uid();

                if (reachingGender != 0) {
                    ActorData actorData = actorDao.getActorData(fromUid);
                    incFlag = actorData.getFb_gender() == reachingGender;
                }

                if (incFlag) {
                    int totalScore = gotScore * sendGiftData.getAid_list().size();
                    int reachingScore = activityOtherRedis.incrOtherReachingScore(activityId, fromUid, totalScore, rankType, roundNum);
                    doOtherReachingAward(reachingConfig, fromUid, reachingScore, totalScore, activityName);
                }
            } else if (ActivityConstant.ROOM_RANK == rankType) {
                String roomId = sendGiftData.getRoomId();
                if (!StringUtils.isEmpty(roomId)) {
                    int totalScore = gotScore * sendGiftData.getAid_list().size();
                    int reachingScore = activityOtherRedis.incrOtherReachingScore(activityId, roomId, totalScore, rankType, roundNum);
                    String hostId = RoomUtils.getRoomHostId(roomId);
                    doOtherReachingAward(reachingConfig, hostId, reachingScore, totalScore, activityName);
                }
            }
        }

    }


    /**
     * 实时下发勋章，升级后需要删除旧版本的勋章
     */
    public void doOtherReachingAward(OtherRankingActivityData.ReachingConfig reachingConfig, String uid, int reachingScore, int gotScore, String activityName) {
        // 钻石数对应等级列表
        List<OtherRankingActivityData.ReachingReward> reachingRewardList = reachingConfig.getReachingRewardList();
        List<Integer> giftNumList = CollectionUtil.getPropertyList(reachingRewardList, OtherRankingActivityData.ReachingReward::getGiftNum, 0);
        // 发送礼物前的等级
        int levelNow = badgeService.getLevelFromSoredList(giftNumList, reachingScore - gotScore);
        if (levelNow >= giftNumList.size()) {
            return;
        }
        // 下一等级所需钻石
        int levelUpNum = giftNumList.get(levelNow);
        if (levelUpNum > 0 && reachingScore >= levelUpNum) {
            // 处理跳级，levelNew的下标下标从1开始
            int levelNew = badgeService.getLevelFromSoredList(giftNumList, reachingScore);
            logger.info("reaching level up, uid={} levelNow={} levelNew={} num={} oldNum={} levelUpNum={}",
                    uid, levelNow, levelNew, reachingScore, reachingScore - gotScore, levelUpNum);
            Set<Integer> badgeIdToDel = new HashSet<>();
            for (int i = 0; i < levelNew - 1; i++) {
                for (OtherRankingActivityData.RewardConfigDetail reward : reachingRewardList.get(i).getRewardConfigDetailList()) {
                    if (ResourceConstant.BADGE.equals(reward.getRewardType())) {
                        badgeIdToDel.add(reward.getSourceId());
                    }
                }
            }
            // 如果发生跳级时，需要补发跳过部分的非勋章奖励
            for (int i = levelNow; i < levelNew - 1; i++) {
                logger.info("reaching skip level reward uid={} level={}", uid, i + 1);
                for (OtherRankingActivityData.RewardConfigDetail reward : reachingRewardList.get(i).getRewardConfigDetailList()) {
                    if (!ResourceConstant.BADGE.equals(reward.getRewardType())) {
                        doSendOtherReachingReward(uid, reward, Collections.emptySet(), reachingScore, activityName);
                    }
                }
            }
            for (OtherRankingActivityData.RewardConfigDetail reward : reachingRewardList.get(levelNew - 1).getRewardConfigDetailList()) {
                doSendOtherReachingReward(uid, reward, badgeIdToDel, reachingScore, activityName);
            }
        }
    }


    /**
     * 配置文件类活动-奖励下发
     *
     * @param sendGiftData    礼物数据
     * @param activityName    活动名称
     * @param rankType        类型
     * @param calculateMethod 计算等级勋章方式 1:数量  2:钻石
     */
    public void doOtherReaching(SendGiftData sendGiftData, String activityName, String uid,
                                List<ActivityAwardConfig.ReachingReward> reachingRewardList,
                                int rankType, int calculateMethod, int rewardGender) {
        int gotScore = getOtherReachingScore(calculateMethod, sendGiftData.getNumber(), sendGiftData);
        int roundNum = 0;

        if (ActivityConstant.RECEIVE_RANK == rankType) {
            for (String aid : sendGiftData.getAid_list()) {
                // int oldScore = activityOtherRedis.getOtherReachingScore(activityName, aid, rankType);
                boolean incFlag = true;

                if (rewardGender != 0) {
                    ActorData actorData = actorDao.getActorData(aid);
                    incFlag = actorData.getFb_gender() == rewardGender;
                }

                if (incFlag) {
                    int reachingScore = activityOtherRedis.incrOtherReachingScore(activityName, aid, gotScore, rankType, roundNum);
                    doOtherReachingBadge(reachingRewardList, aid, reachingScore, gotScore, activityName);
                }

            }
        } else if (ActivityConstant.SEND_RANK == rankType) {
            boolean incFlag = true;
            if (rewardGender != 0) {
                ActorData actorData = actorDao.getActorData(uid);
                incFlag = actorData.getFb_gender() == rewardGender;
            }

            if (incFlag) {
                int totalScore = gotScore * sendGiftData.getAid_list().size();
                int reachingScore = activityOtherRedis.incrOtherReachingScore(activityName, sendGiftData.getFrom_uid(), totalScore, rankType, roundNum);
                doOtherReachingBadge(reachingRewardList, uid, reachingScore, totalScore, activityName);
            }
        }
    }


    /**
     * 实时下发勋章，升级后需要删除旧版本的勋章
     */
    public void doOtherReachingBadge(List<ActivityAwardConfig.ReachingReward> reachingRewardList, String uid, int reachingScore, int gotScore, String activityName) {
        // 钻石数对应等级列表
        List<Integer> giftNumList = CollectionUtil.getPropertyList(reachingRewardList, ActivityAwardConfig.ReachingReward::getGiftNum, 0);
        // 发送礼物前的等级
        int levelNow = badgeService.getLevelFromSoredList(giftNumList, reachingScore - gotScore);
        if (levelNow >= giftNumList.size()) {
            return;
        }
        // 下一等级所需钻石
        int levelUpNum = giftNumList.get(levelNow);
        if (levelUpNum > 0 && reachingScore >= levelUpNum) {
            // 处理跳级，levelNew的下标下标从1开始
            int levelNew = badgeService.getLevelFromSoredList(giftNumList, reachingScore);
            logger.info("reaching level up, uid={} levelNow={} levelNew={} num={} oldNum={} levelUpNum={}",
                    uid, levelNow, levelNew, reachingScore, reachingScore - gotScore, levelUpNum);
            Set<Integer> badgeIdToDel = new HashSet<>();
            for (int i = 0; i < levelNew - 1; i++) {
                for (ActivityAwardConfig.RewardConfig reward : reachingRewardList.get(i).getRewardConfigList()) {
                    if (ResourceConstant.BADGE.equals(reward.getRewardType())) {
                        badgeIdToDel.add(reward.getSourceId());
                    }
                }
            }
            // 如果发生跳级时，需要补发跳过部分的非勋章奖励
            for (int i = levelNow; i < levelNew - 1; i++) {
                logger.info("reaching skip level reward uid={} level={}", uid, i + 1);
                for (ActivityAwardConfig.RewardConfig reward : reachingRewardList.get(i).getRewardConfigList()) {
                    if (!ResourceConstant.BADGE.equals(reward.getRewardType())) {
                        doSendReachingReward(uid, reward, Collections.emptySet(), reachingScore, activityName);
                    }
                }
            }
            for (ActivityAwardConfig.RewardConfig reward : reachingRewardList.get(levelNew - 1).getRewardConfigList()) {
                doSendReachingReward(uid, reward, badgeIdToDel, reachingScore, activityName);
            }
        }
    }

    /**
     * 异步打钻
     */
    public void doAsyncChargeDiamonds(String uid, int changed, String activityName) {
        logger.info("reaching reward uid={} change={}", uid, changed);
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(902);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(activityName);
        moneyDetailReq.setDesc(activityName);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }


    public void doSendReachingReward(String uid, ActivityAwardConfig.RewardConfig reward, Set<Integer> badgeIdToDel, int reachingScore, String activityName) {
        logger.info("reaching reward uid={} sType={} sourceId={} day={} num={}",
                uid, reward.getRewardType(), reward.getSourceId(), reward.getRewardTime(), reward.getRewardNum());
        if (null == reward.getRewardType() || ResourceConstant.OTHER.equals(reward.getRewardType())) {
            return;
        }
        if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
            doAsyncChargeDiamonds(uid, reward.getRewardNum(), activityName);
        } else if (ResourceConstant.BADGE.equals(reward.getRewardType())) {
            // 这里的都是等级勋章，如果是勋章，则删除旧版本的勋章
            badgeDao.removeBadges(uid, badgeIdToDel);
            badgeService.giveBadgeToUser(uid, reward.getSourceId(), reachingScore);
        } else {
            activityUtilService.handleResources(new GiftsMqVo(
                    uid,
                    reward.getRewardType(),
                    reward.getSourceId(),
                    reward.getRewardTime(),
                    reward.getRewardNum())
            );
        }
    }

    public void doSendOtherReachingReward(String uid, OtherRankingActivityData.RewardConfigDetail reward, Set<Integer> badgeIdToDel, int reachingScore, String activityName) {
        logger.info("reaching reward uid={} sType={} sourceId={} day={} num={}",
                uid, reward.getRewardType(), reward.getSourceId(), reward.getRewardTime(), reward.getRewardNum());
        if (null == reward.getRewardType() || ResourceConstant.OTHER.equals(reward.getRewardType())) {
            return;
        }
        if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
            doAsyncChargeDiamonds(uid, reward.getRewardNum(), activityName);
        } else if (ResourceConstant.BADGE.equals(reward.getRewardType())) {
            // 这里的都是等级勋章，如果是勋章，则删除旧版本的勋章
            badgeDao.removeBadges(uid, badgeIdToDel);
            badgeService.giveBadgeToUser(uid, reward.getSourceId(), reachingScore);
        } else {
            activityUtilService.handleResources(new GiftsMqVo(
                    uid,
                    reward.getRewardType(),
                    reward.getSourceId(),
                    reward.getRewardTime(),
                    reward.getRewardNum())
            );
        }
    }

}
