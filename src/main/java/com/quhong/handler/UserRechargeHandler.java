package com.quhong.handler;

import com.quhong.data.RechargeInfo;
import com.quhong.service.LuckyNewYearService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/01
 */
@Component
public class UserRechargeHandler implements ActivityRechargeHandler{

    private static final Logger logger = LoggerFactory.getLogger(UserRechargeHandler.class);

    @Resource
    private LuckyNewYearService luckyNewYearService;


    @Override
    public void process(RechargeInfo rechargeInfo) {

        logger.info("UserRechargeHandler process rechargeInfo{}", rechargeInfo);
        luckyNewYearService.handleUserRecharge(rechargeInfo);
    }

}
