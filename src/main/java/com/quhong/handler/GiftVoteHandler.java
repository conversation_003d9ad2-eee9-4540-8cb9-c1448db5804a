package com.quhong.handler;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.data.SendGiftData;
import com.quhong.mysql.dao.VoteDao;
import com.quhong.mysql.dao.VoteOptionDao;
import com.quhong.mysql.data.VoteData;
import com.quhong.mysql.data.VoteOptionData;
import com.quhong.redis.VoteRedis;
import com.quhong.service.VoteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
@Component
public class GiftVoteHandler implements ActivityHandler{

    private static final Logger logger = LoggerFactory.getLogger(GiftVoteHandler.class);

    private static final String GIFT_LOCK_KEY = "girt_vote_";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private VoteRedis voteRedis;
    @Resource
    private VoteOptionDao voteOptionDao;
    @Resource
    private VoteService voteService;
    @Resource
    private VoteDao voteDao;

    @Override
    public void process(SendGiftData data) {
//        logger.info("start handle vote gift. data={}", JSONObject.toJSONString(data));
        if (data == null || StringUtils.isEmpty(data.getRoomId()) || StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }
        synchronized (stringPool.intern(GIFT_LOCK_KEY + data.getRoomId())) {
            try {
                // 判断房间是否存在投票
                int voteId = voteRedis.getRoomVote(data.getRoomId());
                if (voteId == 0) {
                    return;
                }
                // 判断是否是礼物投票，且当前送的礼物是否是礼物投票指定的礼物
                int voteGiftId = voteRedis.getVoteGiftId(voteId);
                if (voteGiftId == 0 || voteGiftId != data.getGid()) {
                    return;
                }
                List<VoteOptionData> list = voteOptionDao.findList(voteId);
                if (CollectionUtils.isEmpty(list)) {
                    logger.error("can not find vote option data, voteId={}", voteId);
                    return;
                }
                boolean flag = false;
                VoteData voteData = null;
                for (VoteOptionData voteOptionData : list) {
                    if (data.getAid_list().contains(voteOptionData.getOptionContent())) {
                        if (voteData == null) {
                            voteData = voteDao.findData(voteOptionData.getVoteId());
                        }
                        // 投票目标增加票数
                        voteOptionData.setVotesNum(voteOptionData.getVotesNum() + data.getNumber());
                        voteOptionDao.update(voteOptionData);
                        // 记录投票用户的uid
                        voteRedis.saveVoteUserId(voteId, data.getFrom_uid());
                        // 数数埋点
                        voteService.doReportVoteLogEvent(data.getFrom_uid(), data.getNumber(), voteData, Collections.singletonList(voteOptionData));
                        flag = true;
                    }
                }
                if (flag) {
                    voteService.sendRoomVotePushMsg(VoteService.MSG_STATUS_RUNNING, voteDao.findData(voteId), list, null);
                }
            } catch (Exception e) {
                logger.error("update gift vote data error. roomId={} uid={} number={} aid={} {}", data.getRoomId(), data.getFrom_uid(), data.getNumber(), data.getPrice(), e.getMessage(), e);
            }
        }
    }
}
