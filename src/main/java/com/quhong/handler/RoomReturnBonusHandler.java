package com.quhong.handler;

import com.quhong.data.SendGiftData;
import com.quhong.mongo.dao.SysConfigDao;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.service.AcRoomRocketV2Service;
import com.quhong.service.RoomReturnBonusService;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/17
 */
@Component
public class RoomReturnBonusHandler implements ActivityHandler {

    private static final Logger logger = LoggerFactory.getLogger(RoomReturnBonusHandler.class);

    @Resource
    private GiftDao giftDao;
    @Resource
    private RoomReturnBonusService roomReturnBonusService;
    @Resource
    private AcRoomRocketV2Service acRoomRocketV2Service ;
    @Resource
    private SysConfigDao sysConfigDao ;

    @Override
    public void process(SendGiftData data) {
        if (data == null || !RoomUtils.isVoiceRoom(data.getRoomId()) || StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }

        GiftData giftData = giftDao.getGiftFromCache(data.getGid());
        if (giftData != null && giftData.getGtype() == 2) {
            // 金币礼物不加
            return;
        }
        roomReturnBonusService.handleUserScore(data,null);
        acRoomRocketV2Service.handleUserScore(data, null);
    }


}
