package com.quhong.handler;

import com.quhong.config.ActivityAwardConfig;
import com.quhong.config.HonorNationalConfig;
import com.quhong.constant.ActivityConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.room.TestRoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class HonorNationalHandler extends ActivityCommonHandler implements ActivityHandler{

    private static final Logger logger = LoggerFactory.getLogger(HonorNationalHandler.class);

    @Resource
    private HonorNationalConfig honorNationalConfig;

    @Resource
    private TestRoomService testRoomService;

    @Override
    public void process(SendGiftData giftData) {
        List<Integer> giftIdList = honorNationalConfig.getGiftIdList();
        int giftId = giftData.getGid();
        int startTime = honorNationalConfig.getStartTime();
        int endTime = honorNationalConfig.getEndTime();
        int curTime = DateHelper.getNowSeconds();

        String activityName = honorNationalConfig.getActivityName();

        // 灰度时只统计灰度房间的礼物
        if (activityName.startsWith("test") && !StringUtils.isEmpty(giftData.getRoomId()) && !testRoomService.isTestRoom(giftData.getRoomId())) {
            return;
        }

        if(giftIdList.contains(giftId) && (curTime >= startTime && curTime < endTime)){

            Map<String, List<ActivityAwardConfig.ReachingReward>> sendingRewardMap =  honorNationalConfig.getSendingRewardMap();
            List<ActivityAwardConfig.ReachingReward> sendingRewardList = sendingRewardMap.get(String.valueOf(giftId));

            logger.info("ActivityName: {}, SendingRewardList:{}", honorNationalConfig.getActivityName(), sendingRewardList);
            String activityNameGiftId = honorNationalConfig.getActivityName() + giftId;
            doOtherReaching(giftData, activityNameGiftId,
                    giftData.getFrom_uid(),  sendingRewardList,
                    ActivityConstant.SEND_RANK, honorNationalConfig.getCalculateMethod(),
                    honorNationalConfig.getSendingRewardGender());

            // doOtherReaching(giftData, queenAwardConfig.getActivityName(),
            //         giftData.getFrom_uid(),  queenAwardConfig.getReceiveRewardList(),
            //         ActivityConstant.RECEIVE_RANK, queenAwardConfig.getCalculateMethod(),
            //         queenAwardConfig.getReceiveRewardGender());
        }
    }
}
