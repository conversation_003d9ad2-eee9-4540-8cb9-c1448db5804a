package com.quhong.handler;

import com.quhong.constant.AchieveBadgeConstant;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.MoneyTypeDTO;
import com.quhong.enums.ApiResult;
import com.quhong.feign.DataCenterService;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.service.BadgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class GiftAchieveHandler implements ActivityHandler{

    private static final Logger logger = LoggerFactory.getLogger(GiftAchieveHandler.class);

    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private BadgeService badgeService;
    @Resource
    private DataCenterService dataCenterService;

    @Override
    public void process(SendGiftData giftData) {

        // String fromUid = giftData.getFrom_uid();
        // int totalPrice = giftData.getPrice() * giftData.getNumber() * giftData.getAid_list().size();
        //
        // long giftCount = actorConfigDao.getLongUserConfig(fromUid, ActorConfigDao.GIFT_COUNT, -1L);
        //
        // if(giftCount > -1){
        //     giftCount += totalPrice;
        //     logger.info("dealWithGiftBadge fromUid: {}  giftCount:{}", fromUid, giftCount);
        //     actorConfigDao.updateUserConfig(fromUid, ActorConfigDao.GIFT_COUNT, giftCount);
        //     badgeService.doAchieveBadge(fromUid, AchieveBadgeConstant.TYPE_SEND_GIFT, giftCount, totalPrice);
        // }else {
        //
        //     MoneyTypeDTO dto = new MoneyTypeDTO();
        //     dto.setUid(fromUid);
        //     dto.setMoneyType(MoneyTypeConstant.SEND_GIFT);
        //     ApiResult<Long> result = dataCenterService.esMoneyTypeTotal(dto);
        //     if(result.isError()){
        //         logger.error("esMoneyTypeTotal Moment error uid:{}, code: {} msg:{}", fromUid, result.getCode().getCode(), result.getCode().getMsg());
        //     }else {
        //         long totalGiftBean = result.getData();
        //         logger.info("esMoneyTypeTotal totalGiftBean: {}, giftCount:{}", totalGiftBean, giftCount);
        //         if(totalGiftBean > -1){
        //             badgeService.supplyAchieveBadge(fromUid, AchieveBadgeConstant.TYPE_SEND_GIFT, totalGiftBean);
        //             actorConfigDao.updateUserConfig(fromUid, ActorConfigDao.GIFT_COUNT, totalGiftBean);
        //         }
        //     }
        // }

    }
}
