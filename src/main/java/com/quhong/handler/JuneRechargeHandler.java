package com.quhong.handler;

import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RechargeInfo;
import com.quhong.service.BackUserCollectService;
import com.quhong.service.InviteFissionCollectService;
import com.quhong.service.LuckyNewYearService;
import com.quhong.service.RechargeCanivalService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/01
 */
@Component
public class JuneRechargeHandler implements ActivityRechargeHandler {

    private static final Logger logger = LoggerFactory.getLogger(JuneRechargeHandler.class);

    @Resource
    private RechargeCanivalService rechargeCanivalService;
    @Resource
    private BackUserCollectService backUserCollectService;
    @Resource
    private InviteFissionCollectService inviteFissionCollectService;

    @Override
    public void process(RechargeInfo rechargeInfo) {
        logger.info("JuneRechargeHandler process rechargeInfo{}", rechargeInfo);
        if (rechargeInfo == null || !StringUtils.hasLength(rechargeInfo.getUid()) || rechargeInfo.getRechargeMoney() == null) {
            logger.info("JuneRechargeHandler process rechargeInfo is invalid");
            return;
        }
        int rechargeType = rechargeInfo.getRechargeType();
        if (rechargeType != 1) {
            logger.info("not honor recharge return rechargeInfo:{}", rechargeInfo);
            return;
        }
        rechargeCanivalService.handleUserRecharge(rechargeInfo);
        if (StringUtils.hasLength(rechargeInfo.getProductId())) {
            backUserCollectService.handleUserScore(null, null, rechargeInfo);
        }
        inviteFissionCollectService.handleUserScore(null, null, rechargeInfo, null);
    }

}
