/*
package com.quhong.handler;

import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.mongo.data.RankingActivity;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.service.RankActivityService;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Set;

@Component
public class RankingActivityAcHandler implements ActivityHandler {

    private static final Logger logger = LoggerFactory.getLogger(RankingActivityAcHandler.class);

    @Resource
    private RankActivityService rankActivityService;
    @Resource
    private WhiteTestDao whiteTestDao;

    @Override
    public void process(SendGiftData giftData) {

        if (giftData.getGift_cost_type() == 2) {
            // 金币礼物不统计
            return;
        }

        if (RoomUtils.isGameRoom(giftData.getRoomId())) {
            // 游戏房礼物不统计
            return;
        }
        // 当前进行的活动
        for (RankingActivity activity : rankActivityService.getRankingActivities()) {

            // 总礼物配置，默认情况下礼物排行榜都使用此列表的礼物进行钻石数量统计，活动的礼物都在此列表内
            String activityId = activity.get_id().toString();
            int curTime = DateHelper.getNowSeconds();
            int giftId = giftData.getGid();
            int startTime = activity.getStartTime();
            int endTime = activity.getEndTime();
            Set<Integer> giftIds = CollectionUtil.listToPropertySet(activity.getActivityGiftList()
                    , RankingActivity.ActivityGift::getGiftId);
            String activityName = activity.getAcNameEn();

            if (ServerConfig.isProduct()) {
                // 灰度时只统计灰度房间的礼物
                if (activity.getAcNameEn().startsWith("test") && !StringUtils.isEmpty(giftData.getRoomId()) && !whiteTestDao.isMemberByType(giftData.getRoomId(), WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                    continue;
                }

                if (activity.getAcNameEn().startsWith("test") && StringUtils.isEmpty(giftData.getRoomId())) {
                    continue;
                }
            }
            if (giftIds.contains(giftId) && (curTime >= startTime && curTime < endTime)) {
                logger.info("Ranking ActivityName: {}, from_uid:{}, room_id: {}", activityName, giftData.getFrom_uid(), giftData.getRoomId());

            }


        }

    }
}
*/
