package com.quhong.handler;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.mysql.dao.EventSendGiftRecordDao;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.RoomEventDao;
import com.quhong.mysql.data.EventSendGiftRecordData;
import com.quhong.mysql.data.GiftData;
import com.quhong.mysql.data.RoomEventData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@Component
public class RoomEventSendGiftHandler implements ActivityHandler{

    private static final Logger logger = LoggerFactory.getLogger(RoomEventSendGiftHandler.class);

    private static final String EVENT_LOCK_KEY = "event_send_gift_";
    private static final Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private RoomEventDao roomEventDao;
    @Resource
    private EventSendGiftRecordDao sendGiftRecordDao;
    @Resource
    private GiftDao giftDao;

    @Override
    public void process(SendGiftData data) {
        if (data == null || StringUtils.isEmpty(data.getRoomId()) || StringUtils.isEmpty(data.getFrom_uid())) {
            return;
        }
        GiftData giftData = giftDao.getGiftFromCache(data.getGid());
        if (giftData != null && giftData.getGtype() == 2) {
            // 金币礼物不计算活动贡献
            return;
        }

        int nowTime = DateHelper.getNowSeconds();
        long totalPrice = ((long) data.getPrice() * data.getNumber() * data.getAid_list().size());
        synchronized (stringPool.intern(EVENT_LOCK_KEY + data.getRoomId())) {
            try {
                RoomEventData ongoingRoomEvent = roomEventDao.getOngoingRoomEvent(data.getRoomId(), nowTime);
                if (ongoingRoomEvent == null) {
                    return;
                }
                EventSendGiftRecordData sendGiftRecordData = sendGiftRecordDao.selectOne(data.getRoomId(), ongoingRoomEvent.getId(), data.getFrom_uid());
                if (sendGiftRecordData == null) {
                    sendGiftRecordData = new EventSendGiftRecordData();
                    sendGiftRecordData.setEventId(ongoingRoomEvent.getId());
                    sendGiftRecordData.setRoomId(data.getRoomId());
                    sendGiftRecordData.setUid(data.getFrom_uid());
                    sendGiftRecordData.setGiftTotalNum(data.getNumber());
                    sendGiftRecordData.setGiftTotalPrice(totalPrice);
                    sendGiftRecordDao.insert(sendGiftRecordData);
                } else {
                    sendGiftRecordData.setGiftTotalNum(sendGiftRecordData.getGiftTotalNum() + data.getNumber());
                    sendGiftRecordData.setGiftTotalPrice(sendGiftRecordData.getGiftTotalPrice() + totalPrice);
                    sendGiftRecordDao.update(sendGiftRecordData);
                }
            } catch (Exception e) {
                logger.error("update room event send gift record data error. roomId={} uid={} number={} price={} {}", data.getRoomId(), data.getFrom_uid(), data.getNumber(), data.getPrice(), e.getMessage(), e);
            }
        }
    }
}
