package com.quhong.handler;

import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.SendGiftData;
import com.quhong.enums.ActivityRewardTypeEnum;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.NationalDayV2Dao;
import com.quhong.mongo.data.NationalDayV2Data;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.NationalDayV2Redis;
import com.quhong.room.TestRoomService;
import com.quhong.service.NationalDayV2Service;
import com.quhong.service.NationalDayV3Service;
import com.quhong.service.ResourceDistributionService;
import com.quhong.service.ResourceKeyHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Component
public class NationalDayV2Handler implements ActivityHandler {

    private static final Logger logger = LoggerFactory.getLogger(NationalDayV2Handler.class);


    @Resource
    private TestRoomService testRoomService;

    @Resource
    private NationalDayV2Service nationalDayV2Service;
    @Resource
    private NationalDayV2Redis nationalDayV2Redis;
    @Resource
    private ResourceDistributionService distributionService;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private NationalDayV3Service nationalDayV3Service;

    /**
     * @param sendGiftData 礼物数据
     * @param activity     活动对象
     */
    public void doNationalDayV2DataCollect(SendGiftData sendGiftData, NationalDayV2Data activity) {
        String activityId = activity.get_id().toString();
        String uid = sendGiftData.getFrom_uid();
        int score = sendGiftData.getAid_list().size();
        nationalDayV2Redis.incrNationalDayV2RankingScore(activityId, uid, score);

        int rankingScore = nationalDayV2Redis.getNationalDayV2RankingScore(activityId, uid);
        boolean isAward = nationalDayV2Redis.isAwardUser(activityId, uid);
        if (rankingScore >= 10 && !isAward) {
            if (NationalDayV2Dao.NATIONAL_DAY_TEMPLATE_2025 == activity.getTemplateType()) {
                String eventTitle = nationalDayV3Service.getEventTitle(80);
                resourceKeyHandlerService.sendResourceData(uid, activity.getAdvanceResKey(),
                        eventTitle,
                        eventTitle,
                        eventTitle,
                        activity.getAcUrl(), "");
            } else {
                int badgeId = activity.getRewardConfig().getSourceId();
                distributionService.sendRewardResource(uid, badgeId, ActivityRewardTypeEnum.getEnumByName("badge"), 0, 0, NationalDayV3Service.NATIONAL_DAY_TITLE,
                        NationalDayV3Service.NATIONAL_DAY_TITLE, 0);
            }
            nationalDayV2Redis.setAwardV2User(activityId, uid);
        }


    }

    @Override
    public void process(SendGiftData giftData) {

        // 当前进行的活动
        for (NationalDayV2Data activity : nationalDayV2Service.getNationalDayV2Activities()) {

            // 总礼物配置，默认情况下礼物排行榜都使用此列表的礼物进行钻石数量统计，活动的礼物都在此列表内
            int curTime = DateHelper.getNowSeconds();
            int giftId = giftData.getGid();
            int sendNumber = giftData.getNumber();
            int startTime = activity.getStartTime();
            int endTime = activity.getEndTime();
            int giftConfigId = activity.getGiftId();
            String uid = giftData.getFrom_uid();

            int advanceNum = activity.getAdvanceConfig().getAdvanceNum();
            String activityName = activity.getAcNameEn();

            // 灰度时只统计测试用户的礼物
            if ((activity.getAcNameEn().startsWith("test") || activity.getIsGrayTest() == 1) &&
                    !StringUtils.isEmpty(uid)) {
                boolean isWhiteTestUser = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
                if (!isWhiteTestUser) {
                    continue;
                }
            }

//            if (activity.getAcNameEn().contains("test") && !StringUtils.isEmpty(giftData.getRoomId()) && !testRoomService.isTestRoom(giftData.getRoomId())) {
//                continue;
//            }

            if (giftConfigId == giftId && sendNumber >= advanceNum && (curTime >= startTime && curTime < endTime)) {

                logger.info("ActivityName: {}, from_uid:{}, room_id: {}, sendNumber={}", activityName,
                        giftData.getFrom_uid(), giftData.getRoomId(), sendNumber);
                doNationalDayV2DataCollect(giftData, activity);
            }
        }
    }
}
