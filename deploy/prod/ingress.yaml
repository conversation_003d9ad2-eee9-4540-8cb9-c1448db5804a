apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: devops
  name: ustar-java-operation
  annotations:
    alb.ingress.kubernetes.io/group.name: ustar
    alb.ingress.kubernetes.io/load-balancer-name: apiv2
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=600
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}, {"HTTP": 80}, {"HTTP": 8080}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-south-1:239620982073:certificate/56a9e7be-7b61-4b49-8a3c-35a3fe4f706f
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /operation/system/health_check
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '5'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '2'  #健康检查超时时间设置为2s
spec:
  rules:
    - host: apiv2.qmovies.tv
      http:
        paths:
          - path: /operation/
            pathType: Prefix
            backend:
              service:
                name: ustar-java-operation
                port:
                  number: 8080
