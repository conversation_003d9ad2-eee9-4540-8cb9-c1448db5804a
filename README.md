# UStar Activity 服务

## 项目简介
UStar Activity 是一个基于Spring Boot的活动管理服务模块，提供活动相关的业务功能支持。

## 技术栈
- Spring Boot 2.2.13.RELEASE
- Spring Boot Web
- Spring Boot Actuator
- Spring Data MongoDB
- Thymeleaf 3.0.11.RELEASE
- AWS S3 SDK
- Aliyun OSS SDK
- Google Guava

## 项目依赖
- ustar_feign
- ustar_k8s
- ustar_api
- ustar_analysis

## 环境要求
- JDK 1.8
- Maven 3.x

## 项目结构
```
ustar_java_activity/
├── src/
│   └── main/
│       ├── java/        # Java源代码
│       └── resources/   # 配置文件
├── deploy/             # 部署相关文件
├── pom.xml            # Maven配置文件
└── README.md          # 项目说明文档
```

## 构建说明
项目使用Maven进行构建，最终构建产物为`app.jar`。

## 部署说明
1. 确保已安装JDK 1.8和Maven 3.x
2. 执行Maven构建命令：`mvn clean package`
3. 构建完成后，可在target目录下找到`app.jar`
4. 使用命令启动服务：`java -jar app.jar`

## 注意事项
- 项目依赖多个内部模块，请确保相关依赖模块已正确构建
- 部署前请确保相关配置文件已正确配置
- 建议使用Docker或Kubernetes进行容器化部署 